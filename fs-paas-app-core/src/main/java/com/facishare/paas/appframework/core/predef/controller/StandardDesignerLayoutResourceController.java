package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.IButtonDocument;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.SceneDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.layout.FindCustomBasicConfiguration;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.bizfield.BizFieldDocument;
import com.facishare.paas.appframework.metadata.bizfield.BizFieldLogicService;
import com.facishare.paas.appframework.metadata.button.LayoutDesignerButtonManager;
import com.facishare.paas.appframework.metadata.button.SpecialButtonManager;
import com.facishare.paas.appframework.metadata.button.SpecialButtonProvider;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.layout.ButtonOrder;
import com.facishare.paas.appframework.metadata.layout.ButtonRender;
import com.facishare.paas.appframework.metadata.layout.EditLayout;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.*;
import static com.facishare.paas.appframework.metadata.SceneLogicService.IS_OUTER;

@Slf4j
public class StandardDesignerLayoutResourceController extends PreDefineController<StandardDesignerLayoutResourceController.Arg, StandardDesignerLayoutResourceController.Result> {

    private SpecialButtonManager specialButtonManager;
    private LayoutDesignerButtonManager layoutDesignerButtonManager;
    private BizFieldLogicService bizFieldLogicService;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        if (arg.isOuter()) {
            controllerContext.setAttribute(IS_OUTER, true);
        } else {
            controllerContext.setAttribute(IS_OUTER, false);
        }
        super.before(arg);
        specialButtonManager = serviceFacade.getBean(SpecialButtonManager.class);
        layoutDesignerButtonManager = serviceFacade.getBean(LayoutDesignerButtonManager.class);
        bizFieldLogicService = serviceFacade.getBean(BizFieldLogicService.class);
        if (Strings.isNullOrEmpty(arg.getLayoutType())) {
            arg.setLayoutType(LayoutTypes.DETAIL);
        }
    }

    @Override
    protected Result doService(Arg arg) {
        Result result = Result.builder().build();

        //新建自定义对象的时候没有describeApiName，返回通用的字段类型和按钮列表
        ObjectDescribeExt describeExt = null;
        if (!Strings.isNullOrEmpty(arg.getDescribeApiName())) {
            IObjectDescribe describe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getDescribeApiName());
            describeExt = ObjectDescribeExt.of(describe);
        }

        switch (arg.getLayoutType()) {
            case LayoutTypes.DETAIL:
                //详情页布局
                processDetailLayoutResource(controllerContext.getTenantId(), result, describeExt);
                break;
            case LayoutTypes.LIST_LAYOUT:
                //列表页布局
                processListLayoutResource(result, describeExt);
                break;
            case LayoutTypes.EDIT:
                //新建编辑页布局
                processEditLayoutResource(controllerContext.getTenantId(), result, describeExt);
                break;
            case LayoutTypes.WHAT_LIST:
                processWhatListLayoutResource(controllerContext.getUser(), result, describeExt);
                break;
            case LayoutTypes.FLOW_TASK_LIST:
                processFlowTaskLayoutResource(controllerContext.getUser(), result, describeExt);
                break;
            default:
                break;
        }
        buildOptionalFeaturesSwitch(arg.isBigObject(), result);
        return result;
    }

    private void processWhatListLayoutResource(User user, Result result, ObjectDescribeExt describeExt) {
        if (Objects.isNull(describeExt)) {
            return;
        }
        IObjectDescribe whatObjectDescribe = serviceFacade.findObject(user.getTenantId(), arg.getWhatApiName());

        result.setFlowTaskLayoutOptionalFields(findFlowTaskLayoutOptionalFields(user, describeExt, whatObjectDescribe));
        List<String> fieldNameList = FLOW_LIST_LAYOUT_FIELD.get(describeExt.getApiName());
        if (CollectionUtils.notEmpty(fieldNameList)) {
            List<String> flowTaskLayoutDefaultFields = fieldNameList.stream()
                    .filter(describeExt::containsField)
                    .collect(Collectors.toList());
            IFieldDescribe nameField = ObjectDescribeExt.of(whatObjectDescribe).getNameField();
            flowTaskLayoutDefaultFields.add(WhatComponentExt.getWhatFieldName(nameField.getDescribeApiName(), nameField.getApiName()));
            result.setFlowTaskLayoutDefaultFields(flowTaskLayoutDefaultFields);
        }
    }

    private List<String> findFlowTaskLayoutOptionalFields(User user, ObjectDescribeExt describeExt, IObjectDescribe whatObjectDescribe) {
        Set<String> fieldNames = flowInvisibleFieldNameMap.get(describeExt.getApiName());
        if (CollectionUtils.empty(fieldNames)) {
            return null;
        }
        ILayout defaultLayout = serviceFacade.getLayoutLogicService().findObjectLayoutWithWhatDescribe(user, describeExt, whatObjectDescribe);
        return LayoutExt.of(defaultLayout).getFieldList().stream()
                .filter(it -> !fieldNames.contains(it))
                .collect(Collectors.toList());
    }

    private void processFlowTaskLayoutResource(User user, Result result, ObjectDescribeExt describeExt) {
        if (Objects.isNull(describeExt)) {
            return;
        }
        IObjectDescribe whatObjectDescribe = serviceFacade.findObject(user.getTenantId(), arg.getWhatApiName());
        result.setFlowTaskLayoutOptionalFields(findFlowTaskLayoutOptionalFields(user, describeExt, whatObjectDescribe));
    }

    private void buildOptionalFeaturesSwitch(boolean bigObject, Result result) {
        if (bigObject) {
            // 对于大对象，所有功能都禁用
            result.setOptionalFeatures(infraServiceFacade.createDisabledFeaturesMap());
        } else if (Strings.isNullOrEmpty(arg.getDescribeApiName()) && AppFrameworkConfig.isOptionalFeaturesSupport(controllerContext.getTenantId())) {
            // 获取租户的功能开关配置
            OptionalFeaturesSwitchDTO optionalFeaturesSwitch = infraServiceFacade.udobjOptionalFeaturesSwitchResources(controllerContext.getTenantId());
            // 转换为Map格式
            result.setOptionalFeatures(infraServiceFacade.convertToMap(optionalFeaturesSwitch));
        }
    }

    private void processDetailLayoutResource(String tenantId, Result result, ObjectDescribeExt describeExt) {
        boolean bigObject = arg.isBigObject();
        if (arg.getIncludeFieldTypes()) {
            List<String> fieldTypeList = DefObjConstants.getFieldTypeList(tenantId, arg.getDescribeApiName(), bigObject);
            fillFieldTypeBySwitch(fieldTypeList);
            result.setFieldTypes(fieldTypeList);
            result.setMaskFieldTypes(getMaskFieldTypes(tenantId, describeExt, bigObject));
            result.setDisplayCloneFieldTypes(Lists.newArrayList(AppFrameworkConfig.getFieldEnableCloneFieldTypes(controllerContext.getTenantId())));
            if (!bigObject) {
                List<BizFieldDocument> bizFields = bizFieldLogicService.getAllBizFieldsForManagement(tenantId, arg.getDescribeApiName());
                result.setBizFields(bizFields);
            }
        }

        //流程布局不需要按钮
        if (arg.getIncludeButtons() && !isNeedFlowLayouts()) {
            //需要按钮排序
            List<IButton> orderedButtons = findButtonsByUsePage(describeExt, ButtonUsePageType.Detail, controllerContext.getUser(), bigObject);
            //加工按钮
            List<IButton> buttons = processDetailPageButtons(orderedButtons, describeExt, controllerContext.getUser());
            List<IButtonDocument> documents = IButtonDocument.ofList(buttons);
            result.setButtons(documents);
            Boolean inMasterDetailApprovalGrayList = Optional.ofNullable(describeExt)
                    .map(ObjectDescribeExt::isSlaveObjectCreateWithMasterAndInGrayList)
                    .orElse(AppFrameworkConfig.isInMasterDetailApprovalGrayList(tenantId));
            result.setInMasterDetailApprovalGrayList(inMasterDetailApprovalGrayList);
            result.setSupportButtonMix(UdobjGrayConfig.isAllow(UdobjGrayConfigKey.MOBILE_BUTTON_MIX_GRAY, tenantId));
        }
    }

    private void fillFieldTypeBySwitch(List<String> fieldTypeList) {
        String outDepartmentSwitch = serviceFacade.findTenantConfig(controllerContext.getUser(), "metadata_sys_is_open_ErDepartmentObj");
        if (!fieldTypeList.contains(IFieldType.OUT_DEPARTMENT) && BooleanUtils.toBoolean(outDepartmentSwitch)) {
            int index = fieldTypeList.indexOf(IFieldType.DEPARTMENT);
            index = index >= 0 ? index : fieldTypeList.size() - 1;
            fieldTypeList.add(index + 1, IFieldType.OUT_DEPARTMENT);
        }
    }

    private List<String> getMaskFieldTypes(String tenantId, ObjectDescribeExt describeExt, boolean isBigObject) {
        if (isBigObject) {
            return Lists.newArrayList();
        }
        MaskFieldLogicService maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
        String apiName = Optional.ofNullable(describeExt).map(IObjectDescribe::getApiName).orElse(null);
        return maskFieldLogicService.getMaskFieldTypes(tenantId, apiName);
    }

    /**
     * 获取详情页布局的特殊按钮
     *
     * @param describeExt
     * @return
     */
    protected List<IButton> getSpecialDetailPageButtons(ObjectDescribeExt describeExt) {
        if (!describeExt.isCustomObject()) {
            SpecialButtonProvider provider = specialButtonManager.getLocalProvider(describeExt.getApiName());
            return provider.getSpecialButtons();
        }
        return Lists.newArrayList();
    }

    /**
     * 过滤详情页按钮
     *
     * @param buttons
     * @param user
     * @return
     */
    protected List<IButton> filterDetailPageButton(List<IButton> buttons, User user, ObjectDescribeExt describeExt) {
        if (CollectionUtils.empty(buttons)) {
            return buttons;
        }
        if (AppFrameworkConfig.isGrayLayoutDesignerButtonManagerObject(describeExt.getApiName())) {
            return layoutDesignerButtonManager.getLocalProvider(describeExt.getApiName()).getButtons(buttons, user);
        }
        return buttons;
    }

    /**
     * 加工详情页按钮
     *
     * @param buttons
     * @param describeExt
     * @param user
     * @return
     */
    private List<IButton> processDetailPageButtons(List<IButton> buttons, ObjectDescribeExt describeExt, User user) {
        //老对象特殊按钮处理,不启用的按钮不在布局中显示
        if (describeExt != null) {
            List<IButton> specialButtons = getSpecialDetailPageButtons(describeExt);
            ButtonOrder.combineSpecialButtons(buttons, specialButtons);
        }

        if (CollectionUtils.empty(buttons)) {
            return buttons;
        }
        buttons = serviceFacade.filterPartnerButtons(user, arg.getDescribeApiName(), buttons);
        // 处理预置对象,需要开通服务才展示的按钮
        if (describeExt != null) {
            buttons = filterDetailPageButton(buttons, user, describeExt);
        }
        //过滤button
        return ButtonExt.filterLayoutButtons(describeExt, buttons);
    }

    private void processListLayoutResource(Result result, ObjectDescribeExt describeExt) {
        User user = controllerContext.getUser();
        result.setScenes(findScenes(arg.getDescribeApiName(), user));
        boolean bigObject = describeExt.isBigObject();
        List<IButton> buttons = findButtonsByUsePage(describeExt, ButtonUsePageType.ListNormal, user, bigObject);
        List<IButtonDocument> normalButtons = IButtonDocument.ofList(buttons);
        result.setNormalButtons(IButtonDocument.handleTerminal(normalButtons, user.getTenantId()));

        List<IButton> buttons1 = findButtonsByUsePage(describeExt, ButtonUsePageType.ListBatch, user, bigObject);
        List<IButtonDocument> batchButtons = IButtonDocument.ofList(buttons1);
        //批量按钮支持按企业灰度
        result.setBatchButtons(IButtonDocument.handleTerminal(batchButtons, user.getTenantId()));

        List<IButton> buttons2 = findButtonsByUsePage(describeExt, ButtonUsePageType.DataList, user, bigObject);
        result.setSingleButtons(IButtonDocument.ofList(buttons2));
    }

    private void processEditLayoutResource(String tenantId, Result result, ObjectDescribeExt describeExt) {
        if (Objects.isNull(describeExt)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        //从对象通用按钮
        Map<String, List<IButton>> detailObjButtonMap = Maps.newHashMap();
        //从对象批量按钮
        Map<String, List<IButton>> batchDetailObjButtonMap = Maps.newHashMap();
        //从对象单条按钮
        Map<String, List<IButton>> singleDetailObjButtonMap = Maps.newHashMap();

        // 只返回相关对象的资源
        if (arg.isOnlyRelatedResource()) {
            relatedObjectButtonResource(tenantId, detailObjButtonMap, batchDetailObjButtonMap, singleDetailObjButtonMap);
            result.setDetailObjButtonMap(IButtonDocument.ofMap(detailObjButtonMap));
            result.setBatchDetailObjButtonMap(IButtonDocument.ofMap(batchDetailObjButtonMap));
            result.setSingleDetailObjButtonMap(IButtonDocument.ofMap(singleDetailObjButtonMap));
            return;
        }
        List<String> fieldTypeList = DefObjConstants.getFieldTypeList(tenantId, describeExt.getApiName(), describeExt.isBigObject());
        fillFieldTypeBySwitch(fieldTypeList);
        result.setFieldTypes(fieldTypeList);
        result.setMaskFieldTypes(getMaskFieldTypes(tenantId, describeExt, describeExt.isBigObject()));
        List<String> displayCloneFieldTypes = describeExt.isBigObject() ? Lists.newArrayList() : Lists.newArrayList(AppFrameworkConfig.getFieldEnableCloneFieldTypes(controllerContext.getTenantId()));
        result.setDisplayCloneFieldTypes(displayCloneFieldTypes);

        if (!arg.isBigObject()) {
            List<BizFieldDocument> bizFields = bizFieldLogicService.getAllBizFieldsForManagement(tenantId, arg.getDescribeApiName());
            result.setBizFields(bizFields);
        }

        Map<ButtonUsePageType, List<IButton>> customButtonMap = serviceFacade.findButtonsByUsePages(controllerContext.getUser(),
                describeExt, Lists.newArrayList(ButtonUsePageType.Create, ButtonUsePageType.Edit));

        // 创建转换规则检查器，使用 InfraServiceFacade 调用 supportPullOrder
        BiFunction<User, String, Boolean> convertRuleChecker = (user, objectApiName) -> infraServiceFacade.supportPullOrder(user, objectApiName);
        List<IButton> addPageButtons = EditLayout.buildDefaultButtonsAdd(controllerContext.getTenantId(), describeExt.getApiName(), convertRuleChecker);
        ButtonExt.filterButtonsByBlacklist(addPageButtons, describeExt.isBigObject());
        addPageButtons.addAll(customButtonMap.get(ButtonUsePageType.Create));
        addPageButtons = processAddPageButtons(addPageButtons, describeExt);
        result.setButtonsAdd(IButtonDocument.ofList(addPageButtons));

        List<IButton> editPageButtons = EditLayout.buildDefaultButtonsEdit(controllerContext.getTenantId(), describeExt.getApiName(), convertRuleChecker);
        editPageButtons.addAll(customButtonMap.get(ButtonUsePageType.Edit));
        editPageButtons = processEditPageButtons(editPageButtons, describeExt);
        ButtonExt.filterButtonsByBlacklist(editPageButtons, describeExt.isBigObject());
        result.setButtonsEdit(IButtonDocument.ofList(editPageButtons));

        List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribesCreateWithMaster(controllerContext.getTenantId(),
                describeExt.getApiName());
        detailDescribes.forEach(detailDescribe -> {
            List<IButton> detailObjButtons = EditLayout.buildDefaultListNormalDetailObjButtons(detailDescribe);
            detailObjButtons = processDetailObjectButtons(detailObjButtons, detailDescribe, describeExt);
            detailObjButtonMap.put(detailDescribe.getApiName(), detailObjButtons);

            List<IButton> batchDetailObjButtons = EditLayout.buildDefaultListBatchDetailObjButtons(detailDescribe.getApiName());
            batchDetailObjButtons = processBatchDetailObjectButtons(batchDetailObjButtons, detailDescribe, describeExt);
            batchDetailObjButtonMap.put(detailDescribe.getApiName(), batchDetailObjButtons);

            List<IButton> singleDetailObjButtons = EditLayout.buildDefaultListSingleDetailObjButtons(controllerContext.getTenantId(),
                    detailDescribe.getApiName());
            singleDetailObjButtons = processSingleDetailObjectButtons(singleDetailObjButtons, detailDescribe, describeExt);
            singleDetailObjButtonMap.put(detailDescribe.getApiName(), singleDetailObjButtons);
        });

        // 补充一起新建的相关对象的按钮资源
        relatedObjectButtonResource(tenantId, detailObjButtonMap, batchDetailObjButtonMap, singleDetailObjButtonMap);

        result.setDetailObjButtonMap(IButtonDocument.ofMap(detailObjButtonMap));
        result.setBatchDetailObjButtonMap(IButtonDocument.ofMap(batchDetailObjButtonMap));
        result.setSingleDetailObjButtonMap(IButtonDocument.ofMap(singleDetailObjButtonMap));
    }

    private void relatedObjectButtonResource(String tenantId, Map<String, List<IButton>> detailObjButtonMap, Map<String, List<IButton>> batchDetailObjButtonMap, Map<String, List<IButton>> singleDetailObjButtonMap) {
        if (CollectionUtils.empty(arg.getRelatedApiNames())) {
            return;
        }
        Map<String, IObjectDescribe> objectDescribeMap = serviceFacade.findObjectsWithoutCopy(tenantId, arg.getRelatedApiNames());
        objectDescribeMap.values().forEach(relatedDescribe -> {
            List<IButton> detailObjButtons = EditLayout.buildDefaultListNormalDetailObjButtons(relatedDescribe);
            detailObjButtons.removeIf(it -> StringUtils.equals(ObjectAction.IMPORT_EXCEL.getDefaultButtonApiName(), it.getName()));
            detailObjButtonMap.put(relatedDescribe.getApiName(), detailObjButtons);

            /* 本期只保留从查重关联新建的按钮
            List<IButton> batchDetailObjButtons = EditLayout.buildDefaultListBatchDetailObjButtons(relatedDescribe.getApiName());
            batchDetailObjButtonMap.put(relatedDescribe.getApiName(), batchDetailObjButtons);

            List<IButton> singleDetailObjButtons = EditLayout.buildDefaultListSingleDetailObjButtons(controllerContext.getTenantId(), relatedDescribe.getApiName());
            singleDetailObjButtonMap.put(relatedDescribe.getApiName(), singleDetailObjButtons);
            */
        });
    }

    /**
     * 加工新建页按钮
     *
     * @param addPageButtons
     * @param describe
     * @return
     */
    protected List<IButton> processAddPageButtons(List<IButton> addPageButtons, IObjectDescribe describe) {
        return addPageButtons;
    }

    /**
     * 加工编辑页按钮
     *
     * @param editPageButtons
     * @param describe
     * @return
     */
    protected List<IButton> processEditPageButtons(List<IButton> editPageButtons, IObjectDescribe describe) {
        return editPageButtons;
    }

    /**
     * 加工从对象通用按钮
     *
     * @param detailObjButtons
     * @param detailDescribe
     * @param masterDescribe
     * @return
     */
    protected List<IButton> processDetailObjectButtons(List<IButton> detailObjButtons, IObjectDescribe detailDescribe,
                                                       IObjectDescribe masterDescribe) {
        return detailObjButtons;
    }

    /**
     * 加工从对象批量按钮
     *
     * @param batchDetailObjButtons
     * @param detailDescribe
     * @param masterDescribe
     * @return
     */
    protected List<IButton> processBatchDetailObjectButtons(List<IButton> batchDetailObjButtons, IObjectDescribe detailDescribe,
                                                            IObjectDescribe masterDescribe) {
        return batchDetailObjButtons;
    }

    /**
     * 加工从对象单条按钮
     *
     * @param singleDetailObjButtons
     * @param detailDescribe
     * @param masterDescribe
     * @return
     */
    protected List<IButton> processSingleDetailObjectButtons(List<IButton> singleDetailObjButtons, IObjectDescribe detailDescribe,
                                                             IObjectDescribe masterDescribe) {
        return singleDetailObjButtons;
    }

    private List<IButton> findButtonsByUsePage(ObjectDescribeExt describeExt, ButtonUsePageType usePageType, User user, boolean isBigObject) {
        return ButtonRender.builder()
                .describe(describeExt)
                .customButtonService(serviceFacade)
                .layoutType(arg.getLayoutType())
                .licenseService(serviceFacade)
                .buttonLogicService(serviceFacade)
                .optionalFeaturesService(infraServiceFacade)
                .bigObject(isBigObject)
                .user(user)
                .build()
                .render()   // 查询出所有可用按钮
                .getButtonsByUsePage(usePageType);
    }

    private List<SceneDocument> findScenes(String describeApiName, User user) {
        if (StringUtils.isEmpty(describeApiName)) {
            List<ISearchTemplate> templates = infraServiceFacade.findBaseSearchTemplates(UDOBJ, null, user);
            return SceneDocument.ofTemplates(templates);
        }
        List<IScene> scenes = infraServiceFacade.findScenes(describeApiName, user, null);
        return SceneDocument.ofList(scenes.stream().filter(IScene::isActive).collect(Collectors.toList()));
    }

    private boolean isNeedFlowLayouts() {
        return LayoutExt.NAMESPACE_FLOW.equals(arg.getNamespace());
    }

    @Data
    public static class Arg {
        private String describeApiName;
        private String layoutType;
        /**
         * 一起新建的相关对象
         */
        private List<String> relatedApiNames;
        private boolean onlyRelatedResource;

        private String namespace;
        private Boolean includeButtons = true;
        private Boolean includeFieldTypes = true;
        private boolean bigObject;

        private String whatApiName;
        /**
         * 应用Id
         */
        private String appId;
        private boolean isOuter;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<FindCustomBasicConfiguration.Category> components;
        private List<IButtonDocument> buttons;  // 标题可用的按钮
        private List<String> fieldTypes;
        private List<String> maskFieldTypes;
        private List<String> displayCloneFieldTypes;
        private List<BizFieldDocument> bizFields;

        private List<SceneDocument> scenes;
        private List<IButtonDocument> batchButtons;
        private List<IButtonDocument> normalButtons;
        private List<IButtonDocument> singleButtons;
        private List<IButtonDocument> buttonsAdd;
        private List<IButtonDocument> buttonsEdit;
        private Map<String, List<IButtonDocument>> detailObjButtonMap;
        private Map<String, List<IButtonDocument>> batchDetailObjButtonMap;
        private Map<String, List<IButtonDocument>> singleDetailObjButtonMap;
        private Map<String, Boolean> optionalFeatures;
        private Boolean inMasterDetailApprovalGrayList;
        // 支持按钮混排标识
        private Boolean supportButtonMix;

        /**
         * 流程待办布局的可选字段
         */
        private List<String> flowTaskLayoutOptionalFields;
        /**
         * 流程待办摘要布局的默认字段（修建摘要布局时用）
         */
        private List<String> flowTaskLayoutDefaultFields;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Category {
        private String label;
        private String define_type;
        private List<Map> children;

        public static <T extends IComponent> Category of(String defineType, List<T> children) {
            List<Map> childrenMap = children.stream().map(x -> ComponentExt.of(x).toMap()).collect(Collectors.toList());
            return builder().define_type(defineType).children(childrenMap).build();
        }
    }
}
