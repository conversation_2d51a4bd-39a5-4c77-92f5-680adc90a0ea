package com.facishare.paas.appframework.core.predef.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.ManageGroupDTO;
import com.facishare.paas.appframework.core.predef.service.dto.layout.*;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.dto.LayoutResult;
import com.facishare.paas.appframework.metadata.dto.LayoutRoleInfo;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.fieldalign.GlobalFieldAlign;
import com.facishare.paas.appframework.metadata.fieldalign.GlobalFieldAlignService;
import com.facishare.paas.appframework.metadata.fieldextra.FieldBackgroundExtraLogicService;
import com.facishare.paas.appframework.metadata.layout.*;
import com.facishare.paas.appframework.metadata.layout.component.FlowTaskListMobileComponentExt;
import com.facishare.paas.appframework.metadata.layout.factory.ExtraLayoutManage;
import com.facishare.paas.appframework.metadata.layout.factory.IComponentFactoryManager;
import com.facishare.paas.appframework.metadata.layout.factory.LayoutProcessorContext;
import com.facishare.paas.appframework.metadata.layout.factory.ListComponentFactory;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.metadata.relation.SourceTypes;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.auth.model.RoleViewPojo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeExtService;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 对象Layout接口
 * <p>
 * Created by liyiguang on 2017/7/3.
 */
@Slf4j
@Service
@ServiceModule("layout")
public class ObjectLayoutService {

    @Autowired
    private LayoutLogicService layoutLogicService;

    @Autowired
    private ServiceFacade serviceFacade;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private ConfigService configService;

    @Autowired
    private RedissonService redissonService;

    @Autowired
    private GlobalFieldAlignService fieldAlignService;

    @Autowired
    private ListComponentFactory listComponentFactory;

    @Autowired
    private ReferenceLogicService referenceLogicService;

    @Autowired
    LogService logService;

    @Autowired
    private FieldBackgroundExtraLogicService fieldBackgroundExtraLogicService;
    @Autowired
    private ChangeOrderLogicService changeOrderLogicService;
    @Autowired
    private IComponentFactoryManager componentFactoryManager;
    @Autowired
    private ApplicationLayeredLogicService applicationLayeredLogicService;
    @Autowired
    FunctionPrivilegeService functionPrivilegeService;

    @Autowired
    ExtraLayoutManage extraLayoutManage;

    private IObjectDescribeExtService describeExtService;

    @Autowired
    public void setDescribeExtService(IObjectDescribeExtService describeExtService) {
        this.describeExtService = describeExtService;
    }

    @ServiceMethod("createLayout")
    public CreateLayout.Result createLayout(CreateLayout.Arg arg, ServiceContext context) {
        ILayout layout = new Layout(arg.getLayoutData());

        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        layout = layoutLogicService.createLayout(layoutContext, layout, BooleanUtils.isNotTrue(arg.getSkipValidate()));

        return CreateLayout.Result.builder().layout(LayoutDocument.of(layout)).build();
    }

    @ServiceMethod("updateLayout")
    public UpdateLayout.Result updateLayout(UpdateLayout.Arg arg, ServiceContext context) {
        ILayout layout = new Layout(Document.parse(arg.getLayoutData()));

        layout = layoutLogicService.updateLayout(context.getUser(), layout, BooleanUtils.isNotTrue(arg.getSkipValidate()));

        return UpdateLayout.Result.builder().layout(LayoutDocument.of(layout)).build();
    }

    @ServiceMethod("deleteLayout")
    public DeleteLayout.Result deleteLayout(DeleteLayout.Arg arg, ServiceContext context) {
        ILayout layout = layoutLogicService.deleteLayout(context.getUser(), arg.getLayoutId());

        return DeleteLayout.Result.builder().layout(LayoutDocument.of(layout)).build();
    }

    @ServiceMethod("findLayout")
    public FindLayout.Result findLayout(FindLayout.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectDescribeApiName());
        ILayout layout = layoutLogicService.findLayoutByApiName(context.getUser(), arg.getApiName(), describe.getApiName());

        return FindLayout.Result.builder().objectDescribeDraft(ObjectDescribeDocument.of(describe)).objectDescribe(ObjectDescribeDocument.of(describe)).layout(LayoutDocument.of(layout)).build();
    }

    @ServiceMethod("findListLayout")
    public FindMobileListLayout.Result findMobileListLayout(FindMobileListLayout.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectDescribeApiName());
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        List<ILayout> listLayouts = layoutLogicService.findMobileListLayout(layoutContext, describe, true);

        //移除禁用和删除的字段
        listLayouts.forEach(x -> TableComponentRender.builder().describeExt(ObjectDescribeExt.of(describe))
                .tableComponentExt(TableComponentExt.of(LayoutExt.of(x).getTableComponent().get())).build()
                .renderShowImageAndTag()
                .handleIsShowLabel(x)
                .removeInactiveFields()
                .setDefaultFieldListIfEmpty()
                .removeIfEmpty());
        FindMobileListLayout.Result result = FindMobileListLayout.Result.builder().listLayout(LayoutDocument.ofList(listLayouts)).build();
        if (arg.isIncludeDescribe()) {
            result.setObjectDescribe(ObjectDescribeDocument.of(describe));
            result.setObjectDescribeDraft(ObjectDescribeDocument.of(describe));
        }
        return result;
    }

    @ServiceMethod("findDetailLayoutList")
    public FindDetailLayoutList.Result findDetailLayoutList(FindDetailLayoutList.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectDescribeApiName());
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        List<ILayout> detailLayouts = layoutLogicService.getDetailLayouts(layoutContext, describe);

        List<DocumentBaseEntity> layoutDocs = detailLayouts.stream().map(layout -> {
            DocumentBaseEntity doc = new DocumentBaseEntity();
            doc.put(ILayout.NAME, layout.getName());
            doc.put("label", layout.getDisplayName());
            doc.put(ILayout.IS_DEFAULT, layout.isDefault());
            return doc;
        }).collect(Collectors.toList());

        return FindDetailLayoutList.Result.builder().listLayout(layoutDocs).build();
    }

    @ServiceMethod("findDetailLayoutFullList")
    public FindDetailLayoutList.Result findDetailLayoutFullList(FindDetailLayoutList.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectDescribeApiName());
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        List<ILayout> detailLayouts = layoutLogicService.getDetailLayouts(layoutContext, describe);

        List<DocumentBaseEntity> layoutDocs = detailLayouts.stream().map(LayoutDocument::of).collect(Collectors.toList());

        return FindDetailLayoutList.Result.builder().listLayout(layoutDocs).build();
    }

    @ServiceMethod("findSimpleLayoutList")
    public FindSimpleLayoutList.Result findSimpleLayoutList(FindSimpleLayoutList.Arg arg, ServiceContext context) {
        List<ILayout> layouts;
        List<String> layoutTypes = CollectionUtils.nullToEmpty(arg.getLayoutTypes());
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        if (CollectionUtils.notEmpty(arg.getLayoutApiNames())) {
            User user = context.getUser();
            IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), arg.getObjectDescribeApiName());
            Map<String, Layout> layoutMap = changeOrderLogicService.findDetailLayoutsWithChangeOrder(user, describe,
                    describeApiName -> layoutLogicService.findLayoutByApiNames(user.getTenantId(), Lists.newArrayList(arg.getLayoutApiNames()), describeApiName));
            layouts = Lists.newArrayList(layoutMap.values());
        } else {
            if (arg.isExcludeFLowLayout()) {
                layouts = layoutLogicService.findByTypes(layoutContext, arg.getObjectDescribeApiName(),
                        layoutTypes);
            } else {
                layouts = layoutLogicService.findByTypesIncludeFlowLayout(layoutContext,
                        arg.getObjectDescribeApiName(), layoutTypes);
            }
        }
        // 对于未灰度移动端多摘要布局的对象，不返回列表摘要布局
        if (ObjectListConfig.MANGE_GROUP.equals(arg.getSourceInfo())) {
            Set<String> unSupportTypes = Sets.newHashSet();
            if (layoutTypes.contains(LayoutTypes.LIST) && !AppFrameworkConfig.manyAbstractLayout(arg.getObjectDescribeApiName(), context.getTenantId())) {
                unSupportTypes.add(LayoutTypes.LIST);
            }
            if (layoutTypes.contains(LayoutTypes.LIST_LAYOUT) && !AppFrameworkConfig.isGrayListLayout(context.getTenantId(), arg.getObjectDescribeApiName())) {
                unSupportTypes.add(LayoutTypes.LIST_LAYOUT);
            }
            if (CollectionUtils.notEmpty(unSupportTypes)) {
                layouts.removeIf(it -> unSupportTypes.contains(it.getLayoutType()));
            }
        }

        List<ILayout> flowLayouts = layouts.stream().filter(x -> LayoutExt.of(x).isFlowLayout()).collect(Collectors.toList());
        layouts.removeIf(x -> LayoutExt.of(x).isFlowLayout());
        List<ILayout> sortedLayouts = Lists.newArrayList();
        sortedLayouts.addAll(layouts.stream().filter(x -> LayoutExt.of(x).isDetailLayout() && BooleanUtils.isTrue(x.isDefault())).collect(Collectors.toList()));
        sortedLayouts.addAll(layouts.stream().filter(x -> LayoutExt.of(x).isDetailLayout() && BooleanUtils.isNotTrue(x.isDefault())).collect(Collectors.toList()));
        sortedLayouts.addAll(layouts.stream().filter(x -> LayoutExt.of(x).isEditLayout() && BooleanUtils.isTrue(x.isDefault())).collect(Collectors.toList()));
        sortedLayouts.addAll(layouts.stream().filter(x -> LayoutExt.of(x).isEditLayout() && BooleanUtils.isNotTrue(x.isDefault())).collect(Collectors.toList()));
        sortedLayouts.addAll(layouts.stream().filter(x -> !LayoutExt.of(x).isDetailLayout() && !LayoutExt.of(x).isEditLayout()).collect(Collectors.toList()));
        sortedLayouts.addAll(flowLayouts);
        Map<String, String> layoutConfig = Maps.newHashMap();
        layouts.forEach(x -> {
            String referenceFieldConfig = LayoutExt.of(x).getReferenceFieldConfig();
            if (StringUtils.isNotEmpty(referenceFieldConfig)) {
                layoutConfig.put(x.getName(), referenceFieldConfig);
            }
        });
        List<DocumentBaseEntity> layoutDocs = sortedLayouts.stream().map(layout -> {
            DocumentBaseEntity doc = new DocumentBaseEntity();
            doc.put(ILayout.NAME, layout.getName());
            doc.put("label", layout.getDisplayName());
            doc.put(ILayout.IS_DEFAULT, layout.isDefault());
            doc.put(ILayout.LAYOUT_TYPE, layout.getLayoutType());
            doc.put(ILayout.NAMESPACE, layout.getNamespace());
            doc.put(ILayout.CREATED_BY, layout.getCreatedBy());
            doc.put(ILayout.CREATE_TIME, layout.getCreateTime());
            if (layoutConfig.containsKey(layout.getName())) {
                doc.put(FormComponentExt.REFERENCE_FIELD_CONFIG, layoutConfig.get(layout.getName()));
            }
            return doc;
        }).collect(Collectors.toList());

        return FindSimpleLayoutList.Result.builder().layouts(layoutDocs).build();
    }

    @ServiceMethod("findByObjectDescribeApiName")
    public FindByObjectApiName.Result findByObjectApiName(FindByObjectApiName.Arg arg, ServiceContext context) {
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        List<ILayout> layouts = layoutLogicService.findLayoutByObjectApiName(layoutContext, arg.getObjectDescribeApiName());
        return FindByObjectApiName.Result.builder().layouts(LayoutDocument.ofList(layouts)).build();
    }

    @ServiceMethod("findLayoutByApplication")
    public FindLayoutByApplication.Result findLayoutByApplication(FindLayoutByApplication.Arg arg, ServiceContext context) {
        List<ILayout> layouts = applicationLayeredLogicService.findLayoutByAppId(context.getUser(), arg.getAppId());
        return FindLayoutByApplication.Result.builder()
                .layouts(LayoutDocument.ofList(layouts))
                .build();
    }

    @ServiceMethod("createAndUpdateDescribe")
    public CreateAndUpdateDescribe.Result createLayoutAndUpdateDescribe(CreateAndUpdateDescribe.Arg arg, ServiceContext context) {
        ILayout layout = new Layout(Document.parse(arg.getLayoutData()));
        IObjectDescribe describeDraft = new ObjectDescribe();
        describeDraft.fromJsonString(arg.getDescribeData());

        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        LayoutResult layoutResult = layoutLogicService.createLayoutAndUpdateDescribe(layoutContext, layout, describeDraft);

        return CreateAndUpdateDescribe.Result.builder().layout(LayoutDocument.of(layoutResult.getLayout())).objectDescribeDraft(ObjectDescribeDocument.of(layoutResult.getObjectDescribe())).objectDescribe(ObjectDescribeDocument.of(layoutResult.getObjectDescribe())).build();
    }

    @ServiceMethod("updateLayoutAndUpdateDescribe")
    public UpdateLayoutAndUpdateDescribe.Result updateLayoutAndUpdateDescribe(UpdateLayoutAndUpdateDescribe.Arg arg, ServiceContext context) {
        ILayout layout = new Layout(Document.parse(arg.getLayoutData()));
        IObjectDescribe describe = new ObjectDescribe();
        describe.fromJsonString(arg.getDescribeData());

        LayoutResult layoutResult = layoutLogicService.updateLayoutAndUpdateDescribe(context.getUser(), layout, describe);

        return UpdateLayoutAndUpdateDescribe.Result.builder().layout(LayoutDocument.of(layoutResult.getLayout())).objectDescribeDraft(ObjectDescribeDocument.of(layoutResult.getObjectDescribe())).objectDescribe(ObjectDescribeDocument.of(layoutResult.getObjectDescribe())).build();
    }

    @ServiceMethod("findByObjDescribeApiName")
    public FindByObjDescribeApiName.Result findByObjectDescribeApiName(FindByObjDescribeApiName.Arg arg, ServiceContext context) {
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        // 判断类型，查询流程待办列表页布局
        if (LayoutTypes.FLOW_TASK_LIST.equals(arg.getLayoutType()) || LayoutTypes.WHAT_LIST.equals(arg.getLayoutType())) {
            List<ILayout> layouts = layoutLogicService.findFlowTaskListLayoutsByType(layoutContext, arg.getObjectDescribeApiName(), arg.getWhatDescribeApiName(), arg.getLayoutType());
            return new FindByObjDescribeApiName.Result(LayoutDocument.ofList(layouts), null);
        }
        List<ILayout> layouts = layoutLogicService.findLayoutByObjectApiNameAndLayoutTypeIncludeFlowLayout(layoutContext,
                arg.getObjectDescribeApiName(), arg.getLayoutType());
        //传了namespace则按照namespace过滤
        if (!Strings.isNullOrEmpty(arg.getNamespace())) {
            layouts = layouts.stream().filter(x -> Objects.equals(LayoutExt.of(x).getNamespace(), arg.getNamespace())).collect(Collectors.toList());
        } else if (!arg.includeAllNamespace()) {
            layouts = layouts.stream().filter(x -> Strings.isNullOrEmpty(LayoutExt.of(x).getNamespace())).collect(Collectors.toList());
        }

        if (CollectionUtils.notEmpty(layouts) && LayoutExt.NAMESPACE_FLOW.equals(arg.getNamespace())) {
            List<ReferenceData> referenceDataList = referenceLogicService.findReferenceByTarget(context.getTenantId(),
                    TargetTypes.DESCRIBE_LAYOUT, arg.getObjectDescribeApiName(), true);

            //查询主对象流程布局的名称
            List<ReferenceData> referenceDataListByMasterLayout = referenceDataList.stream()
                    .filter(x -> SourceTypes.MASTER_FLOW_LAYOUT.equals(x.getSourceType()))
                    .collect(Collectors.toList());
            Map<String, Set<String>> masterLayoutApiNameMap = Maps.newHashMap();
            referenceDataListByMasterLayout.forEach(x -> {
                String[] sourceValues = StringUtils.split(x.getSourceValue(), ".");
                masterLayoutApiNameMap.computeIfAbsent(sourceValues[0], k -> Sets.newHashSet()).add(sourceValues[1]);
            });
            Map<String, String> layoutLabelMap = Maps.newHashMap();
            masterLayoutApiNameMap.forEach((describeApiName, layoutApiNames) -> {
                Map<String, Layout> layoutMap = layoutLogicService.findLayoutByApiNames(context.getTenantId(), Lists.newArrayList(layoutApiNames), describeApiName);
                layoutMap.values().forEach(layout -> layoutLabelMap.put(describeApiName + "." + layout.getName(), layout.getDisplayName()));
            });

            Map<String, List<ReferenceData>> referenceDataMap = referenceDataList.stream().collect(Collectors.groupingBy(x -> x.getTargetValue()));
            layouts.stream().forEach(layout -> {
                List<ReferenceData> referenceDataByLayout = referenceDataMap.get(arg.getObjectDescribeApiName() + "." + layout.getName());
                if (CollectionUtils.empty(referenceDataByLayout)) {
                    return;
                }
                List<String> usedInfoList = referenceDataByLayout.stream().map(referenceData -> {
                    String sourceLabel = layoutLabelMap.getOrDefault(referenceData.getSourceValue(), referenceData.getSourceLabel());
                    return "[" + I18NExt.text(referenceData.getSourceType()) + "]" + sourceLabel;
                }).collect(Collectors.toList());
                String usedInfo = StringUtils.join(usedInfoList, ",");
                LayoutExt.of(layout).setUsedInfo(usedInfo);
            });
        }
        ManageGroup manageGroup = layoutLogicService.queryLayoutManageGroup(context.getUser(), arg.getObjectDescribeApiName(), arg.getSourceInfo());
        return new FindByObjDescribeApiName.Result(LayoutDocument.ofList(layouts), ManageGroupDTO.of(manageGroup));
    }

    @ServiceMethod("findLayoutByRecordTypeAndRole")
    public FindLayoutByRecordTypeAndRole.Result findLayoutByRecordTypeAndRole(FindLayoutByRecordTypeAndRole.Arg arg, ServiceContext context) {
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        ILayout layout = layoutLogicService.findObjectLayoutByRole(layoutContext, arg.getRecordType(), arg.getDescribeApiName(), arg.getRole(), arg.getLayoutType());

        return FindLayoutByRecordTypeAndRole.Result.builder().layout(LayoutDocument.of(layout)).build();
    }

    @ServiceMethod("findLayoutByRecordType")
    public FindLayoutByRecordType.Result findLayoutByRecordType(FindLayoutByRecordType.Arg arg, ServiceContext context) {
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        ILayout layout = layoutLogicService.findObjectLayoutByType(layoutContext, arg.getRecordType(), arg.getDescribeApiName(), arg.getLayoutType());

        // 处理布局中特殊字段的字段类型信息
        LayoutExt.of(layout).getFormComponent().ifPresent(formComponentExt -> formComponentExt.adjustFieldRenderType(arg.getDescribeApiName()));

        return FindLayoutByRecordType.Result.builder().layout(LayoutDocument.of(layout)).build();
    }

    @ServiceMethod("find_what_list_layout")
    public FindWhatListLayout.Result findWhatListLayout(FindWhatListLayout.Arg arg, ServiceContext context) {
        Map<String, IObjectDescribe> objects = describeLogicService.findObjects(context.getTenantId(), Lists.newArrayList(arg.getObjectDescribeApiName(), arg.getWhatApiName()));
        Lists.newArrayList(objects.keySet()).forEach(k -> objects.put(k, objects.get(k).copy()));

        IObjectDescribe describe = objects.get(arg.getObjectDescribeApiName());
        IObjectDescribe whatDescribe = objects.get(arg.getWhatApiName());
        ObjectDescribeExt.of(whatDescribe).removeAbstractField();

        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        List<ILayout> listLayouts = layoutLogicService.findWhatListLayout(layoutContext, describe, whatDescribe);

        //移除禁用和删除的字段
        listLayouts.forEach(a -> WhatComponentRender.builder().describeExt(ObjectDescribeExt.of(describe)).whatDescribeExt(ObjectDescribeExt.of(whatDescribe)).whatComponentExt(WhatComponentExt.of(LayoutExt.of(a).getWhatComponent().get())).build().render());

        return FindWhatListLayout.Result.builder().objectDescribe(ObjectDescribeDocument.of(describe)).whatDescribe(ObjectDescribeDocument.of(whatDescribe)).listLayout(LayoutDocument.ofList(listLayouts)).build();
    }

    @ServiceMethod("createWebLayoutAndCreateDescribe")
    public CreateWebLayoutAndCreateDescribe.Result createWebLayoutAndCreateDescribe(CreateWebLayoutAndCreateDescribe.Arg arg, ServiceContext context) {
        ILayout layout = new Layout(arg.getLayoutData());
        ILayout listLayout = new Layout(arg.getListLayoutData());
        IObjectDescribe describeDraft = new ObjectDescribe(arg.getDescribeData());

        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        LayoutResult layoutResult = layoutLogicService.createLayoutAndCreateDescribe(layoutContext, layout, listLayout, describeDraft);
        ObjectDescribeDocument describeExtraArg = arg.getDescribeExtra();
        ObjectDescribeDocument describeExtra = updateDescribeExtra(context.getUser(), describeDraft.getApiName(), describeExtraArg);
        IActionContext metadataContext = ActionContextExt.of(context.getUser()).getContext();
        describeExtService.upsert(metadataContext, Lists.newArrayList(ObjectDescribeExtraExt.of(describeDraft.getApiName(), describeDraft, describeExtraArg).getObjectDescribeExtra()));
        fieldBackgroundExtraLogicService.bulkUpsertFieldsExtra(context.getUser(), describeDraft.getApiName(), arg.getFieldsExtra());
        createOptionalFeaturesSwitch(context.getUser(), describeDraft, describeExtraArg, describeExtra);
        return CreateWebLayoutAndCreateDescribe.Result.builder()
                .layout(LayoutDocument.of(layoutResult.getLayout()))
                .objectDescribe(ObjectDescribeDocument.of(layoutResult.getObjectDescribe()))
                .describeExtra(describeExtra)
                .fieldsExtra(arg.getFieldsExtra())
                .build();
    }

    private void createOptionalFeaturesSwitch(User user, IObjectDescribe objectDescribe, ObjectDescribeDocument describeExtraArg, ObjectDescribeDocument describeExtra) {
        if (CollectionUtils.notEmpty(describeExtraArg) && AppFrameworkConfig.isOptionalFeaturesSupport(user.getTenantId())) {
            OptionalFeaturesSwitchDTO optionalFeaturesSwitch = generateOptionalFeaturesSwitchDTO(user, objectDescribe, describeExtraArg);
            configService.upsertTenantConfig(user, objectDescribe.getApiName() + "|" + OptionalFeaturesService.OPTIONAL_FEATURES, JSON.toJSONString(optionalFeaturesSwitch), ConfigValueType.JSON);
            if (CollectionUtils.empty(describeExtra)) {
                describeExtra = new ObjectDescribeDocument();
                describeExtra.put(IObjectDescribe.API_NAME, objectDescribe.getApiName());
            }
            describeExtra.put(OptionalFeaturesService.RELATED_TEAM_SWITCH, optionalFeaturesSwitch.getIsRelatedTeamEnabled());
            describeExtra.put(OptionalFeaturesService.GLOBAL_SEARCH_SWITCH, optionalFeaturesSwitch.getIsGlobalSearchEnabled());
            describeExtra.put(OptionalFeaturesService.FOLLOW_UP_DYNAMIC_SWITCH, optionalFeaturesSwitch.getIsFollowUpDynamicEnabled());
            describeExtra.put(OptionalFeaturesService.MODIFY_RECORD_SWITCH, optionalFeaturesSwitch.getIsModifyRecordEnabled());
            describeExtra.put(OptionalFeaturesService.MULTI_FIELD_SORT, optionalFeaturesSwitch.getMultiFieldSort());
            describeExtra.put(OptionalFeaturesService.CROSS_OBJECT_FILTER_BUTTON, optionalFeaturesSwitch.getCrossObjectFilterButton());
            logService.log(user, EventType.ADD, ActionType.CREATE_OBJ, objectDescribe.getApiName(),
                    I18NExt.text(I18NKey.OPTIONAL_FEATURES_DETAILS,
                            I18NExt.getSwitchState(optionalFeaturesSwitch.getIsRelatedTeamEnabled()),
                            I18NExt.getSwitchState(optionalFeaturesSwitch.getIsGlobalSearchEnabled()),
                            I18NExt.getSwitchState(optionalFeaturesSwitch.getIsFollowUpDynamicEnabled()),
                            I18NExt.getSwitchState(optionalFeaturesSwitch.getIsModifyRecordEnabled()),
                            I18NExt.getSwitchState(optionalFeaturesSwitch.getMultiFieldSort()),
                            I18NExt.getSwitchState(optionalFeaturesSwitch.getCrossObjectFilterButton())));
        }
    }

    private static OptionalFeaturesSwitchDTO generateOptionalFeaturesSwitchDTO(User user, IObjectDescribe objectDescribe, ObjectDescribeDocument describeExtraArg) {
        Boolean relatedTeamSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.RELATED_TEAM_SWITCH);
        Boolean globalSearchSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.GLOBAL_SEARCH_SWITCH);
        Boolean followUpDynamicSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.FOLLOW_UP_DYNAMIC_SWITCH);
        Boolean modifyRecordSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.MODIFY_RECORD_SWITCH);
        Boolean multiFieldSortSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.MULTI_FIELD_SORT);
        Boolean crossObjectFilterButtonSwitch = (Boolean) describeExtraArg.get(OptionalFeaturesService.CROSS_OBJECT_FILTER_BUTTON);
        OptionalFeaturesSwitchDTO mackDefaultByDescribe = OptionalFeaturesSwitchDTO.mackDefaultByDescribe(user.getTenantId(), objectDescribe);
        return OptionalFeaturesSwitchDTO.builder()
                .isRelatedTeamEnabled(Objects.isNull(relatedTeamSwitch) ? mackDefaultByDescribe.getIsRelatedTeamEnabled() : relatedTeamSwitch)
                .isGlobalSearchEnabled(Objects.isNull(globalSearchSwitch) ? mackDefaultByDescribe.getIsGlobalSearchEnabled() : globalSearchSwitch)
                .isFollowUpDynamicEnabled(Objects.isNull(followUpDynamicSwitch) ? mackDefaultByDescribe.getIsFollowUpDynamicEnabled() : followUpDynamicSwitch)
                .isModifyRecordEnabled(Objects.isNull(modifyRecordSwitch) ? mackDefaultByDescribe.getIsModifyRecordEnabled() : modifyRecordSwitch)
                .multiFieldSort(BooleanUtils.isTrue(multiFieldSortSwitch))
                .crossObjectFilterButton(Objects.isNull(crossObjectFilterButtonSwitch) ? mackDefaultByDescribe.getCrossObjectFilterButton() : crossObjectFilterButtonSwitch)
                .build();
    }

    @ServiceMethod("createWebLayoutAndUpdateDescribe")
    public CreateWebLayoutAndUpdateDescribe.Result createWebLayoutAndUpdateDescribe(CreateWebLayoutAndUpdateDescribe.Arg arg, ServiceContext context) {
        ILayout layout = new Layout(arg.getLayoutData());
        // 列表页布局
        LayoutExt layoutExt = LayoutExt.of(layout);
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        if (layoutExt.isListLayout()) {
            ILayout layoutResult = layoutLogicService.createListLayout(layoutContext, layout);
            return CreateWebLayoutAndUpdateDescribe.Result.builder().layout(LayoutDocument.of(layoutResult)).build();
        }

        if (layoutExt.isMobileListLayout()) {
            ILayout layoutResult = layoutLogicService.createAbstractLayout(layoutContext, layout);
            return CreateWebLayoutAndUpdateDescribe.Result.builder().layout(LayoutDocument.of(layoutResult)).build();
        }

        if (layoutExt.isFlowTaskLayout()) {
            ILayout layoutResult = layoutLogicService.createFLowTaskListLayout(layoutContext, layout);
            return CreateWebLayoutAndUpdateDescribe.Result.builder().layout(LayoutDocument.of(layoutResult)).build();
        }
        if (layoutExt.isWhatListLayout()) {
            ILayout layoutResult = layoutLogicService.createLayout(context.getUser(), layout);
            return CreateWebLayoutAndUpdateDescribe.Result.builder().layout(LayoutDocument.of(layoutResult)).build();
        }

        ObjectDescribeDocument describeDocument = CollectionUtils.empty(arg.getDescribeData()) ? arg.getDraftData() : arg.getDescribeData();
        IObjectDescribe describeDraft = new ObjectDescribe(describeDocument);

        LayoutResult layoutResult = layoutLogicService.createLayoutAndUpdateDescribe(layoutContext, layout, describeDraft, arg.getPersistentDataCalc());
        ObjectDescribeDocument describeExtra = updateDescribeExtra(context.getUser(), describeDraft.getApiName(), arg.getDescribeExtra());
        fieldBackgroundExtraLogicService.bulkUpsertFieldsExtra(context.getUser(), describeDraft.getApiName(), arg.getFieldsExtra());
        return CreateWebLayoutAndUpdateDescribe.Result.builder()
                .layout(LayoutDocument.of(layoutResult.getLayout()))
                .objectDescribeDraft(ObjectDescribeDocument.of(layoutResult.getObjectDescribe()))
                .objectDescribe(ObjectDescribeDocument.of(layoutResult.getObjectDescribe()))
                .describeExtra(describeExtra)
                .fieldsExtra(arg.getFieldsExtra())
                .build();
    }

    @ServiceMethod("updateWebLayoutAndUpdateDescribe")
    public UpdateWebLayoutAndUpdateDescribe.Result updateWebLayoutAndUpdateDescribe(UpdateWebLayoutAndUpdateDescribe.Arg arg, ServiceContext context) {
        ILayout layout = new Layout(arg.getLayoutData());
        LayoutExt layoutExt = LayoutExt.of(layout);
        if (layoutExt.isListLayout()) {
            ILayout layoutResult = layoutLogicService.updateListLayout(context.getUser(), layout);
            return UpdateWebLayoutAndUpdateDescribe.Result.builder().layout(LayoutDocument.of(layoutResult)).build();
        }

        if (layoutExt.isMobileListLayout()) {
            ILayout layoutResult = layoutLogicService.updateAbstractLayout(context.getUser(), layout);
            return UpdateWebLayoutAndUpdateDescribe.Result.builder().layout(LayoutDocument.of(layoutResult)).build();
        }
        if (layoutExt.isFlowTaskLayout()) {
            ILayout layoutResult = layoutLogicService.updateFLowTaskListLayout(context.getUser(), layout);
            return UpdateWebLayoutAndUpdateDescribe.Result.builder().layout(LayoutDocument.of(layoutResult)).build();
        }
        if (layoutExt.isWhatListLayout()) {
            ILayout layoutResult = layoutLogicService.updateLayout(context.getUser(), layout);
            return UpdateWebLayoutAndUpdateDescribe.Result.builder().layout(LayoutDocument.of(layoutResult)).build();
        }

        ObjectDescribeDocument describeDocument = CollectionUtils.empty(arg.getDescribeData()) ? arg.getDraftData() : arg.getDescribeData();
        IObjectDescribe describe = new ObjectDescribe(describeDocument);

        LayoutResult layoutResult = layoutLogicService.updateLayoutAndUpdateDescribe(context.getUser(), layout, describe, arg.getPersistentDataCalc());
        ObjectDescribeDocument describeExtra = updateDescribeExtra(context.getUser(), describe.getApiName(), arg.getDescribeExtra());
        fieldBackgroundExtraLogicService.bulkUpsertFieldsExtra(context.getUser(), describe.getApiName(), arg.getFieldsExtra());
        return UpdateWebLayoutAndUpdateDescribe.Result.builder()
                .layout(LayoutDocument.of(layoutResult.getLayout())).objectDescribeDraft(ObjectDescribeDocument.of(layoutResult.getObjectDescribe()))
                .objectDescribe(ObjectDescribeDocument.of(layoutResult.getObjectDescribe()))
                .describeExtra(describeExtra)
                .fieldsExtra(arg.getFieldsExtra())
                .build();
    }

    @ServiceMethod("getBusinessComponents")
    public GetBusinessComponents.Result getBusinessComponents(GetBusinessComponents.Arg arg, ServiceContext context) {
        List<IComponent> businessComponents = layoutLogicService.getBusinessComponents(context.getTenantId(), arg.getObjectApiName(), arg.getLayoutType());
        List<Map> maps = businessComponents.stream().map(x -> ComponentExt.of(x).toMap()).collect(Collectors.toList());
        return GetBusinessComponents.Result.builder().components(maps).build();
    }

    @ServiceMethod("convertWebLayout2MobileLayout")
    public ConvertWebLayout2MobileLayout.Result convertWebLayout2MobileLayout(ConvertWebLayout2MobileLayout.Arg arg, ServiceContext context) {
        LayoutExt webLayout = LayoutExt.of(arg.getWebLayout());
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), webLayout.getRefObjectApiName());

        LayoutExt mobileLayout;
        if (webLayout.isListLayout()) {
            mobileLayout = MobileListLayoutBuilder.builder()
                    .listLayout(webLayout)
                    .pageType(PageType.Designer)
                    .listComponentFactory(listComponentFactory)
                    .build()
                    .getMobileListLayout();
        } else {
            mobileLayout = extraLayoutManage.getLayoutFactory(LayoutTypes.MOBILE)
                    .getLayoutExt(LayoutProcessorContext
                            .builder()
                            .pageType(PageType.Designer)
                            .webLayout(LayoutExt.of(webLayout))
                            .describeExt(ObjectDescribeExt.of(describe))
                            .build());
            Integer exposedNum = webLayout.getHeadInfoComponent()
                    .map(c -> c.get("exposedButton", Integer.class))
                    .orElse(3);
            mobileLayout.initButtonStyle(context.getTenantId(), exposedNum);
        }

        return ConvertWebLayout2MobileLayout.Result.builder().mobileLayout(LayoutDocument.of(mobileLayout)).build();
    }

    @ServiceMethod("convertWebLayout2SidebarLayout")
    public ConvertWebLayout2SidebarLayout.Result convertWebLayout2SidebarLayout(ConvertWebLayout2SidebarLayout.Arg arg, ServiceContext context) {
        LayoutExt webLayout = LayoutExt.of(arg.getWebLayout());
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), webLayout.getRefObjectApiName());

        LayoutExt sidebarLayout = extraLayoutManage.getLayoutFactory(LayoutTypes.SIDEBAR)
                .getLayoutExt(LayoutProcessorContext
                        .builder()
                        .pageType(PageType.Designer)
                        .webLayout(LayoutExt.of(webLayout))
                        .describeExt(ObjectDescribeExt.of(describe))
                        .build());


        Integer exposedNum = webLayout.getHeadInfoComponent()
                .map(c -> c.get("exposedButton", Integer.class))
                .orElse(3);
        sidebarLayout.initButtonStyle(context.getTenantId(), exposedNum);

        return ConvertWebLayout2SidebarLayout.Result.builder().sidebarLayout(LayoutDocument.of(sidebarLayout)).build();
    }

    @ServiceMethod("convertLayout2MobileLayout")
    public ConvertLayout2MobileLayout.Result convertLayout2MobileLayout(ConvertLayout2MobileLayout.Arg arg, ServiceContext context) {
        LayoutExt webLayout = LayoutExt.of(arg.getLayout());

        LayoutExt mobileLayout;
        if (webLayout.isListLayout()) {
            mobileLayout = MobileListLayoutBuilder.builder()
                    .listLayout(webLayout)
                    .pageType(PageType.Designer)
                    .listComponentFactory(listComponentFactory)
                    .build()
                    .getMobileListLayout();
        } else if (webLayout.isFlowTaskLayout()) {
            mobileLayout = MobileFlowTaskListLayoutBuilder.builder()
                    .layout(webLayout)
                    .pageType(PageType.Designer)
                    .componentFactoryManager(componentFactoryManager)
                    .describeLogicService(describeLogicService)
                    .layoutLogicService(layoutLogicService)
                    .user(context.getUser())
                    .build()
                    .getMobileListLayout();
        } else {
            IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), webLayout.getRefObjectApiName());
            mobileLayout = extraLayoutManage.getLayoutFactory(LayoutTypes.MOBILE)
                    .getLayoutExt(LayoutProcessorContext
                            .builder()
                            .pageType(PageType.Designer)
                            .webLayout(LayoutExt.of(webLayout))
                            .describeExt(ObjectDescribeExt.of(describe))
                            .build());
        }

        return ConvertLayout2MobileLayout.Result.builder().mobileLayout(LayoutDocument.of(mobileLayout)).build();
    }

    @ServiceMethod("convertLayoutByPageType")
    public ConvertLayoutByPageType.Result convertLayoutByPageType(ConvertLayoutByPageType.Arg arg, ServiceContext context) {
        IObjectDescribe describe = describeLogicService.findObject(context.getTenantId(), arg.getObjectApiName());
        ILayout layout = layoutLogicService.convertLayoutByPageType(context.getUser(), describe, arg.getLayout().toLayout(), arg.getPageType());

        return ConvertLayoutByPageType.Result.builder().layout(LayoutDocument.of(layout)).build();
    }

    @ServiceMethod("findAssignedListLayout")
    public FindAssignedListLayout.Result findAssignedListLayout(FindAssignedListLayout.Arg arg, ServiceContext context) {
        User user = context.getUser();
        List<FindAssignedListLayout.LayoutListItem> layoutList = findLayoutListItems(user, arg);
        Set<String> listLayoutNames = layoutList.stream()
                .map(FindAssignedListLayout.LayoutListItem::getApiName)
                .collect(Collectors.toSet());

        String defaultLayoutName = layoutList.stream()
                .filter(it -> BooleanUtils.isTrue(it.getIsDefault()))
                .map(FindAssignedListLayout.LayoutListItem::getApiName)
                .findFirst()
                .orElse(null);

        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(user, arg.getAppId());
        List<LayoutRoleInfo> layoutRoleInfos = layoutLogicService.findAssignedListLayout(layoutContext, arg.getDescribeApiName(),
                arg.getWhatDescribeApiName(), listLayoutNames, defaultLayoutName, arg.getLayoutType(), arg.getSourceInfo());
        ManageGroup manageGroup = layoutLogicService.queryLayoutManageGroup(user, arg.getDescribeApiName(), arg.getSourceInfo());
        return FindAssignedListLayout.Result.builder()
                .layoutList(layoutList)
                .roleList(layoutRoleInfos)
                .layoutManageGroup(ManageGroupDTO.of(manageGroup))
                .build();
    }

    private List<FindAssignedListLayout.LayoutListItem> findLayoutListItems(User user, FindAssignedListLayout.Arg arg) {
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(user, arg.getAppId());
        if (arg.flowTaskLayout()) {
            List<ILayout> layouts = layoutLogicService.findFlowTaskListLayoutsByType(layoutContext, arg.getDescribeApiName(),
                    arg.getWhatDescribeApiName(), arg.getLayoutType());
            return FindAssignedListLayout.LayoutListItem.fromLayouts(layouts);
        }
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), arg.getDescribeApiName());
        List<ILayout> listLayout = layoutLogicService.findListLayout(layoutContext, describe);
        return FindAssignedListLayout.LayoutListItem.fromLayouts(listLayout);
    }

    @ServiceMethod("saveListLayoutAssign")
    public SaveListLayoutAssign.Result saveListLayoutAssign(SaveListLayoutAssign.Arg arg, ServiceContext context) {
        List<LayoutRoleInfo> roleList = JSON.parseArray(arg.getRoleList(), LayoutRoleInfo.class);
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        layoutLogicService.saveListLayoutAssign(layoutContext, arg.getDescribeApiName(), arg.getWhatDescribeApiName(), roleList, arg.getLayoutType());
        return SaveListLayoutAssign.Result.builder().success(true).build();
    }

    @ServiceMethod("get_edit_layout_status")
    public GetEditLayoutStatus.Result getEditLayoutStatus(GetEditLayoutStatus.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
        byte status = layoutLogicService.getEditLayoutStatus(layoutContext, arg.getDescribeApiName());
        return GetEditLayoutStatus.Result.builder().status(status).build();
    }

    @ServiceMethod("enable_edit_layout")
    public EnableEditLayout.Result enableEditLayout(EnableEditLayout.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        LayoutContext.get().setRemoveI18n(arg.isRemoveI18n());
        String lockKey = "enable_edit_layout|" + context.getTenantId() + "|" + arg.getDescribeApiName();
        RLock lock = redissonService.tryLockWithErrorMsg(0, 60, TimeUnit.SECONDS, lockKey, I18NExt.text(I18NKey.PROCESSING_ALERT), AppFrameworkErrorCode.PROCESSING_ALERT.getCode());
        try {
            LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(context.getUser(), arg.getAppId());
            layoutLogicService.enableEditLayout(layoutContext, arg.getDescribeApiName(), BooleanUtils.isTrue(arg.getDetailPageReferenceFieldConfig()));
        } finally {
            redissonService.unlock(lock);
        }
        return EnableEditLayout.Result.builder().build();
    }

    @ServiceMethod("convertDetailToEditLayout")
    public ConvertDetailToEditLayout.Result convertDetailToEditLayout(ConvertDetailToEditLayout.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getDescribeApiName()) || Strings.isNullOrEmpty(arg.getLayoutApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        LayoutContext.get().setRemoveI18n(arg.isRemoveI18n());
        ILayout editLayout = serviceFacade.getLayoutLogicService().convertDetailToEditLayout(context.getUser(), arg.getDescribeApiName(), arg.getLayoutApiName());
        return ConvertDetailToEditLayout.Result.builder()
                .layout(LayoutDocument.of(editLayout))
                .build();
    }

    @ServiceMethod("convertEditToDetailLayout")
    public ConvertEditToDetailLayout.Result convertEditToDetailLayout(ConvertEditToDetailLayout.Arg arg, ServiceContext serviceContext) {
        if (Strings.isNullOrEmpty(arg.getDescribeApiName()) || Strings.isNullOrEmpty(arg.getLayoutApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        LayoutContext.get().setRemoveI18n(arg.isRemoveI18n());
        ILayout detailLayout = serviceFacade.getLayoutLogicService().convertEditToDetailLayout(serviceContext.getUser(), arg.getDescribeApiName(), arg.getLayoutApiName(), BooleanUtils.isTrue(arg.getDetailPageReferenceFieldConfig()));
        return ConvertEditToDetailLayout.Result.builder()
                .layout(LayoutDocument.of(detailLayout))
                .build();
    }

    @ServiceMethod("disable_edit_layout")
    public EnableEditLayout.Result disableEditLayout(EnableEditLayout.Arg arg, ServiceContext context) {
        if (Strings.isNullOrEmpty(arg.getDescribeApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        String lockKey = "enable_edit_layout|" + context.getTenantId() + "|" + arg.getDescribeApiName();
        RLock lock = redissonService.tryLockWithErrorMsg(0, 60, TimeUnit.SECONDS, lockKey, I18NExt.text(I18NKey.PROCESSING_ALERT), AppFrameworkErrorCode.PROCESSING_ALERT.getCode());
        try {
            layoutLogicService.disableEditLayout(context.getUser(), arg.getDescribeApiName());
        } finally {
            redissonService.unlock(lock);
        }
        return EnableEditLayout.Result.builder().build();
    }

    @ServiceMethod("getGlobalFieldAlign")
    public UpdateGlobalFieldAlign.GlobalFieldAlignVO getGlobalFieldAlign(ServiceContext context) {
        GlobalFieldAlign globalFieldAlign = fieldAlignService.getGlobalFieldAlign(context.getTenantId());
        return UpdateGlobalFieldAlign.GlobalFieldAlignVO.fromGlobalFieldAlign(globalFieldAlign);
    }

    @ServiceMethod("updateGlobalFieldAlign")
    public UpdateGlobalFieldAlign.Result updateGlobalFieldAlign(UpdateGlobalFieldAlign.GlobalFieldAlignVO arg, ServiceContext context) {
        // 转换 FieldLangAlignVO 到 FieldLangAlign
        List<GlobalFieldAlign.FieldLangAlign> fieldLangAligns = Lists.newArrayList();
        if (arg.getFieldAlignByLang() != null) {
            fieldLangAligns = arg.getFieldAlignByLang().stream()
                    .map(vo -> new GlobalFieldAlign.FieldLangAlign(vo.getLang(), GlobalFieldAlign.FieldAlign.of(vo.getFieldAlign())))
                    .collect(Collectors.toList());
        }
        GlobalFieldAlign fieldAlign = GlobalFieldAlign.of(arg.getFieldAlign(), arg.getDetailFormLayout(),
                arg.isFieldAlignFollowLang(), fieldLangAligns);
        if (!fieldAlign.validateFieldAlign()) {
            throw new ValidateException(arg.getFieldAlign());
        }
        fieldAlignService.updateGlobalFieldAlign(context.getTenantId(), fieldAlign);
        return new UpdateGlobalFieldAlign.Result();
    }

    private ObjectDescribeDocument updateDescribeExtra(User user, String describeAPIName, ObjectDescribeDocument argDescribeExtra) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_EXTRA_GRAY_EI, user.getTenantId()) || CollectionUtils.empty(argDescribeExtra)) {
            return null;
        }
        DescribeExtra describeExtra = describeLogicService.updateDescribeExtra(user, describeAPIName, argDescribeExtra);
        return ObjectDescribeDocument.of(describeExtra);
    }

    @ServiceMethod("findLayoutTemplates")
    public FindLayoutTemplates.Result findLayoutTemplates(FindLayoutTemplates.Arg arg, ServiceContext context) {
        List<Map<String, Object>> layoutTemplates = layoutLogicService.findLayoutTemplates(context.getUser(), arg.getDescribeApiName(), arg.getBusiness(), arg.getCardStyle());
        List<DocumentBaseEntity> templates = layoutTemplates.stream()
                .map(DocumentBaseEntity::new)
                .collect(Collectors.toList());
        return FindLayoutTemplates.Result.of(templates);
    }

    /**
     * 初始化工具使用，单独灰度fs企业
     *
     * @param layouts
     * @param context
     * @return
     */
    @ServiceMethod("createOrUpdateLayouts")
    public CreateOrUpdateLayouts.Result createOrUpdateLayouts(CreateOrUpdateLayouts.Arg layouts, ServiceContext context) {
        List<ILayout> layoutList = Lists.newArrayList();
        if (Objects.nonNull(layouts.getDetailLayout())) {
            ILayout detailLayout = new Layout(layouts.getDetailLayout());
            layoutList.add(detailLayout);
        }
        if (Objects.nonNull(layouts.getMobileLayout())) {
            ILayout mobileLayout = new Layout(layouts.getMobileLayout());
            layoutList.add(mobileLayout);
        }
        if (Objects.nonNull(layouts.getListLayout())) {
            ILayout listLayout = new Layout(layouts.getListLayout());
            layoutList.add(listLayout);
        }
        if (Objects.nonNull(layouts.getAddEditLayout())) {
            ILayout addEditLayout = new Layout(layouts.getAddEditLayout());
            layoutList.add(addEditLayout);
        }
        layoutLogicService.createOrUpdateLayoutForDevTools(context.getTenantId(), layouts.getDescribeApiName(), layoutList);
        return CreateOrUpdateLayouts.Result.builder().build();
    }

    @ServiceMethod("findWhatListLayoutByRoleCodes")
    public FindWhatListLayoutByRoleCodes.Result findWhatListLayoutByRoleCodes(FindWhatListLayoutByRoleCodes.Arg arg, ServiceContext context) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopy(context.getTenantId(), arg.getObjectDescribeApiName());
        IObjectDescribe whatDescribe = describeLogicService.findObjectWithoutCopy(context.getTenantId(), arg.getWhatApiName());

        IObjectData objectData = serviceFacade.findObjectData(context.getUser(), arg.getWhatDataId(), whatDescribe);
        List<FindWhatListLayoutByRoleCodes.RecordTypeLayoutStructure> recordTypeLayoutStructures = finRecordTypeLayoutStructure(context.getUser(),
                objectDescribe, whatDescribe, arg.getRoleCodes(), ObjectDataExt.of(objectData).getRecordType(), context.getAppId());
        return FindWhatListLayoutByRoleCodes.Result.builder()
                .layoutInfos(recordTypeLayoutStructures)
                .build();
    }

    private List<FindWhatListLayoutByRoleCodes.RecordTypeLayoutStructure> finRecordTypeLayoutStructure(User user,
                                                                                                       IObjectDescribe objectDescribe,
                                                                                                       IObjectDescribe whatDescribe,
                                                                                                       Set<String> roleCodes,
                                                                                                       String recordType,
                                                                                                       String appId) {
        // 查询流程待办布局中的列表布局
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(user, appId);
        List<ILayout> flowTaskListLayouts = layoutLogicService.findFlowTaskListLayoutsByType(layoutContext, objectDescribe.getApiName(), whatDescribe.getApiName(), LayoutTypes.FLOW_TASK_LIST);
        Map<String, ILayout> flowTaskListLayoutMap = flowTaskListLayouts.stream().collect(Collectors.toMap(ILayout::getName, Function.identity()));
        ILayout defaultFlowTaskListLayout = flowTaskListLayoutMap.get(FlowTaskLayoutExt.generateDefaultLayoutName(objectDescribe, whatDescribe));

        // 查询流程代办布局中列表页布局按角色分配的结果
        List<RoleViewPojo> roleViewPojos = serviceFacade.batchFindRoleViewList(user, whatDescribe.getApiName(),
                Sets.newHashSet(ListLayoutExt.DEFAULT_LIST_LAYOUT_RECORD_TYPE), roleCodes,
                Sets.newHashSet(FlowTaskLayoutExt.getRoleViewType(objectDescribe.getApiName(), LayoutTypes.FLOW_TASK_LIST)), appId);
        Map<String, RoleViewPojo> roleViewMap = roleViewPojos.stream().collect(Collectors.toMap(RoleViewPojo::getRoleCode, Function.identity()));

        // 查询流程待办的移动端摘要布局，并按角色code分组
        Set<String> recordTypes = getRecordTypes(whatDescribe, recordType);
        List<RoleViewPojo> defaultRoleViews = serviceFacade.batchFindRoleViewList(user, whatDescribe.getApiName(), recordTypes,
                roleCodes, Sets.newHashSet(FlowTaskLayoutExt.getRoleViewType(objectDescribe.getApiName(), LayoutTypes.WHAT_LIST)), appId);

        List<ILayout> whatListLayout = layoutLogicService.findWhatListLayout(layoutContext, objectDescribe, whatDescribe);
        Map<String, List<ILayout>> whatLayoutMapByRoleCode = removeWhatLayoutDisableFiled(user, objectDescribe, whatDescribe, whatListLayout, roleCodes);

        Set<String> whatListLayoutNames = whatListLayout.stream().map(ILayout::getName).collect(Collectors.toSet());
        String defaultLayoutName = whatListLayout.stream()
                .filter(it -> BooleanUtils.isTrue(it.isDefault()))
                .map(ILayout::getName)
                .findFirst()
                .orElse(null);

        Map<String, Set<RoleViewPojo>> roleCode2RoleViewMap = Optional.ofNullable(defaultRoleViews)
                .orElse(Lists.newArrayList()).stream()
                .filter(it -> whatListLayoutNames.contains(it.getViewId()))
                .collect(Collectors.groupingBy(RoleViewPojo::getRoleCode, Collectors.mapping(Function.identity(), Collectors.toSet())));

        List<FindWhatListLayoutByRoleCodes.RecordTypeLayoutStructure> results = Lists.newArrayList();
        for (String roleCode : roleCodes) {
            FindWhatListLayoutByRoleCodes.RecordTypeLayoutStructure layoutStructure = FindWhatListLayoutByRoleCodes.RecordTypeLayoutStructure.builder()
                    .roleCode(roleCode)
                    .build();

            FlowTaskLayoutExt flowTaskLayoutExt = getFlowTaskLayoutAndRender(user, objectDescribe, whatDescribe, flowTaskListLayoutMap, defaultFlowTaskListLayout, roleViewMap, roleCode);

            // 开启了继承待办摘要布局
            if (!isUserDefineMobileField(flowTaskLayoutExt)) {
                layoutStructure.setAbstractLayoutList(LayoutDocument.ofList(whatLayoutMapByRoleCode.get(roleCode)));
                Map<String, String> recordLayoutMapping = roleCode2RoleViewMap.getOrDefault(roleCode, Collections.emptySet()).stream()
                        .collect(Collectors.toMap(RoleViewPojo::getRoleCode, RoleViewPojo::getRecordTypeId));
                recordTypes.forEach(recordApiName -> recordLayoutMapping.putIfAbsent(recordApiName, defaultLayoutName));
                layoutStructure.setRecordLayoutMapping(recordLayoutMapping);
            } else {
                DocumentBaseEntity flowTaskLayoutComponent = flowTaskLayoutExt.getFlowTaskListMobileComponent()
                        .map(it -> new DocumentBaseEntity(it.toMap()))
                        .orElse(null);
                layoutStructure.setFlowTaskLayoutComponent(flowTaskLayoutComponent);
            }
            results.add(layoutStructure);
        }
        return results;
    }

    private Set<String> getRecordTypes(IObjectDescribe whatDescribe, String recordType) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(whatDescribe);
        return describeExt.getRecordTypeOption(recordType)
                .map(it -> Sets.newHashSet(it.getApiName()))
                .orElseGet(() -> Sets.newHashSet(describeExt.getRecordTypeNames()));
    }

    private FlowTaskLayoutExt getFlowTaskLayoutAndRender(User user, IObjectDescribe objectDescribe, IObjectDescribe whatDescribe,
                                                         Map<String, ILayout> flowTaskListLayoutMap, ILayout defaultFlowTaskListLayout,
                                                         Map<String, RoleViewPojo> roleViewMap, String roleCode) {
        ILayout layout = Optional.ofNullable(roleViewMap.get(roleCode))
                .map(RoleViewPojo::getViewId)
                .map(flowTaskListLayoutMap::get)
                .orElse(defaultFlowTaskListLayout);
        FlowTaskLayoutExt flowTaskLayoutExt = FlowTaskLayoutExt.of(LayoutExt.of(layout).copy());

        FLowTaskLayoutRender.builder()
                .flowTaskLayout(flowTaskLayoutExt.getLayout())
                .objectDescribe(objectDescribe)
                .whatObjectDescribe(whatDescribe)
                .user(user)
                .pageType(PageType.List)
                .layoutAgentType(LayoutAgentType.MOBILE)
                .layoutLogicService(layoutLogicService)
                .componentFactoryManager(componentFactoryManager)
                .functionPrivilegeService(functionPrivilegeService)
                .build()
                .renderByRoleCode(roleCode);
        return flowTaskLayoutExt;
    }

    private boolean isUserDefineMobileField(FlowTaskLayoutExt flowTaskLayoutExt) {
        return flowTaskLayoutExt.getFlowTaskListMobileComponent()
                .map(FlowTaskListMobileComponentExt::mobileFieldTypeIsUserDefine)
                .orElse(false);
    }

    private Map<String, List<ILayout>> removeWhatLayoutDisableFiled(User user, IObjectDescribe objectDescribe, IObjectDescribe whatDescribe, List<ILayout> whatListLayouts, Set<String> roleCodes) {
        WhatComponentRender.WhatComponentRenderBuilder whatComponentRenderBuilder = WhatComponentRender.builder()
                .describeExt(ObjectDescribeExt.of(objectDescribe))
                .whatDescribeExt(ObjectDescribeExt.of(whatDescribe))
                .user(user);

        // 遍历所有的whatListLayout，渲染出对应的whatComponentExt，然后移除掉disable的field
        whatListLayouts.forEach(whatListLayout -> {
            whatComponentRenderBuilder.whatComponentExt(getWhatComponentExt(whatListLayout))
                    .build()
                    .render();
        });

        Map<String, List<ILayout>> resultMap = Maps.newHashMap();
        roleCodes.forEach(roleCode -> {
            List<ILayout> layoutList = whatListLayouts.stream()
                    .map(whatListLayout -> LayoutExt.of(whatListLayout).copy())
                    .peek(whatListLayout -> {
                        whatComponentRenderBuilder.whatComponentExt(getWhatComponentExt(whatListLayout))
                                .functionPrivilegeService(functionPrivilegeService)
                                .build()
                                .filterUnauthorizedFieldsByRole(roleCode);

                    })
                    .collect(Collectors.toList());
            resultMap.put(roleCode, layoutList);
        });
        return resultMap;
    }

    private WhatComponentExt getWhatComponentExt(ILayout layout) {
        return WhatComponentExt.of(LayoutExt.of(layout).getWhatComponent().get());
    }
}