package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.TreeViewUtil;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.FunctionParamBuildUtils;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.function.util.FunctionQueryTemplateUtils;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.treeview.TreeViewService;
import com.facishare.paas.appframework.metadata.treeview.TreeViewServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class StandardTreeListController extends AbstractStandardController<StandardTreeListController.Arg, StandardTreeListController.Result> {

    private ObjectDescribeExt objectDescribeExt;
    private TreeViewService treeViewService;

    public static final int limit = 2000;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        IObjectDescribe describe = findObject();
        objectDescribeExt = ObjectDescribeExt.of(describe);
        stopWatch.lap("findObject");
        treeViewService = serviceFacade.getBean(TreeViewServiceImpl.class);
    }

    @Override
    protected Result doService(Arg arg) {
        List<TreeViewData> dataList = Lists.newArrayList();
        Result result = Result.builder()
                .dataList(dataList)
                .build();
        if (!objectDescribeExt.isSupportTreeViewObject()) {
            return result;
        }
        Optional<IFieldDescribe> fieldDescribe = objectDescribeExt.getActiveLookupFieldDescribes().stream()
                .filter(field -> FieldDescribeExt.of(field).isTreeViewSelfAssociatedField())
                .findFirst();
        if (!fieldDescribe.isPresent()) {
            return result;
        }
        IObjectReferenceField treeViewSelfAssociatedField = (IObjectReferenceField) fieldDescribe.get();
        String parentApiName = treeViewSelfAssociatedField.getApiName();
        SearchTemplateQuery searchTemplateQuery = buildSearchTemplateQuery();
        stopWatch.lap("buildSearchTemplateQuery");
        List<IObjectData> objectDataList = treeViewService.findTreeViewObjDataListByQuery(controllerContext.getUser(),
                objectDescribeExt.getApiName(), searchTemplateQuery, objectDescribeExt.getTreeViewObjectFieldsToReturn());
        stopWatch.lap("findTreeViewObjDataListByQuery");
        if (objectDescribeExt.isBigTreeList()) {
            List<TreeViewData> treeViewDataList = objectDataList.stream()
                    .map(data -> TreeViewData.builder()
                            .dataId(data.getId())
                            .parentId(data.get(parentApiName, String.class))
                            .label(objectDescribeExt.isSupportDisplayName() ? data.getDisplayName() : data.getName())
                            .build())
                    .collect(Collectors.toList());
            dataList.addAll(treeViewDataList);
            stopWatch.lap("findBigTreeViewDataListByQuery");
        } else if (needQueryAllData(arg)) {
            getDataListAndFilterByFunction(treeViewSelfAssociatedField.getTreeViewField(), parentApiName, objectDataList, dataList);
            stopWatch.lap("getDataListAndFilterByFunction");
        } else {
            List<String> needRetainIds = filterOutPermissionDeniedData(parentApiName, objectDataList);
            List<TreeViewData> treeViewDataListFiltered = objectDataList.stream()
                    .filter(data -> needRetainIds.contains(data.getId()))
                    .map(data -> TreeViewData.builder()
                            .dataId(data.getId())
                            .parentId(data.get(parentApiName, String.class))
                            .label(objectDescribeExt.isSupportDisplayName() ? data.getDisplayName() : data.getName())
                            .build())
                    .collect(Collectors.toList());
            dataList.addAll(treeViewDataListFiltered);
            stopWatch.lap("filterOutPermissionDeniedData");
        }
        // 传化紧急需求，先按企业灰度控制只能选末级节点，自关联字段选择时可以选所有节点
        if (!objectDescribeExt.isBigTreeList() && StringUtils.isNotEmpty(arg.getObjectApiName()) && StringUtils.isNotEmpty(arg.getObjectFieldApiName())
                && !(StringUtils.equals(arg.getObjectApiName(), objectDescribeExt.getApiName()) && StringUtils.equals(parentApiName, arg.getObjectFieldApiName()))
                && UdobjGrayConfig.isAllow("tree_only_select_leaf", controllerContext.getTenantId())) {
            result.setOnlySelectLeaf(true);
        }
        return result;
    }

    private boolean needQueryAllData(Arg arg) {
        ISearchTemplateQuery templateQuery = SearchTemplateQueryExt.fromJsonString(getSearchQueryInfo(arg));
        return templateQuery.getFilters().stream()
                .anyMatch(filter -> CollectionUtils.notEmpty(filter.getFilters()))
                || templateQuery.getWheres().stream()
                .anyMatch(where -> CollectionUtils.notEmpty(where.getFilters()));
    }

    private void getDataListAndFilterByFunction(String treeViewFieldApiName, String parentApiName, List<IObjectData> objectDataList, List<TreeViewData> dataList) {
        List<String> idListFiltered = objectDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        List<IObjectData> allDataList = treeViewService.findTreeViewObjDataListByQuery(controllerContext.getUser(),
                objectDescribeExt.getApiName(), buildSearchTemplateQuery(true), objectDescribeExt.getTreeViewObjectFieldsToReturn());

        List<String> needRetainIds = filterOutPermissionDeniedData(parentApiName, allDataList);
        List<TreeViewData> treeViewDataListFiltered = allDataList.stream()
                .filter(data -> needRetainIds.contains(data.getId()))
                .map(data -> TreeViewData.builder()
                        .dataId(data.getId()).parentId(data.get(parentApiName, String.class))
                        .label(objectDescribeExt.isSupportDisplayName() ? data.getDisplayName() : data.getName())
                        .notSelected(!idListFiltered.contains(data.getId()))
                        .build()).collect(Collectors.toList());
        dataList.addAll(treeViewDataListFiltered);
        // 过滤掉不能选择的叶子节点
        Map<String, List<TreeViewData>> treeViewDataListGroup = dataList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getParentId()))
                .collect(Collectors.groupingBy(TreeViewData::getParentId));
        // 获取每个数据子节点数量
        Map<String, Integer> childNumMap = getChildNumMap(treeViewFieldApiName, allDataList);
        Set<String> needRemoveIds = filterOutNotSelectedLeafNode(dataList, treeViewDataListGroup, childNumMap);
        dataList.removeIf(data -> needRemoveIds.contains(data.getDataId()));
    }

    private Map<String, Integer> getChildNumMap(String treeViewFieldApiName, List<IObjectData> allDataList) {
        Map<String, Integer> childNumMap = Maps.newHashMap();
        List<String> treeViewPathList = allDataList.stream()
                .map(data -> data.get(treeViewFieldApiName, String.class))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        allDataList.forEach(data -> {
            AtomicInteger childNum = new AtomicInteger();
            treeViewPathList.forEach(treeViewPath -> {
                if (treeViewPath.contains(data.getId())) {
                    childNum.getAndIncrement();
                }
            });
            childNumMap.put(data.getId(), childNum.get());
        });
        return childNumMap;
    }

    private Set<String> filterOutNotSelectedLeafNode(List<TreeViewData> dataList, Map<String, List<TreeViewData>> treeViewDataListGroup, Map<String, Integer> childNumMap) {
        Set<String> needRemoveIds = Sets.newHashSet();
        for (TreeViewData treeViewData : dataList) {
            List<TreeViewData> sub = treeViewDataListGroup.get(treeViewData.getDataId());
            if (CollectionUtils.notEmpty(sub)) {
                Set<String> subIds = filterOutNotSelectedLeafNode(sub, treeViewDataListGroup, childNumMap);
                needRemoveIds.addAll(subIds);
                Integer childNum = childNumMap.get(treeViewData.getDataId());
                if (Objects.nonNull(childNum) && subIds.size() == childNum && BooleanUtils.isTrue(treeViewData.getNotSelected())) {
                    needRemoveIds.add(treeViewData.getDataId());
                }
            } else if (BooleanUtils.isTrue(treeViewData.getNotSelected())) {
                needRemoveIds.add(treeViewData.getDataId());
            }
        }
        return needRemoveIds;
    }

    private List<String> filterOutPermissionDeniedData(String parentApiName, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return Lists.newArrayList();
        }
        List<String> topLevelDataIds = objectDataList.stream()
                .filter(data -> StringUtils.isEmpty(data.get(parentApiName, String.class)))
                .map(IObjectData::getId)
                .collect(Collectors.toList());
        List<String> needRetainIds = Lists.newArrayList();
        for (String pid : topLevelDataIds) {
            List<String> parentAndChildIds = TreeViewUtil.getParentAndChildIds(objectDataList, pid, parentApiName);
            needRetainIds.addAll(parentAndChildIds);
        }
        return needRetainIds;
    }

    private String getSearchQueryInfo(Arg arg) {
        String searchQueryInfo = arg.getSearchQueryInfo();
        if (StringUtils.isEmpty(searchQueryInfo)) {
            searchQueryInfo = getAllTreeViewDataSearchTemplateQuery();
        }
        return searchQueryInfo;
    }

    private SearchTemplateQuery buildSearchTemplateQuery() {
        return buildSearchTemplateQuery(false);
    }

    private SearchTemplateQuery buildSearchTemplateQuery(boolean allData) {
        String searchQueryInfo;
        if (allData) {
            searchQueryInfo = getAllTreeViewDataSearchTemplateQuery();
        } else {
            ISearchTemplateQuery templateQuery = SearchTemplateQueryExt.fromJsonString(getSearchQueryInfo(arg));
            ObjectDataExt data = Objects.isNull(arg.getObjectData()) ? null : ObjectDataExt.of(arg.getObjectData());
            SearchTemplateQueryExt.of(templateQuery).handleWheresFilter(data);
            searchQueryInfo = templateQuery.toJsonString();
        }
        Query searchQuery = serviceFacade.findSearchQuery(controllerContext.getUser(), objectDescribeExt, searchQueryInfo, SearchQueryContext.builder().build());
        handleHierarchyFilter(searchQuery);
        boolean haveFunctionQuery = haveFunctionQuery(searchQuery);
        if (haveFunctionQuery) {
            handleFiltersByFunc(searchQuery);
        }
        SearchTemplateQuery searchTemplateQuery = (SearchTemplateQuery) searchQuery.toSearchTemplateQuery();
        SearchTemplateQueryExt.of(searchTemplateQuery).validateWheresAndFilters(controllerContext.getUser(), objectDescribeExt.getObjectDescribe());
        if (!allData && haveFunctionQuery && RequestUtil.isCepRequest()) {
            RequestUtil.setEsRecentUpdate();
        }
        if (searchTemplateQuery.getLimit() > limit) {
            searchTemplateQuery.setLimit(limit);
        }
        return searchTemplateQuery;
    }

    private boolean haveFunctionQuery(Query searchQuery) {
        return searchQuery.getFilters()
                .stream()
                .anyMatch(filter -> Objects.equals(filter.getValueType(), FilterExt.FilterValueTypes.FUNCTION_VARIABLE));
    }

    private void handleFiltersByFunc(Query query) {
        IFilter filter = null;
        for (int i = 0; i < query.getFilters().size(); i++) {
            IFilter currentFilter = query.getFilters().get(i);
            if (Objects.equals(currentFilter.getValueType(), FilterExt.FilterValueTypes.FUNCTION_VARIABLE)) {
                filter = currentFilter;
            }
        }
        if (Objects.isNull(filter)) {
            return;
        }
        Tuple<String, String> objectAndFunctionAPIName = getObjectAndFunctionAPIName(FilterExt.of(filter));
        if (Objects.isNull(objectAndFunctionAPIName)) {
            return;
        }
        RunResult runResult = serviceFacade.getFunctionLogicService().findAndExecuteFunction(controllerContext.getUser(), () -> {
            Tuple<IObjectData, Map<String, List<IObjectData>>> result = getFunctionData();
            infraServiceFacade.fillQuoteValueVirtualField(controllerContext.getUser(), result.getKey(), result.getValue());
            return result;
        }, objectAndFunctionAPIName.getKey(), objectAndFunctionAPIName.getValue());
        FunctionQueryTemplateUtils.handleFunctionResult(controllerContext.getTenantId(), query, filter, runResult.getReturnType(), runResult.getFunctionResult());
    }

    private Tuple<String, String> getObjectAndFunctionAPIName(FilterExt filterExt) {
        String objectAPIName = CollectionUtils.notEmpty(arg.getObjectData()) ?
                arg.getObjectData().toObjectData().getDescribeApiName() :
                null;
        String functionAPIName = filterExt.getFieldValues().get(0);

        if (StringUtils.isEmpty(objectAPIName) || StringUtils.isEmpty(functionAPIName)) {
            return null;
        }
        return Tuple.of(objectAPIName, functionAPIName);
    }

    private Tuple<IObjectData, Map<String, List<IObjectData>>> getFunctionData() {
        return FunctionParamBuildUtils.getFunctionData(arg.getMasterData(), arg.getObjectData(), arg.getDetails());
    }


    private IObjectDescribe findObject() {
        IObjectDescribe objectDescribe = serviceFacade.findObject(controllerContext.getTenantId(), controllerContext.getObjectApiName());
        ObjectDescribeExt ret = ObjectDescribeExt.of(objectDescribe);
        //先在这个地方处理，可以到跟底层处理掉
        ret.removeFieldDescribe("extend_obj_data_id");
        return ret;
    }

    private String getAllTreeViewDataSearchTemplateQuery() {
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        searchTemplateQueryExt.setOffset(0);
        searchTemplateQueryExt.setLimit(limit);
        searchTemplateQueryExt.setNeedReturnCountNum(false);
        return searchTemplateQueryExt.toSearchTemplateQuery().toJsonString();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.TreeList.getFuncPrivilegeCodes();
    }

    private void handleHierarchyFilter(Query query) {
        if (!objectDescribeExt.isBigTreeList() || isCustomFilters(query.getFilters())) {
            return;
        }
        objectDescribeExt.getActiveLookupFieldDescribes().stream()
                .filter(field -> FieldDescribeExt.of(field).isTreeViewSelfAssociatedField())
                .findFirst()
                .ifPresent(x -> {
                    IObjectReferenceField treeViewSelfAssociatedField = (IObjectReferenceField) x;
                    if (StringUtils.isBlank(arg.getParentId())) {
                        query.and((FilterExt.of(Operator.IS, treeViewSelfAssociatedField.getApiName(), Lists.newArrayList("")).getFilter()));
                    } else {
                        query.and((FilterExt.of(Operator.EQ, treeViewSelfAssociatedField.getApiName(), arg.getParentId()).getFilter()));
                    }
                });
    }

    private boolean isCustomFilters(List<IFilter> filters) {
        return CollectionUtils.notEmpty(filters) && filters.size() > 1;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        @JsonProperty("search_query_info")
        private String searchQueryInfo;
        @JsonProperty("object_api_name")
        private String objectApiName;
        @JsonProperty("object_field_api_name")
        private String objectFieldApiName;
        @JsonProperty("object_data")
        private ObjectDataDocument objectData;
        @JsonProperty("master_data")
        private ObjectDataDocument masterData;
        @JsonProperty("parent_id")
        private String parentId;
        @JsonProperty("details")
        private Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<TreeViewData> dataList;
        private boolean onlySelectLeaf;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TreeViewData {
        private String dataId;

        private String parentId;

        private String label;

        private Boolean notSelected;
    }
}
