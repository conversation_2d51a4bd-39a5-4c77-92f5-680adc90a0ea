package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Pair;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.ImportTemplateActionDomainPlugin;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.describe.ImageFieldDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import lombok.NoArgsConstructor;
import com.facishare.paas.appframework.metadata.dto.HeaderMergeInfo;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.IMPORT_TEMPLATE_ORDER_EI;

@Slf4j
public abstract class BaseImportTemplateAction<A extends BaseImportTemplateAction.Arg> extends PreDefineAction<A, BaseImportTemplateAction.Result> {
    public static int IMPORT_TYPE_ADD = 0;
    public static int IMPORT_TYPE_EDIT = 1;
    public static int IMPORT_TYPE_ADD_EDIT = 2;
    protected static final String PERCENTAGE_SYMBOL = "（%）";
    protected static final String REQUIRED_SYMBOL = I18NKey.MUST_FILL_IN;
    protected static final String TEXTURE_IMPORT_ENABLED_KEY = "isTextureImportEnabled";


    /**
     * 有效业务类型
     */
    protected List<IRecordTypeOption> recordTypeOptionList = Lists.newArrayList();

    private List<IFieldDescribe> headerFieldList;

    protected boolean referenceFieldMappingSwitch = false;

    @Override
    protected void before(A arg) {
        super.before(arg);
        referenceFieldMappingSwitch = loadReferenceFieldMappingSwitch();
    }

    private boolean loadReferenceFieldMappingSwitch() {
        ImportReferenceMapping referenceConfig = infraServiceFacade.findImportReferenceMapping(actionContext.getUser(), objectDescribe.getApiName());
        return BooleanUtils.isTrue(referenceConfig.getReferenceFieldMappingSwitch());
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(A arg) {
        return null;
    }

    @Override
    protected Result doAct(A arg) {
        //根据权限获取人员可以查看的字段列表
        headerFieldList = getImportTemplateField(arg);
        //"销售记录"不支持业务类型
        if (!"SaleEventObj".equals(objectDescribe.getApiName())) {
            recordTypeOptionList = serviceFacade.findValidRecordTypeList(objectDescribe.getApiName(), actionContext.getUser());
        }

        customHeader(headerFieldList);
        //通过领域插件定制导入表头
        customHeaderByDomainPlugin();
        List<IFieldDescribe> sortedFieldList = sortHeader(headerFieldList);
        //处理唯一性规则字段
        handelUniqueRuleFields(sortedFieldList);
        //补充多语字段
        ImportExportExt.supportMultiLangField(objectDescribe.getTenantId(), sortedFieldList, objectDescribe.getApiName());
        //模板支持ID列，原有字段上包装一层
        handleIDAndReferenceField(sortedFieldList);
        //key: 标题名称， value: 字段类型
        List<Pair<String, IFieldDescribe>> labelList = Lists.newArrayList();
        List<List<String>> sampleRows = Lists.newArrayList();
        List<String> sampleList = Lists.newArrayList();
        // 用于记录需要合并的表头信息
        List<HeaderMergeInfo> mergeInfoList = Lists.newArrayList();

        for (IFieldDescribe field : sortedFieldList) {
            //业务类型字段改为必填
            if (IFieldType.RECORD_TYPE.equals(field.getType())) {
                //业务类型只有一个选项模版不下发
                RecordTypeFieldDescribe recordTypeFieldDescribe = (RecordTypeFieldDescribe) field;
                if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_RECORD_TYPE, actionContext.getTenantId())
                        && recordTypeFieldDescribe.getRecordTypeOptions().size() == 1) {
                    continue;
                }
                field = FieldDescribeExt.of(field).copyOnWrite();
                field.setRequired(Boolean.TRUE);
            }

            // 图片字段多列处理
            if (IFieldType.IMAGE.equals(field.getType()) && BooleanUtils.isTrue(arg.getIsTextureImportEnabled())) {
                processImageFieldForTemplate((ImageFieldDescribe) field, labelList, sampleList, mergeInfoList);
            } else {
                // 其他字段类型（保持原有逻辑）
                String title = buildFieldTitle(field);
                labelList.add(Pair.of(title, field));
                sampleList.add(getFieldSampleValue(field));
            }
        }
        sampleRows.add(sampleList);
        sampleRows = customSampleList(sampleRows);
        labelList = customLabelList(labelList);

        String path;
        if (CollectionUtils.notEmpty(mergeInfoList)) {
            // 有合并信息时使用新的方法
            path = serviceFacade.generateTemplateWithMerge(actionContext.getUser(), objectDescribe.getDisplayName(),
                    labelList, sampleRows, mergeInfoList);
        } else {
            // 保持原有逻辑
            path = serviceFacade.generateTemplate(actionContext.getUser(), objectDescribe.getDisplayName(),
                    labelList, sampleRows);
        }

        return Result.builder().path(path).build();
    }

    protected void handleIDAndReferenceField(List<IFieldDescribe> sortedFieldList) {
        if (arg.getMatchingType() == BaseImportDataAction.MATCHING_TYPE_ID) {
            ImportExportExt.supportIDAndReference(sortedFieldList, arg.getImportType(), referenceFieldMappingSwitch);
            handleReferenceFields(sortedFieldList);
        } else if (referenceFieldMappingSwitch) {
            ImportExportExt.supportReferenceField(sortedFieldList, true);
            handleReferenceFields(sortedFieldList);
        }
    }

    /**
     * 按照唯一性ID下载的导入模板，主属性(更新导入)或者主从，关联字段无需加"（必填）"符号
     */
    private void handleReferenceFields(List<IFieldDescribe> sortedFieldList) {
        sortedFieldList.stream()
                .filter(f ->
                        ("name".equals(f.getApiName()) && arg.getImportType() == IMPORT_TYPE_EDIT && arg.getMatchingType() == BaseImportDataAction.MATCHING_TYPE_ID)
                                || IFieldType.MASTER_DETAIL.equals(f.getType())
                                || IFieldType.OBJECT_REFERENCE_MANY.equals(f.getType())
                                || IFieldType.OBJECT_REFERENCE.equals(f.getType()))
                .forEach(f -> f.setRequired(false));
    }

    protected List<IFieldDescribe> handelUniqueRuleFields(List<IFieldDescribe> sortedFieldList) {
        return sortedFieldList;
    }

    protected List<Pair<String, IFieldDescribe>> customLabelList(List<Pair<String, IFieldDescribe>> labelList) {
        return labelList;
    }

    /**
     * 将不支持更新导入的字段，但在唯一性规则中出现的字段，设置必填字段 required 为 true
     */
    protected abstract List<IFieldDescribe> getImportTemplateField(A arg);

    protected List<List<String>> customSampleList(List<List<String>> sampleList) {
        return sampleList;
    }



    /**
     * 处理图片字段的模板生成逻辑
     * 支持多列和表头合并
     */
    private void processImageFieldForTemplate(ImageFieldDescribe imageField,
                                               List<Pair<String, IFieldDescribe>> labelList,
                                               List<String> sampleList,
                                               List<HeaderMergeInfo> mergeInfoList) {
        int columnCount = imageField.getFileAmountLimit();

        // 为图片字段设置扩展信息，标记启用了图片嵌入功能
        IFieldDescribe fieldWithExtendInfo = FieldDescribeExt.of(imageField).copyOnWrite();
        Map<String, Object> extendInfo = fieldWithExtendInfo.getExtendInfo();
        if (extendInfo == null) {
            extendInfo = Maps.newHashMap();
        }
        extendInfo.put(TEXTURE_IMPORT_ENABLED_KEY, Boolean.TRUE);
        fieldWithExtendInfo.setExtendInfo(extendInfo);

        if (columnCount > 1) {
            // 记录合并信息，包含必填标记
            int startCol = labelList.size();
            String parentTitle = imageField.getLabel();
            if (Objects.equals(Boolean.TRUE, imageField.isRequired())) {
                parentTitle = parentTitle + I18N.text(REQUIRED_SYMBOL);
            }
            mergeInfoList.add(new HeaderMergeInfo(parentTitle, startCol, startCol + columnCount - 1,
                                                 Objects.equals(Boolean.TRUE, imageField.isRequired())));

            // 生成多个列（用于数据填写，不需要显示子标题）
            for (int i = 1; i <= columnCount; i++) {
                labelList.add(Pair.of("", fieldWithExtendInfo)); // 空标题，因为会被合并表头覆盖
                // 每个图片列都显示提示文本
                String sampleValue = getFieldSampleValue(fieldWithExtendInfo);
                sampleList.add(sampleValue);
            }
        } else {
            // 单列处理
            String title = buildFieldTitle(fieldWithExtendInfo);
            labelList.add(Pair.of(title, fieldWithExtendInfo));
            sampleList.add(getFieldSampleValue(fieldWithExtendInfo));
        }
    }

    /**
     * 构建字段标题
     * 处理百分比符号和必填标记
     */
    protected String buildFieldTitle(IFieldDescribe field) {
        String title = field.getLabel();
        if (IFieldType.PERCENTILE.equals(field.getType())) {
            //百分比类型要在表头加（%）
            title = title + PERCENTAGE_SYMBOL;
        }
        if (Objects.equals(Boolean.TRUE, field.isRequired())) {
            title = title + I18N.text(REQUIRED_SYMBOL);
        }
        return title;
    }

    protected void customHeader(List<IFieldDescribe> headerFieldList) {
        //过滤字段
        //图片字段设置为水印时，不导出图片
        Iterator<IFieldDescribe> iterator = headerFieldList.iterator();
        while (iterator.hasNext()) {
            IFieldDescribe iField = iterator.next();
            if (IFieldType.IMAGE.equals(iField.getType())) {
                if (((ImageFieldDescribe) iField).getIsWaterMark()) {
                    iterator.remove();
                }
            }
        }
    }

    @Override
    protected final ImportTemplateActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return ImportTemplateActionDomainPlugin.Arg.builder()
                .headerFieldList(CollectionUtils.nullToEmpty(headerFieldList).stream().map(IFieldDescribe::getApiName).collect(Collectors.toList()))
                .build();
    }

    @Override
    protected final void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
        ImportTemplateActionDomainPlugin.Result domainPluginResult = (ImportTemplateActionDomainPlugin.Result) pluginResult;
        if (CollectionUtils.notEmpty(domainPluginResult.getHeaderFieldListToRemove()) && CollectionUtils.notEmpty(headerFieldList)) {
            headerFieldList.removeIf(x -> domainPluginResult.getHeaderFieldListToRemove().contains(x.getApiName()));
        }
    }

    @Override
    protected void findDescribe() {
        // 查询描述需要 copy
        objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName());
    }

    private void customHeaderByDomainPlugin() {
        if (CollectionUtils.empty(headerFieldList)) {
            return;
        }
        runDomainPlugin("customHeader", true, (domainPlugin, pluginArg) ->
                ((ImportTemplateActionDomainPlugin) domainPlugin).customHeader(actionContext, (ImportTemplateActionDomainPlugin.Arg) pluginArg));
    }

    protected void sortFieldByCreateTime(List<IFieldDescribe> sortedList) {
        if (sortedList == null || sortedList.isEmpty()) {
            return;
        }
        sortedList.sort((a, b) -> {
            Long aCreateTime = a.getCreateTime();
            Long bCreateTime = b.getCreateTime();
            long aTimeStamp = aCreateTime == null ? -1 : aCreateTime;
            long bTimeStamp = bCreateTime == null ? -1 : bCreateTime;

            return Long.compare(aTimeStamp, bTimeStamp);
        });
    }

    protected String getFieldSampleValue(IFieldDescribe field) {
        Calendar instance = Calendar.getInstance();
        switch (field.getType()) {
            case IFieldType.NUMBER:
            case IFieldType.CURRENCY:
                return "123";
            case IFieldType.DATE:
                return I18N.text(I18NKey.ONLY_DEMO_FORMAT_EQULE_CAN_IMPORT_ELSE_OTHER_FORMAT_CAN_NOT_IMPORT,
                        instance.get(Calendar.YEAR), instance.get(Calendar.MONTH) + 1, instance.get(Calendar.DAY_OF_MONTH));
            case IFieldType.DATE_TIME:
                return I18N.text(I18NKey.ONLY_DEMO_FORMAT_EQULE_CAN_IMPORT,
                        instance.get(Calendar.YEAR), instance.get(Calendar.MONTH) + 1, instance.get(Calendar.DAY_OF_MONTH));
            case IFieldType.TEXT:
                if (Objects.nonNull(field.getExtendInfo())) {
                    // 临时增加的虚拟的数据ID列示例
                    Map<String, String> map = field.getExtendInfo();
                    if (map.containsKey(ImportExportExt.IMPORT_TYPE)) {
                        String info = map.get(ImportExportExt.IMPORT_TYPE);
                        if (ImportExportExt.EXT_INFO_MAIN_ID.equals(info)) {
                            return I18N.text(I18NKey.FILL_UNQUE_ID, field.getLabel());
                        } else if (ImportExportExt.TEAM_MEMBER_MARK.equals(info)) {
                            if (TeamMember.isTeamMemberTypeExportGray(actionContext.getTenantId())) {
                                return I18N.text(I18NKey.IMPORT_TEAM_MEMBER_NAME) + "|" + I18N.text(I18NKey.IMPORT_TEAM_MEMBER_NAME);
                            }
                            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EMPLOYEE_SUPPORT_REPEAT, actionContext.getTenantId())) {
                                return I18N.text(I18NKey.PERSONNEL_NAME_CODE) + "|" + I18N.text(I18NKey.PERSONNEL_NAME_CODE);
                            }
                            return I18N.text(I18NKey.PERSONNEL_NAME) + "|" + I18N.text(I18NKey.PERSONNEL_NAME);
                        }
                    }
                }
                return I18N.text(I18NKey.SAMPLE_TEXT);
            case IFieldType.AUTO_NUMBER:
            case IFieldType.LONG_TEXT:
                return I18N.text(I18NKey.SAMPLE_TEXT);
            case IFieldType.BIG_TEXT:
                return I18N.text(I18NKey.SAMPLE_BIG_TEXT);
            case IFieldType.SELECT_ONE:
                SelectOneFieldDescribe selectOneField = (SelectOneFieldDescribe) field;
                List<String> labels = selectOneField.getSelectOptions().stream().filter(iSelectOption -> !iSelectOption
                        .isNotUsable()).map(ISelectOption::getLabel).collect(Collectors.toList());
                return convertToString(labels);
            case IFieldType.SELECT_MANY:
                return I18N.text(I18NKey.OPTION_ONE_OPTION_TWO);
            case IFieldType.PHONE_NUMBER:
                return "13900000000";
            case IFieldType.EMPLOYEE:
                Employee employee = (Employee) field;
                if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUT_OWNER_GRAY, actionContext.getTenantId())
                        && ObjectDataExt.OUTER_OWNER.equals(field.getApiName())) {
                    return I18N.text(I18NKey.OUT_ENTERPRISE_OUT_EMPLOYEE);
                }
                if (Boolean.FALSE.equals(employee.isSingle())) {
                    if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EMPLOYEE_SUPPORT_REPEAT, actionContext.getTenantId())) {
                        return I18N.text(I18NKey.PERSONNEL_NAME_CODE) + "|" + I18N.text(I18NKey.PERSONNEL_NAME_CODE);
                    } else {
                        return I18N.text(I18NKey.PERSONNEL_NAME) + "|" + I18N.text(I18NKey.PERSONNEL_NAME);
                    }
                }
                if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EMPLOYEE_SUPPORT_REPEAT, actionContext.getTenantId())) {
                    return I18N.text(I18NKey.PERSONNEL_NAME_CODE);
                }
                return I18N.text(I18NKey.PERSONNEL_NAME);
            case IFieldType.EMPLOYEE_MANY:
                if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EMPLOYEE_SUPPORT_REPEAT, actionContext.getTenantId())) {
                    return I18N.text(I18NKey.PERSONNEL_NAME_CODE) + "|" + I18N.text(I18NKey.PERSONNEL_NAME_CODE);
                }
                return I18N.text(I18NKey.PERSONNEL_NAME) + "|" + I18N.text(I18NKey.PERSONNEL_NAME);
            case IFieldType.DEPARTMENT:
                if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEPARTMENT_SUPPORT_REPEAT, actionContext.getTenantId())) {
                    if (ObjectDataExt.DATA_OWN_ORGANIZATION.equals(field.getApiName())) {
                        return I18N.text(I18NKey.ORGANIZATION_NAME_CODE);
                    }
                    return I18N.text(I18NKey.DEPARTMENT_NAME_CODE);
                }
                if (ObjectDataExt.DATA_OWN_ORGANIZATION.equals(field.getApiName())) {
                    return I18N.text(I18NKey.ORGANIZATION_NAME);
                }
                return I18N.text(I18NKey.DEPARTMENT_NAME);
            case IFieldType.DEPARTMENT_MANY:
                if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEPARTMENT_SUPPORT_REPEAT, actionContext.getTenantId())) {
                    return I18N.text(I18NKey.DEPARTMENT_NAME_CODE) + "|" + I18N.text(I18NKey.DEPARTMENT_NAME_CODE);
                }
                return I18N.text(I18NKey.DEPARTMENT_NAME) + "|" + I18N.text(I18NKey.DEPARTMENT_NAME);
            case IFieldType.OBJECT_REFERENCE:
//                if (referenceFieldMappingSwitch) {
//                    return I18N.text(I18NKey.RELATED_OBJECT_NAME_UNIQUE);
//                }
                return I18N.text(I18NKey.RELATED_OBJECT_NAME);
            case IFieldType.OBJECT_REFERENCE_MANY:
//                if (referenceFieldMappingSwitch) {
//                    return I18N.text(I18NKey.RELATED_OBJECT_NAME_UNIQUE) + "|" + I18N.text(I18NKey.RELATED_OBJECT_NAME_UNIQUE);
//                }
                return I18N.text(I18NKey.RELATED_OBJECT_NAME) + "|" + I18N.text(I18NKey.RELATED_OBJECT_NAME);
            case IFieldType.MASTER_DETAIL:
//                if (referenceFieldMappingSwitch) {
//                    return I18N.text(I18NKey.RELATED_OBJECT_NAME_UNIQUE);
//                }
                return I18N.text(I18NKey.MASTER_NAME);
            case IFieldType.TIME:
                return "10:21";
            case IFieldType.RECORD_TYPE:
                List<String> types = recordTypeOptionList.stream()
                        .filter(IRecordTypeOption::isActive).map(IRecordTypeOption::getLabel)
                        .collect(Collectors.toList());
                return convertToString(types);
            case IFieldType.MULTI_LEVEL_SELECT_ONE:
                return I18N.text(I18NKey.PRIMARY_OPTION_OR_SECONDARY_OPTION);
            case IFieldType.EMAIL:
                return "<EMAIL>";
            case IFieldType.URL:
                return "www.fxiaoke.com";
            case IFieldType.PERCENTILE:
                return "12.66";
            case IFieldType.TRUE_OR_FALSE:
                TrueOrFalse boolField = (TrueOrFalse) field;
                List<String> boolLabels = boolField.getSelectOptions().stream().map(ISelectOption::getLabel).collect(Collectors.toList());
                return convertToString(boolLabels);
            case IFieldType.LOCATION:
                return I18N.text(I18NKey.LOCATION_INFORMATION);
            case IFieldType.COUNTRY:
                return I18N.text(I18NKey.CHINA);
            case IFieldType.PROVINCE:
            case IFieldType.CITY:
                return I18N.text(I18NKey.BEI_JING);
            case IFieldType.DISTRICT:
                return I18N.text(I18NKey.HAI_DIAN);
            case IFieldType.TOWN:
                return I18N.text(I18NKey.ZHONG_GUANG_CUN_JIE_DAO);
            case IFieldType.VILLAGE:
                return I18N.text(I18NKey.ZHI_CHUN_LU_SHE_QU);
            case IFieldType.IMAGE:
                // 检查是否启用了图片嵌入功能
                if (Objects.nonNull(field.getExtendInfo()) &&
                    BooleanUtils.isTrue((Boolean) field.getExtendInfo().get(TEXTURE_IMPORT_ENABLED_KEY))) {
                    return I18N.text(I18NKey.IMAGE_EMBED_INSTRUCTION);
                } else {
                    return I18N.text(I18NKey.PATH_SAMPLE);
                }
            case IFieldType.FILE_ATTACHMENT:
                return I18N.text(I18NKey.PATH_SAMPLE);
            case IFieldType.DIMENSION:
                Dimension dimensionField = (Dimension) field;
                if (Boolean.FALSE.equals(dimensionField.isSingle())) {
                    if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DIMENSION_SUPPORT_REPEAT, actionContext.getTenantId())) {
                        return I18N.text(I18NKey.DIMENSION_CODE) + "|" + I18N.text(I18NKey.DIMENSION_CODE);
                    }
                    return I18N.text(I18NKey.DIMENSION_VALUE) + "|" + I18N.text(I18NKey.DIMENSION_VALUE);
                }
                if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DIMENSION_SUPPORT_REPEAT, actionContext.getTenantId())) {
                    return I18N.text(I18NKey.DIMENSION_CODE);
                }
                return I18N.text(I18NKey.DIMENSION_VALUE);
            case IFieldType.HTML_RICH_TEXT:
                return I18N.text(I18NKey.HTML_RICH_TEXT);
            case IFieldType.OUT_EMPLOYEE:
                return I18N.text(I18NKey.OUT_ENTERPRISE_OUT_EMPLOYEE) + "|" + I18N.text(I18NKey.OUT_ENTERPRISE_OUT_EMPLOYEE);
            case IFieldType.OUT_DEPARTMENT:
                return I18N.text(I18NKey.OUT_ENTERPRISE_OUT_DEPARTMENT) + "|" + I18N.text(I18NKey.OUT_ENTERPRISE_OUT_DEPARTMENT);
            default:
                return "";
        }
    }

    // 将列表转为字符串以便于后边解析
    protected String convertToString(List<String> labels) {
        if (CollectionUtils.empty(labels)) {
            return "";
        }
        // 不用Arrays.toString，是为了防止单选中有"," 选项导致解析出错误的选项
        StringBuilder result = new StringBuilder();
        for (String label : labels) {
            result.append(label).append("||");
        }
        return result.delete(result.length() - 2, result.length()).toString();
    }

    protected List<IFieldDescribe> sortHeader(List<IFieldDescribe> validFieldList) {
        if (CollectionUtils.empty(validFieldList)) {
            return validFieldList;
        }

        boolean notSortHeader = isNotSortHeader(validFieldList.get(0).getDescribeApiName());
        if (notSortHeader) {
            return validFieldList;
        }
        Iterator<IFieldDescribe> iterator = validFieldList.iterator();
        IFieldDescribe nameField = null;
        IFieldDescribe recordTypeIField = null;
        List<IFieldDescribe> systemList = Lists.newLinkedList();
        List<IFieldDescribe> customList = Lists.newLinkedList();
        List<IFieldDescribe> virtualList = Lists.newLinkedList();
        while (iterator.hasNext()) {
            IFieldDescribe next = iterator.next();
            if (IObjectData.NAME.equals(next.getApiName())) {
                nameField = next;
                iterator.remove();
            } else if (IObjectData.RECORD_TYPE.equals(next.getApiName())) {
                recordTypeIField = next;
                iterator.remove();
            } else if (IObjectDescribe.DEFINE_TYPE_SYSTEM.equals(next.getDefineType()) ||
                    IObjectDescribe.DEFINE_TYPE_PACKAGE.equals(next.getDefineType())) {
                systemList.add(next);
            } else if (CollectionUtils.notEmpty(next.getExtendInfo()) &&
                    next.getExtendInfo().containsKey("importType")) {
                // 相关团队成员字段系虚拟字段
                virtualList.add(next);
            } else {
                customList.add(next);
            }
        }
        //系统字段排序
        sortFieldByCreateTime(systemList);
        //自定义字段排序
        sortFieldByCreateTime(customList);

        List<IFieldDescribe> resultList = Lists.newLinkedList();
        resultList.addAll(customList);
        resultList.addAll(systemList);
        if (null != nameField) {
            resultList.add(0, nameField);
        }

        if (null != recordTypeIField) {
            resultList.add(recordTypeIField);
        }
        resultList.addAll(virtualList);
        return resultList;
    }

    private boolean isNotSortHeader(String describeApiName) {
        if (UdobjGrayConfig.isAllow(IMPORT_TEMPLATE_ORDER_EI, actionContext.getTenantId())) {
            if (Objects.equals(objectDescribe.getApiName(), describeApiName)) {
                return StringUtils.isNotBlank(arg.getRecordType());
            }
            if (CollectionUtils.empty(arg.getDetailArg())) {
                return false;
            }
            String detailRecordType = arg.getDetailArg().stream()
                    .filter(x -> Objects.equals(x.getDetailApiName(), describeApiName))
                    .map(DetailArg::getDetailRecordType)
                    .filter(Objects::nonNull)
                    .findFirst()
                    .orElse("");
            return StringUtils.isNotBlank(detailRecordType);
        }
        return false;
    }

    @Data
    public static class Arg {
        private String describeApiName;
        @JsonProperty(value = "ImportType")
        @SerializedName(value = "ImportType")
        private Integer importType = 0;

        @JsonProperty(value = "MatchingType")
        @SerializedName(value = "MatchingType")
        private Integer matchingType = 2;

        @JsonProperty(value = "unionImportApiNameList")
        @SerializedName(value = "unionImportApiNameList")
        private List<String> unionImportApiNameList;

        // 部分不同老对象导入时用同一个apiName，加一个objectCode参数用于区分是哪个
        @JsonProperty(value = "objectCode")
        @SerializedName(value = "objectCode")
        private String objectCode;

        // Only for SaleEvent
        @JsonProperty(value = "relatedApiNameList")
        @SerializedName(value = "relatedApiNameList")
        private List<String> relatedApiNameList;

        // Only for SaleEvent
        @JsonProperty(value = "Type")
        @SerializedName(value = "Type")
        private Integer type;

        @JsonProperty(value = "supportFieldMapping")
        @SerializedName("supportFieldMapping")
        private Boolean supportFieldMapping;

        @JsonProperty(value = "IsTextureImportEnabled")
        @SerializedName(value = "IsTextureImportEnabled")
        private Boolean isTextureImportEnabled;

        // 业务类型apiname
        private String recordType;

        // 从对象业务类型
        private String detailRecordType;

        private List<DetailArg> detailArg;
    }

    @Data
    public static class DetailArg {
        private String detailApiName;
        private String detailRecordType;
    }



    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Result {
        private String path;
    }
}
