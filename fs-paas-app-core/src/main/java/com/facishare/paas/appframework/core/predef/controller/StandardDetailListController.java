package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.appframework.metadata.layout.MasterDetailGroupComponentBuilder;
import com.facishare.paas.appframework.metadata.relation.CalculateFields;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.IMultiTableComponent;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.Biz.ApprovalFlow;

/**
 * Created by zhouwr on 2019/6/26
 */
public class StandardDetailListController extends BaseListController<StandardDetailListController.Arg> {

    protected IObjectDescribe masterDescribe;
    protected IObjectData masterData;

    @Override
    protected String getSearchQueryInfo() {
        return arg.getSearchQueryInfo();
    }

    @Override
    protected String getSearchTemplateId() {
        return arg.getSearchTemplateId();
    }

    @Override
    protected String getSearchTemplateApiName() {
        return null;
    }

    @Override
    protected String getSearchTemplateType() {
        return arg.getSearchTemplateType();
    }

    @Override
    protected boolean isIgnoreSceneFilter() {
        return Boolean.TRUE.equals(arg.getIgnoreSceneFilter());
    }

    @Override
    protected boolean isIgnoreSceneRecordType() {
        return Boolean.TRUE.equals(arg.getIgnoreSceneRecordType());
    }

    @Override
    protected Map<String, Integer> getDescribeVersionMap() {
        return arg.getDescribeVersionMap();
    }

    @Override
    protected String getUsePageType() {
        return ButtonUsePageType.RelatedList.getId();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.DetailList.getFuncPrivilegeCodes();
    }

    @Override
    protected boolean isRelatedPage() {
        return true;
    }

    @Override
    protected boolean serializeEmpty() {
        return !Boolean.FALSE.equals(arg.getSerializeEmpty());
    }

    @Override
    protected Map<String, Object> getRelatedListComponent() {
        return arg.getRelatedListComponent();
    }

    @Override
    protected boolean needSearchRichTextExtra() {
        return Boolean.TRUE.equals(arg.getSearchRichTextExtra());
    }

    @Override
    protected void init() {
        super.init();
        masterDescribe = serviceFacade.findObjectIncludeMultiField(controllerContext.getTenantId(), arg.getMasterApiName());
        masterData = serviceFacade.findObjectDataIncludeDeleted(controllerContext.getUser(),
                arg.getMasterDataId(), masterDescribe.getApiName());
    }

    @Override
    protected QueryResult<IObjectData> findData(SearchTemplateQuery query) {
        QueryResult<IObjectData> result = super.findData(query);
        stopWatch.lap("super.findData");
        if (arg.mergeSnapshot()) {
            findAndMergeSnapshot(result.getData());
        }
        infraServiceFacade.findAndMergeObjectDataWithOriginalData(controllerContext.getUser(), objectDescribe, result.getData());
        return result;
    }

    private void findAndMergeSnapshot(List<IObjectData> detailDataList) {
        ObjectDataSnapshot dataSnapshot = infraServiceFacade.findAndMergeSnapshot(controllerContext.getTenantId(), arg.getMasterApiName(),
                arg.getMasterDataId(), ApprovalFlow.getCode(), arg.getBizInfo().getOtherBizId());
        stopWatch.lap("findSnapshot");

        if (CollectionUtils.empty(dataSnapshot.getMasterSnapshot())
                && (CollectionUtils.empty(dataSnapshot.getDetailSnapshot()) || !dataSnapshot.getDetailSnapshot().containsKey(controllerContext.getObjectApiName()))) {
            return;
        }

        Map<String, List<IObjectData>> detailObjectDataMap = Maps.newHashMap();
        detailObjectDataMap.put(controllerContext.getObjectApiName(), detailDataList);
        dataSnapshot.mergeInto(masterData, detailObjectDataMap);
        stopWatch.lap("mergeSnapshot");

        //计算主对象和从对象的计算字段和主对象统计从对象的统计字段
        CalculateFields calculateFields = infraServiceFacade.computeCalculateFieldsForAddAction(masterDescribe, Lists.newArrayList(objectDescribe), true);
        serviceFacade.batchCalculateBySortFields(controllerContext.getUser(), masterData, detailObjectDataMap, calculateFields);
        stopWatch.lap("calculateSnapshot");
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        ISearchTemplate searchTemplate = serviceFacade.findSearchTemplateByIdAndType(controllerContext.getUser(), getSearchTemplateId(), objectDescribe.getApiName(), getSearchTemplateType());
        setSceneShowTag(searchTemplate);
        SearchTemplateQuery query = serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter(controllerContext.getUser(), objectDescribe, searchTemplate, getSearchQueryInfo(),
                true, isIgnoreSceneFilter(), isIgnoreSceneRecordType());
        handleScene(searchTemplate, query);

        checkQueryLimit(query);
        modifyQueryByRefFieldName(query);
        handleDataRights(query);
        handleOrderBys(query);
        return query;
    }

    @Override
    protected Query defineQuery() {
        Query query = super.defineQuery();
        checkQueryLimit(query);
        modifyQueryByRefFieldName(query);
        handleDataRights(query);
        handleOrderBys(query);
        return query;
    }

    protected void handleOrderBys(SearchTemplateQuery query) {
        if (notAssignedOrder()) {
            SearchTemplateQueryExt.of(query).orderByFirst();
        }
    }

    protected void handleOrderBys(Query query) {
        if (notAssignedOrder()) {
            // 从对象明细按照"order_by"字段升序排序
            List<OrderBy> newOrderBys = Lists.newArrayList(OrderByExt.relatedDetailOrderBy());
            Optional.ofNullable(query.getOrders()).ifPresent(newOrderBys::addAll);
            query.resetOrders(newOrderBys);
        }
    }

    protected void modifyQueryByRefFieldName(SearchTemplateQuery query) {
        String refFieldName = objectDescribe.getMasterDetailFieldName(arg.getMasterApiName())
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, refFieldName, arg.getMasterDataId());
    }

    protected void modifyQueryByRefFieldName(Query query) {
        String refFieldName = objectDescribe.getMasterDetailFieldName(arg.getMasterApiName())
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        query.and(FilterExt.of(Operator.EQ, refFieldName, arg.getMasterDataId()).getFilter());
    }

    protected void handleDataRights(SearchTemplateQuery query) {
        //多级关联（3级以上）的从对象不校验数据权限
        if (ObjectDescribeExt.of(masterDescribe).isSlaveObject()) {
            query.setDataRightsParameter(null);
        }
    }

    protected void handleDataRights(Query query) {
        //多级关联（3级以上）的从对象不校验数据权限
        if (ObjectDescribeExt.of(masterDescribe).isSlaveObject()) {
            query.setDataRightsParameter(null);
        }
    }

    @Override
    protected List<ILayout> findMobileLayouts() {
        List<ILayout> listLayouts = serviceFacade.getLayoutLogicService().findMobileListLayout(buildLayoutContext(), objectDescribe, false);
        listLayouts.forEach(this::doRender);

        return listLayouts;
    }

    @Override
    protected ILayout findLayout() {
        ILayout layout = super.findLayout();
        if (layout == null) {
            return null;
        }

        IComponent component = getComponent(layout);
        layout.setComponents(Lists.newArrayList(component));

        return layout;
    }

    protected IComponent getComponent(ILayout layout) {
        String describeApiName = objectDescribe.getApiName();

        List<IRecordTypeOption> recordTypeOptions = objectDescribe.getRecordTypeField()
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR))).getRecordTypeOptions();
        Map<String, List<IRecordTypeOption>> recordTypeOptionMap = ImmutableMap.of(describeApiName, recordTypeOptions);
        List<String> matchRecordTypes = serviceFacade.findValidAndMatchRecordTypes(controllerContext.getUser(),
                masterDescribe.getApiName(), masterData.getRecordType(), describeApiName);
        List<String> hasDataRecordTypes = CollectionUtils.nullToEmpty(queryResult.getData()).stream().map(x -> x.getRecordType())
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        MasterDetailFieldDescribe masterDetail = objectDescribe.getMasterDetailFieldDescribe()
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
        RelatedObjectDescribeStructure relatedStructure = RelatedObjectDescribeStructure.builder()
                .relatedObjectDescribe(objectDescribe.getObjectDescribe())
                .fieldApiName(masterDetail.getApiName())
                .fieldLabel(masterDetail.getLabel())
                .relatedListName(masterDetail.getTargetRelatedListName())
                .relatedListLabel(masterDetail.getTargetRelatedListLabel())
                .build();

        IMultiTableComponent component = MasterDetailGroupComponentBuilder.builder()
                .buttonLogicService(serviceFacade)
                .user(controllerContext.getUser())
                .objectDescribeExt(ObjectDescribeExt.of(masterDescribe))
                .objectData(masterData)
                .listLayoutMap(ImmutableMap.of(describeApiName, layout))
                .recordTypeOptionMap(recordTypeOptionMap)
                .matchRecordTypeMap(ImmutableMap.of(describeApiName, matchRecordTypes))
                .hasDataRecordTypeMap(ImmutableMap.of(describeApiName, hasDataRecordTypes))
                .relatedListComponent(arg.getRelatedListComponent())
                .build()
                .generateMultiTableComponent(relatedStructure, true);
        component.setButtons(Lists.newArrayList());

        return component;
    }

    @Override
    protected Result buildResult(List<ILayout> layouts, ISearchTemplateQuery query, QueryResult<IObjectData> queryResult) {
        Result result = super.buildResult(layouts, query, queryResult);
        result.setListLayouts(null);
        return result;
    }

    /*
     * 前端是否指定了排序
     * searchQueryInfo中指定了Orders或者指定了场景ID
     */
    private boolean notAssignedOrder() {
        ISearchTemplateQuery tmpQuery = SearchTemplateQueryExt.fromJsonString(getSearchQueryInfo());
        return CollectionUtils.empty(tmpQuery.getOrders());
    }

    @Override
    protected boolean needDescribe(ISearchTemplateQuery query) {
        if (Boolean.FALSE.equals(arg.getIncludeDescribe())) {
            return false;
        }
        return super.needDescribe(query);
    }

    @Override
    protected boolean needLayout(ISearchTemplateQuery query) {
        if (Boolean.FALSE.equals(arg.getIncludeLayout())) {
            return false;
        }
        return super.needLayout(query);
    }

    @Data
    public static class Arg {
        private String masterApiName;

        private String masterDataId;

        private String searchQueryInfo;

        private Map<String, Integer> describeVersionMap;

        private Boolean includeDescribe = true;

        private Boolean includeLayout = true;

        private String searchTemplateId;
        private String searchTemplateType;
        private Boolean ignoreSceneFilter;
        private Boolean ignoreSceneRecordType;

        //是否序列化返回对象中的空值
        private Boolean serializeEmpty;

        //Detail接口返回的布局中的相关对象组件(用于加工场景和按钮)
        @JSONField(name = "related_list_component")
        @JsonProperty("related_list_component")
        @SerializedName("related_list_component")
        private Map<String, Object> relatedListComponent;

        private Boolean mergeSnapshot;
        private RequestContext.BizInfo bizInfo;

        /**
         * 查询富文本、协同富文本、长文本『从对象只支持长文本』
         */
        @JSONField(name = "search_rich_text_extra")
        @JsonProperty("search_rich_text_extra")
        @SerializedName("search_rich_text_extra")
        private Boolean searchRichTextExtra;

        public boolean mergeSnapshot() {
            return Boolean.TRUE.equals(mergeSnapshot);
        }
    }

}
