package com.facishare.paas.appframework.core.predef.controller;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerFunctions;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.domain.DescribeLayoutControllerDomainPlugin;
import com.facishare.paas.appframework.core.predef.handler.describelayout.DescribeLayoutHandler;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig;
import com.facishare.paas.appframework.metadata.dto.DescribeDetailResult;
import com.facishare.paas.appframework.metadata.dto.DetailObjectListResult;
import com.facishare.paas.appframework.metadata.dto.RecordTypeLayoutStructure;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.appframework.metadata.layout.*;
import com.facishare.paas.appframework.metadata.layout.component.ISummaryComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.layout.factory.ExtraLayoutManage;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.bizconf.bean.BizType;
import com.fxiaoke.bizconf.bean.ConfigPojo;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.common.util.UdobjConstants.OWNER_API_NAME;

/**
 * Created by zhouwr on 2020/7/21
 */
public class AbstractStandardDescribeLayoutController<A extends StandardDescribeLayoutController.Arg> extends PreDefineController<A, StandardDescribeLayoutController.Result> {

    protected IObjectData objectData;

    protected IObjectDescribe describe;

    protected ILayout layout;

    private Map<String, List<IRecordTypeOption>> validRecordTypeListMap;

    private DescribeExtra describeExtra;

    private List<MtChangeOrderRule> orderRuleList;

    private boolean ignoreFunctionPrivilege;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        if (ignoreFunctionPrivilege) {
            return Collections.emptyList();
        }
        if (needCheckFuncPrivilege()) {
            if (LayoutExt.Edit_LAYOUT_TYPE.equals(arg.getLayout_type())) {
                return StandardAction.Edit.getFunPrivilegeCodes();
            } else if (LayoutExt.Add_LAYOUT_TYPE.equals(arg.getLayout_type())) {
                return StandardAction.Add.getFunPrivilegeCodes();
            }
        }
        return StandardController.DescribeLayout.getFuncPrivilegeCodes();
    }

    private boolean needCheckFuncPrivilege() {
        User user = controllerContext.getUser();
        return UdobjGrayUtil.isObjectAndTenantGray(UdobjGrayKey.DESCRIBE_LAYOUT_CHECK_FUNC_PRIVILEGE, user.getTenantId(), controllerContext.getObjectApiName())
                && RequestUtil.isCepRequest()
                && user.isOutUser();
    }

    /**
     * 是否跳过功能权限的校验和过滤，如果返回true，则不校验主对象的功能权限以及不按照功能权限过滤从对象和相关对象
     *
     * @return true: 跳过 false: 不跳过
     */
    protected boolean ignoreFunctionPrivilege() {
        return false;
    }

    @Override
    protected List<String> getRecordTypes() {
        if (Strings.isNullOrEmpty(arg.getRecordType_apiName())) {
            return null;
        }
        return Lists.newArrayList(arg.getRecordType_apiName());
    }

    @Override
    protected DomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return DescribeLayoutControllerDomainPlugin.Arg.builder()
                .arg(arg)
                .result(result)
                .build();
    }

    @Override
    protected void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
        if (Objects.isNull(this.result)) {
            return;
        }
        StandardDescribeLayoutController.Result newResult = ((DescribeLayoutControllerDomainPlugin.Result) pluginResult).getResult();
        if (Objects.isNull(newResult)) {
            return;
        }
        //合并主对象布局
        if (Objects.nonNull(newResult.getLayout())) {
            this.result.setLayout(newResult.getLayout());
        }
        //合并从对象布局
        if (CollectionUtils.notEmpty(newResult.getDetailObjectList()) && CollectionUtils.notEmpty(this.result.getDetailObjectList())) {
            newResult.getDetailObjectList().forEach(detailObjectResult -> {
                if (CollectionUtils.empty(detailObjectResult.getLayoutList())) {
                    return;
                }
                this.result.getDetailObjectList().stream()
                        .filter(x -> Objects.equals(x.getObjectApiName(), detailObjectResult.getObjectApiName()))
                        .filter(x -> Objects.equals(x.getFieldApiName(), detailObjectResult.getFieldApiName()))
                        .forEach(x -> x.setLayoutList(detailObjectResult.getLayoutList()));
            });
        }
    }

    @Override
    protected void doInitBefore() {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.H5_REQUEST_SOURCE, controllerContext.getTenantId())) {
            controllerContext.setAttribute(UdobjGrayConfigKey.H5_REQUEST_SOURCE, arg.getSource());
        }
        ContextCacheUtil.openContextCache();
        if (Strings.isNullOrEmpty(arg.getApiname())) {
            log.warn("leaving findDescribeByApiName(),apiName is null or emptyObj");
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (Strings.isNullOrEmpty(arg.getLayout_type())) {
            arg.setLayout_type(ILayout.DETAIL_LAYOUT_TYPE);
        }
        LayoutContext.get().setLayoutAgentType(getLayoutAgentType());
        validateByRelationField(arg);
        ignoreFunctionPrivilege = ignoreFunctionPrivilege();
    }

    private void validateByRelationField(A arg) {
        if (!isRelationObject()) {
            return;
        }
        IObjectDescribe objectDescribe = findObject();
        if (!serviceFacade.isRelatedListFormSupportObject(controllerContext.getUser(), null, objectDescribe, arg.getRelationFieldApiName())) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
    }

    @Override
    protected void doInit() {
        objectData = findObjectData();
        stopWatch.lap("findObjectData");
    }

    @Override
    protected StandardDescribeLayoutController.Result doService(A arg) {
        DescribeDetailResult describeDetailResult = findDescribeDetailResult();

        if (arg.isIncludeDescribe()) {
            //处理业务类型中每个option增加value属相
            addValueToRecordType(describeDetailResult);
            stopWatch.lap("addValueToRecordType");
        }

        if (arg.isIncludeLayout()) {
            handleRecordTypeMatchRelation(describeDetailResult);
            stopWatch.lap("handleRecordTypeMatchRelation");

            handleSlaveObjectSupportAdd(describeDetailResult);
            stopWatch.lap("handleSlaveObjectSupportAdd");

            processMaskField(describeDetailResult);
            stopWatch.lap("processMaskField");

            processLayoutRule(describeDetailResult);
            stopWatch.lap("processLayoutRule");

            //被业务插件使用且在插件定义里配置了只读的字段设置为只读
            processLayoutByDomainPlugin(describeDetailResult);
            stopWatch.lap("processLayoutByDomainPlugin");
        }

        if (arg.calculateExpression()) {
            objectData = calculateExpression(describeDetailResult);
            stopWatch.lap("calculateExpression");
            fillInfo(controllerContext.getUser(), describeDetailResult, objectData);
            stopWatch.lap("fillInfo");
        }

        StandardDescribeLayoutController.Result result = buildResult(describeDetailResult);
        stopWatch.lap("buildResult");

        return result;
    }

    private void fillInfo(User user, DescribeDetailResult describeDetailResult, IObjectData objectData) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, user.getTenantId())
                || !LayoutExt.Add_LAYOUT_TYPE.equals(arg.getLayout_type())) {
            return;
        }
        Map objectDescribe = describeDetailResult.getObjectDescribe();
        if (CollectionUtils.empty(objectDescribe)) {
            return;
        }
        fillUserInfo(user, ObjectDescribeExt.of(objectDescribe), objectData);
    }

    private void fillUserInfo(User user, IObjectDescribe objectDescribe, IObjectData data) {
        serviceFacade.fillUserInfoExcludeSingleValue(objectDescribe, Lists.newArrayList(data), user);
        serviceFacade.fillDepartmentInfoExcludeSingleValue(objectDescribe, Lists.newArrayList(data), user);
    }

    private void processLayoutByDomainPlugin(DescribeDetailResult describeDetailResult) {
        serviceFacade.getLayoutLogicService().processLayoutByDomainPlugin(controllerContext.getTenantId(), controllerContext.getObjectApiName(),
                describeDetailResult, getRecordTypes(), getPageType().name());
    }

    private void processLayoutRule(DescribeDetailResult describeDetailResult) {
        Map layout = describeDetailResult.getLayout();
        LayoutExt layoutExt = LayoutExt.of(layout);
        List<LayoutRuleInfo> layoutRule = getLayoutRule(layoutExt);
        //添加从对象布局规则
        getDetailObjectLayoutRule(describeDetailResult);
        // 通过布局规则修改布局
        handleLayoutByLayoutRule(layout, layoutRule);
        // 过滤页面控制的布局规则
        filterPageTypeRule(layoutRule);
        LayoutDocument.of(layoutExt).configLayoutRules(LayoutRuleDocument.ofList(layoutRule));
    }

    /**
     * 是否支持保存草稿
     * <p>
     * 如果是外部人员，则不支持保存草稿箱
     *
     * @return true: 支持  false: 不支持
     */
    protected boolean supportSaveDraft() {
        //黑名单企业不支持草稿
        return !describe.isBigObject() && AppFrameworkConfig.isSupportSaveDraftButton(controllerContext.getTenantId(), describe.getApiName());
    }

    protected boolean supportEditDraft() {
        return !isChangeOrder() && !describe.isBigObject() && AppFrameworkConfig.isSupportEditDraftButton(controllerContext.getTenantId(), describe.getApiName());
    }

    /**
     * 新建页面是否支持保存并新建按钮
     * <p>
     * 默认自定义对象都支持
     *
     * @return true 支持 false 不支持
     */
    protected boolean supportSaveAndCreate() {
        if (!AppFrameworkConfig.isSupportSaveAndCreate(controllerContext.getTenantId())) {
            return false;
        }
        if (ObjectDescribeExt.of(describe).isCustomObject()) {
            return true;
        }
        return layout != null && LayoutExt.of(layout).isEditLayout();
    }

    protected boolean supportAiUserLicense() {
        boolean hasUsingAi = ObjectDescribeExt.of(describe).getFieldDescribes().stream()
                .anyMatch(x -> {
                    FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(x);
                    return StringUtils.isNotBlank(fieldDescribeExt.getAiApiName()) || CollectionUtils.notEmpty(fieldDescribeExt.getMutilAiApiName());
                });
        if (hasUsingAi) {
            Map<String, Boolean> result = serviceFacade.checkFuncPrivilege(controllerContext.getUser(), Lists.newArrayList(PrivilegeConstants.AI_OBJECT_USER_LICENSE));
            return result.getOrDefault(PrivilegeConstants.AI_OBJECT_USER_LICENSE, false);
        }
        return false;
    }

    protected IObjectData findObjectData() {
        if (Strings.isNullOrEmpty(arg.getData_id()) || LayoutExt.Add_LAYOUT_TYPE.equals(arg.getLayout_type())) {
            return null;
        }
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_LAYOUT_FIND_DATA_IGNORE_RELEVANT_TEAM_GRAY, controllerContext.getTenantId())) {
            return serviceFacade.findObjectDataIgnoreRelevantTeam(controllerContext.getUser(), arg.getData_id(), arg.getApiname());
        }
        return serviceFacade.findObjectData(controllerContext.getUser(), arg.getData_id(), arg.getApiname());
    }

    private void addValueToRecordType(DescribeDetailResult describeDetailResult) {
        if (Objects.isNull(describeDetailResult.getObjectDescribe())) {
            return;
        }

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(new ObjectDescribe(describeDetailResult.getObjectDescribe()));
        describeExt.fillValueToRecordType();

        List<DetailObjectListResult> detailObjectList = describeDetailResult.getDetailObjectList();
        if (CollectionUtils.empty(detailObjectList)) {
            return;
        }

        for (DetailObjectListResult result : detailObjectList) {
            Map objectDescribe = result.getObjectDescribe();
            if (Objects.isNull(objectDescribe)) {
                continue;
            }
            ObjectDescribeExt detailExt = ObjectDescribeExt.of(new ObjectDescribe(objectDescribe));
            detailExt.fillValueToRecordType();
        }
    }

    private IObjectDescribe findObject() {
        if (Objects.isNull(describe)) {
            describe = serviceFacade.findObject(controllerContext.getTenantId(), arg.getApiname());
            stopWatch.lap("findObject");
        }
        return describe;
    }

    protected DescribeDetailResult findDescribeDetailResult() {
        DescribeDetailResult result = new DescribeDetailResult();
        //获取describe
        findObject();
        List<IObjectDescribe> detailDescribes = Lists.newArrayList();
        List<IObjectDescribe> detailDescribesCreateWithMaster = Lists.newArrayList();
        if (arg.isIncludeDetailDescribe()) {
            detailDescribes = getDetailDescribes(describe);
            detailDescribesCreateWithMaster = detailDescribes.stream()
                    .filter(x -> ObjectDescribeExt.of(x).isCreateWithMaster())
                    .collect(Collectors.toList());
        }

        //获取layout
        ILayout layout = getLayout(describe, detailDescribesCreateWithMaster);
        LayoutExt layoutExt = LayoutExt.of(layout);
        // 获取变更规则
        initChangeOrderRule();
        setReadOnlyByChangeOrderRules(describe, Lists.newArrayList(layoutExt), orderRuleList);
        // 补充变更单分组
        addChangeOrderSection(describe, layoutExt);
        //处理从对象
        handleDetailObject(arg.getLayout_type(), result, detailDescribes, layout);

        //除了Detail，其他请求只保留formComponent
        if (arg.isIncludeLayout()
                && !ILayout.DETAIL_LAYOUT_TYPE.equals(arg.getLayout_type())
                && layoutExt.isDetailLayout()) {
            layoutExt.retainComponentsWithField();
            //未开启新建编辑页，新建布局，layoutStructure没有用，所以之前不下发，850后layoutStructure中加入了是否展示字段提示信息属性，所以要下发
            rebuildLayoutStructure(layoutExt);
        }

        //清理mobile_layout
        if (Objects.nonNull(layout.getMobileLayout())) {
            layout.setMobileLayout(null);
        }

        result.setLayout(LayoutExt.of(layout).toMap());
        result.setObjectDescribe(ObjectDescribeExt.of(describe).toMap());
        // 描述拓展
        describeExtracted(describe, detailDescribes);
        return result;
    }

    private void rebuildLayoutStructure(LayoutExt layoutExt) {
        if (Objects.isNull(layoutExt.getLayoutStructure())) {
            return;
        }
        //移除插件
        LayoutStructure.removePlugins(layoutExt);

        //详情页布局重建layout_structure中的layout
        if (layoutExt.isDetailLayout() && !layoutExt.isFlowLayout() && Objects.nonNull(layoutExt.getLayoutStructure())) {
            Map<String, Object> row1 = Maps.newLinkedHashMap();
            row1.put(LayoutStructure.COLUMNS, Lists.newArrayList(ImmutableMap.of("width", "100%")));
            List<List<String>> components1 = Lists.newArrayList();
            components1.add(layoutExt.getComponentsSilently().stream().map(IComponent::getName).collect(Collectors.toList()));
            row1.put(LayoutStructure.COMPONENTS, Lists.newArrayList(components1));
            layoutExt.getLayoutStructure().put(LayoutStructure.LAYOUT, Lists.newArrayList(row1));
        }
    }

    private void describeExtracted(IObjectDescribe describe, List<IObjectDescribe> detailDescribes) {
        if (!arg.isIncludeDescribe()) {
            return;
        }

        IObjectData data = objectData;
        User user = controllerContext.getUser();
        if (Objects.isNull(data) && LayoutExt.Add_LAYOUT_TYPE.equals(arg.getLayout_type())) {
            data = new ObjectData();
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, user.getTenantId())) {
                ObjectDataExt.OwnerPolicy policy = ObjectDataExt.OwnerPolicy.builder()
                        .allowOutUserByArg(true)
                        .outUserAssignOwner(ObjectDescribeExt.of(describe).getExpectEmployeeAllocateRuleByGray(user, false))
                        .defaultDataOwnerId(() -> infraServiceFacade.getDefaultDataOwnerByUser(user).orElse(null))
                        .build();
                ObjectDataExt.of(data).setDataOwnerIfAbsent(user, policy);
            } else if (!ObjectDescribeExt.of(describe).isManualByOuterAllocateOwner(user)) {
                ObjectDataExt.of(data).setDataOwnerIfAbsent(user);
            }
        }
        describeExtra = serviceFacade.findDescribeExtraByRenderType(user, describe, detailDescribes,
                DescribeExpansionRender.RenderType.DescribeLayout, arg.describeCacheable(), isRelationObject() ? false : null, data);
        stopWatch.lap("handleDescribeExtra");
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(controllerContext.getUser(), controllerContext.getAppId());
    }

    private void handleDetailObject(String findLayoutType, DescribeDetailResult result, List<IObjectDescribe> detailDescribes, ILayout layout) {
        if (!arg.isIncludeDetailDescribe()) {
            return;
        }
        List<DetailObjectListResult> detailObjectListResultList = Lists.newArrayList();
        List<DetailObjectListResult> allObjectListResultList = Lists.newArrayList();

        if (CollectionUtils.notEmpty(detailDescribes)) {
            //按照布局里配置的显示顺序来对从对象进行排序，先将从对象按照id排序，防止没有在布局管理配置过顺序的从对象出现乱序。
            Collections.sort(detailDescribes, Comparator.comparing(IObjectDescribe::getId));
            List<String> detailApiNames = detailDescribes.stream().map(x -> x.getApiName()).distinct().collect(Collectors.toList());
            // 返回 多个移动端摘要布局，根据业务类型匹配
            Map<String, Map<String, ILayout>> layoutByRecordTypeMap;
            Map<String, List<IRecordTypeOption>> recordTypeOptionMap;
            if (arg.isIncludeLayout()) {
                //移除布局中隐藏的从对象
                if (LayoutExt.of(layout).isEditLayout() || LayoutExt.of(layout).isFlowLayout()) {
                    detailDescribes.removeIf(x -> !EditLayout.of(layout).containsDetailObjComponent(x.getApiName()));
                }
                Collections.sort(detailDescribes, Comparator.comparingInt(o -> LayoutExt.of(layout).getDetailComponentOrder(o.getApiName())));
                if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_673)) {
                    recordTypeOptionMap = serviceFacade.findValidRecordTypeListMap(detailApiNames, controllerContext.getUser());
                } else {
                    recordTypeOptionMap = serviceFacade.findRecordTypes(controllerContext.getTenantId(), detailApiNames);
                }
                stopWatch.lap("findRecordTypes");

                layoutByRecordTypeMap = serviceFacade.getLayoutLogicService().findMobileListLayoutByDescApis(buildLayoutContext(), detailDescribes, recordTypeOptionMap, false);
                stopWatch.lap("findMobileListLayouts");
            } else {
                layoutByRecordTypeMap = Maps.newHashMap();
                recordTypeOptionMap = Maps.newHashMap();
            }

            detailDescribes.forEach(detailDescribe -> {
                //处理水印字段
                serviceFacade.processWaterMarkField(detailDescribe, controllerContext.getUser());
                stopWatch.lap("processWaterMarkField");
                //获取到主从类型的字段
                ObjectDescribeExt describeExt = ObjectDescribeExt.of(detailDescribe);
                Optional<MasterDetailFieldDescribe> masterDetailField = describeExt.getMasterDetailFieldDescribe();
                if (!masterDetailField.isPresent()) {
                    return;
                }

                List<RecordTypeLayoutStructure> layoutStructures = Lists.newArrayList();
                if (arg.isIncludeLayout()) {
                    LayoutContext.get().setIsReplaceFormTable(true);
                    layoutStructures = getRecordTypeLayoutStructureList(controllerContext.getUser(), findLayoutType,
                            detailDescribe, masterDetailField.get(), layoutByRecordTypeMap.get(detailDescribe.getApiName()),
                            recordTypeOptionMap.get(detailDescribe.getApiName()), layout);
                    stopWatch.lap("getDetailLayout");
                }
                DetailObjectListResult detailObjectListResult = DetailObjectListResult.builder()
                        .objectApiName(detailDescribe.getApiName())
                        .fieldApiName(masterDetailField.get().getApiName())
                        .fieldLabel(masterDetailField.get().getLabel())
                        .objectDescribe(describeExt.toMap())
                        .relatedListName(masterDetailField.get().getTargetRelatedListName())
                        .relatedListLabel(masterDetailField.get().getTargetRelatedListLabel())
                        .layoutList(layoutStructures)
                        .build();

                if (StringUtils.equals(arg.getLayout_type(), LayoutExt.Edit_LAYOUT_TYPE)) {
                    modifyDetailSelectOneReadOnlyProperty(controllerContext.getUser(), detailDescribe, detailObjectListResult);
                    stopWatch.lap("modifyDetailSelectOneReadOnlyProperty");
                }
                //王老吉特殊业务，移动端主从同时新建的从对象，按表格样式展示
                if (AppFrameworkConfig.isDetailRenderTable(controllerContext.getTenantId(), detailDescribe.getApiName())) {
                    detailObjectListResult.setRenderType("table");
                } else if (LayoutExt.of(layout).isDetailLayout() && AppFrameworkConfig.isGrayMobileCardRenderType(controllerContext.getTenantId(), describe.getApiName())) {
                    //详情页布局根据主从交互灰度下发从对象的renderType，让用户可以不开启新建编辑布局即可使用小程序的新交互
                    detailObjectListResult.setRenderType(ComponentRenderTypes.CARD);
                }
                allObjectListResultList.add(detailObjectListResult);

                //判断是否勾选了"新建主对象时跟随着一起新建的属性",未勾选则不返回
                if (masterDetailField.get().getIsCreateWhenMasterCreate()) {
                    detailObjectListResultList.add(detailObjectListResult);
                }
            });
        }
        //获取相关对象的describe
        result.setDetailObjectList(detailObjectListResultList);
        result.setAllObjectDetailObjectList(allObjectListResultList);
    }

    protected List<IObjectDescribe> getDetailDescribes(IObjectDescribe masterDescribe) {
        List<IObjectDescribe> detailDescribes = serviceFacade.findDetailDescribes(masterDescribe.getTenantId(), masterDescribe.getApiName());
        stopWatch.lap("findDetailDescribes");
        if (!arg.isIncludeActiveDetail()) {
            detailDescribes = detailDescribes.stream().filter(IObjectDescribe::isActive).collect(Collectors.toList());
        }
        if (CollectionUtils.notEmpty(detailDescribes) && !ignoreFunctionPrivilege) {
            detailDescribes = serviceFacade.filterDescribesWithActionCode(controllerContext.getUser(), detailDescribes, getObjectActionCode(arg.getLayout_type()));
            stopWatch.lap("filterDetailDescribes");
        }
        return detailDescribes;
    }

    private ILayout getLayout(IObjectDescribe describe, List<IObjectDescribe> detailDescribes) {
        layout = new Layout();
        if (!arg.isIncludeLayout()) {
            return layout;
        }
        layout = finLayout(describe);
        stopWatch.lap("findLayout");
        //判断是否为流程布局
        if (LayoutExt.of(layout).isFlowLayout()) {
            //流程布局返回edit类型，让前端走新建编辑布局的逻辑
            layout.setLayoutType(LayoutTypes.EDIT);

            boolean supportMultiLanguage = serviceFacade.isSupportMultiLanguage(controllerContext.getTenantId());
            stopWatch.lap("checkMultiLanguage");
            EditLayoutRender.builder()
                    .functionPrivilegeService(serviceFacade)
                    .licenseService(serviceFacade)
                    .describeLogicService(serviceFacade)
                    .layoutLogicService(serviceFacade.getLayoutLogicService())
                    .extraLayoutManage(serviceFacade.getBean(ExtraLayoutManage.class))
                    .user(controllerContext.getUser())
                    .pageType(getPageType())
                    .layoutAgentType(getLayoutAgentType())
                    .layout(layout)
                    .describe(describe)
                    .detailDescribes(detailDescribes)
                    .existMultiLanguage(supportMultiLanguage)
                    .ignoreFunctionPrivilege(ignoreFunctionPrivilege)
                    .appId(controllerContext.getAppId())
                    .build()
                    .render();
            stopWatch.lap("renderLayout");
        } else {
            //860下游不屏蔽负责人字段，根据用户选择是否自己回填
            if (controllerContext.getUser().isOutUser() && !ObjectDescribeExt.of(describe).getExpectEmployeeAllocateRuleByGray(controllerContext.getUser(), true)) {
                EditLayout.of(layout).removeField(IObjectData.OWNER);
            }
            LayoutExt layoutExt = LayoutExt.of(layout);
            if (isRelationObject()) {
                String relationFieldApiName = arg.getRelationFieldApiName();
                // 移除关联字段
                layoutExt.removeFields(Lists.newArrayList(relationFieldApiName, IObjectData.OWNER, IObjectData.DATA_OWN_DEPARTMENT, IObjectData.DATA_OWN_ORGANIZATION, IObjectData.OUT_OWNER));
                // 移除不支持的字段
                layoutExt.removeFieldByTypes(ObjectDescribeExt.of(describe), IFieldType.HTML_RICH_TEXT, IFieldType.RICH_TEXT, IFieldType.COUNT, IFieldType.FORMULA, IFieldType.QUOTE);
            }
            if (AppFrameworkConfig.mobileSupportUiPaasButtonGrayEi(controllerContext.getTenantId())) {
                LayoutContext.get().setEnableMobileLayout(layoutExt.isEnableMobileLayout());
            }
            stopWatch.lap("removeFields");

            //处理按钮
            List<IButton> buttons;
            Map<ButtonUsePageType, List<IButton>> customButtonMap = Maps.newHashMap();
            if (LayoutExt.Add_LAYOUT_TYPE.equals(arg.getLayout_type())) {
                buttons = serviceFacade.findButtonsForCreate(describe, controllerContext.getUser());
                customButtonMap.put(ButtonUsePageType.Create, buttons);
            } else if (LayoutExt.Edit_LAYOUT_TYPE.equals(arg.getLayout_type())) {
                buttons = serviceFacade.findButtonsForEdit(describe, controllerContext.getUser());
                customButtonMap.put(ButtonUsePageType.Edit, buttons);
            } else {
                buttons = serviceFacade.getButtonByComponentActions(controllerContext.getUser(), ComponentActions.LIST_PAGE_HEADER,
                        describe, null, false);
            }
            stopWatch.lap("findButtons");

            boolean supportMultiLanguage = serviceFacade.isSupportMultiLanguage(controllerContext.getTenantId());
            stopWatch.lap("checkMultiLanguage");
            //判断布局类型是详情页还是新建编辑页
            if (layoutExt.isEditLayout()) {
                EditLayoutRender.builder()
                        .functionPrivilegeService(serviceFacade)
                        .licenseService(serviceFacade)
                        .describeLogicService(serviceFacade)
                        .layoutLogicService(serviceFacade.getLayoutLogicService())
                        .extraLayoutManage(serviceFacade.getBean(ExtraLayoutManage.class))
                        .objectConvertRuleService(infraServiceFacade)
                        .user(controllerContext.getUser())
                        .pageType(getPageType())
                        .layoutAgentType(getLayoutAgentType())
                        .layout(layout)
                        .describe(describe)
                        .detailDescribes(detailDescribes)
                        .customButtonMap(customButtonMap)
                        .existMultiLanguage(supportMultiLanguage)
                        .ignoreFunctionPrivilege(ignoreFunctionPrivilege)
                        .appId(controllerContext.getAppId())
                        .build()
                        .render();
                stopWatch.lap("renderEditLayout");
                List<String> removeActions = Lists.newArrayList();
                if (!supportSaveDraft()) {
                    removeActions.add(ObjectAction.CREATE_SAVE_DRAFT.getActionCode());
                }
                if (!supportEditDraft()) {
                    removeActions.add(ObjectAction.EDIT_SAVE_DRAFT.getActionCode());
                }
                if (!supportSaveAndCreate()) {
                    removeActions.add(ObjectAction.CREATE_SAVE_CONTINUE.getActionCode());
                }
                WebDetailLayout.of(layout).removeButtonsByActionCode(removeActions);
            } else {
                layout.setButtons(buttons);
                if (LayoutAgentType.MOBILE == getLayoutAgentType() && LayoutExt.of(layout).isDetailLayout()) {
                    LayoutExt.of(layout).syncFieldsFromFormTable(null);
                    LayoutExt.of(layout).removeFormTableComponents();

                    //从移动端布局的layout_structure同步配置
                    LayoutExt.of(layout).syncLayoutStructurerFromMobileLayout();
                }
                ComponentHeaderSetter.builder()
                        .tenantId(layoutExt.getTenantId())
                        .layoutType(layoutExt.getLayoutType())
                        .components(layoutExt.getComponentsSilently())
                        .objectApiName(layoutExt.getRefObjectApiName())
                        .layoutApiName(layoutExt.getName())
                        .existMultiLanguage(supportMultiLanguage)
                        .componentPreKeyMap(serviceFacade.getLayoutLogicService().findComponentPreKeys(layoutExt.getComponentsSilently()))
                        .build()
                        .reset();
            }
        }

        //清空从对象布局中的events
        if (ObjectDescribeExt.of(describe).isCreateWithMaster() || isRelationObject()) {
            LayoutExt.of(layout).removeUIEvent();
        }
        if (LayoutExt.Edit_LAYOUT_TYPE.equals(arg.getLayout_type())) {
            serviceFacade.processSelectOneByStageInstance(controllerContext.getUser(), describe, layout, objectData);
            stopWatch.lap("processSelectOneByStageInstance");
        }

        // 处理 fieldAlign
        handleFieldAlign(layout);
        if (LayoutAgentType.MOBILE == getLayoutAgentType()) {
            LayoutStructure.handleIsTileHelpText(layout);
        }
        stopWatch.lap("handleFieldAlign");

        return layout;
    }

    private void initChangeOrderRule() {
        if (!LayoutExt.Edit_LAYOUT_TYPE.equals(arg.getLayout_type())) {
            return;
        }
        if (!ChangeOrderConfig.changeOrderDescribeGray(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
            return;
        }
        if (!isChangeOrder()) {
            return;
        }
        if (!ObjectDescribeExt.of(describe).enabledChangeOrder()) {
            return;
        }
        List<MtChangeOrderRule> orderRules = infraServiceFacade.findByDescribeApiName(controllerContext.getUser(), describe.getApiName());
        if (CollectionUtils.empty(orderRules)) {
            throw new ValidateException(I18NExt.text(I18NKey.NO_AVAILABLE_CHANGE_RULES));
        }
        if (orderRules.size() > 1) {
            throw new ValidateException(I18NExt.text(I18NKey.UNSUPPORTED_MULTI_CHANGE_RULE));
        }
        this.orderRuleList = orderRules;
    }

    private boolean isChangeOrder() {
        return ChangeOrderConfig.isChangeOrderAction(arg.getActionType());
    }

    private void setReadOnlyByChangeOrderRules(IObjectDescribe describe, List<ILayout> layoutList, List<MtChangeOrderRule> orderRuleList) {
        if (CollectionUtils.empty(orderRuleList)) {
            return;
        }
        for (MtChangeOrderRule rule : orderRuleList) {
            MtChangeOrderRule.ObjectFieldMapper objectFieldMapper = rule.toObjectFieldMapper(describe);
            for (ILayout layout : layoutList) {
                LayoutExt layoutExt = LayoutExt.of(layout);
                for (FormComponentExt formComponent : layoutExt.getFormComponents()) {
                    for (IFormField formField : formComponent.getFormFields()) {
                        if (BooleanUtils.isNotTrue(formField.isReadOnly())) {
                            formField.setReadOnly(!objectFieldMapper.changeableBySource(describe.getApiName(), formField.getFieldName()));
                        }
                    }
                }

                for (FormTable formTable : layoutExt.getFormTables()) {
                    for (IFormField formField : formTable.getFields()) {
                        if (BooleanUtils.isNotTrue(formField.isReadOnly())) {
                            formField.setReadOnly(!objectFieldMapper.changeableBySource(describe.getApiName(), formField.getFieldName()));
                        }
                    }
                }
            }
        }
    }

    private void addChangeOrderSection(IObjectDescribe describe, LayoutExt layoutExt) {
        // 流程布局不需要补充变更单分组
        if (CollectionUtils.empty(orderRuleList) || layoutExt.isFlowLayout()) {
            return;
        }
        ChangeOrderConfig.getChangedReasonField()
                .ifPresent(field -> {
                    field.setRequired(true);
                    layoutExt.addChangeOrderSection(Lists.newArrayList(field), false);
                });
        // 变更单屏蔽编辑页的保存草稿按钮
        List<IButton> buttons = layoutExt.getButtons();
        if (CollectionUtils.empty(buttons)) {
            return;
        }
        buttons.removeIf(button -> ObjectAction.EDIT_SAVE_DRAFT.getButtonApiName().equals(button.getName()));
        layoutExt.setButtons(buttons);
    }

    private ILayout finLayout(IObjectDescribe describe) {
        LayoutContext.get().setIsReplaceFormTable(false);
        String layoutApiName = getLayoutApiName();
        if (Strings.isNullOrEmpty(layoutApiName)) {
            return serviceFacade.getLayoutLogicService().findObjectLayoutWithTypeIncludeAllComponent(buildLayoutContext(),
                    arg.getRecordType_apiName(), describe, arg.getLayout_type(), objectData);
        }
        // 有layoutApiName则直接使用apiName查布局
        return serviceFacade.getLayoutLogicService().findObjectLayoutByApiNameAndType(controllerContext.getUser(), layoutApiName,
                arg.getLayout_type(), describe, objectData);
    }

    protected String getLayoutApiName() {
        return arg.getLayoutApiName();
    }

    private boolean isRelationObject() {
        return !Strings.isNullOrEmpty(arg.getRelationFieldApiName());
    }

    private void handleFieldAlign(ILayout layout) {
        if (!infraServiceFacade.isGrayFieldAlign(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
            return;
        }
        LayoutExt layoutExt = LayoutExt.of(layout);
        // 当前布局为详情页布局,需要单独获取移动端独立布局
        ILayout fieldAlignLayout;
        boolean layoutIsChange = false;
        if (LayoutAgentType.MOBILE == getLayoutAgentType()
                && layoutExt.isDetailLayout() && layoutExt.isEnableMobileLayout()) {
            fieldAlignLayout = LayoutExt.of(layoutExt.getMobileLayout());
            layoutIsChange = true;
        } else {
            fieldAlignLayout = layout;
        }
        infraServiceFacade.handleLayoutWithGlobalFieldAlign(controllerContext.getTenantId(), describe, fieldAlignLayout, controllerContext.getLang());

        if (layoutIsChange) {
            layoutExt.set(LayoutStructure.FIELD_ALIGN, fieldAlignLayout.get(LayoutStructure.FIELD_ALIGN));
        }
    }

    private PageType getPageType() {
        if (LayoutTypes.ADD.equals(arg.getLayout_type())) {
            return PageType.Add;
        }
        return PageType.Edit;
    }

    private LayoutAgentType getLayoutAgentType() {
        if (LayoutAgentType.MOBILE.getCode().equals(arg.getLayoutAgentType()) || RequestUtil.isMobileOrH5Request()) {
            return LayoutAgentType.MOBILE;
        }
        return LayoutAgentType.WEB;
    }

    private void modifyDetailSelectOneReadOnlyProperty(User user, IObjectDescribe describe, DetailObjectListResult detailObjectListResult) {
        List<RecordTypeLayoutStructure> layoutList = detailObjectListResult.getLayoutList();

        layoutList.forEach(x -> {
            LayoutExt detailLayout = LayoutExt.of(x.getDetail_layout());
            serviceFacade.processSelectOneByStageInstance(user, describe, detailLayout, null);
        });
    }

    private List<RecordTypeLayoutStructure> getRecordTypeLayoutStructureList(User user,
                                                                             String layoutType,
                                                                             IObjectDescribe detailObject,
                                                                             MasterDetail refField,
                                                                             Map<String, ILayout> abLayoutByType,
                                                                             List<IRecordTypeOption> recordTypeOptionList,
                                                                             ILayout masterLayout) {
        RelatedObjectDescribeStructure relatedStructure = RelatedObjectDescribeStructure.builder()
                .relatedObjectDescribe(detailObject)
                .fieldApiName(refField.getApiName())
                .fieldLabel(refField.getLabel())
                .relatedListName(refField.getTargetRelatedListName())
                .relatedListLabel(refField.getTargetRelatedListLabel())
                .build();

        if (CollectionUtils.notEmpty(abLayoutByType)) {
            abLayoutByType.forEach((type, layout) -> {
                TableComponentRender.builder()
                        .functionPrivilegeService(serviceFacade)
                        .user(user)
                        .describeExt(ObjectDescribeExt.of(detailObject))
                        .tableComponentExt(TableComponentExt.of(LayoutExt.of(layout).getTableComponent()
                                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)))))
                        .build()
                        .render();
            });
        }

        List<RecordTypeLayoutStructure> result = Lists.newArrayList();
        for (IRecordTypeOption recordTypeOption : recordTypeOptionList) {
            ILayout detailLayout;
            //流程布局的从对象的布局apiName存在从对象组件里
            if (LayoutExt.of(masterLayout).isFlowLayout()) {
                IComponent detailObjComponent = LayoutExt.of(masterLayout).getComponentByApiName(ComponentExt.getDetailComponentName(detailObject.getApiName()))
                        .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
                //复用对象侧布局的话直接查对象布局，否则查询指定的流程布局
                if (ComponentExt.of(detailObjComponent).isLayoutSourceFromObject()) {
                    detailLayout = serviceFacade.getLayoutLogicService().findObjectLayoutWithType(buildLayoutContext(), recordTypeOption.getApiName(),
                            detailObject, layoutType, null);
                } else {
                    String detailLayoutApiName = ComponentExt.of(detailObjComponent).getFlowLayoutApiName();
                    detailLayout = serviceFacade.getLayoutLogicService().findObjectLayoutByApiNameAndType(user, detailLayoutApiName,
                            layoutType, detailObject, null);
                }
            } else {
                detailLayout = serviceFacade.getLayoutLogicService().findObjectLayoutWithType(buildLayoutContext(), recordTypeOption.getApiName(),
                        detailObject, layoutType, null, StringUtils.equals(controllerContext.getObjectApiName(), detailObject.getApiName()));
            }
            LayoutExt detailLayoutExt = LayoutExt.of(detailLayout);
            detailLayoutExt.removeFieldByTypes(ObjectDescribeExt.of(detailObject), IFieldType.MASTER_DETAIL);
            // 更具变更规则处理从对象的布局
            setReadOnlyByChangeOrderRules(detailObject, Lists.newArrayList(detailLayoutExt), orderRuleList);

            //处理移动端新建数据时，从对象分组名称没有翻译的问题
            if (LayoutContext.isMobileLayout()) {
                boolean existMultiLanguage = serviceFacade.isSupportMultiLanguage(user.getTenantId());
                ComponentHeaderSetter.builder()
                        .tenantId(user.getTenantId())
                        .layoutType(detailLayoutExt.getLayoutType())
                        .layoutVersion(LayoutContext.get().getLayoutVersion())
                        .components(detailLayoutExt.getComponentsSilently())
                        .objectApiName(detailLayoutExt.getRefObjectApiName())
                        .layoutApiName(detailLayoutExt.getName())
                        .existMultiLanguage(existMultiLanguage)
                        .componentPreKeyMap(serviceFacade.getLayoutLogicService().findComponentPreKeys(detailLayoutExt.getComponentsSilently()))
                        .build()
                        .reset();
            }

            List<IButton> listBatchButtons = null;
            List<IButton> listSingleButtons = null;
            List<ISummaryComponentInfo> allPageSummaryInfo = null;
            if (LayoutExt.of(masterLayout).isEditLayout()) {
                EditLayout editLayout = EditLayout.of(masterLayout);
                List<IButton> listNormalButtons = editLayout.getListNormalDetailObjButtons(detailObject,
                        detailLayout);
                detailLayout.setButtons(listNormalButtons);
                listBatchButtons = editLayout.getListBatchDetailObjButtons(detailObject.getApiName());
                listSingleButtons = editLayout.getListSingleDetailObjButtons(user.getTenantId(), detailObject.getApiName());

                allPageSummaryInfo = editLayout.getAllPageSummaryInfo(detailObject.getApiName());
                // 变更单，屏蔽从对象的复制按钮
                if (CollectionUtils.notEmpty(orderRuleList)) {
                    listBatchButtons.removeIf(it -> Objects.equals(ObjectAction.CLONE.getButtonApiName(), it.getName()));
                    listSingleButtons.removeIf(it -> Objects.equals(ObjectAction.CLONE.getButtonApiName(), it.getName()));
                }
            }

            LayoutExt listLayoutExt = LayoutExt.of(new Layout(Maps.newLinkedHashMap(detailLayoutExt.toMap())));
            TableComponent tableComponentByRecordType = LayoutExt.of(abLayoutByType.get(recordTypeOption.getApiName())).getTableComponentByRecordType(relatedStructure,
                    recordTypeOption);
            listLayoutExt.setComponents(Lists.newArrayList(tableComponentByRecordType));

            RecordTypeLayoutStructure recordTypeLayoutStructure = RecordTypeLayoutStructure.builder()
                    .record_type(recordTypeOption.getApiName())
                    .detail_layout(detailLayoutExt.toMap())
                    .list_layout(listLayoutExt.toMap())
                    .build();
            //从对象批量按钮
            if (listBatchButtons != null) {
                recordTypeLayoutStructure.setBatchButtons(ButtonDocument.fromButtons(listBatchButtons).stream().collect(Collectors.toList()));
            }
            //从对象单条按钮
            if (listSingleButtons != null) {
                recordTypeLayoutStructure.setSingleButtons(ButtonDocument.fromButtons(listSingleButtons).stream().collect(Collectors.toList()));
            }
            if (CollectionUtils.notEmpty(allPageSummaryInfo)) {
                Set<String> unauthorizedFields = serviceFacade.getUnauthorizedFields(user, detailObject.getApiName());
                Set<String> authorizedFields = ObjectDescribeExt.of(detailObject).stream()
                        .map(IFieldDescribe::getApiName)
                        .filter(it -> !unauthorizedFields.contains(it))
                        .collect(Collectors.toSet());
                List<Map> summaryInfo = ListComponentExt.filterSummaryInfos(detailObject, allPageSummaryInfo, authorizedFields).stream()
                        .map(ISummaryComponentInfo::toMap)
                        .collect(Collectors.toList());
                recordTypeLayoutStructure.setAllPageSummaryInfo(summaryInfo);
            }
            result.add(recordTypeLayoutStructure);
        }
        return result;
    }

    private String getObjectActionCode(String layoutType) {
        ObjectAction action;
        if (LayoutExt.Add_LAYOUT_TYPE.equals(layoutType)) {
            action = ObjectAction.CREATE;
        } else if (LayoutExt.Edit_LAYOUT_TYPE.equals(layoutType)) {
            action = ObjectAction.UPDATE;
        } else {
            action = ObjectAction.VIEW_LIST;
        }
        return action.getActionCode();
    }

    protected IObjectData calculateExpression(DescribeDetailResult describeDetailResult) {
        if (!LayoutExt.Add_LAYOUT_TYPE.equals(arg.getLayout_type())) {
            return objectData;
        }
        ObjectDescribe objectDescribe = new ObjectDescribe(describeDetailResult.getObjectDescribe());
        User user = controllerContext.getUser();
        dealOwnerAllocateType(describeDetailResult.getLayout(), objectDescribe);
        return calculateForCreateData(describeDetailResult, objectDescribe, user);
    }

    private IObjectData calculateForCreateData(DescribeDetailResult describeDetailResult, ObjectDescribe describe, User user) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, controllerContext.getTenantId())) {
            return serviceFacade.calculateExpressionForCreateData(user, arg.getRecordType_apiName(), describe, data -> {
                // 新建场景，执行计算之前修改数据，如：修改负责人字段的默认值等
                modifyDataBeforeCalculateWithCreate(user, describe, data, describeDetailResult);
            });
        }
        IObjectData data = serviceFacade.calculateExpressionForCreateData(user, arg.getRecordType_apiName(), describe);

        //新建时，布局中隐藏了 负责人 字段，数据中默认不下发
        if (CollectionUtils.empty(describeDetailResult.getLayout())) {
            return data;
        }
        if (needRemoveOwnerField(user, describeDetailResult)) {
            ObjectDataExt.of(data).remove(Sets.newHashSet(IObjectData.OWNER, IObjectData.OWNER + "__r"));
        }
        ObjectDataExt.of(data).removeEmptyMultiCurrencyFields();
        ObjectDataExt.of(data).removeDefaultValueIfInvalid(ObjectDescribeExt.of(describeDetailResult.getObjectDescribe()));
        return data;
    }

    protected void modifyDataBeforeCalculateWithCreate(User user, IObjectDescribe describe, IObjectData objectData, DescribeDetailResult describeDetailResult) {
        //新建时，布局中隐藏了 负责人 字段，数据中默认不下发
        if (CollectionUtils.notEmpty(describeDetailResult.getLayout()) && needRemoveOwnerField(user, describeDetailResult)) {
            ObjectDataExt.of(objectData).remove(Sets.newHashSet(IObjectData.OWNER, IObjectData.OWNER + "__r"));
        }
        ObjectDataExt.of(objectData).removeEmptyMultiCurrencyFields();
        ObjectDataExt.of(objectData).removeDefaultValueIfInvalid(ObjectDescribeExt.of(describe));
    }

    private boolean needRemoveOwnerField(User user, DescribeDetailResult describeDetailResult) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, user.getTenantId()) && user.isOutUser()) {
            return false;
        }
        Optional<IFieldDescribe> ownerField = ObjectDescribeExt.of(describeDetailResult.getObjectDescribe()).getOwnerField();
        return !LayoutExt.of(describeDetailResult.getLayout()).getField(IObjectData.OWNER).isPresent()
                && ownerField.isPresent()
                && !ownerField.get().isRequired();
    }

    /**
     * 如果负责人字段在布局中隐藏，那么负责人需要补默认值
     *
     * @param layout
     * @param describe
     */
    private void dealOwnerAllocateType(Map layout, ObjectDescribe describe) {
        Optional<IFormField> layoutOwnerField = LayoutExt.of(layout).getField(OWNER_API_NAME);
        IFieldDescribe fieldDescribe = ObjectDescribeExt.of(describe).getFieldDescribe(OWNER_API_NAME);
        if (!layoutOwnerField.isPresent() && Objects.nonNull(fieldDescribe)) {
            ((Employee) fieldDescribe).setPeopleAllocateRule(EmployeeAllocateRuleType.AUTO.getType());
        }
    }

    private void processMaskField(DescribeDetailResult describeDetailResult) {
        // 开起灰度后,掩码字段加密的逻辑需要在最后处理
        if (AppFrameworkConfig.maskFieldEncryptGray(controllerContext.getTenantId(), describe.getApiName())) {
            return;
        }
        if (!LayoutExt.Edit_LAYOUT_TYPE.equals(arg.getLayout_type())) {
            return;
        }

        IObjectDescribe describe = ObjectDescribeExt.of(describeDetailResult.getObjectDescribe()).getObjectDescribe();
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(describe.getApiName(), describe);
        List<DetailObjectListResult> detailObjectList = describeDetailResult.getDetailObjectList();
        Map<String, List<RecordTypeLayoutStructure>> detailLayoutMap = Maps.newHashMap();
        Map<String, List<IObjectData>> detailDataMap = Maps.newHashMap();

        //防止有些场景调这个接口没有传data_id
        IObjectData masterData;
        if (objectData == null) {
            masterData = new ObjectData();
            masterData.setDescribeApiName(describe.getApiName());
            masterData.setTenantId(controllerContext.getTenantId());
        } else {
            masterData = objectData;
        }

        if (CollectionUtils.notEmpty(detailObjectList)) {
            detailObjectList.forEach(x -> {
                IObjectDescribe detailDescribe = ObjectDescribeExt.of(x.getObjectDescribe()).getObjectDescribe();
                describeMap.put(detailDescribe.getApiName(), detailDescribe);
                detailLayoutMap.put(detailDescribe.getApiName(), x.getLayoutList());
                IObjectData detailData = new ObjectData();
                detailData.setTenantId(controllerContext.getTenantId());
                detailData.setDescribeApiName(detailDescribe.getApiName());
                detailData.setOwner(masterData.getOwner());
                detailDataMap.put(detailDescribe.getApiName(), Lists.newArrayList(detailData));
            });
        }
        serviceFacade.fillMaskFieldValue(controllerContext.getUser(), masterData, detailDataMap, describeMap, false);

        if (arg.isIncludeLayout()) {
            ILayout layout = LayoutExt.of(describeDetailResult.getLayout()).getLayout();
            //隐藏主对象的掩码字段
            ObjectDescribeExt.of(describe).getMaskFields().stream()
                    .filter(x -> ObjectDataExt.of(masterData).containsField(FieldDescribeExt.getShowFieldName(x.getApiName())))
                    .forEach(x -> EditLayout.of(layout).removeField(x.getApiName()));
            //隐藏从对象的掩码字段
            describeMap.values().stream().filter(x -> !describe.getApiName().equals(x.getApiName())).forEach(x -> {
                ObjectDescribeExt.of(x).getMaskFields().stream()
                        .filter(y -> ObjectDataExt.of(detailDataMap.get(x.getApiName()).get(0)).containsField(FieldDescribeExt.getShowFieldName(y.getApiName())))
                        .forEach(y -> {
                            List<RecordTypeLayoutStructure> layoutList = detailLayoutMap.get(x.getApiName());
                            layoutList.forEach(l -> {
                                EditLayout.of(l.getDetail_layout()).removeField(y.getApiName());
                                EditLayout.of(l.getList_layout()).removeField(y.getApiName());
                            });
                        });
            });
        }
    }

    private IObjectData fillMaskFieldValue(User user, IObjectDescribe describe, IObjectData objectData, boolean removeOtherFields) {
        if (!AppFrameworkConfig.maskFieldEncryptGray(user.getTenantId(), describe.getApiName())) {
            return null;
        }
        if (AppFrameworkConfig.maskFieldEncryptObjectPagesGray(user.getTenantId(), describe.getApiName())) {
            return null;
        }
        if (!arg.isIncludeDescribe()) {
            return null;
        }
        MaskFieldLogicService maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);

        ObjectDescribeExt masterDescribeExt = getMasterDescribeExt(describe);
        List<IFieldDescribe> maskFields = masterDescribeExt.stream()
                .filter(it -> BooleanUtils.isTrue(FieldDescribeExt.of(it).getMaskFieldEncrypt()))
                .collect(Collectors.toList());
        maskFieldLogicService.fillMaskFieldValue(controllerContext.getUser(), describe, Lists.newArrayList(objectData), maskFields,
                MaskFieldLogicService.MaskFieldConfig.defaultMaskFieldConfig());

        if (removeOtherFields) {
            Set<String> maskFieldNames = maskFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
            Set<String> fields = ObjectDescribeExt.of(describe).stream()
                    .map(IFieldDescribe::getApiName)
                    .filter(it -> !maskFieldNames.contains(it))
                    .collect(Collectors.toSet());
            ObjectDataExt.filterUnauthorizedFieldsByDataList(Lists.newArrayList(objectData), fields, describe);
        }
        return objectData;
    }

    private ObjectDescribeExt getMasterDescribeExt(IObjectDescribe describe) {
        Map<String, Object> objectMap = describeExtra.getDescribeExpansion();
        if (Objects.isNull(objectMap)) {
            return ObjectDescribeExt.of(describe);
        }
        return ObjectDescribeExt.of(objectMap);
    }

    protected StandardDescribeLayoutController.Result buildResult(DescribeDetailResult describeDetailResult) {
        StandardDescribeLayoutController.Result result = StandardDescribeLayoutController.Result.builder()
                .layout(LayoutDocument.of(describeDetailResult.getLayout()))
                .objectDescribe(ObjectDescribeDocument.of(describeDetailResult.getObjectDescribe()))
                .refObjectDescribeList(describeDetailResult.getRefObjectDescribeList())
                .templates(QueryTemplateDocument.ofList(describeDetailResult.getTemplates()))
                .detailObjectList(describeDetailResult.getDetailObjectList())
                .objectData(getDataResult())
                .maskData(getMaskData())
                .fieldList(describeDetailResult.getFieldList())
                .allDetailObjectList(arg.isIncludeAllDetailDescribe() ? describeDetailResult.getAllObjectDetailObjectList() : null)
                // web端是否支持保存草稿
                .supportDraft(supportSaveDraft())
                // web端是否支持编辑页保存草稿
                .supportEditDraft(supportEditDraft())
                // 新建页面支持保存并新建
                .supportSaveAndCreate(supportSaveAndCreate())
                .supportAiUserLicense(supportAiUserLicense())
                .plugin(getPlugin())
                .build();

        fillDescribeExt(result);

        return result;
    }

    private StandardPluginListController.Result getPlugin() {
        if (BooleanUtils.isNotTrue(arg.getIncludePlugin())) {
            return StandardPluginListController.Result.builder().build();
        }
        ControllerContext controllerContext = ContextManager.buildControllerContext(getControllerContext().getObjectApiName(),
                StandardController.PluginList.name());
        StandardPluginListController.Arg argParam = StandardPluginListController.Arg.builder()
                .actionCode(arg.getLayout_type()).agentType(getLayoutAgentType().getCode()).build();
        return serviceFacade.triggerController(controllerContext, argParam, StandardPluginListController.Result.class);
    }

    private void fillDescribeExt(StandardDescribeLayoutController.Result result) {
        if (!arg.isIncludeDescribe()) {
            return;
        }
        result.setObjectDescribeExt(ObjectDescribeDocument.of(describeExtra.getDescribeExpansion()));
        if (CollectionUtils.notEmpty(result.getDetailObjectList())) {
            result.getDetailObjectList().forEach(x ->
                    x.setObjectDescribeExt(ObjectDescribeDocument.of(describeExtra.getDescribeExpansionByApiName(x.getObjectApiName()))));
        }
    }

    private void filterPageTypeRule(List<LayoutRuleInfo> layoutRule) {
        layoutRule.removeIf(x -> LayoutRuleExt.of(x).isPageTypeRule());
    }

    private void handleLayoutByLayoutRule(Map layout, List<LayoutRuleInfo> layoutRule) {
        LayoutExt.of(layout).handleLayoutByLayoutRule(layoutRule, arg.getLayout_type());
    }

    private ObjectDataDocument getDataResult() {
        if (!LayoutExt.Add_LAYOUT_TYPE.equals(arg.getLayout_type())) {
            return null;
        }
        return ObjectDataDocument.of(objectData);
    }

    private ObjectDataDocument getMaskData() {
        if (LayoutExt.Add_LAYOUT_TYPE.equals(arg.getLayout_type()) || Objects.isNull(objectData) || ChangeOrderConfig.isReChangeOrderAction(arg.getActionType())) {
            return null;
        }
        IObjectData data = fillMaskFieldValue(controllerContext.getUser(), describe, ObjectDataExt.of(objectData).copy(), true);
        return ObjectDataDocument.of(data);
    }

    private List<LayoutRuleInfo> getLayoutRule(LayoutExt layout) {
        List<LayoutRuleInfo> layoutRuleInfos = infraServiceFacade.findValidLayoutRuleByLayout(controllerContext.getUser(),
                layout.getRefObjectApiName(), layout.getName());
        return layoutRuleInfos;
//        return LayoutRuleExt.handleLayoutRuleByDescribe(layoutRuleInfos, describe);
    }

    private void getDetailObjectLayoutRule(DescribeDetailResult describeDetailResult) {
        List<DetailObjectListResult> detailObjectList = describeDetailResult.getDetailObjectList();
        if (CollectionUtils.notEmpty(detailObjectList)) {
            for (Iterator<DetailObjectListResult> iterator = detailObjectList.iterator(); iterator.hasNext(); ) {
                DetailObjectListResult detailObject = iterator.next();
                Map<String, List<LayoutRuleDocument>> detailLayoutRules = Maps.newHashMap();

                List<String> layoutApiNames = detailObject.getLayoutList().stream().map(x -> (String) x.getDetail_layout().get(Layout.NAME)).collect(Collectors.toList());
                ObjectDescribeExt objectDescribe = ObjectDescribeExt.of(detailObject.getObjectDescribe());
                List<LayoutRuleInfo> layoutRuleInfos = infraServiceFacade.findLayoutRuleByDescribe(controllerContext.getUser(), objectDescribe.getApiName());
//                List<LayoutRuleInfo> layoutRuleInfos = LayoutRuleExt.handleLayoutRuleByDescribe(list, objectDescribe);
                layoutApiNames.forEach(x -> {
                    List<LayoutRuleDocument> layoutRules = layoutRuleInfos.stream()
                            .filter(y -> y.getStatus() == 0)
                            .filter(y -> StringUtils.equals(x, y.getLayoutApiName()))
                            .map(y -> LayoutRuleDocument.of(y))
                            .collect(Collectors.toList());
                    detailLayoutRules.put(x, layoutRules);
                });
                detailObject.getLayoutList().forEach(x ->
                        x.getDetail_layout().put("layout_rule", detailLayoutRules.get((String) x.getDetail_layout().get(Layout.NAME))));
            }
        }
    }

    protected void handleSlaveObjectSupportAdd(DescribeDetailResult describeDetailResult) {
        List<DetailObjectListResult> detailObjectList = describeDetailResult.getDetailObjectList();
        if (CollectionUtils.empty(detailObjectList)) {
            return;
        }
        Map<String, List<IRecordTypeOption>> validRecordTypeListMap = getValidRecordTypeListMap(describeDetailResult);
        for (DetailObjectListResult result : detailObjectList) {
            String detailApiName = (String) result.getObjectDescribe().get(IObjectDescribe.API_NAME);
            List<String> bizTypeValueList = validRecordTypeListMap.getOrDefault(detailApiName, Collections.emptyList()).stream()
                    .map(IRecordTypeOption::getApiName).collect(Collectors.toList());
            Map<String, ConfigPojo> configPojoMap = infraServiceFacade.queryConfigDataIncludeDefault(controllerContext.getTenantId(), detailApiName,
                    BizType.RECORD_TYPE.getValue(), BizConfServiceImpl.getSupportManualAddConfigCode(detailApiName), bizTypeValueList);
            List<RecordTypeLayoutStructure> layoutList = result.getLayoutList();
            layoutList.stream()
                    .filter(it -> configPojoMap.containsKey(it.getRecord_type()))
                    .forEach(it -> {
                        ConfigPojo configPojo = configPojoMap.get(it.getRecord_type());
                        boolean supportManualAdd = Boolean.valueOf(configPojo.getConfigValue());
                        it.setSupport_manual_add(supportManualAdd);
                        if (!supportManualAdd) {
                            WebDetailLayout.of(new Layout(it.getDetail_layout())).removeButtonsByActionCode(Lists.
                                    newArrayList(ObjectAction.SINGLE_CREATE.getActionCode()));
                        }
                    });
        }
    }

    private void handleRecordTypeMatchRelation(DescribeDetailResult describeDetailResult) {
        if (Objects.isNull(describeDetailResult.getObjectDescribe()) || RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_673)) {
            return;
        }
        IObjectDescribe masterDescribe = ObjectDescribeExt.of(describeDetailResult.getObjectDescribe());
        List<DetailObjectListResult> detailObjectList = describeDetailResult.getDetailObjectList();
        if (CollectionUtils.empty(detailObjectList)) {
            return;
        }
        Map<String, List<IRecordTypeOption>> validRecordTypeListMap = getValidRecordTypeListMap(describeDetailResult);
        validRecordTypeListMap = serviceFacade.filterUnMatchRecordTypes(controllerContext.getUser().getTenantId(), validRecordTypeListMap, masterDescribe.getApiName(), arg.getRecordType_apiName());

        for (DetailObjectListResult result : detailObjectList) {
            String detailApiName = (String) result.getObjectDescribe().get(IObjectDescribe.API_NAME);
            List<String> validRecordType = validRecordTypeListMap.getOrDefault(detailApiName, Lists.newArrayList()).stream().map(x -> x.getApiName()).collect(Collectors.toList());
            List<RecordTypeLayoutStructure> layoutList = result.getLayoutList();

            if (StringUtils.equals(LayoutTypes.ADD, arg.getLayout_type())) {
                layoutList.removeIf(x -> !validRecordType.contains(x.getRecord_type()));
            } else if (StringUtils.equals(LayoutTypes.EDIT, arg.getLayout_type())) {
                layoutList.forEach(x -> {
                    if (!validRecordType.contains(x.getRecord_type())) {
                        x.setNot_match(true);
                        WebDetailLayout.of(new Layout(x.getDetail_layout())).removeButtonsByActionCode(Lists.
                                newArrayList(ObjectAction.SINGLE_CREATE.getActionCode(), ObjectAction.BATCH_LOOKUP_CREATE.getActionCode()));
                    } else {
                        x.setNot_match(false);
                    }
                });
            }
        }
    }

    private Map<String, List<IRecordTypeOption>> getValidRecordTypeListMap(DescribeDetailResult describeDetailResult) {
        if (validRecordTypeListMap != null) {
            return validRecordTypeListMap;
        }
        List<DetailObjectListResult> detailObjectList = describeDetailResult.getDetailObjectList();
        if (CollectionUtils.empty(detailObjectList)) {
            return Maps.newHashMap();
        }
        List<String> detailApiNameList = detailObjectList.stream()
                .map(x -> (String) x.getObjectDescribe().get(IObjectDescribe.API_NAME))
                .collect(Collectors.toList());
        validRecordTypeListMap = serviceFacade.findValidRecordTypeListMap(detailApiNameList, controllerContext.getUser());
        return validRecordTypeListMap;
    }

    @Override
    protected void finallyDo() {
        if (result == null) {
            super.finallyDo();
            return;
        }
        //传了include_describe=false，则不返回主和从的describe
        if (!arg.isIncludeDescribe()) {
            result.setObjectDescribe(null);
            if (CollectionUtils.notEmpty(result.getDetailObjectList())) {
                result.getDetailObjectList().forEach(x -> x.setObjectDescribe(null));
            }
        } else {
            result.setObjectDescribe(ObjectDescribeDocument.handleDescribeCache(arg.getDescribeVersionMap(),
                    new ObjectDescribe(result.getObjectDescribe())));
            if (CollectionUtils.notEmpty(result.getDetailObjectList())) {
                result.getDetailObjectList().forEach(x -> x.setObjectDescribe(ObjectDescribeDocument.handleDescribeCache(arg.getDescribeVersionMap(),
                        new ObjectDescribe(x.getObjectDescribe()))));
            }
        }
        //传了include_layout=false，则不返回布局
        if (!arg.isIncludeLayout()) {
            result.setLayout(null);
        }
        fillMaskFieldForAddAction();
        LayoutContext.remove();
        super.finallyDo();
    }

    private void fillMaskFieldForAddAction() {
        if (LayoutExt.Add_LAYOUT_TYPE.equals(arg.getLayout_type()) && Objects.nonNull(result.getObjectData())) {
            fillMaskFieldValue(controllerContext.getUser(), describe, result.getObjectData().toObjectData(), false);
        }
    }

    @Override
    protected final Handler.Arg<A> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        return (Handler.Arg<A>) new DescribeLayoutHandler.Arg();
    }

    @Override
    protected final void processHandlerResult(HandlerContext handlerContext, Handler.Arg<A> handlerArg, Handler.Result<StandardDescribeLayoutController.Result> handlerResult) {
        if (Objects.nonNull(handlerResult.getSkipFunctionPrivilegeCheck())) {
            this.ignoreFunctionPrivilege = handlerResult.getSkipFunctionPrivilegeCheck();
        }
    }

    @Override
    protected final void registerHandlerExecuteFunctions(Map<String, HandlerFunctions.ExecuteFunction> executeFunctionMap) {
        executeFunctionMap.put("defaultDescribeLayoutServiceHandler", () -> this.result = doService(this.arg));
    }
}
