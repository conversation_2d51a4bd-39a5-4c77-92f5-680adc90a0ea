package com.facishare.paas.appframework.core.predef.controller;


import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.domain.ControllerDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.model.plugin.APLControllerPlugin;
import com.facishare.paas.appframework.core.model.plugin.Plugin;
import com.facishare.paas.appframework.core.predef.domain.ListControllerDomainPlugin;
import com.facishare.paas.appframework.core.predef.handler.list.BaseListHandler;
import com.facishare.paas.appframework.core.predef.handler.list.ListHandler;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.CrossObjectFilter;
import com.facishare.paas.appframework.metadata.layout.LayoutContext;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.metadata.layout.component.IComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.api.search.ITagParameter;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by liyiguang on 2019/3/19.
 */
public class AbstractStandardListController<A extends StandardListController.Arg> extends BaseListController<A> {

    private ListComponentExt listComponent;

    private String biSearchQueryInfo;

    /**
     * 跨对象筛选数据总数量，其他场景为空
     */
    private Integer totalNum;

    /**
     * 跨对象筛选原始offset，其他场景为空
     */
    private Integer originalOffset;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.List.getFuncPrivilegeCodes();
    }

    @Override
    protected String getSearchQueryInfo() {
        if (isCrossObjectFilter()) {
            return crossObjectFilter();
        }
        return arg.getSearchQueryInfo();
    }

    @Override
    protected boolean needTagInfo() {
        return arg.isNeedTag() && super.needTagInfo();
    }

    @Override
    protected boolean serializeEmpty() {
        return arg.serializeEmpty();
    }

    @Override
    protected boolean extractExtendInfo() {
        return arg.extractExtendInfo();
    }

    @Override
    protected Optional<ListComponentExt> getListComponent() {
        if (Objects.nonNull(listComponent)) {
            return Optional.of(listComponent);
        }
        if (CollectionUtils.notEmpty(arg.getListComponent())) {
            this.listComponent = ListComponentExt.of(arg.getListComponent());
            return Optional.of(listComponent);
        }
        LayoutExt listLayout = findListLayout();
        Optional<ListComponentExt> listLayoutComponent = ListLayoutExt.of(listLayout).getFirstListComponent();
        listLayoutComponent.ifPresent(it -> listComponent = it);
        return listLayoutComponent;
    }

    @Override
    protected Map<String, String> findRecordTypeMapping() {
        return serviceFacade.getMainRoleRecordLayoutMappingByRecordTypes(controllerContext.getUser(), layout.getName(), objectDescribe.getApiName(),
                layoutApiNames -> listLayouts.stream()
                        .filter(it -> layoutApiNames.contains(it.getName()))
                        .map(it -> ((Layout) it))
                        .collect(Collectors.toMap(ILayout::getName, Function.identity(), (x, y) -> x)));
    }

    private LayoutExt findListLayout() {
        String listLayoutApiName = getListLayoutApiName();
        if (!Strings.isNullOrEmpty(listLayoutApiName)) {
            ILayout listLayout = serviceFacade.getLayoutLogicService().getListLayoutByApiNameWitchComponents(controllerContext.getUser(),
                    listLayoutApiName, objectDescribe, PageType.List, IComponentInfo.PAGE_TYPE_LIST);
            return LayoutExt.of(listLayout);
        }
        // 需要查询默认场景中指定了业务类型，需要查业务类型下的listLayout
        StandardListController.ParamsOfDefaultTemplate params = arg.getParamsOfDefaultTemplate();
        if (Objects.nonNull(params)) {
            String recordType = params.getThirdRecordType();
            if (!Strings.isNullOrEmpty(recordType)) {
                ILayout listLayout = serviceFacade.getLayoutLogicService().getListLayoutWitchComponents(buildLayoutContext(), objectDescribe,
                        PageType.List, IComponentInfo.PAGE_TYPE_LIST, recordType);
                return LayoutExt.of(listLayout);
            }
        }
        ILayout listLayout = serviceFacade.getLayoutLogicService().getListLayoutWitchComponents(buildLayoutContext(), objectDescribe,
                PageType.List, IComponentInfo.PAGE_TYPE_LIST);
        return LayoutExt.of(listLayout);
    }

    protected String getListLayoutApiName() {
        return arg.getListLayoutApiName();
    }

    @Override
    protected String getSearchTemplateId() {
        String searchTemplateId = arg.getSearchTemplateId();
        if (!Strings.isNullOrEmpty(searchTemplateId)) {
            return searchTemplateId;
        }
        return Optional.ofNullable(defaultTemplate)
                .map(ISearchTemplate::getId)
                .orElse(searchTemplateId);
    }

    @Override
    protected ISearchTemplate getDefaultTemplate() {
        // 参数中有apiName，优先通过apiName查找指定场景，没有找到走之前的逻辑
        String templateApiName = arg.getTemplateApiName();
        if (StringUtils.isNotBlank(templateApiName)) {
            List<ISearchTemplate> templates = serviceFacade.findByDescribeApiNameAndExtendAttribute(objectDescribe.getApiName(),
                    arg.getExtendAttribute(), controllerContext.getUser());
            ISearchTemplate template = templates.stream()
                    .filter(it -> Objects.equals(it.getApiName(), templateApiName))
                    .findFirst()
                    .orElse(null);
            if (Objects.nonNull(template)) {
                return template;
            }
        }
        StandardListController.ParamsOfDefaultTemplate params = arg.getParamsOfDefaultTemplate();
        if (Objects.isNull(params)) {
            return null;
        }
        return serviceFacade.findDefaultByDescribeApiName(controllerContext.getUser(), objectDescribe,
                        params.getExtendAttribute(), getListComponent().orElse(null), params.getThirdRecordType())
                .orElse(null);
    }

    /**
     * 判断是否为跨对象筛选
     */
    private boolean isCrossObjectFilter() {
        return CollectionUtils.notEmpty(arg.getBusinessObjects());
    }


    private String crossObjectFilter() {
        if (StringUtils.isNotEmpty(biSearchQueryInfo)) {
            return biSearchQueryInfo;
        }
        try {
            ISearchTemplateQuery query = SearchTemplateQueryExt.fromJsonString(arg.getSearchQueryInfo());
            // 获取当前用户信息
            // 构建查询参数
            CrossObjectFilter.QueryReportArg queryReportArg = CrossObjectFilter.QueryReportArg.builder()
                    .businessObjects(arg.getBusinessObjects())
                    .pageNumber(query.getOffset() / query.getLimit() + 1)  // 首次查询设置为第一页
                    .pageSize(query.getLimit())  // 设置较大的页面大小以获取所有ID
                    .commonFilterList(new Gson().toJson(query.getFilters()))
                    .isPreview("0")
                    .build();
            // 调用BI服务查询数据
            CrossObjectFilter.QueryReportResult queryReportResult = infraServiceFacade.queryReportData(controllerContext.getUser(), queryReportArg);
            Filter filter = new Filter();
            filter.setFieldName(IObjectData.ID);
            filter.setOperator(Operator.IN);
            if (Objects.nonNull(queryReportResult) && CollectionUtils.notEmpty(queryReportResult.getId())) {
                filter.setFieldValues(queryReportResult.getId());
            } else {
                filter.setFieldValues(Lists.newArrayList(""));
            }
            originalOffset = query.getOffset();  // 保存原始offset
            query.setOffset(0);
            query.resetFilters(Lists.newArrayList(filter));
            totalNum = queryReportResult.getTotalNum();
            return biSearchQueryInfo = query.toJsonString();
        } catch (Exception e) {
            log.error("invoke cross object filter error", e);
            throw new ValidateException(I18NExt.text(I18NKey.BI_SERVICE_ERROR));
        }
    }


    @Override
    protected String getSearchTemplateApiName() {
        return arg.getTemplateApiName();
    }

    @Override
    protected String getSearchTemplateType() {
        return arg.getSearchTemplateType();
    }

    @Override
    protected SearchTemplateQuery buildSearchTemplateQuery() {
        SearchTemplateQuery query = super.buildSearchTemplateQuery();
        checkQueryLimit(query);
        setTagParameter(query);     // 设置标签参数
        return query;
    }

    @Override
    protected Query defineQuery() {
        Query query = super.defineQuery();
        checkQueryLimit(query);
        // 设置标签参数
        setTagParameter(query);
        return query;
    }

    /*
     * 设置标签参数
     */
    private void setTagParameter(SearchTemplateQuery searchTemplateQuery) {
        if (StringUtils.isBlank(arg.getTagOperator())) {
            return;
        }
        ITagParameter tagParameter = buildTagParameter();
        searchTemplateQuery.setTagParameter(tagParameter);

    }

    /*
     * 设置标签参数
     */
    private void setTagParameter(Query query) {
        if (StringUtils.isBlank(arg.getTagOperator())) {
            return;
        }
        ITagParameter tagParameter = buildTagParameter();
        query.setTagParameter(tagParameter);

    }

    private ITagParameter buildTagParameter() {
        ITagParameter tagParameter = new TagParameter();
        tagParameter.setTagOperator(TagOperator.valueOf(arg.getTagOperator()));
//         需要将name List转换成ID list
        List<String> tagIdList = arg.getTags();
        tagParameter.setSubTagIdList(tagIdList);
        return tagParameter;
    }

    @Override
    protected void doRender(ILayout layout) {
        getComponentRender(layout).render(arg.isReplaceOptionValue());
    }

    @Override
    protected Map<String, Integer> getDescribeVersionMap() {
        return arg.getDescribeVersionMap();
    }

    @Override
    protected boolean isGetDataOnly() {
        return arg.isGetDataOnly();
    }

    @Override
    protected boolean isIncludeInvalidData() {
        return arg.isIncludeInvalidData();
    }

    @Override
    protected String getUsePageType() {
        return ButtonUsePageType.DataList.getId();
    }

    @Override
    protected boolean isIgnoreSceneFilter() {
        return Objects.equals(arg.getIsIgnoreSceneFilter(), Boolean.TRUE);
    }

    @Override
    protected boolean isIgnoreSceneRecordType() {
        return arg.isIgnoreSceneRecordType();
    }

    @Override
    protected Boolean getFindExplicitTotalNum() {
        if (RequestUtil.isCepRequest()) {
            return Boolean.TRUE.equals(arg.getFindExplicitTotalNum());
        }
        return arg.getFindExplicitTotalNum();
    }

    @Override
    protected Boolean getNeedReturnCountNum() {
        return arg.isNeedReturnCountNum();
    }

    @Override
    protected void initLayoutContext() {
        if (!isGrayListLayout()) {
            return;
        }
        LayoutContext layoutContext = LayoutContext.get();
        layoutContext.setLayoutAgentType(getLayoutAgentType());
    }

    @Override
    protected void finallyDo() {
        try {
            if (Objects.nonNull(result)) {
                // 是否选择替换 option 信息
                if (arg.isReplaceOptionValue()) {
                    // 格式化时间类型 和选项option
                    convertFieldForView(controllerContext.getUser(), objectDescribe.getFieldDescribes(), ObjectDataDocument.ofDataList(result.getDataList()));
                    stopWatch.lap("convertFieldForView");
                }
                if (RequestUtil.isOpenAPIRequest()) {
                    //补充option
                    convertFieldForDisplay(controllerContext.getUser(), objectDescribe, ObjectDataDocument.ofDataList(result.getDataList()));
                    stopWatch.lap("convertFieldForDisplay");
                }
            }
            super.finallyDo();
        } finally {
            LayoutContext.remove();
        }
    }

    private void convertFieldForDisplay(User user, ObjectDescribeExt objectDescribe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<SelectOne> selectOneFields = ObjectDescribeExt.of(objectDescribe).getSelectOneFields();
        for (SelectOne selectOneField : selectOneFields) {
            dataList.forEach(data -> {
                String convertValue = infraServiceFacade.convertData(data, selectOneField, user, true);
                if (StringUtils.isNotBlank(convertValue)) {
                    data.set(FieldDescribeExt.getLookupNameByFieldName(selectOneField.getApiName()), convertValue);
                }
            });
        }
    }

    @Override
    protected Plugin.Arg buildAPLPluginArg(String method) {
        APLControllerPlugin.TriggerInfo triggerInfo = buildTriggerInfo();
        StandardListController.Arg controllerArg = Objects.isNull(arg) ? null : arg.copy2Plugin();
        Result controllerResult = Objects.isNull(result) ? null : result.copy2Plugin();
        return new APLControllerPlugin.Arg(controllerContext.getObjectApiName(), controllerArg, controllerResult, triggerInfo);
    }

    @Override
    protected String getSearchQueryInfoFromAplPlugin(APLControllerPlugin.Result aplPluginResult) {
        StandardListController.Arg controllerArg = aplPluginResult.getControllerArg(StandardListController.Arg.class);
        if (Objects.isNull(controllerArg)) {
            return null;
        }
        return controllerArg.getSearchQueryInfo();
    }

    @Override
    protected void setSearchTemplateQuery2Arg(SearchTemplateQuery query) {
        arg.setSearchQueryInfo(query.toJsonString());
    }

    @Override
    protected boolean isExtractTimeFormatValue() {
        return BooleanUtils.isTrue(arg.getExtractTimeFormatValue());
    }

    @Override
    protected Result buildResult(List<ILayout> layouts, ISearchTemplateQuery query, QueryResult<IObjectData> queryResult) {
        if (isCrossObjectFilter()) {
            queryResult.setTotalNumber(totalNum);
            if (Objects.nonNull(originalOffset)) {
                query.setOffset(originalOffset);  // 恢复原始offset
            }
        }
        Result result = super.buildResult(layouts, query, queryResult);
        if (!arg.includeDescribe) {
            result.setObjectDescribe(null);
        }
        if (!arg.includeLayout) {
            result.setListLayouts(null);
            result.setLayout(null);
        }
        if (!arg.includeButtonInfo) {
            result.setButtonInfo(null);
        }

        return result;
    }


    @Override
    protected boolean needDescribe(ISearchTemplateQuery query) {
        if (!arg.isIncludeDescribe()) {
            return false;
        }
        return super.needDescribe(query);
    }

    @Override
    protected boolean needLayout(ISearchTemplateQuery query) {
        if (!arg.isIncludeLayout()) {
            return false;
        }
        return super.needLayout(query);
    }

    @Override
    protected boolean needRecordTypeMapping() {
        return BooleanUtils.isTrue(arg.getRecordTypeMapping());
    }

    @Override
    protected boolean needButtonInfo() {
        if (!arg.isIncludeButtonInfo()) {
            return false;
        }
        if (AppFrameworkConfig.unSupportButtonInternalObject(controllerContext.getObjectApiName())) {
            return false;
        }
        return super.needButtonInfo();
    }

    @Override
    protected boolean needSearchRichTextExtra() {
        return BooleanUtils.isTrue(arg.getSearchRichTextExtra());
    }

    @Override
    protected List<String> parseProjectFields() {
        return arg.getFieldProjection();
    }

    @Override
    protected final BaseListHandler.Arg<A> buildListHandlerArg() {
        return (BaseListHandler.Arg<A>) new ListHandler.Arg();
    }

    @Override
    protected final void updateSearchQueryInfo(String searchQueryInfo) {
        arg.setSearchQueryInfo(searchQueryInfo);
    }

    @Override
    protected final DomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return ListControllerDomainPlugin.Arg.builder().arg(arg).result(result).build();
    }

    @Override
    protected final void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
        if (pluginResult instanceof ListControllerDomainPlugin.Result) {
            ListControllerDomainPlugin.Result listPluginResult = (ListControllerDomainPlugin.Result) pluginResult;
            if (ControllerDomainPlugin.BEFORE.equals(method) && Objects.nonNull(listPluginResult.getArg())) {
                if (!Strings.isNullOrEmpty(listPluginResult.getArg().getSearchQueryInfo())) {
                    updateSearchQueryInfo(listPluginResult.getArg().getSearchQueryInfo());
                }
            }
            if (ControllerDomainPlugin.AFTER.equals(method) && Objects.nonNull(listPluginResult.getResult())) {
                if (Objects.nonNull(listPluginResult.getResult().getDataList())) {
                    this.result.setDataList(listPluginResult.getResult().getDataList());
                }
            }
        }
    }
}
