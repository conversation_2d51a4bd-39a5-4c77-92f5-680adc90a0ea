package com.facishare.paas.appframework.core.predef.controller;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.OutUserInfo;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerFunctions;
import com.facishare.paas.appframework.core.predef.handler.detail.DetailHandler;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.changeorder.OriginalAndChangeDescribes;
import com.facishare.paas.appframework.metadata.dto.MasterApprovalResult;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.appframework.metadata.layout.LayoutAgentType;
import com.facishare.paas.appframework.metadata.layout.LayoutContext;
import com.facishare.paas.appframework.metadata.layout.LayoutVersion;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.repository.model.GdprCompliance;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.LayoutRuleInfo;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.RequestContext.Biz.ApprovalFlow;
import static com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController.Arg;
import static com.facishare.paas.appframework.core.predef.controller.AbstractStandardDetailController.Result;

/**
 * Created by zhouwr on 2020/7/21
 */
public class AbstractStandardDetailController<A extends Arg> extends PreDefineController<A, Result> {

    protected IObjectDescribe describe;
    protected IObjectData data;
    protected List<IObjectDescribe> related = Lists.newArrayList();
    protected List<RelatedObjectDescribeStructure> relatedObjects = Lists.newArrayList();
    protected List<RelatedObjectDescribeStructure> detailObjects = Lists.newArrayList();
    private Set<String> unauthorizedFields;
    protected Boolean supportTag;
    protected Boolean hasTagData;
    protected Boolean isSupportGdpr;
    protected List<LayoutRuleInfo> layoutRuleInfoList;
    private Map<String, Object> snapshot;

    private boolean dataNotFound = false;

    private ObjectDescribeDocument describeExt;

    private boolean skipDataPrivilegeCheck;
    protected boolean defaultEnableQixinGroup;

    protected boolean defaultEnableQixinGroup() {
        return infraServiceFacade.isEnableQixinGroup(controllerContext.getUser(), arg.getObjectDescribeApiName());
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(controllerContext.getUser(), controllerContext.getAppId());
    }

    @Override
    protected void doInitBefore() {
        //校验数据快照的参数
        if (arg.mergeSnapshot() && (Objects.isNull(arg.getBizInfo()) || Strings.isNullOrEmpty(arg.getBizInfo().getOtherBizId()))) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }

        //获取对象描述
        describe = findObject(arg);
        stopWatch.lap("findObject");

        if (!arg.serializeEmpty() && !ObjectDescribeExt.of(describe).isChangeOrderObject()) {
            RequestUtil.setSerializeEmptyFalse();
        }
        if (Objects.nonNull(arg.getBizInfo())) {
            controllerContext.setBizInfo(arg.getBizInfo());
        }


        //查询相关对象
        if (arg.includeLayout()) {
            related = findRelatedObjects();
            stopWatch.lap("findRelatedObjects");
        }

        //初始化context的缓存，可能包括功能权限、数据权限、当前用户是否CRM管理员
        initContextCache();
        stopWatch.lap("initContextCache");

        //根据List权限过滤相关对象
        filterRelatedObjects();
        stopWatch.lap("filterRelatedObjects");
    }

    @Override
    protected void doInit() {
        //查询objectData
        data = findObjectData(arg);
        stopWatch.lap("findObjectData");

        initLayoutContext();
        stopWatch.lap("initLayoutContext");

        initRequestContext();
        //查询数据快照，供审批流使用
        if (RequestUtil.isUseSnapshotForApproval() || arg.mergeSnapshot()) {
            snapshot = findSnapshot();
            //合并快照，供审批中编辑数据使用
            if (arg.mergeSnapshot()) {
                ObjectDataExt.of(data).putAll(snapshot);
            }
        }
        skipDataPrivilegeCheck = skipDataPrivilegeCheck();
        stopWatch.lap("skipDataPrivilegeCheck");

        defaultEnableQixinGroup = defaultEnableQixinGroup();
        stopWatch.lap("defaultEnableQixinGroup");
    }

    @Override
    protected void before(A arg) {
        checkParams(arg);
        super.before(arg);
        //检查数据权限
        checkDataPrivilege();
    }

    private void checkParams(A arg) {
        // 仅在灰度开关开启时校验
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DETAIL_CHECK_OBJECT_API_NAME_GRAY, controllerContext.getTenantId())) {
            return;
        }
        // arg 为空直接返回
        if (Objects.isNull(arg)) {
            return;
        }
        // objectDescribeApiName 与上下文不一致时，阻断请求
        if (!Objects.equals(arg.getObjectDescribeApiName(), controllerContext.getObjectApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
    }

    protected boolean skipDataPrivilegeCheck() {
        return false;
    }

    private void checkDataPrivilege() {
        if (!dataNotFound && !skipDataPrivilegeCheck) {
            doDataPrivilegeCheck(arg);
            stopWatch.lap("doDataPrivilegeCheck");
        }
    }

    protected void initRequestContext() {
    }

    private boolean isDetailObjectAndMergeSnapshot() {
        return ObjectDescribeExt.of(describe).isSlaveObjectCreateWithMasterAndInGrayList() && arg.mergeSnapshot();
    }

    private Map<String, Object> findSnapshot() {
        if (isDetailObjectAndMergeSnapshot()) {
            String masterApiName = ObjectDescribeExt.of(describe).getMasterAPIName()
                    .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
            String masterDataId = (String) data.get(ObjectDescribeExt.of(describe).getMasterDetailField()
                    .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR))).getApiName());
            ObjectDataSnapshot dataSnapshot = infraServiceFacade.findAndMergeSnapshot(controllerContext.getTenantId(), masterApiName,
                    masterDataId, ApprovalFlow.getCode(), RequestUtil.getOtherBizId());
            stopWatch.lap("findAndMergeSnapshot");
            return dataSnapshot.getDetailSnapshotById(arg.getObjectDescribeApiName(), arg.getObjectDataId());
        }

        ObjectDataSnapshot dataSnapshot = infraServiceFacade.findAndMergeSnapshot(controllerContext.getTenantId(), arg.getObjectDescribeApiName(),
                arg.getObjectDataId(), ApprovalFlow.getCode(), RequestUtil.getOtherBizId());
        stopWatch.lap("findAndMergeSnapshot");

        if (CollectionUtils.empty(dataSnapshot.getMasterSnapshot()) && CollectionUtils.empty(dataSnapshot.getDetailSnapshot())) {
            return Maps.newHashMap();
        }

        //计算变更之后的统计字段和计算字段
        IObjectData snapshotData = serviceFacade.calculateForSnapshot(controllerContext.getUser(), describe, data, dataSnapshot);
        stopWatch.lap("calculateForSnapshot");
        //跟数据库里的数据做一次diff，diff结果供审批流展示
        Map<String, Object> diffMap = ObjectDataExt.of(data).diff(snapshotData, describe);

        if (!arg.mergeSnapshot()) {
            ObjectDataExt diffData = ObjectDataExt.of(diffMap);
            fillExtendFieldInfo(diffData.getObjectData());
            stopWatch.lap("snapshot-fillExtendFieldInfo");
        }
        return diffMap;
    }

    private void initLayoutContext() {
        LayoutContext layoutContext = LayoutContext.get();
        layoutContext.setLayoutVersion(LayoutVersion.of(arg.getLayoutVersion()));
        layoutContext.setLayoutAgentType(LayoutAgentType.of(arg.getLayoutAgentType()));
    }

    private void initContextCache() {
        ContextCacheUtil.openContextCache();
        //-10000系统用户不需要初始化
        if (!controllerContext.getUser().isSupperAdmin()) {
            //功能权限
            serviceFacade.queryAndCacheFuncPrivilege(controllerContext.getUser(), controllerContext.getObjectApiName(), getObjectActionCodeMap());
            stopWatch.lap("queryAndCacheFuncPrivilege");
        }
    }

    //业务方自定义的需要校验功能权限的code
    protected Map<String, Set<String>> getCustomObjectActionCodeMap() {
        return Maps.newHashMap();
    }

    //需要初始化的功能权限列表
    private Map<String, Set<String>> getObjectActionCodeMap() {
        Map<String, Set<String>> codeMap = Maps.newHashMap();

        //相关对象
        related.forEach(relatedDescribe -> {
            Set<String> actions = Sets.newHashSet(ObjectAction.VIEW_LIST.getActionCode());
            if (!arg.isFromRecycleBin()) {
                actions.addAll(ComponentActions.RELATED_OBJECT.getActionCodes());
            }
            codeMap.put(relatedDescribe.getApiName(), actions);
        });

        //业务方自定义的需要校验功能权限的code
        Map<String, Set<String>> customObjectActionCodeMap = getCustomObjectActionCodeMap();
        if (CollectionUtils.notEmpty(customObjectActionCodeMap)) {
            customObjectActionCodeMap.forEach((k, v) -> {
                codeMap.putIfAbsent(k, Sets.newHashSet());
                codeMap.get(k).addAll(v);
            });
        }

        return codeMap;
    }

    protected void doDataPrivilegeCheck(A arg) {
        serviceFacade.doDataPrivilegeCheck(controllerContext.getUser(),
                Lists.newArrayList(data),
                describe,
                ObjectAction.VIEW_DETAIL.getActionCode());
    }

    protected IObjectDescribe findObject(A arg) {
        //灰度企业不拷贝对象描述
        if (AppFrameworkConfig.notCopyDescribeInController(controllerContext.getTenantId(), arg.getObjectDescribeApiName())) {
            return serviceFacade.findObjectWithoutCopy(controllerContext.getTenantId(), arg.getObjectDescribeApiName());
        }
        return serviceFacade.findObjectIncludeMultiField(controllerContext.getTenantId(), arg.getObjectDescribeApiName());
    }

    protected List<IObjectDescribe> findRelatedObjects() {
        return serviceFacade.findRelatedDescribes(controllerContext.getTenantId(),
                arg.getObjectDescribeApiName());
    }

    private void filterRelatedObjects() {
        related = serviceFacade.filterDescribesWithActionCode(controllerContext.getUser(), related, ObjectAction.VIEW_LIST.getActionCode());
        relatedObjects = getRelatedDescribes(related);
        detailObjects = getDetailDescribes(related);
    }

    protected IObjectData findObjectData(A arg) {
        if (skipRelevantTeam()) {
            controllerContext.setAttribute(RequestContext.Attributes.SKIP_RELEVANT_TEAM, true);
        }
        if (arg.isFromRecycleBin()) {
            //回收站查询已作废数据的详情
            return serviceFacade.getObjectDataIncludeDeleted(buildActionContext(), arg.getObjectDataId(),
                    arg.getObjectDescribeApiName());
        }
        try {
            return findObjectDataWithChangeOrder(arg);
        } catch (ObjectDataNotFoundException e) {
            dataNotFound = true;
            //审批中新建的从对象只有快照，这里返回空数据，后边后快照合并
            if (isDetailObjectAndMergeSnapshot()) {
                if (Strings.isNullOrEmpty(arg.getMasterDataId())) {
                    throw e;
                }
                IObjectData detailData = new ObjectData();
                detailData.setTenantId(controllerContext.getTenantId());
                detailData.setDescribeApiName(describe.getApiName());
                detailData.setId(arg.getObjectDataId());
                detailData.set(ObjectDescribeExt.of(describe).getMasterDetailField()
                        .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR))).getApiName(), arg.getMasterDataId());
                return detailData;
            }
            checkMasterApprovalResult();
            throw e;
        }
    }

    private IObjectData findObjectDataWithChangeOrder(A arg) {
        IObjectData objectData = serviceFacade.findObjectData(buildActionContext(), arg.getObjectDataId(), describe);
        infraServiceFacade.findAndMergeObjectDataWithOriginalData(controllerContext.getUser(), describe, Lists.newArrayList(objectData));
        return objectData;
    }

    private boolean skipRelevantTeam() {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DETAIL_SKIP_RELEVANT_TEAM_GRAY, controllerContext.getTenantId())
                && Boolean.FALSE.equals(arg.getIncludeTeamMember());
    }

    private void checkMasterApprovalResult() {
        //只给终端老版本返回异常提示
        if (!RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_680)) {
            return;
        }
        if (!ObjectDescribeExt.of(describe).isSlaveObjectCreateWithMasterAndInGrayList()) {
            return;
        }

        MasterApprovalResult approvalResult = serviceFacade.getMasterDataApprovalResult(controllerContext.getTenantId(),
                describe.getApiName(), arg.getObjectDataId());
        if (Objects.isNull(approvalResult)) {
            return;
        }
        String errMsg = I18N.text(I18NKey.DETAIL_HINT_FOR_LOW_MOBILE_WHEN_TRIGGER_MASTER_APPROVAL,
                approvalResult.getMasterDisplayName(), ObjectDataExt.of(approvalResult.getMasterData()).getName());
        throw new ValidateException(errMsg);
    }

    //告诉元数据不要处理引用字段
    private IActionContext buildActionContext() {
        IActionContext context = ActionContextExt.of(controllerContext.getUser()).disableDeepQuote().getContext();
        ActionContextExt actionContextExt = ActionContextExt.of(context);
        actionContextExt.setSkipRelevantTeam(RequestUtil.skipRelevantTeam());
        actionContextExt.setKeepAllMultiLangValue(keepAllMultiLangValue());
        return context;
    }

    @Override
    protected StandardDetailController.Result doService(A arg) {
        //实时计算落地的计算字段
        if (arg.calculateFormula()) {
            //实时计算所有统计字段
            calculateCountFields();
            calculateFormulaFields();
            stopWatch.lap("calculateFormulaFields");
        }

        boolean isMasterObject = false;
        removeGdprField(data);
        stopWatch.lap("removeGdprField");
        if (!arg.simpleData()) {
            //相关团队排序
            processRelevantTeamMember();
            stopWatch.lap("processRelevantTeamMember");

            // 补充外部相关团队信息
            handleOutTeamMemberInfo(data);
            stopWatch.lap("fillOutTeamMemberInfo");

            //补充字段扩展信息
            fillExtendFieldInfo(data);

            //设置是否包含子对象，基于pg的元数据中有对应方法
            isMasterObject = serviceFacade.isMasterObject(
                    controllerContext.getTenantId(), arg.getObjectDescribeApiName());
            stopWatch.lap("isMasterObject");

            supportTag = !describe.isBigObject() && infraServiceFacade.isSupportTag(arg.getObjectDescribeApiName(), controllerContext.getUser());
            stopWatch.lap("checkIfSupportTag");

            if (BooleanUtils.isTrue(supportTag)) {
                hasTagData = hasAnyTagData();
                stopWatch.lap("hasAnyTagData");
            }

            isSupportGdpr = infraServiceFacade.findGdprComplianceStatusByCache(controllerContext.getUser(), describe.getApiName());
            stopWatch.lap("checkIfSupportGdpr");
        }
        //查layout，包括关联对象对应的component，相关对象的layout由pg元数据提供接口
        ILayout layout = new Layout();
        if (arg.includeLayout()) {
            layout = getLayout();

            //加工布局
            processLayout(layout);
            stopWatch.lap("processLayout");

            layoutRuleInfoList = getLayoutRuleList(layout);
            stopWatch.lap("getLayoutRuleList");
        }

        if (BooleanUtils.isTrue(arg.getIncludeStatistics())) {
            //如果是查描述不拷贝的灰度企业，则先拷贝一下，因为computeCalculateRelation方法会修改描述
            if (AppFrameworkConfig.notCopyDescribeInController(controllerContext.getTenantId(), arg.getObjectDescribeApiName())) {
                describe = ObjectDescribeExt.of(describe).copyOnWrite();
            }
            infraServiceFacade.computeCalculateRelation(describe, null);
            stopWatch.lap("computeCalculateRelation");
        }

        describeExt = buildDescribeExt();
        stopWatch.lap("buildDescribeExt");

        return buildResult(arg, isMasterObject, layout);
    }

    private void calculateCountFields() {
        if (AppFrameworkConfig.isGrayCalculateCountWhenFindData(controllerContext.getTenantId(), describe.getApiName())) {
            List<Count> countList = ObjectDescribeExt.of(describe).getCountFields();
            serviceFacade.calculateCountField(Lists.newArrayList(data), describe, countList);
            stopWatch.lap("calculateCountField");
        }
    }

    private void calculateFormulaFields() {
        List<IFieldDescribe> formulas = ObjectDescribeExt.of(describe).getFormulaFields().stream()
                .filter(x -> FieldDescribeExt.of(x).isIndex())
                .collect(Collectors.toList());
        infraServiceFacade.bulkCalculate(describe, Lists.newArrayList(data), formulas);
    }

    private void processLayout(ILayout layout) {
        //开启了新建编辑页布局时，详情页布局的字段忽略必填和只读属性
        if (serviceFacade.getLayoutLogicService().isEditLayoutEnable(buildLayoutContext(), controllerContext.getObjectApiName(), true)) {
            LayoutExt.of(layout).getFormComponents().forEach(formComponentExt -> {
                formComponentExt.getFormFields().forEach(formField -> {
                    if (FormFieldExt.of(formField).required()) {
                        formField.setRequired(false);
                    }
                    if (FormFieldExt.of(formField).readOnly()) {
                        formField.setReadOnly(false);
                    }
                });
            });
        }
        //根据插件配置过滤布局中的字段
        serviceFacade.getLayoutLogicService().processLayoutByDomainPlugin(controllerContext.getTenantId(), describe, layout, getRecordTypes(),
                PageType.Detail.name());
    }

    @Override
    protected List<String> getRecordTypes() {
        return Lists.newArrayList(data.getRecordType());
    }

    private void removeGdprField(IObjectData data) {
        if (!RequestUtil.isOpenAPIRequest()) {
            return;
        }
        List<GdprCompliance> personalField = infraServiceFacade.findGdprCompliance(controllerContext.getUser(), describe.getApiName());
        if (CollectionUtils.notEmpty(personalField)) {
            Set<String> needFilterPersonalField = personalField.stream()
                    .filter(x -> Objects.equals(x.getApiName(), describe.getApiName()))
                    .filter(x -> x.isOpenStatus())
                    .filter(x -> x.getForbidExport().contains(GdprCompliance.ORDINARY_AND_SENSITIVE)
                            || x.getForbidExport().contains(GdprCompliance.SENSITIVE))
                    .map(x -> {
                        if (CollectionUtils.notEmpty(x.getOrdinaryFields())) {
                            return x.getOrdinaryFields();
                        }
                        if (CollectionUtils.notEmpty(x.getSensitiveFields())) {
                            return x.getSensitiveFields();
                        }
                        return new ArrayList<String>();
                    })
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());
            ObjectDataExt.of(data).remove(needFilterPersonalField);
        }
    }

    private boolean hasAnyTagData() {
        return infraServiceFacade.hasAnyTagData(describe.getApiName(), controllerContext.getUser());
    }

    protected StandardDetailController.Result buildResult(StandardDetailController.Arg arg, boolean isMasterObject, ILayout layout) {
        StandardDetailController.Result result = Result.builder()
                .data(ObjectDataDocument.of(data))
                .describe(arg.includeDescribe() ? ObjectDescribeDocument.handleDescribeCache(arg.getDescribeVersionMap(), describe) : null)
                .isHaveDetailObj(isMasterObject)
                .objectDescribeExt(describeExt)
                .isInApprovalWhiteList(AppFrameworkConfig.isInMasterDetailApprovalWhiteList(controllerContext.getTenantId()))
                .supportTag(supportTag)
                .hasTagData(hasTagData)
                .snapshot(RequestUtil.isUseSnapshotForApproval() ? ObjectDataDocument.of(snapshot) : null)
                .isSupportGdpr(isSupportGdpr)
                .supportButtonMix(UdobjGrayConfig.isAllow(UdobjGrayConfigKey.MOBILE_BUTTON_MIX_GRAY, controllerContext.getTenantId()))
                .build();
        if (arg.includeLayout()) {
            result.setLayout(LayoutDocument.of(layout).configLayoutRules(LayoutRuleDocument.ofList(layoutRuleInfoList)));
        }
        if (CollectionUtils.notEmpty(arg.getDetailsQuery())) {
            Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();
            for (DetailsQuery detailsQuery : arg.getDetailsQuery()) {
                String detailApi = detailsQuery.getObjApi();
                ControllerContext context = ContextManager.buildControllerContext(detailApi, "RelatedList");
                StandardRelatedListController.Arg body = new StandardRelatedListController.Arg();
                body.setObjectApiName(detailApi);
                body.setSearchQueryInfo(detailsQuery.getQueryInfo());
                body.setTargetObjectApiName(describe.getApiName());
                body.setRelatedFieldName(detailsQuery.getRelatedListName());
                body.setTargetObjectDataId(arg.getObjectDataId());
                body.setIncludeAssociated(true);
                body.setIncludeDescribe(false);
                body.setIncludeLayout(false);

                BaseListController.Result detailRes = serviceFacade.triggerController(context, body, BaseListController.Result.class);
                details.put(detailApi, detailRes.getDataList());
            }
            result.setDetails(details);
        }
        return result;
    }

    protected ObjectDescribeDocument buildDescribeExt() {
        DescribeExtra describeExtra = serviceFacade.findDescribeExtraByRenderType(controllerContext.getUser(), describe,
                Collections.emptyList(), DescribeExpansionRender.RenderType.Detail, true, null, data);
        return ObjectDescribeDocument.of(describeExtra);
    }

    private void fillExtendFieldInfo(IObjectData data) {
        IObjectDescribe objectDescribe = getObjectDescribe();
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DETAIL_ASYNC_FILL_FIELD_INFO_GRAY, controllerContext.getTenantId())) {
            asyncFillFieldInfo(data, objectDescribe);
            return;
        }
        //补充引用字段数据(包括负责人所在部门)
        fillQuoteFieldValue(data, objectDescribe);
        stopWatch.lap("fillQuoteFieldValue");
        //补充关联对象的名称
        fillRefObjectName(data, objectDescribe);
        stopWatch.lap("fillRefObjectName");
        fillOutUserInfo(data, objectDescribe);
        stopWatch.lap("fillOutUserInfo");

        fillCountryAreaLabel(data, objectDescribe);
        stopWatch.lap("fillCountryAreaLabel");
        fillMaskFieldValue(data, objectDescribe);
        stopWatch.lap("fillMaskFieldValue");
        fillDimensionFieldValue(data, objectDescribe);
        stopWatch.lap("fillDimensionFieldValue");

        //对于灰度企业，只有cep请求才查询手机归属地
        if (RequestUtil.isCepRequest() || !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.QUERY_PHONE_INFORMATION_ONLY_CEP, controllerContext.getTenantId())) {
            serviceFacade.fillPhoneNumberInformation(objectDescribe, data);
            stopWatch.lap("fillPhoneNumberInformation");
        }

        serviceFacade.fillCurrencyFieldInfo(objectDescribe, Lists.newArrayList(data), controllerContext.getUser());
        stopWatch.lap("fillCurrencyFieldInfo");

        serviceFacade.fillDataVisibilityRange(controllerContext.getUser(), objectDescribe, Lists.newArrayList(data));
        stopWatch.lap("fillDataVisibilityRange");

        serviceFacade.fillCountWithMultiRegion(controllerContext.getUser(), objectDescribe, Lists.newArrayList(data));
        stopWatch.lap("fillCountWithMultiRegion");

    }

    private void asyncFillFieldInfo(IObjectData objectData, IObjectDescribe objectDescribe) {
        IObjectData data = ObjectDataExt.synchronize(objectData);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            //补充引用字段数据(包括负责人所在部门)
            fillQuoteFieldValue(data, objectDescribe);
            fillMaskFieldValue(data, objectDescribe);
        }).submit(() -> {
            //补充关联对象的名称
            fillRefObjectName(data, objectDescribe);
        }).submit(() -> {
            fillOutUserInfo(data, objectDescribe);
        }).submit(() -> {
            fillCountryAreaLabel(data, objectDescribe);
        }).submit(() -> {
            fillDimensionFieldValue(data, objectDescribe);
        }).submit(() -> {
            serviceFacade.fillPhoneNumberInformation(objectDescribe, data);
        }).submit(() -> {
            serviceFacade.fillCurrencyFieldInfo(objectDescribe, Lists.newArrayList(data), controllerContext.getUser());
        }).submit(() -> {
            serviceFacade.fillDataVisibilityRange(controllerContext.getUser(), objectDescribe, Lists.newArrayList(data));
        });
        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Error in fill info of webDetailController, ei:{}, object:{}", controllerContext.getTenantId(), describe.getApiName(), e);
        }
    }

    /**
     * 加工描述
     * 需要将不存在的原单字段也加到临时描述中
     */
    private IObjectDescribe getObjectDescribe() {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (!describeExt.isChangeOrderObject()) {
            return describe;
        }
        IObjectDescribe originalDescribe = serviceFacade.findObject(controllerContext.getTenantId(), describe.getOriginalDescribeApiName());
        Map<String, IFieldDescribe> changeFieldMapping = OriginalAndChangeDescribes.buildByDescribe(originalDescribe, describe)
                .getChangeFieldMapping(originalDescribe.getApiName(), describe.getApiName());

        IObjectDescribe describe = describeExt.copy();
        changeFieldMapping.forEach((apiName, field) -> {
            String originalFieldName = FieldDescribeExt.getOriginalFieldName(apiName);
            if (!describe.containsField(originalFieldName)) {
                field.setApiName(originalFieldName);
                describe.addFieldDescribe(field);
            }
        });
        return describe;
    }

    protected void fillDimensionFieldValue(IObjectData data, IObjectDescribe describe) {
        if (data == null) {
            return;
        }
        serviceFacade.fillDimensionFieldValue(controllerContext.getUser(), describe, Lists.newArrayList(data));
    }

    protected void fillCountryAreaLabel(IObjectData data, IObjectDescribe describe) {
        if (data == null) {
            return;
        }
        serviceFacade.fillCountryAreaLabel(describe, Lists.newArrayList(data), controllerContext.getUser());
    }

    protected void fillMaskFieldValue(IObjectData data, IObjectDescribe describe) {
        if (AppFrameworkConfig.maskFieldEncryptGray(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
            if (AppFrameworkConfig.maskFieldEncryptObjectPagesGray(controllerContext.getTenantId(), controllerContext.getObjectApiName())) {
                return;
            }
            if (arg.find2Edit()) {
                MaskFieldLogicService maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
                Map<String, IObjectDescribe> describeMap = ImmutableMap.of(describe.getApiName(), describe);
                maskFieldLogicService.processMaskFieldValue(controllerContext.getUser(), data, Collections.emptyMap(), describeMap,
                        MaskFieldLogicService.MaskFieldConfig.createByRemoveMaskOrigValue(needRemoveMaskOrigValue()));
                return;
            }
        }
        serviceFacade.fillMaskFieldValue(controllerContext.getUser(), Lists.newArrayList(data), describe, needRemoveMaskOrigValue());
    }

    protected boolean needRemoveMaskOrigValue() {
        return RequestUtil.isCepRequest();
    }

    private List<LayoutRuleInfo> getLayoutRuleList(ILayout layout) {
        List<LayoutRuleInfo> layoutRules = infraServiceFacade.findValidLayoutRuleByLayout(controllerContext.getUser(),
                layout.getRefObjectApiName(), layout.getName());
        return layoutRules;
//        return LayoutRuleExt.handleLayoutRuleByDescribe(layoutRules, describe);
    }

    protected void removeHelpTextForSysField() {
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);
        objectDescribeExt.removeHelpTextForSysField();
    }


    protected void processRelevantTeamMember() {
        ObjectDataExt dataExt = ObjectDataExt.of(data);
        dataExt.sortRelevantTeamMemberOfObjectData();
    }

    protected void fillRefObjectName(IObjectData data, IObjectDescribe describe) {
        serviceFacade.fillObjectDataWithRefObject(describe, Lists.newArrayList(data), controllerContext.getUser(),
                null, false);
    }

    protected void fillOutUserInfo(IObjectData data, IObjectDescribe describe) {
        serviceFacade.fillUserInfo(describe, Lists.newArrayList(data), controllerContext.getUser());
        serviceFacade.fillDepartmentInfo(describe, Lists.newArrayList(data), controllerContext.getUser());
        serviceFacade.fillImageInformation(describe, Lists.newArrayList(data), controllerContext.getUser());
        serviceFacade.fillRichTextImageInfo(describe, Lists.newArrayList(data), controllerContext.getUser());
    }

    protected void fillQuoteFieldValue(IObjectData data, IObjectDescribe describe) {
        infraServiceFacade.fillQuoteFieldValue(controllerContext.getUser(), Lists.newArrayList(data), describe,
                null, false);
    }

    protected ILayout getLayout() {
        ILayout layout;
        if (!Strings.isNullOrEmpty(arg.getLayoutApiName())) {
            layout = serviceFacade.getLayoutLogicService().getLayoutByApiNameWithComponents(controllerContext.getUser(), arg.getLayoutApiName(),
                    describe, data);
            stopWatch.lap("getLayoutByApiNameWithComponents");
            return layout;
        }
        layout = serviceFacade.getLayoutLogicService().getLayoutWithComponents(buildLayoutContext(), data.getRecordType(),
                describe, data, relatedObjects, detailObjects, getUnauthorizedFields(), PageType.Detail);
        stopWatch.lap("getLayoutWithComponents");

        //回收站详情页不展示自定义按钮
        if (arg.isFromRecycleBin()) {
            return layout;
        }

        if (defaultEnableQixinGroup) {
            LayoutExt.of(layout).addTopInfoComponentButton(ButtonExt.generateQinxinGroupButton());
        }
        stopWatch.lap("generateQixinGroupButton");

        return layout;
    }

    protected List<RelatedObjectDescribeStructure> getRelatedDescribes(List<IObjectDescribe> related) {
        return ObjectDescribeExt.of(describe).getRelatedObjectDescribeStructures(related);
    }

    protected List<RelatedObjectDescribeStructure> getDetailDescribes(List<IObjectDescribe> related) {
        return ObjectDescribeExt.of(describe).getDetailObjectDescribeStructures(related);
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardController.Detail.getFuncPrivilegeCodes();
    }

    protected Set<String> getUnauthorizedFields() {
        if (unauthorizedFields == null) {
            Set<String> fields = serviceFacade.getUnauthorizedFields(controllerContext.getUser(), describe.getApiName());
            return unauthorizedFields = CollectionUtils.empty(fields) ? Collections.emptySet() : fields;
        }
        return unauthorizedFields;
    }

    @Override
    protected final void finallyDo() {
        try {
            if (Objects.nonNull(data)) {
                formatData();
                filterUnauthorizedFields();
            }
            super.finallyDo();
        } finally {
            LayoutContext.remove();
        }
    }

    private void formatData() {
        if (!Boolean.TRUE.equals(arg.getFormatData())) {
            return;
        }
        ObjectDataFormatter.builder().describe(describe).dataList(Lists.newArrayList(data)).build().format();
        stopWatch.lap("formatData");
    }

    private void filterUnauthorizedFields() {
        if (controllerContext.getUser().isSupperAdmin()) {
            return;
        }
        ObjectDataExt.filterUnauthorizedFieldsByDataList(Lists.newArrayList(data), getUnauthorizedFields(), describe);
        stopWatch.lap("filterUnauthorizedFields");
    }

    // 补充外部相关团队信息
    private void handleOutTeamMemberInfo(IObjectData data) {
        ObjectDataExt dataExt = ObjectDataExt.of(data);
        /* 先将这段代码屏蔽掉 bugfix#1226869
        if (!dataExt.getOutOwnerId().isPresent()) {
            // 过滤掉外部成员
            filterOutTeamMember(dataExt);
            return;
        }
        */
        List<TeamMemberInfoPoJo> relevantTeamFromObjectData = dataExt.getRelevantTeamFromObjectData();
        // 下游详情页，不展示相关团队中，其他企业的人员信息
        if (controllerContext.getUser().isOutUser()) {
            relevantTeamFromObjectData.removeIf(it -> !Objects.equals(it.getOutTenantId(), controllerContext.getUser().getOutTenantId()));
        }
        Set<String> employeeIds = relevantTeamFromObjectData.stream().filter(TeamMemberInfoPoJo::isOutTeamMember)
                .filter(x -> TeamMember.MemberType.EMPLOYEE.getValue().equals(x.getTeamMemberType()))
                .flatMap(t -> t.getTeamMemberEmployee().stream())
                .filter(NumberUtils::isDigits)
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(employeeIds)) {
            return;
        }
        List<OutUserInfo> outUserInfos = serviceFacade.batchGetOutUsers(controllerContext.getTenantId(), employeeIds);
        relevantTeamFromObjectData.stream().filter(TeamMemberInfoPoJo::isOutTeamMember).forEach(member -> {
            for (OutUserInfo outUserInfo : outUserInfos) {
                if (outUserInfo.getId().equals(member.getTeamMemberEmployee().get(0))) {
                    member.setOutUserName(outUserInfo.getName());
                    member.setOutUserProfile(outUserInfo.getProfile());
                }
            }
        });
        // 内部相关团队在前，外部相关团队在后
        List<TeamMemberInfoPoJo> innerMembers = relevantTeamFromObjectData.stream().filter(t -> !t.isOutTeamMember())
                .collect(Collectors.toList());
        List<TeamMemberInfoPoJo> outMembers = relevantTeamFromObjectData.stream().filter(TeamMemberInfoPoJo::isOutTeamMember)
                .collect(Collectors.toList());
        innerMembers.addAll(outMembers);
        dataExt.setRelevantTeam(innerMembers);
    }

    /*
     * 过滤掉外部人员
     */
    private void filterOutTeamMember(ObjectDataExt data) {
        List<TeamMember> teamMembers = data.getTeamMembers();
        teamMembers.removeIf(TeamMember::isOutMember);
        data.setTeamMembers(teamMembers);
    }

    protected boolean keepAllMultiLangValue() {
        return BooleanUtils.isTrue(arg.getKeepAllMultiLangValue());
    }

    @Override
    protected final Handler.Arg<A> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        DetailHandler.Arg handlerArg = new DetailHandler.Arg();
        handlerArg.setObjectData(ObjectDataDocument.of(data));
        return (Handler.Arg<A>) handlerArg;
    }

    @Override
    protected final void processHandlerResult(HandlerContext handlerContext, Handler.Arg<A> handlerArg, Handler.Result<Result> handlerResult) {
        if (handlerResult instanceof DetailHandler.Result) {
            DetailHandler.Result detailResult = (DetailHandler.Result) handlerResult;
            if (Objects.nonNull(detailResult.getDefaultEnableQixinGroup())) {
                this.defaultEnableQixinGroup = detailResult.getDefaultEnableQixinGroup();
            }
            if (Objects.nonNull(detailResult.getSkipDataPrivilegeCheck())) {
                this.skipDataPrivilegeCheck = detailResult.getSkipDataPrivilegeCheck();
            }
            if (Objects.nonNull(detailResult.getObjectData())) {
                this.data = detailResult.getObjectData().toObjectData();
            }
        }
    }

    @Override
    protected final void registerHandlerExecuteFunctions(Map<String, HandlerFunctions.ExecuteFunction> executeFunctionMap) {
        executeFunctionMap.put("defaultDetailDataPrivilegeCheckHandler", this::checkDataPrivilege);
        executeFunctionMap.put("defaultDetailServiceHandler", () -> this.result = doService(this.arg));
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        @JSONField(name = "M1")
        private String objectDataId;

        //兼容数据格式
        @JSONField(name = "M2")
        private String objectDescribeApiName;

        //是否回收站的详情页请求
        private boolean fromRecycleBin;

        //返回值是否包含组织机构信息
        private Boolean includeOrgInfo = false;

        //返回值是否包含layout
        private Boolean includeLayout = true;
        //返回数据为最简单的数据
        private Boolean isSimpleData = false;

        @JSONField(name = "M3")
        private Map<String, Integer> describeVersionMap;

        //返回描述中是否包含计算统计信息
        private Boolean includeStatistics = false;

        private Boolean includeDescribe = true;

        //是否格式化数据（主要是数值类型和日期时间类型的格式化，目前只有审批流需要）
        private Boolean formatData = false;

        private String layoutType;
        //布局适用端类型(mobile-移动端；web-网页端；sidebar-侧边栏布局，不传默认web)
        private String layoutAgentType;
        //布局版本(v3-V3版本；v2-V2版本，不传默认v3)
        private String layoutVersion;

        //是否序列化返回对象中的空值
        private Boolean serializeEmpty;

        //布局apiName（流程布局使用）
        private String layoutApiName;

        //是否合并快照
        private Boolean mergeSnapshot;

        //业务信息（查快照需要）
        private RequestContext.BizInfo bizInfo;

        //主对象数据id（查询从对象数据快照的时候需要使用）
        private String masterDataId;

        //是否实时计算落地的计算字段
        private Boolean calculateFormula;

        // 保留数据中的所有语言
        private Boolean keepAllMultiLangValue;

        private String sourcePage;

        private Boolean includeTeamMember;

        /**
         * 拓展参数
         */
        private Map<String, Object> extraParams;

        /**
         * 同时查询从对象数据
         */
        private List<DetailsQuery> detailsQuery;

        public boolean serializeEmpty() {
            return !Boolean.FALSE.equals(serializeEmpty);
        }

        public boolean includeLayout() {
            return !Boolean.FALSE.equals(includeLayout);
        }

        public boolean includeDescribe() {
            return !Boolean.FALSE.equals(includeDescribe);
        }

        public boolean simpleData() {
            return Boolean.TRUE.equals(isSimpleData);
        }

        public boolean mergeSnapshot() {
            return Boolean.TRUE.equals(mergeSnapshot);
        }

        public boolean calculateFormula() {
            return Boolean.TRUE.equals(calculateFormula);
        }

        public boolean find2Edit() {
            return "edit".equalsIgnoreCase(sourcePage);
        }

        public Arg copy2Plugin() {
            Arg result = new Arg();
            result.setObjectDataId(objectDataId);
            result.setObjectDescribeApiName(objectDescribeApiName);
            result.setFromRecycleBin(fromRecycleBin);
            return result;
        }
    }

    @Data
    public static class DetailsQuery {
        /**
         * 从对象API
         */
        private String objApi;
        /**
         * 从对象相关列表名称
         */
        private String relatedListName;
        /**
         * 从对象数据查询条件信息
         */
        private String queryInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {

        @JSONField(name = "M1")
        private LayoutDocument layout;

        @JSONField(name = "M2")
        private ObjectDescribeDocument describe;

        @JSONField(name = "M3")
        private ObjectDataDocument data;

        @Builder.Default
        @JSONField(name = "M5")
        private Boolean isHaveDetailObj = false;

        @JSONField(name = "M6")
        private List<UserInfo> userInfos;

        @JSONField(name = "M7")
        private ObjectDescribeDocument objectDescribeExt;

        //是否主从审批的白名单企业，用于前端区分锁定的提示信息
        @JSONField(name = "M8")
        private Boolean isInApprovalWhiteList;

        @JSONField(name = "M9")
        private Boolean supportTag;

        /**
         * 当前对象是否存在标签数据
         */
        private Boolean hasTagData;

        @Builder.Default
        @JSONField(name = "M10")
        private Boolean isSupportGdpr = false;

        //数据快照
        private ObjectDataDocument snapshot;

        // 支持按钮混排标识
        private Boolean supportButtonMix;

        /**
         * 同时返回从对象数据
         */
        private Map<String, List<ObjectDataDocument>> details;

        public Result copy2Plugin() {
            return Result.builder()
                    .data(data)
                    .build();
        }

        public IFieldDescribe copyFieldToDescribeExt(String fieldApiName) {
            if (Objects.isNull(describe) || !describe.toObjectDescribe().containsField(fieldApiName)) {
                return null;
            }
            if (Objects.isNull(objectDescribeExt)) {
                objectDescribeExt = ObjectDescribeDocument.of(new ObjectDescribe());
            }
            IFieldDescribe fieldDescribe = describe.toObjectDescribe().getFieldDescribe(fieldApiName);
            return ObjectDescribeExt.of(objectDescribeExt).getOrCopyField(fieldDescribe);
        }
    }

}
