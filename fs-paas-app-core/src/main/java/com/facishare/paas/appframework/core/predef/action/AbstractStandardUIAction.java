package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.*;

/**
 * create by zhaoju on 2020/09/21
 */
public abstract class AbstractStandardUIAction<A, R> extends AbstractStandardAction<A, R> {
    private Set<String> unauthorizedFields;

    protected IObjectData objectData;
    protected Map<String, List<IObjectData>> detailObjectData = Maps.newHashMap();

    protected void fillInfo(IObjectData data) {
        if (Objects.isNull(data)) {
            return;
        }
        //补充引用字段数据(包括负责人所在部门)
        fillQuoteFieldValue(data);
        stopWatch.lap("fillQuoteFieldValue");
        //补充关联对象的名称
        fillRefObjectName(data);
        stopWatch.lap("fillRefObjectName");
        fillOutUserInfo(data);
        stopWatch.lap("fillOutUserInfo");
        fillCountryAreaLabel(data);
        stopWatch.lap("fillCountryAreaLabel");
        fillMaskFieldValue(data);
        stopWatch.lap("fillMaskFieldValue");
        fillPhoneNumberInfo(data);
        stopWatch.lap("fillPhoneNumberInformation");
        fillDimensionInfo(data);
        stopWatch.lap("fillDimensionInfo");
        fillDataVisibilityRange(data);
        stopWatch.lap("fillDataVisibilityRange");
        ObjectDataExt.of(data).handleMultiLangField(objectDescribe);
    }

    private void fillDataVisibilityRange(IObjectData objectData) {
        serviceFacade.fillDataVisibilityRange(actionContext.getUser(), objectDescribe, Lists.newArrayList(objectData));
    }

    public void fillDimensionInfo(IObjectData data) {
        serviceFacade.fillDimensionFieldValue(actionContext.getUser(), objectDescribe, Lists.newArrayList(data));
    }

    private void fillPhoneNumberInfo(IObjectData data) {
        serviceFacade.fillPhoneNumberInformation(objectDescribe, data);
    }

    private void fillMaskFieldValue(IObjectData data) {
        if (AppFrameworkConfig.maskFieldEncryptGray(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            if (AppFrameworkConfig.maskFieldEncryptObjectPagesGray(actionContext.getTenantId(), actionContext.getObjectApiName())) {
                return;
            }
            MaskFieldLogicService maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
            Map<String, IObjectDescribe> describeMap = ImmutableMap.of(objectDescribe.getApiName(), objectDescribe);
            maskFieldLogicService.processMaskFieldValue(actionContext.getUser(), data, Collections.emptyMap(), describeMap,
                    MaskFieldLogicService.MaskFieldConfig.defaultMaskFieldConfig());
            return;
        }
        serviceFacade.fillMaskFieldValue(actionContext.getUser(), Lists.newArrayList(data), objectDescribe, needRemoveMaskOrigValue());
    }

    protected boolean needRemoveMaskOrigValue() {
        return RequestUtil.isCepRequest();
    }

    private void fillCountryAreaLabel(IObjectData data) {
        serviceFacade.fillCountryAreaLabel(objectDescribe, Lists.newArrayList(data), actionContext.getUser());
    }

    private void fillOutUserInfo(IObjectData data) {
        serviceFacade.fillUserInfo(objectDescribe, Lists.newArrayList(data), actionContext.getUser());
        serviceFacade.fillDepartmentInfo(objectDescribe, Lists.newArrayList(data), actionContext.getUser());
        serviceFacade.fillImageInformation(objectDescribe, Lists.newArrayList(data), actionContext.getUser());
        // 转换富文本中的图片和附件路径
        serviceFacade.fillRichTextImageInfo(objectDescribe, Lists.newArrayList(data), actionContext.getUser());
    }

    private void fillRefObjectName(IObjectData data) {
        serviceFacade.fillObjectDataWithRefObject(objectDescribe, Lists.newArrayList(data), actionContext.getUser(), null, false);
    }

    private void fillQuoteFieldValue(IObjectData data) {
        infraServiceFacade.fillQuoteFieldValue(actionContext.getUser(), Lists.newArrayList(data), objectDescribe, null, false);
    }

    @Override
    protected Collection<String> getIgnoreFields() {
        Set<String> invisibleFields = Sets.newHashSet();
        // 无权限的字段
        invisibleFields.addAll(getUnauthorizedFields());
        // InvisibleFieldList代表在各种场景的Layout下都不应该显示的字段
        invisibleFields.addAll(invisibleFieldListFormObjectMap.getOrDefault(UDOBJ, Collections.emptySet()));
        invisibleFields.addAll(invisibleFieldListFormObjectMap.getOrDefault(objectDescribe.getApiName(), Collections.emptySet()));
        // -SystemFieldList代表初始化layout的时候放在详细信息的(系统分组)中的字段
        invisibleFields.addAll(systemFieldListFormObjectMap.getOrDefault(UDOBJ, Collections.emptySet()));
        invisibleFields.addAll(systemFieldListFormObjectMap.getOrDefault(objectDescribe.getApiName(), Collections.emptySet()));

        // InvisibleFieldTypeList4AddOrUpdateLayout表示在create和edit的layout中,不应该显示的字段类型
        invisibleFields.addAll(ObjectDescribeExt.of(objectDescribe).stream()
                .filter(it -> invisibleFieldTypeList4AddOrUpdateLayoutFormObjectMap.getOrDefault(UDOBJ, Collections.emptySet()).contains(it.getType()))
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet()));
        return invisibleFields;
    }

    protected Set<String> getUnauthorizedFields() {
        if (unauthorizedFields == null) {
            Set<String> fields = serviceFacade.getUnauthorizedFields(actionContext.getUser(), objectDescribe.getApiName());
            return unauthorizedFields = CollectionUtils.empty(fields) ? Collections.emptySet() : fields;
        }
        return unauthorizedFields;
    }

    private void filterUnauthorizedFields() {
        if (actionContext.getUser().isSupperAdmin()) {
            return;
        }
        ObjectDataExt.filterUnauthorizedFieldsByDataList(Lists.newArrayList(objectData), getUnauthorizedFields(), objectDescribe);
        stopWatch.lap("filterUnauthorizedFields");
    }

    protected void decodeMaskFieldEncryptValue() {
        if (!AppFrameworkConfig.maskFieldEncryptGray(actionContext.getTenantId(), objectDescribe.getApiName())) {
            return;
        }
        MaskFieldLogicService maskFieldLogicService = serviceFacade.getBean(MaskFieldLogicService.class);
        maskFieldLogicService.decodeMaskFieldEncryptValue(actionContext.getUser(), Lists.newArrayList(objectData), objectDescribe);
    }

    protected LayoutLogicService.LayoutContext buildLayoutContext() {
        return LayoutLogicService.LayoutContext.of(actionContext.getUser(), actionContext.getAppId());
    }

    @Override
    protected void finallyDo() {
        try {
            if (Objects.nonNull(objectData)) {
                filterUnauthorizedFields();
            }
        } finally {
            doSuperFinallyDo();
        }
    }

    /**
     * 抽取的辅助方法，用于调用父类的finallyDo方法
     * 这样可以在测试中更容易进行Mock
     */
    protected void doSuperFinallyDo() {
        super.finallyDo();
    }
}
