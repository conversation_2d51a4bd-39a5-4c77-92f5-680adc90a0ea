package com.facishare.paas.appframework.core.model;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;

import java.util.Map;

public class SearchTemplateQueryDocument extends DocumentBaseEntity {
    public SearchTemplateQueryDocument() {
    }

    public SearchTemplateQueryDocument(Map<String, Object> map) {
        super(map);
    }

    public static SearchTemplateQueryDocument of(Map<String, Object> data) {
        return new SearchTemplateQueryDocument(data);
    }

    public ISearchTemplateQuery toSearchTemplateQuery() {
        String jsonString = JSON.toJSONString(data);
        return SearchTemplateQueryExt.fromJsonString(jsonString);
    }

}
