package com.facishare.paas.appframework.core.predef.service.dto.objectImport;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.UniqueRuleExt;
import com.facishare.paas.appframework.metadata.dto.ImportObjectModule;
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping;
import com.facishare.paas.appframework.metadata.importobject.ImportObject;
import com.facishare.paas.appframework.metadata.importobject.ImportType;
import com.facishare.paas.appframework.metadata.importobject.MatchingType;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.metadata.importobject.ImportObject.ImportUniquenessRule;
import static com.facishare.paas.appframework.metadata.importobject.ImportObject.RuleField;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface GetImportObjectList {
    // 支持按唯一性规则导入的对象
    List<String> UNIQUE_RULE_OBJECT_LIST = Lists.newArrayList(Utils.ACCOUNT_API_NAME, Utils.OPPORTUNITY_API_NAME,
            Utils.HIGHSEAS_API_NAME, Utils.LEADS_API_NAME, Utils.CONTACT_API_NAME);

    @Data
    class Arg {
        private boolean management;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        List<ImportObjectDTO> importObjectList;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class ImportObjectDTO {
        private String objectCode;
        private String objectName;
        private String objectApiName;
        private String tip;
        private ImportUniquenessRule importUniquenessRule;
        private List<RuleField> primaryAttributeFieldList;
        private Integer duplicateJudgmentType;
        @JSONField(name = "IsOpenWorkFlow")
        @JsonProperty(value = "IsOpenWorkFlow")
        @SerializedName("IsOpenWorkFlow")
        private Boolean isOpenWorkFlow;
        @JSONField(name = "IsApprovalFlow")
        @JsonProperty(value = "IsApprovalFlow")
        @SerializedName("IsApprovalFlow")
        private Boolean isApprovalFlow;
        @JSONField(name = "IsTextureImport")
        @JsonProperty(value = "IsTextureImport")
        @SerializedName("IsTextureImport")
        private Boolean isTextureImport;
        private Boolean isNoBatch;
        @JSONField(name = "IsEnableUnionDuplicateChecking")
        @JsonProperty(value = "IsEnableUnionDuplicateChecking")
        @SerializedName("IsEnableUnionDuplicateChecking")
        @Builder.Default
        private Boolean isEnableUnionDuplicateChecking = Boolean.FALSE;
        @JSONField(name = "unionImportApiNameList")
        @JsonProperty(value = "unionImportApiNameList")
        @SerializedName(value = "unionImportApiNameList")
        private List<String> unionImportApiNameList;
        private Boolean isNotSupportSaleEvent;
        @JSONField(name = "IsVerifyEnterprise")
        @JsonProperty(value = "IsVerifyEnterprise")
        @SerializedName("IsVerifyEnterprise")
        private Boolean isVerifyEnterprise;
        @JSONField(name = "IsBackFillIndustrialAndCommercialInfo")
        @JsonProperty(value = "IsBackFillIndustrialAndCommercialInfo")
        @SerializedName("IsBackFillIndustrialAndCommercialInfo")
        private Boolean isBackFillIndustrialAndCommercialInfo;
        @JSONField(name = "IsUpdateOwner")
        @JsonProperty(value = "IsUpdateOwner")
        @SerializedName("IsUpdateOwner")
        private Boolean updateOwner;

        private ImportType supportType;
        private Map<String, List<MatchingType>> matchingTypes;
        private String describeType;
        private ImportManagerSetting insertImport;
        private ImportManagerSetting updateImport;
        private List<ImportReferenceMapping.ReferenceFieldMapping> referenceFieldMapping;

        /**
         * 校验外部负责人
         */
        @JSONField(name = "IsCheckOutOwner")
        @JsonProperty(value = "IsCheckOutOwner")
        @SerializedName("IsCheckOutOwner")
        private Boolean checkOutOwner;
        /**
         * 移除外部相关团队
         */
        @JSONField(name = "IsRemoveOutTeamMember")
        @JsonProperty(value = "IsRemoveOutTeamMember")
        @SerializedName("IsRemoveOutTeamMember")
        private Boolean removeOutTeamMember;


        @Data
        public static class ImportManagerSetting {
            private ImportAttribute triggerApprovalFlow;
            private ImportAttribute triggerWorkFlow;
            private ImportAttribute textureImport;
            private String importMethod;
        }

        @Data
        public static class ImportAttribute {
            private boolean readOnly;
            private boolean value;
        }


        public static ImportObjectDTO from(ImportObject importObject) {
            return baseBuilder(importObject).build();
        }

        private static ImportObjectDTOBuilder baseBuilder(ImportObject importObject) {
            return ImportObjectDTO.builder()
                    .objectCode(importObject.getObjectCode())
                    .objectName(importObject.getObjectName())
                    .objectApiName(importObject.getObjectApiName())
                    .unionImportApiNameList(importObject.getUnionImportApiNameList())
                    .isEnableUnionDuplicateChecking(importObject.isEnableUnionDuplicateChecking())
                    .isNotSupportSaleEvent(importObject.isNotSupportSaleEvent())
                    .describeType(importObject.getDescribeType())
                    .tip(importObject.getTip());
        }

        public static ImportObjectDTO from(ImportObject importObject, IUniqueRule uniqueRule, IObjectDescribe describe) {
            describe.getTenantId();
            ImportObjectDTO result = baseBuilder(importObject)
                    .supportType(importObject.getImportType())
                    .matchingTypes(importObject.getMatchingTypes())
                    .primaryAttributeFieldList(importObject.getPrimaryAttributeFieldList())
                    .isOpenWorkFlow(importObject.isOpenWorkFlow())
                    .isApprovalFlow(importObject.isApprovalFlow())
                    .isTextureImport(importObject.isTextureImport())
                    .isNoBatch(importObject.isNoBatch())
                    .isEnableUnionDuplicateChecking(importObject.isEnableUnionDuplicateChecking())
                    .isVerifyEnterprise(importObject.isVerifyEnterprise())
                    .isBackFillIndustrialAndCommercialInfo(importObject.isBackFillIndustrialAndCommercialInfo())
                    .tip(importObject.getTip())
                    .checkOutOwner(importObject.isCheckOutOwner())
                    .removeOutTeamMember(importObject.isRemoveOutTeamMember())
                    .updateOwner(importObject.isUpdateOwner())
                    .build();
            fillUniqueRule(uniqueRule, describe, result);
            return result;
        }

        public static ImportObjectDTO fromImportObjectModule(ImportObjectModule.ImportModule module) {
            return ImportObjectDTO.builder()
                    .objectApiName(module.getObjectApiName())
                    .objectCode(module.getObjectCode())
                    .describeType(module.getDescribeType())
                    .objectName(module.getObjectName())
                    .build();
        }

        private static void fillUniqueRule(IUniqueRule uniqueRule, IObjectDescribe describe, ImportObjectDTO result) {
            // 开启灰度的企业，不需要下发 唯一性规则
            if ((Objects.isNull(uniqueRule) || !UniqueRuleExt.of(uniqueRule).isEffectiveWhenImport()) &&
                    AppFrameworkConfig.isUpdateImportByIdGrayTenant(describe.getApiName(), RequestContextManager.getContext().getTenantId())) {
                return;
            }
            if (Objects.isNull(uniqueRule)) {
                // TODO: 2019/5/30 老对象支持按主属性更新导入后，去掉这段代码
                if (UNIQUE_RULE_OBJECT_LIST.contains(describe.getApiName())) {
                    result.setImportUniquenessRule(ImportUniquenessRule.empty());
                }
                return;
            }
            ImportUniquenessRule uniquenessRule = ImportUniquenessRule.of(uniqueRule, describe);
            result.setImportUniquenessRule(uniquenessRule);
        }
    }
}
