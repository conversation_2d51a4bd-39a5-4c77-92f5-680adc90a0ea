package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.CallBackActionParams;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * AbstractStandardAsyncBulkAction的JUnit 5测试类
 * 
 * 测试覆盖范围：
 * 1. 异步批量任务提交功能测试
 * 2. 参数转换和验证测试
 * 3. 批量数据处理限制测试
 * 4. CallBackActionParams构建测试
 * 5. 抽象方法的Mock处理测试
 * 6. 异常情况处理测试
 */
@ExtendWith(MockitoExtension.class)
class AbstractStandardAsyncBulkActionTest extends BaseActionTest {

    /**
     * 测试用的AbstractStandardAsyncBulkAction实现类
     * 使用Spy对象来Mock抽象方法
     */
    private static class TestableAbstractStandardAsyncBulkAction 
            extends AbstractStandardAsyncBulkAction<TestBulkArg, TestBulkParam> {
        
        @Override
        protected String getDataIdByParam(TestBulkParam param) {
            return param.getDataId();
        }

        @Override
        protected List<TestBulkParam> getButtonParams() {
            // 这个方法会在测试中被spy mock
            return Arrays.asList(
                new TestBulkParam("data_001", "param1"),
                new TestBulkParam("data_002", "param2")
            );
        }

        @Override
        protected String getButtonApiName() {
            return "test_bulk_button";
        }

        @Override
        protected String getActionCode() {
            return "TEST_BULK_ACTION";
        }

        @Override
        protected List<String> getFuncPrivilegeCodes() {
            return Collections.emptyList();
        }
    }

    /**
     * 测试用的批量参数类
     */
    private static class TestBulkArg {
        private List<String> dataIds;
        
        public TestBulkArg(List<String> dataIds) {
            this.dataIds = dataIds;
        }
        
        public List<String> getDataIds() {
            return dataIds;
        }
    }

    /**
     * 测试用的批量处理参数类
     */
    private static class TestBulkParam {
        private String dataId;
        private String paramValue;
        
        public TestBulkParam(String dataId, String paramValue) {
            this.dataId = dataId;
            this.paramValue = paramValue;
        }
        
        public String getDataId() {
            return dataId;
        }
        
        public String getParamValue() {
            return paramValue;
        }
    }

    @Spy
    private TestableAbstractStandardAsyncBulkAction action;

    private TestBulkArg testBulkArg;
    private String expectedJobId = "test_job_12345";

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testBulkArg = new TestBulkArg(Arrays.asList("data_001", "data_002"));
        
        // 注入Mock依赖
        injectMockDependencies(action);
        
        // 设置action的基本属性
        ActionTestUtils.setField(action, "actionContext", requestContext);
        
        // 配置Mock对象的默认行为
        configureMockDefaults();
    }

    private void configureMockDefaults() {
        // 配置requestContext的默认行为
        when(requestContext.getUser()).thenReturn(testUser);
        when(requestContext.getObjectApiName()).thenReturn("TestObj__c");
        when(requestContext.getLang()).thenReturn(com.facishare.paas.appframework.core.util.Lang.zh_CN);
        when(requestContext.getAppId()).thenReturn("test_app");
        when(requestContext.getUpstreamOwnerId()).thenReturn("upstream_001");
        when(requestContext.getRequestSource()).thenReturn(RequestContext.RequestSource.CEP);
        
        // 配置testUser的默认行为
        when(testUser.getOutTenantId()).thenReturn("out_tenant_001");
        when(testUser.getOutUserId()).thenReturn("out_user_001");
        
        // 配置serviceFacade的默认行为
        when(serviceFacade.submitBulkActionJob(any(User.class), any(CallBackActionParams.class), anyString()))
            .thenReturn(expectedJobId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doAct方法的正常执行流程
     */
    @Test
    @DisplayName("doAct方法正常执行流程测试")
    void testDoAct_正常流程() {
        // Act: 执行doAct方法
        AbstractStandardAsyncBulkAction.Result result = action.doAct(testBulkArg);
        
        // Assert: 验证执行结果
        assertNotNull(result, "执行结果不应为null");
        assertEquals(expectedJobId, result.getJobId(), "任务ID应该正确");
        
        // Assert: 验证submitBulkActionJob被调用
        verify(serviceFacade, times(1)).submitBulkActionJob(
            eq(testUser), 
            any(CallBackActionParams.class), 
            eq("TestObj__c")
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试submitJob方法的任务提交功能
     */
    @Test
    @DisplayName("submitJob任务提交功能测试")
    void testSubmitJob_任务提交() {
        // Act: 调用submitJob方法
        AbstractStandardAsyncBulkAction.Result result = action.submitJob(testBulkArg);
        
        // Assert: 验证结果
        assertNotNull(result, "提交结果不应为null");
        assertEquals(expectedJobId, result.getJobId(), "任务ID应该正确");
        
        // Assert: 验证CallBackActionParams的构建
        ArgumentCaptor<CallBackActionParams> paramsCaptor = ArgumentCaptor.forClass(CallBackActionParams.class);
        verify(serviceFacade).submitBulkActionJob(eq(testUser), paramsCaptor.capture(), eq("TestObj__c"));
        
        CallBackActionParams capturedParams = paramsCaptor.getValue();
        assertEquals("TEST_BULK_ACTION", capturedParams.getAction(), "Action代码应该正确");
        assertEquals("test_bulk_button", capturedParams.getButtonApiName(), "按钮API名称应该正确");
        assertEquals("zh_CN", capturedParams.getLang(), "语言应该正确");
        assertEquals("test_app", capturedParams.getAppId(), "应用ID应该正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getCallBackActionParams方法的参数构建
     */
    @Test
    @DisplayName("getCallBackActionParams参数构建测试")
    void testGetCallBackActionParams_参数构建() {
        // Act: 调用getCallBackActionParams方法
        CallBackActionParams<TestBulkParam> params = action.getCallBackActionParams(testBulkArg);
        
        // Assert: 验证参数构建结果
        assertNotNull(params, "CallBackActionParams不应为null");
        assertEquals("TEST_BULK_ACTION", params.getAction(), "Action代码应该正确");
        assertEquals("test_bulk_button", params.getButtonApiName(), "按钮API名称应该正确");
        assertEquals("zh_CN", params.getLang(), "语言应该正确");
        assertEquals("test_app", params.getAppId(), "应用ID应该正确");
        assertEquals("upstream_001", params.getUpstreamOwnerId(), "上游所有者ID应该正确");
        assertEquals("out_tenant_001", params.getOutTenantId(), "外部租户ID应该正确");
        assertEquals("out_user_001", params.getOutUserId(), "外部用户ID应该正确");
        assertEquals("CEP", params.getOriginalRequestSource(), "原始请求来源应该正确");
        assertFalse(params.isSkipMessage(), "默认不应跳过消息发送");
        
        // Assert: 验证actionParamMap
        Map<String, TestBulkParam> actionParamMap = params.getActionParamMap();
        assertNotNull(actionParamMap, "ActionParamMap不应为null");
        assertEquals(2, actionParamMap.size(), "应该包含2个参数");
        assertTrue(actionParamMap.containsKey("data_001"), "应该包含data_001");
        assertTrue(actionParamMap.containsKey("data_002"), "应该包含data_002");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipSendCRMMessage方法的默认实现
     */
    @Test
    @DisplayName("skipSendCRMMessage默认实现测试")
    void testSkipSendCRMMessage_默认实现() {
        // Act: 调用skipSendCRMMessage方法
        boolean result = action.skipSendCRMMessage();
        
        // Assert: 验证默认返回false
        assertFalse(result, "skipSendCRMMessage的默认实现应该返回false");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtonParams为空时的异常处理
     */
    @Test
    @DisplayName("getButtonParams为空时异常处理测试")
    void testGetButtonParams_为空异常() {
        // Arrange: 设置getButtonParams返回空列表
        doReturn(Collections.emptyList()).when(action).getButtonParams();
        
        // Act & Assert: 调用doAct应该抛出ValidateException
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            action.doAct(testBulkArg);
        });
        
        // Assert: 验证异常信息
        assertNotNull(exception.getMessage(), "异常消息不应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtonParams超过400个时的异常处理
     */
    @Test
    @DisplayName("getButtonParams超过限制时异常处理测试")
    void testGetButtonParams_超过限制异常() {
        // Arrange: 创建超过400个的参数列表
        List<TestBulkParam> largeParamList = Collections.nCopies(401, 
            new TestBulkParam("data_001", "param"));
        doReturn(largeParamList).when(action).getButtonParams();
        
        // Act & Assert: 调用doAct应该抛出ValidateException
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            action.doAct(testBulkArg);
        });
        
        // Assert: 验证异常信息
        assertNotNull(exception.getMessage(), "异常消息不应为null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试参数ID重复时的覆盖逻辑
     */
    @Test
    @DisplayName("参数ID重复时覆盖逻辑测试")
    void testGetButtonParams_ID重复覆盖() {
        // Arrange: 设置包含重复ID的参数列表
        List<TestBulkParam> duplicateParamList = Arrays.asList(
            new TestBulkParam("data_001", "param1"),
            new TestBulkParam("data_002", "param2"),
            new TestBulkParam("data_001", "param1_updated")  // 重复ID，应该覆盖前一个
        );
        doReturn(duplicateParamList).when(action).getButtonParams();
        
        // Act: 调用getCallBackActionParams方法
        CallBackActionParams<TestBulkParam> params = action.getCallBackActionParams(testBulkArg);
        
        // Assert: 验证重复ID被正确覆盖
        Map<String, TestBulkParam> actionParamMap = params.getActionParamMap();
        assertEquals(2, actionParamMap.size(), "应该只有2个不同的ID");
        assertEquals("param1_updated", actionParamMap.get("data_001").getParamValue(), 
            "重复ID应该使用后一个值覆盖");
        assertEquals("param2", actionParamMap.get("data_002").getParamValue(), 
            "非重复ID应该保持原值");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDataPrivilegeIds方法的默认实现
     */
    @Test
    @DisplayName("getDataPrivilegeIds默认实现测试")
    void testGetDataPrivilegeIds_默认实现() {
        // Act: 调用getDataPrivilegeIds方法
        List<String> result = action.getDataPrivilegeIds(testBulkArg);
        
        // Assert: 验证默认返回null
        assertNull(result, "getDataPrivilegeIds的默认实现应该返回null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Result类的构建和功能
     */
    @Test
    @DisplayName("Result类构建和功能测试")
    void testResult_构建和功能() {
        // Act: 使用of方法创建Result
        AbstractStandardAsyncBulkAction.Result result1 = 
            AbstractStandardAsyncBulkAction.Result.of("job_001");
        
        // Assert: 验证of方法创建的结果
        assertNotNull(result1, "Result不应为null");
        assertEquals("job_001", result1.getJobId(), "JobId应该正确");
        
        // Act: 使用builder创建Result
        AbstractStandardAsyncBulkAction.Result result2 = 
            AbstractStandardAsyncBulkAction.Result.builder()
                .jobId("job_002")
                .build();
        
        // Assert: 验证builder创建的结果
        assertNotNull(result2, "Result不应为null");
        assertEquals("job_002", result2.getJobId(), "JobId应该正确");
        
        // Act: 使用无参构造函数创建Result
        AbstractStandardAsyncBulkAction.Result result3 = 
            new AbstractStandardAsyncBulkAction.Result();
        result3.setJobId("job_003");
        
        // Assert: 验证无参构造函数创建的结果
        assertNotNull(result3, "Result不应为null");
        assertEquals("job_003", result3.getJobId(), "JobId应该正确");
    }
}
