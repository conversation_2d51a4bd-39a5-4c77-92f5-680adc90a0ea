package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.button.action.UpdateFieldAction;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * AbstractStandardAction的JUnit 5测试类
 * 
 * 测试覆盖范围：
 * 1. 模板方法模式的执行流程测试
 * 2. 抽象方法的Mock处理测试
 * 3. 按钮条件检查和参数验证测试
 * 4. 函数执行和结果处理测试
 * 5. 批量操作判断测试
 * 6. 各种protected方法的功能测试
 */
@ExtendWith(MockitoExtension.class)
class AbstractStandardActionTest extends BaseActionTest {

    /**
     * 测试用的AbstractStandardAction实现类
     * 使用Spy对象来Mock抽象方法
     */
    private static class TestableAbstractStandardAction extends AbstractStandardAction<TestArg, TestResult> {

        @Override
        protected TestResult doAct(TestArg arg) {
            return new TestResult("success");
        }

        @Override
        protected void initButton() {
            // 测试实现
        }

        @Override
        protected boolean skipPreFunction() {
            return false;
        }

        @Override
        protected List<String> getDataPrivilegeIds(TestArg arg) {
            return Collections.emptyList();
        }

        @Override
        protected List<String> getFuncPrivilegeCodes() {
            return Collections.emptyList();
        }
    }

    /**
     * 测试用的参数类
     */
    private static class TestArg {
        private String value;
        
        public TestArg(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
    }

    /**
     * 测试用的结果类
     */
    private static class TestResult {
        private String result;
        
        public TestResult(String result) {
            this.result = result;
        }
        
        public String getResult() {
            return result;
        }
    }

    @Spy
    private TestableAbstractStandardAction action;

    @Mock
    private IUdefButton mockUdefButton;

    @Mock
    private UpdateFieldAction mockUpdateFieldAction;

    @Mock
    private ButtonExecutor.Result mockValidatedFunctionResult;

    private TestArg testArg;
    private IObjectData testObjectData;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testArg = new TestArg("test_value");
        testObjectData = ActionMockFactory.createStandardTestData();
        
        // 注入Mock依赖
        injectMockDependencies(action);
        
        // 设置action的基本属性
        ActionTestUtils.setField(action, "objectDescribe", mockObjectDescribe);
        ActionTestUtils.setField(action, "actionContext", requestContext);
        ActionTestUtils.setField(action, "udefButton", mockUdefButton);
        
        // 配置Mock对象的默认行为
        configureMockDefaults();
    }

    private void configureMockDefaults() {
        // 配置mockObjectDescribe的默认行为
        when(mockObjectDescribe.getApiName()).thenReturn("TestObj__c");
        when(mockObjectDescribe.containsField(anyString())).thenReturn(true);
        
        // 配置mockUdefButton的默认行为
        when(mockUdefButton.getParamForm()).thenReturn(Collections.emptyList());
        
        // 配置serviceFacade的默认行为
        when(serviceFacade.getBean(UpdateFieldAction.class)).thenReturn(mockUpdateFieldAction);
        doNothing().when(serviceFacade).processData(any(), any());
        
        // 配置infraServiceFacade的默认行为
        doNothing().when(infraServiceFacade).checkParam(any(), any(), any(), any(), any());
        when(infraServiceFacade.triggerFunctionAction(any(), any())).thenReturn(mockValidatedFunctionResult);
        
        // 配置mockValidatedFunctionResult的默认行为
        when(mockValidatedFunctionResult.isHasReturnValue()).thenReturn(false);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试preAction模板方法的正常执行流程
     */
    @Test
    @DisplayName("preAction模板方法正常执行流程测试")
    void testPreAction_正常流程() {
        // Arrange: 准备测试数据
        // setUp中已完成基本配置
        
        // Act: 执行preAction方法
        assertDoesNotThrow(() -> {
            ActionTestUtils.setField(action, "stopWatch", mock(com.facishare.paas.appframework.common.util.StopWatch.class));
            action.preAction(testArg);
        });
        
        // Assert: 验证方法调用顺序和次数
        verify(action, times(1)).initButton();
        verify(action, times(1)).prepareData();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getOldObjectData方法的功能
     */
    @Test
    @DisplayName("getOldObjectData方法测试")
    void testGetOldObjectData() {
        // Arrange: 设置getPreObjectData的返回值
        IObjectData expectedData = testObjectData;
        doReturn(expectedData).when(action).getPreObjectData();
        
        // Act: 调用方法
        IObjectData result = action.getOldObjectData();
        
        // Assert: 验证结果
        assertEquals(expectedData, result, "getOldObjectData应该返回getPreObjectData的结果");
        verify(action, times(1)).getPreObjectData();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getPreObjectData方法的默认实现
     */
    @Test
    @DisplayName("getPreObjectData默认实现测试")
    void testGetPreObjectData_默认实现() {
        // Act: 调用方法（使用真实实现，不是spy）
        TestableAbstractStandardAction realAction = new TestableAbstractStandardAction();
        IObjectData result = realAction.getPreObjectData();
        
        // Assert: 验证默认返回null
        assertNull(result, "getPreObjectData的默认实现应该返回null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getPostObjectData方法的默认实现
     */
    @Test
    @DisplayName("getPostObjectData默认实现测试")
    void testGetPostObjectData_默认实现() {
        // Act: 调用方法
        IObjectData result = action.getPostObjectData();
        
        // Assert: 验证默认返回null
        assertNull(result, "getPostObjectData的默认实现应该返回null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getButtonApiName方法的默认实现
     */
    @Test
    @DisplayName("getButtonApiName默认实现测试")
    void testGetButtonApiName_默认实现() {
        // Act: 调用方法
        String result = action.getButtonApiName();
        
        // Assert: 验证默认返回null
        assertNull(result, "getButtonApiName的默认实现应该返回null");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getActionParams方法的默认实现
     */
    @Test
    @DisplayName("getActionParams默认实现测试")
    void testGetActionParams_默认实现() {
        // Act: 调用方法
        Map<String, Object> result = action.getActionParams(testArg);
        
        // Assert: 验证返回空Map
        assertNotNull(result, "getActionParams应该返回非null的Map");
        assertTrue(result.isEmpty(), "getActionParams的默认实现应该返回空Map");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getArgs方法的默认实现
     */
    @Test
    @DisplayName("getArgs默认实现测试")
    void testGetArgs_默认实现() {
        // Act: 调用方法
        Map<String, Object> result = action.getArgs();
        
        // Assert: 验证返回空Map
        assertNotNull(result, "getArgs应该返回非null的Map");
        assertTrue(result.isEmpty(), "getArgs的默认实现应该返回空Map");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildDataMapByObjectId方法的默认实现
     */
    @Test
    @DisplayName("buildDataMapByObjectId默认实现测试")
    void testBuildDataMapByObjectId_默认实现() {
        // Act: 调用方法
        Map<String, Map<String, Object>> result = action.buildDataMapByObjectId();
        
        // Assert: 验证返回空Map
        assertNotNull(result, "buildDataMapByObjectId应该返回非null的Map");
        assertTrue(result.isEmpty(), "buildDataMapByObjectId的默认实现应该返回空Map");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试buildCallBackDataMapByObjectId方法的默认实现
     */
    @Test
    @DisplayName("buildCallBackDataMapByObjectId默认实现测试")
    void testBuildCallBackDataMapByObjectId_默认实现() {
        // Act: 调用方法
        Map<String, Map<String, Object>> result = action.buildCallBackDataMapByObjectId();
        
        // Assert: 验证返回空Map
        assertNotNull(result, "buildCallBackDataMapByObjectId应该返回非null的Map");
        assertTrue(result.isEmpty(), "buildCallBackDataMapByObjectId的默认实现应该返回空Map");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试needSkipFunctionAction方法的功能
     */
    @Test
    @DisplayName("needSkipFunctionAction方法测试")
    void testNeedSkipFunctionAction() {
        // Arrange: 设置requestContext的属性
        when(requestContext.getAttribute(RequestContext.Attributes.SKIP_FUNCTION_ACTION)).thenReturn(true);
        
        // Act: 调用方法
        boolean result = action.needSkipFunctionAction();
        
        // Assert: 验证结果
        assertTrue(result, "当SKIP_FUNCTION_ACTION为true时，应该返回true");
        
        // Arrange: 设置为false
        when(requestContext.getAttribute(RequestContext.Attributes.SKIP_FUNCTION_ACTION)).thenReturn(false);
        
        // Act: 再次调用
        result = action.needSkipFunctionAction();
        
        // Assert: 验证结果
        assertFalse(result, "当SKIP_FUNCTION_ACTION为false时，应该返回false");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试needSkipButtonConditions方法的功能
     */
    @Test
    @DisplayName("needSkipButtonConditions方法测试")
    void testNeedSkipButtonConditions() {
        // Arrange: 设置requestContext的属性
        when(requestContext.getAttribute(RequestContext.Attributes.SKIP_BUTTON_CONDITIONS)).thenReturn(true);

        // Act: 调用方法
        boolean result = action.needSkipButtonConditions();

        // Assert: 验证结果
        assertTrue(result, "当SKIP_BUTTON_CONDITIONS为true时，应该返回true");

        // Arrange: 设置为false
        when(requestContext.getAttribute(RequestContext.Attributes.SKIP_BUTTON_CONDITIONS)).thenReturn(false);

        // Act: 再次调用
        result = action.needSkipButtonConditions();

        // Assert: 验证结果
        assertFalse(result, "当SKIP_BUTTON_CONDITIONS为false时，应该返回false");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试prepareData方法当udefButton为null时的处理
     */
    @Test
    @DisplayName("prepareData方法_udefButton为null")
    void testPrepareData_udefButtonNull() {
        // Arrange: 设置udefButton为null
        ActionTestUtils.setField(action, "udefButton", null);

        // Act & Assert: 调用方法不应抛出异常
        assertDoesNotThrow(() -> {
            action.prepareData();
        });

        // Assert: 验证没有调用相关方法
        verify(serviceFacade, never()).processData(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试prepareData方法当args为空时的处理
     */
    @Test
    @DisplayName("prepareData方法_args为空")
    void testPrepareData_argsEmpty() {
        // Arrange: 设置getArgs返回空Map
        doReturn(new HashMap<>()).when(action).getArgs();

        // Act & Assert: 调用方法不应抛出异常
        assertDoesNotThrow(() -> {
            action.prepareData();
        });

        // Assert: 验证没有调用processData
        verify(serviceFacade, never()).processData(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isBatchAction方法的批量判断逻辑
     */
    @Test
    @DisplayName("isBatchAction批量判断测试")
    void testIsBatchAction() {
        // Arrange: 设置单个数据
        List<IObjectData> singleDataList = Collections.singletonList(testObjectData);
        ActionTestUtils.setField(action, "dataList", singleDataList);

        // Act: 调用方法
        boolean result = action.isBatchAction();

        // Assert: 验证单个数据不是批量操作
        assertFalse(result, "单个数据不应该被判断为批量操作");

        // Arrange: 设置多个数据
        List<IObjectData> multipleDataList = Arrays.asList(testObjectData, testObjectData);
        ActionTestUtils.setField(action, "dataList", multipleDataList);

        // Act: 再次调用
        result = action.isBatchAction();

        // Assert: 验证多个数据是批量操作
        assertTrue(result, "多个数据应该被判断为批量操作");

        // Arrange: 设置空数据列表
        ActionTestUtils.setField(action, "dataList", Collections.emptyList());

        // Act: 再次调用
        result = action.isBatchAction();

        // Assert: 验证空列表不是批量操作
        assertFalse(result, "空数据列表不应该被判断为批量操作");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipPreFunction方法的默认实现
     */
    @Test
    @DisplayName("skipPreFunction默认实现测试")
    void testSkipPreFunction_默认实现() {
        // Act: 调用方法（测试实现返回false）
        boolean result = action.skipPreFunction();

        // Assert: 验证测试实现返回false
        assertFalse(result, "测试实现的skipPreFunction应该返回false");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipPreFunctionArgValidate方法的默认实现
     */
    @Test
    @DisplayName("skipPreFunctionArgValidate默认实现测试")
    void testSkipPreFunctionArgValidate_默认实现() {
        // Act: 调用方法
        boolean result = action.skipPreFunctionArgValidate();

        // Assert: 验证默认返回false
        assertFalse(result, "skipPreFunctionArgValidate的默认实现应该返回false");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipPostFunction方法的逻辑
     */
    @Test
    @DisplayName("skipPostFunction方法测试")
    void testSkipPostFunction() {
        // Arrange: 设置getButtonApiName返回null
        doReturn(null).when(action).getButtonApiName();

        // Act: 调用方法
        boolean result = action.skipPostFunction();

        // Assert: 验证当buttonApiName为null时跳过
        assertTrue(result, "当buttonApiName为null时应该跳过后置函数");

        // Arrange: 设置getButtonApiName返回有效值，但是批量操作
        doReturn("test_button").when(action).getButtonApiName();
        List<IObjectData> multipleDataList = Arrays.asList(testObjectData, testObjectData);
        ActionTestUtils.setField(action, "dataList", multipleDataList);

        // Act: 再次调用
        result = action.skipPostFunction();

        // Assert: 验证批量操作时跳过
        assertTrue(result, "批量操作时应该跳过后置函数");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipNonBlockingPreFunction方法的默认实现
     */
    @Test
    @DisplayName("skipNonBlockingPreFunction默认实现测试")
    void testSkipNonBlockingPreFunction_默认实现() {
        // Act: 调用方法
        boolean result = action.skipNonBlockingPreFunction();

        // Assert: 验证默认返回true
        assertTrue(result, "skipNonBlockingPreFunction的默认实现应该返回true");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipRedirectAction方法的默认实现
     */
    @Test
    @DisplayName("skipRedirectAction默认实现测试")
    void testSkipRedirectAction_默认实现() {
        // Act: 调用方法
        boolean result = action.skipRedirectAction();

        // Assert: 验证默认返回true
        assertTrue(result, "skipRedirectAction的默认实现应该返回true");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getIgnoreFields方法的默认实现
     */
    @Test
    @DisplayName("getIgnoreFields默认实现测试")
    void testGetIgnoreFields_默认实现() {
        // Act: 调用方法
        Collection<String> result = action.getIgnoreFields();

        // Assert: 验证返回空集合
        assertNotNull(result, "getIgnoreFields应该返回非null的集合");
        assertTrue(result.isEmpty(), "getIgnoreFields的默认实现应该返回空集合");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试各种get方法的默认返回值
     */
    @Test
    @DisplayName("各种get方法默认返回值测试")
    void testVariousGetMethods_默认返回值() {
        // Act & Assert: 测试各种返回null的方法
        assertNull(action.getPreObjectDataDetails(), "getPreObjectDataDetails默认应该返回null");
        assertNull(action.getPostObjectData(), "getPostObjectData默认应该返回null");
        assertNull(action.getPostObjectDetails(), "getPostObjectDetails默认应该返回null");
        assertNull(action.getPostObjectRelatedData(), "getPostObjectRelatedData默认应该返回null");
        assertNull(action.getRedirectObjectData(), "getRedirectObjectData默认应该返回null");
        assertNull(action.getRedirectObjectDetails(), "getRedirectObjectDetails默认应该返回null");

        // Act & Assert: 测试返回空Map的方法
        Map<String, Object> redirectArgs = action.getRedirectArgs();
        assertNotNull(redirectArgs, "getRedirectArgs应该返回非null的Map");
        assertTrue(redirectArgs.isEmpty(), "getRedirectArgs默认应该返回空Map");

        Map<String, Object> redirectActionParams = action.getRedirectActionParams();
        assertNotNull(redirectActionParams, "getRedirectActionParams应该返回非null的Map");
        assertTrue(redirectActionParams.isEmpty(), "getRedirectActionParams默认应该返回空Map");
    }
}
