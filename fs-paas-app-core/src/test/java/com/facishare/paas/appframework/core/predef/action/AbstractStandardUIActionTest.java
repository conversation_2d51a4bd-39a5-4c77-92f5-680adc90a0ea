package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * AbstractStandardUIAction的JUnit 5测试类
 * 
 * 测试覆盖范围：
 * 1. UI数据填充和处理逻辑测试
 * 2. 字段权限过滤功能测试
 * 3. super调用的处理测试（场景2：super.method() + 无返回值）
 * 4. fillInfo方法的各种场景测试
 * 5. 抽象方法的Mock处理测试
 */
@ExtendWith(MockitoExtension.class)
class AbstractStandardUIActionTest extends BaseActionTest {

    /**
     * 测试用的AbstractStandardUIAction实现类
     * 使用Spy对象来Mock抽象方法和super调用
     */
    private static class TestableAbstractStandardUIAction extends AbstractStandardUIAction<TestUIArg, TestUIResult> {
        
        @Override
        protected TestUIResult doAct(TestUIArg arg) {
            return new TestUIResult("ui_success");
        }

        @Override
        protected void initButton() {
            // 测试实现
        }

        @Override
        protected boolean skipPreFunction() {
            return false;
        }

        @Override
        protected List<String> getDataPrivilegeIds(TestUIArg arg) {
            return Collections.emptyList();
        }

        @Override
        protected List<String> getFuncPrivilegeCodes() {
            return Collections.emptyList();
        }

        @Override
        protected void fillInfo(TestUIArg arg) {
            // 测试实现，可以在测试中被spy mock
        }
    }

    /**
     * 测试用的UI参数类
     */
    private static class TestUIArg {
        private String uiValue;
        
        public TestUIArg(String uiValue) {
            this.uiValue = uiValue;
        }
        
        public String getUiValue() {
            return uiValue;
        }
    }

    /**
     * 测试用的UI结果类
     */
    private static class TestUIResult {
        private String uiResult;
        
        public TestUIResult(String uiResult) {
            this.uiResult = uiResult;
        }
        
        public String getUiResult() {
            return uiResult;
        }
    }

    @Spy
    private TestableAbstractStandardUIAction action;

    private TestUIArg testUIArg;
    private IObjectData testObjectData;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testUIArg = new TestUIArg("test_ui_value");
        testObjectData = ActionMockFactory.createStandardTestData();
        
        // 注入Mock依赖
        injectMockDependencies(action);
        
        // 设置action的基本属性
        ActionTestUtils.setField(action, "objectDescribe", mockObjectDescribe);
        ActionTestUtils.setField(action, "actionContext", requestContext);
        ActionTestUtils.setField(action, "objectData", testObjectData);
        
        // 配置Mock对象的默认行为
        configureMockDefaults();
    }

    private void configureMockDefaults() {
        // 配置mockObjectDescribe的默认行为
        when(mockObjectDescribe.getApiName()).thenReturn("TestObj__c");
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(createMockFieldDescribes());
        
        // 配置requestContext的默认行为
        when(requestContext.getTenantId()).thenReturn("74255");
        when(requestContext.getUser()).thenReturn(testUser);
    }

    private List<IFieldDescribe> createMockFieldDescribes() {
        IFieldDescribe nameField = mock(IFieldDescribe.class);
        when(nameField.getApiName()).thenReturn("name");
        when(nameField.isReadonly()).thenReturn(false);
        
        IFieldDescribe ownerField = mock(IFieldDescribe.class);
        when(ownerField.getApiName()).thenReturn("owner");
        when(ownerField.isReadonly()).thenReturn(true);
        
        return Arrays.asList(nameField, ownerField);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试fillInfo方法的调用
     */
    @Test
    @DisplayName("fillInfo方法调用测试")
    void testFillInfo_方法调用() {
        // Arrange: 准备测试参数
        // setUp中已完成基本配置
        
        // Act: 调用fillInfo方法
        assertDoesNotThrow(() -> {
            action.fillInfo(testUIArg);
        });
        
        // Assert: 验证方法被调用
        verify(action, times(1)).fillInfo(testUIArg);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filterUnauthorizedFields方法的字段过滤功能
     */
    @Test
    @DisplayName("filterUnauthorizedFields字段过滤测试")
    void testFilterUnauthorizedFields() {
        // Arrange: 设置objectData包含多个字段
        testObjectData.put("name", "测试名称");
        testObjectData.put("owner", "test_owner");
        testObjectData.put("readonly_field", "readonly_value");
        
        // 配置字段权限
        when(infraServiceFacade.hasFieldPrivilege(eq("74255"), eq("TestObj__c"), eq("name"), any())).thenReturn(true);
        when(infraServiceFacade.hasFieldPrivilege(eq("74255"), eq("TestObj__c"), eq("owner"), any())).thenReturn(false);
        when(infraServiceFacade.hasFieldPrivilege(eq("74255"), eq("TestObj__c"), eq("readonly_field"), any())).thenReturn(true);
        
        // Act: 调用字段过滤方法
        assertDoesNotThrow(() -> {
            action.filterUnauthorizedFields();
        });
        
        // Assert: 验证字段权限检查被调用
        verify(infraServiceFacade, atLeastOnce()).hasFieldPrivilege(eq("74255"), eq("TestObj__c"), anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试finallyDo方法的执行流程，包括super调用处理
     */
    @Test
    @DisplayName("finallyDo方法执行流程测试_包含super调用")
    void testFinallyDo_包含super调用() {
        // Arrange: 设置objectData不为null
        ActionTestUtils.setField(action, "objectData", testObjectData);
        
        // Mock doSuperFinallyDo方法以验证super调用
        doNothing().when(action).doSuperFinallyDo();
        
        // Act: 调用finallyDo方法
        assertDoesNotThrow(() -> {
            action.finallyDo();
        });
        
        // Assert: 验证doSuperFinallyDo被调用（这是对super.finallyDo()的封装）
        verify(action, times(1)).doSuperFinallyDo();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试finallyDo方法当objectData为null时的处理
     */
    @Test
    @DisplayName("finallyDo方法_objectData为null")
    void testFinallyDo_objectDataNull() {
        // Arrange: 设置objectData为null
        ActionTestUtils.setField(action, "objectData", null);
        
        // Mock doSuperFinallyDo方法
        doNothing().when(action).doSuperFinallyDo();
        
        // Act: 调用finallyDo方法
        assertDoesNotThrow(() -> {
            action.finallyDo();
        });
        
        // Assert: 验证doSuperFinallyDo仍然被调用
        verify(action, times(1)).doSuperFinallyDo();
        
        // Assert: 验证没有调用字段权限检查（因为objectData为null）
        verify(infraServiceFacade, never()).hasFieldPrivilege(anyString(), anyString(), anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试doSuperFinallyDo辅助方法的功能
     */
    @Test
    @DisplayName("doSuperFinallyDo辅助方法测试")
    void testDoSuperFinallyDo_辅助方法() {
        // Arrange: 使用真实的action实例（不是spy）来测试真实的super调用
        TestableAbstractStandardUIAction realAction = new TestableAbstractStandardUIAction();
        
        // Act & Assert: 调用doSuperFinallyDo方法不应抛出异常
        assertDoesNotThrow(() -> {
            realAction.doSuperFinallyDo();
        });
        
        // Note: 这里主要验证方法能够正常调用，不抛出异常
        // 实际的super.finallyDo()行为由父类负责
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filterUnauthorizedFields方法的异常处理
     */
    @Test
    @DisplayName("filterUnauthorizedFields异常处理测试")
    void testFilterUnauthorizedFields_异常处理() {
        // Arrange: 设置infraServiceFacade抛出异常
        when(infraServiceFacade.hasFieldPrivilege(anyString(), anyString(), anyString(), any()))
            .thenThrow(new RuntimeException("权限检查异常"));
        
        // Act & Assert: 调用方法应该能够处理异常
        assertDoesNotThrow(() -> {
            action.filterUnauthorizedFields();
        });
        
        // Assert: 验证异常被正确处理，方法仍然完成执行
        verify(infraServiceFacade, atLeastOnce()).hasFieldPrivilege(anyString(), anyString(), anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试UI Action的完整执行流程
     */
    @Test
    @DisplayName("UI Action完整执行流程测试")
    void testUIAction_完整执行流程() {
        // Arrange: 设置完整的执行环境
        doNothing().when(action).fillInfo(any());
        doNothing().when(action).doSuperFinallyDo();
        
        // Act: 执行doAct方法
        TestUIResult result = action.doAct(testUIArg);
        
        // Assert: 验证执行结果
        assertNotNull(result, "UI Action执行结果不应为null");
        assertEquals("ui_success", result.getUiResult(), "UI Action执行结果应该正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试字段权限过滤的边界条件
     */
    @Test
    @DisplayName("字段权限过滤边界条件测试")
    void testFilterUnauthorizedFields_边界条件() {
        // Arrange: 设置空的字段描述列表
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(Collections.emptyList());
        
        // Act: 调用字段过滤方法
        assertDoesNotThrow(() -> {
            action.filterUnauthorizedFields();
        });
        
        // Assert: 验证没有进行权限检查（因为没有字段）
        verify(infraServiceFacade, never()).hasFieldPrivilege(anyString(), anyString(), anyString(), any());
        
        // Arrange: 设置null的字段描述列表
        when(mockObjectDescribe.getFieldDescribes()).thenReturn(null);
        
        // Act: 再次调用字段过滤方法
        assertDoesNotThrow(() -> {
            action.filterUnauthorizedFields();
        });
        
        // Assert: 验证仍然没有进行权限检查
        verify(infraServiceFacade, never()).hasFieldPrivilege(anyString(), anyString(), anyString(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试finallyDo方法的异常恢复机制
     */
    @Test
    @DisplayName("finallyDo异常恢复机制测试")
    void testFinallyDo_异常恢复() {
        // Arrange: 设置filterUnauthorizedFields抛出异常
        ActionTestUtils.setField(action, "objectData", testObjectData);
        doThrow(new RuntimeException("字段过滤异常")).when(action).filterUnauthorizedFields();
        doNothing().when(action).doSuperFinallyDo();
        
        // Act: 调用finallyDo方法
        assertDoesNotThrow(() -> {
            action.finallyDo();
        });
        
        // Assert: 验证即使filterUnauthorizedFields抛出异常，doSuperFinallyDo仍然被调用
        verify(action, times(1)).doSuperFinallyDo();
    }
}
