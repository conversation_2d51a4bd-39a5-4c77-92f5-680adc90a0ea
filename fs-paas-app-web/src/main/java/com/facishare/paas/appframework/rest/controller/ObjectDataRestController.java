package com.facishare.paas.appframework.rest.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.rest.dto.data.*;
import com.facishare.paas.appframework.rest.service.ObjectDataRestService;
import com.facishare.paas.metadata.api.ISearchQuery;
import com.facishare.paas.metadata.api.search.IRelatedListQuery;
import com.facishare.paas.metadata.impl.search.RelatedListQuery;
import com.facishare.paas.metadata.impl.search.SearchQuery;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;

import javax.ws.rs.*;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.facishare.paas.appframework.core.model.RequestContext.Attributes.APPLY_DATA_PRIVILEGE_CHECK;

/**
 * Created by zhouwr on 2018/4/20
 */
@RestAPI
@Controller
@Path("/v1/inner/rest/object_data")
@Slf4j
public class ObjectDataRestController {

    @Autowired
    private ObjectDataRestService objectDataRestService;


    @POST
    @Path("/batch_update/{apiName}")
    public BatchUpdateData.Result batchUpdate(String dataListJson,
                                              @PathParam("apiName") String describeApiName,
                                              @QueryParam("ruleName") String ruleName,
                                              @QueryParam("isSpecifyTime") Boolean isSpecifyTime,
                                              @QueryParam("skipImmutableFieldValidate") Boolean skipImmutableFieldValidate) {
        RequestContext context = RequestContextManager.getContext();
        context.setAttribute(ActionContextExt.IS_SPECIFY_TIME, isSpecifyTime);
        if (Objects.nonNull(skipImmutableFieldValidate)) {
            context.setAttribute(RequestContext.Attributes.SKIP_IMMUTABLE_FIELD_VALIDATE, skipImmutableFieldValidate);
        }
        BatchUpdateData.Arg arg = BatchUpdateData.Arg.builder().dataListJson(dataListJson)
                .describeApiName(describeApiName).ruleName(ruleName).build();
        return objectDataRestService.batchUpdate(arg, context);
    }

    @PUT
    @Path("/batch_increment_update/{apiName}")
    public BatchIncrementUpdate.Result batchIncrementUpdate(String dataListJson,
                                                            @PathParam("apiName") String apiName) {
        RequestContext context = RequestContextManager.getContext();
        BatchIncrementUpdate.Arg arg = BatchIncrementUpdate.Arg.builder().apiName(apiName).dataListJson(dataListJson).build();
        return objectDataRestService.batchIncrementUpdate(arg, context);
    }

    /**
     * 创建数据
     *
     * @param json            数据json
     * @param describeAPIName 对象描述api
     * @param isTool          isTool
     * @return 创建的数据以及对象描述
     */
    @POST
    @Path("/{describeAPIName}")
    public CreateData.Result createObjectData(String json,
                                              @PathParam("describeAPIName") String describeAPIName,
                                              @QueryParam("isTool") Boolean isTool,
                                              @QueryParam("isSpecifyTime") Boolean isSpecifyTime,
                                              @QueryParam("triggerWorkFlow") Boolean triggerWorkFlow,
                                              @QueryParam("calculateDefaultValue") Boolean calculateDefaultValue) {
        RequestContext context = RequestContextManager.getContext();
        CreateData.Arg arg = CreateData.Arg.builder()
                .descAPIName(describeAPIName)
                .isTool(isTool)
                .isSpecifyTime(isSpecifyTime)
                .triggerWorkFlow(Optional.ofNullable(triggerWorkFlow).orElse(false))
                .calculateDefaultValue(calculateDefaultValue)
                .json(json)
                .build();
        return objectDataRestService.createObjectData(arg, context);
    }

    /**
     * 批量创建数据
     *
     * @param json
     * @param describeAPIName
     * @return
     */
    @POST
    @Path("/batch_create/{describeAPIName}")
    public BulkCreateData.Result batchCreateObjectData(String json,
                                                       @PathParam("describeAPIName") String describeAPIName,
                                                       @QueryParam("triggerWorkFlow") boolean triggerWorkFlow,
                                                       @QueryParam("triggerApprovalFlow") boolean triggerApprovalFlow,
                                                       @QueryParam("isSpecifyTime") Boolean isSpecifyTime,
                                                       @QueryParam("calculateDefaultValue") Boolean calculateDefaultValue) {
        RequestContext context = RequestContextManager.getContext();
        context.setAttribute(ActionContextExt.IS_SPECIFY_TIME, isSpecifyTime);
        BulkCreateData.Arg arg = BulkCreateData.Arg.builder()
                .describeApiName(describeAPIName)
                .dataListJson(json)
                .triggerWorkFlow(triggerWorkFlow)
                .triggerApprovalFlow(triggerApprovalFlow)
                .calculateDefaultValue(calculateDefaultValue)
                .build();
        return objectDataRestService.batchCreateObjectData(arg, context);
    }

    /**
     * 根据id更新数据
     *
     * @param dataJson          对象Json
     * @param dataId            对象Id
     * @param describeAPIName   对象描述ApiName
     * @param triggerFlow       是否触发流程（包括审批流、工作流）默认true
     * @param isTool            isTool
     * @param incrementalUpdate 是否增量修改
     * @return 修改后的数据
     */
    @PUT
    @Path("/{describeAPIName}/{dataId}")
    public UpdateData.Result updateObjectData(String dataJson,
                                              @PathParam("dataId") String dataId,
                                              @PathParam("describeAPIName") String describeAPIName,
                                              @DefaultValue("true") @QueryParam("triggerFlow") boolean triggerFlow,
                                              @QueryParam("isTool") boolean isTool,
                                              @QueryParam("isSpecifyTime") boolean isSpecifyTime,
                                              @QueryParam("incrementalUpdate") boolean incrementalUpdate,
                                              @QueryParam("applyValidationRule") boolean applyValidationRule,
                                              @QueryParam(APPLY_DATA_PRIVILEGE_CHECK) boolean applyDataPrivilegeCheck,
                                              @QueryParam("notValidate") boolean notValidate,
                                              @DefaultValue("true") @QueryParam("includeDescribe") boolean includeDescribe,
                                              @QueryParam("useSnapshotForApproval") boolean useSnapshotForApproval,
                                              @QueryParam("skipImmutableFieldValidate") Boolean skipImmutableFieldValidate,
                                              @QueryParam("processDataConflicts") boolean processDataConflicts,
                                              @QueryParam("skipModifyLog") boolean skipModifyLog) {

        RequestContext context = RequestContextManager.getContext();
        UpdateData.Arg arg = UpdateData.Arg.builder()
                .dataId(dataId)
                .dataJson(dataJson)
                .describeAPIName(describeAPIName)
                .triggerFlow(triggerFlow)
                .isTool(isTool)
                .isSpecifyTime(isSpecifyTime)
                .incrementalUpdate(incrementalUpdate)
                .applyValidationRule(applyValidationRule)
                .applyDataPrivilegeCheck(applyDataPrivilegeCheck)
                .includeDescribe(includeDescribe)
                .notValidate(notValidate)
                .useSnapshotForApproval(useSnapshotForApproval)
                .skipImmutableFieldValidate(skipImmutableFieldValidate)
                .processDataConflicts(processDataConflicts)
                .skipModifyLog(skipModifyLog)
                .build();

        return objectDataRestService.updateObjectData(arg, context);

    }

    /**
     * 根据数据Id作废数据
     *
     * @param dataId          数据Id
     * @param describeAPIName apiname
     * @return 数据和描述
     */
    @DELETE
    @Path("/{describeAPIName}/invalid/{dataId}")
    public DeletedById.Result invalid(@PathParam("dataId") String dataId,
                                      @PathParam("describeAPIName") String describeAPIName) {
        RequestContext context = RequestContextManager.getContext();
        DeletedById.Arg arg = DeletedById.Arg.builder()
                .dataId(dataId)
                .describeAPIName(describeAPIName)
                .eventId(context.getEventId()).build();

        return objectDataRestService.invalidByDataId(arg, context);
    }

    /**
     * 根据数据Id列表查询对象
     *
     * @param idJson          数据对象列表
     * @param describeAPIName 对象描述API名称
     * @return 返回数据列表
     */
    @POST
    @Path("/{describeAPIName}/object_names")
    public FindNameByIds.Result findNameByIds(String idJson,
                                              @PathParam("describeAPIName") String describeAPIName,
                                              @QueryParam("namingConvention") String namingConvention) {

        RequestContext context = RequestContextManager.getContext();
        List<String> idList = JSON.parseArray(idJson, String.class);

        FindNameByIds.Arg arg = FindNameByIds.Arg.builder()
                .describeAPIName(describeAPIName)
                .namingConvention(namingConvention)
                .idList(idList).build();

        return objectDataRestService.findNameByIds(arg, context);
    }

    /**
     * 根据查询条件（SearchQuery对象）来进行查询
     *
     * @param queryJson       查询条件Json格式串
     * @param describeAPIName 元数据的APIName
     * @param includeDescribe 返回结果中是否包含对象描述信息
     * @return 查询结果
     */
    @POST
    @Path("/{describeAPIName}/query")
    public FindByQuery.Result findBySearchQuery(String queryJson,
                                                @PathParam("describeAPIName") String describeAPIName,
                                                @QueryParam("includeDescribe") Boolean includeDescribe) {
        //不打印计算日志
        RequestUtil.setRecordCalculateLog(false);
        RequestContext context = RequestContextManager.getContext();
        ISearchQuery searchQuery = null;
        try {
            searchQuery = SearchQuery.fromJsonString(Optional.ofNullable(queryJson).orElse(" "));
        } catch (Exception e) {
            log.error("query data error", e);
            //log.error("describeAPIName ==>{}, RequestContext ==> {},queryJson ==> {}", describeAPIName, context, queryJson, e);
            throw e;
        }
        FindByQuery.Arg arg = FindByQuery.Arg.builder()
                .describeAPIName(Optional.ofNullable(describeAPIName).orElse("customer"))
                .query(searchQuery)
                .includeDescribe(Optional.ofNullable(includeDescribe).orElse(Boolean.FALSE)).build();

        return objectDataRestService.findBySearchQuery(arg, context);
    }

    /**
     * 根据查询条件（SearchTemplateQuery对象）来进行查询
     *
     * @param queryJson       查询条件Json格式串
     * @param describeAPIName 元数据的APIName
     * @param includeDescribe 返回结果中是否包含对象描述信息
     * @return 查询结果
     */
    @POST
    @Path("/{describeAPIName}/queryBySearchTemplate")
    public FindBySearchTemplateQuery.Result findBySearchTemplateQuery(String queryJson,
                                                                      @PathParam("describeAPIName") String describeAPIName,
                                                                      @QueryParam("includeDescribe") Boolean includeDescribe,
                                                                      @QueryParam("esSearchSkipRecentUpdateCheck") Boolean esSearchSkipRecentUpdateCheck,
                                                                      @QueryParam("ignoreBaseVisibleRange") Boolean ignoreBaseVisibleRange) {
        //不打印计算日志
        RequestUtil.setRecordCalculateLog(false);
        RequestContext context = RequestContextManager.getContext();
        SearchTemplateQuery searchQuery;
        try {
            searchQuery = (SearchTemplateQuery) SearchTemplateQueryExt.fromJsonString(Optional.ofNullable(queryJson).orElse(""));
        } catch (Exception e) {
            log.error("queryBySearchTemplate data error", e);
            //log.error("describeAPIName ==>{}, RequestContext ==> {},queryJson ==> {}", describeAPIName, context, queryJson, e);
            throw e;
        }
        FindBySearchTemplateQuery.Arg arg = FindBySearchTemplateQuery.Arg.builder()
                .describeAPIName(Optional.ofNullable(describeAPIName).orElse(null))
                .searchTemplateQuery(searchQuery)
                .includeDescribe(Optional.ofNullable(includeDescribe).orElse(Boolean.FALSE))
                .esSearchSkipRecentUpdateCheck(esSearchSkipRecentUpdateCheck)
                .ignoreBaseVisibleRange(ignoreBaseVisibleRange)
                .build();

        return objectDataRestService.findBySearchTemplateQuery(arg, context);
    }

    /**
     * 根据查询条件（SearchTemplateQuery对象）来进行查询
     *
     * @param queryJson       查询条件Json格式串
     * @param describeAPIName 元数据的APIName
     * @param includeDescribe 返回结果中是否包含对象描述信息
     * @return 查询结果
     */
    @POST
    @Path("/{describeAPIName}/findBySearchTemplateQueryWithFields")
    public FindBySearchTemplateQuery.Result findBySearchTemplateQueryWithFields(String queryJson,
                                                                                @PathParam("describeAPIName") String describeAPIName,
                                                                                @QueryParam("includeDescribe") Boolean includeDescribe,
                                                                                @QueryParam("esSearchSkipRecentUpdateCheck") Boolean esSearchSkipRecentUpdateCheck,
                                                                                @QueryParam("selectSpecialFields") Boolean selectSpecialFields) {
        //不打印计算日志
        RequestUtil.setRecordCalculateLog(false);
        FindBySearchTemplateQueryWithFields.Arg arg = JSON.parseObject(queryJson, FindBySearchTemplateQueryWithFields.Arg.class);
        SearchTemplateQuery searchQuery;
        try {
            searchQuery = (SearchTemplateQuery) SearchTemplateQueryExt.fromJsonString(Optional.ofNullable(arg.getSearchTemplateQuery()).orElse(""));
        } catch (Exception e) {
            log.error("findBySearchTemplateQueryWithFields data error", e);
//            log.error("describeAPIName ==>{}, RequestContext ==> {},queryJson ==> {}", describeAPIName,
//                    RequestContextManager.getContext(), queryJson, e);
            throw e;
        }

        return objectDataRestService.findBySearchTemplateQuery(FindBySearchTemplateQuery.Arg.builder()
                        .describeAPIName(describeAPIName)
                        .searchTemplateQuery(searchQuery)
                        .fieldList(arg.getFieldList())
                        .selectSpecialFields(selectSpecialFields)
                        .keepAllMultiLangValue(arg.getKeepAllMultiLangValue())
                        .esSearchSkipRecentUpdateCheck(esSearchSkipRecentUpdateCheck)
                        .includeDescribe(Optional.ofNullable(includeDescribe).orElse(Boolean.FALSE)).build(),
                RequestContextManager.getContext());
    }

    @POST
    @Path("/{describeAPIName}/findDataByIdWithFields")
    public FindByIdWithFields.Result findByIdWithFields(String queryJson,
                                                        @PathParam("describeAPIName") String describeAPIName) {
        //不打印计算日志
        RequestUtil.setRecordCalculateLog(false);
        RequestContext context = RequestContextManager.getContext();
        FindByIdWithFields.Arg arg = JSON.parseObject(queryJson, FindByIdWithFields.Arg.class);
        arg.setDescribeApiName(describeAPIName);
        FindByIdWithFields.Result result = objectDataRestService.findByIdWithFields(arg, context);
        return result;
    }

    /**
     * 简易的数据查询接口，以 id 为条件，查询对象的指定字段
     * 因为是简易查询方法，并不会关联表，所以对查询字段有限制
     * 专表对象（预置对象）只能查询专表中的字段，通表对象（自定义对象）不支持自定义字段
     *
     * @param queryJson       {"ids":["5dcd4fb7b481f97cd382bee2"], "fieldList":["owner","name"]}
     * @param describeApiName object_tJbHx__c
     */
    @POST
    @Path("/{describeApiName}/findDataByIdsWithFields")
    public FindSimpleDataByIds.Result findDataByIdsWithFields(String queryJson, @PathParam("describeApiName") String describeApiName) {
        //不打印计算日志
        RequestUtil.setRecordCalculateLog(false);
        RequestContext context = RequestContextManager.getContext();
        FindSimpleDataByIds.Arg arg = JSON.parseObject(queryJson, FindSimpleDataByIds.Arg.class);
        arg.setDescribeApiName(describeApiName);
        return objectDataRestService.findSimpleDataByIds(arg, context);
    }

    /**
     * 根据对象Id列表查询
     *
     * @param idJson          对象Id的列表 ["77274df543fc4ffd863d895a3d48ec6d", "4ad8a4db8ed04428b34b3fbb65bfb548"]
     * @param describeAPIName 对象米寿APIName
     * @return 返回数据列表
     */
    @POST
    @Path("/{describeAPIName}/query_list_by_ids")
    public FindDataByIds.Result findByIds(String idJson,
                                          @PathParam("describeAPIName") String describeAPIName,
                                          @QueryParam("isFillInfo") boolean isFillInfo,
                                          @QueryParam("isFillExtendField") boolean isFillExtendField) {
        //不打印计算日志
        RequestUtil.setRecordCalculateLog(false);
        RequestContext context = RequestContextManager.getContext();
        List<String> idList = JSON.parseArray(Optional.ofNullable(idJson).orElse(" "), String.class);
        FindDataByIds.Arg arg = FindDataByIds.Arg.builder()
                .describeAPIName(describeAPIName)
                .isFillInfo(isFillInfo)
                .isFillExtendField(isFillExtendField)
                .idList(idList).build();
        return objectDataRestService.findByIds(arg, context);

    }

    /**
     * 根据id进行数据对象的恢复操作
     *
     * @param dataId          对象Id
     * @param describeAPIName 描述APIName
     * @return 返回对象数据和描述
     */
    @GET
    @Path("/{describeAPIName}/recover/{dataId}")
    public RecoverData.Result recover(@PathParam("dataId") String dataId,
                                      @PathParam("describeAPIName") String describeAPIName) {

        RequestContext context = RequestContextManager.getContext();

        RecoverData.Arg arg = RecoverData.Arg.builder()
                .dataId(dataId)
                .describeAPIName(describeAPIName).build();

        return objectDataRestService.recover(arg, context);
    }

    /**
     * @param queryJson       查询条件对象(RelatedListQuery的Json格式字符串）
     * @param describeAPIName 元数据的APIName
     * @param dataId          数据Id
     * @param includeDescribe 返回结果中是否包含对象描述信息
     * @return 数据对象列表
     */
    @POST
    @Path("/{describeAPIName}/related_query/{dataId}")
    public FindRelatedList.Result findRelatedList(String queryJson,
                                                  @PathParam("describeAPIName") String describeAPIName,
                                                  @PathParam("dataId") String dataId,
                                                  @QueryParam("includeDescribe") Boolean includeDescribe) {
        //不打印计算日志
        RequestUtil.setRecordCalculateLog(false);
        RequestContext context = RequestContextManager.getContext();

        IRelatedListQuery query = RelatedListQuery.fromJSONString(queryJson);
        query.setObjectDescribeAPIName(describeAPIName);
        query.setDataId(dataId);
        query.setTenantId(context.getTenantId());

        FindRelatedList.Arg arg = FindRelatedList.Arg.builder()
                .query(query)
                .includeDescribe(Optional.ofNullable(includeDescribe).orElse(Boolean.FALSE)).build();

        return objectDataRestService.findRelatedList(arg, context);

    }

    /**
     * 根据id获取数据
     *
     * @param descAPIName     元数据对象描述的API name
     * @param dataId          自定义对象id
     * @param includeDescribe 返回结果是否包含对象描述，默认为false
     * @return 数据
     */
    @GET
    @Path("/{descAPIName}/{dataId}")
    public FindById.Result findDataById(@PathParam("descAPIName") String descAPIName,
                                        @PathParam("dataId") String dataId,
                                        @QueryParam("includeDescribe") Boolean includeDescribe,
                                        @QueryParam("includeInvalid") boolean includeInvalid,
                                        @QueryParam("includeDeleted") boolean includeDeleted,
                                        @QueryParam("checkPrivilege") boolean checkPrivilege,
                                        @QueryParam("isFillExtendField") boolean isFillExtendField,
                                        @QueryParam("keepAllMultiLangValue") boolean keepAllMultiLangValue,
                                        @QueryParam("includeLookupName") boolean includeLookup) {
        //不打印计算日志
        RequestUtil.setRecordCalculateLog(false);
        RequestContext context = RequestContextManager.getContext();
        FindById.Arg arg = FindById.Arg.builder()
                .dataId(dataId)
                .descAPIName(descAPIName)
                .includeDescribe(Optional.ofNullable(includeDescribe).orElse(Boolean.FALSE))
                .includeInvalid(includeInvalid)
                .includeDeleted(includeDeleted)
                .checkPrivilege(checkPrivilege)
                .isFillExtendField(isFillExtendField)
                .includeLookup(includeLookup)
                .keepAllMultiLangValue(keepAllMultiLangValue)
                .build();
        return objectDataRestService.findDataById(arg, context);
    }

    @GET
    @Path("/{descAPIName}/find_detail/{dataId}")
    public FindDetailDataList.Result findDetailDataList(@PathParam("descAPIName") String descAPIName,
                                                        @PathParam("dataId") String dataId) {
        //不打印计算日志
        RequestUtil.setRecordCalculateLog(false);
        return objectDataRestService.findDetailDataListByMasterId(descAPIName, dataId);
    }

    /**
     * 根据id删除数据
     *
     * @param dataId          自定义对象Id
     * @param describeAPIName 元数据的APIName
     * @return 返回删除的自定义对象
     */
    @DELETE
    @Path("/{describeAPIName}/{dataId}")
    public DeletedById.Result delete(@PathParam("dataId") String dataId,
                                     @PathParam("describeAPIName") String describeAPIName,
                                     @DefaultValue("true") @QueryParam("includeDescribe") boolean includeDescribe) {

        RequestContext context = RequestContextManager.getContext();
        DeletedById.Arg arg = DeletedById.Arg.builder()
                .dataId(dataId)
                .includeDescribe(includeDescribe)
                .describeAPIName(describeAPIName).build();

        return objectDataRestService.delete(arg, context);
    }

    @POST
    @Path("/{describeAPIName}/bulk_delete")
    @Consumes("application/json")
    public BulkDeleted.Result bulkDelete(BulkDeleted.Arg body,
                                         @PathParam("describeAPIName") String describeAPIName,
                                         @DefaultValue("false") @QueryParam("direct") boolean direct) {
        List<String> idList = body.getDataIds();
        if (CollectionUtils.isEmpty(idList)) {
            throw new ValidateException("id list is empty");
        }
        return objectDataRestService.bulkDelete(idList, describeAPIName, direct);
    }

    @POST
    @Path("/calculate/count_value")
    public CalculateCountValue.Result calculateCountValue(CalculateCountValue.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        return objectDataRestService.calculateCountValue(context, arg);
    }
}
