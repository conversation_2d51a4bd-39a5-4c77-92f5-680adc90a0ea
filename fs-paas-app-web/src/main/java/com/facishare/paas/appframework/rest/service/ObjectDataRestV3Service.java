package com.facishare.paas.appframework.rest.service;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.rest.dto.data.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.core.model.RequestContext.Biz.ApprovalFlow;

/**
 * Created by zhouwr on 2022/5/6.
 */
@Service
@Slf4j
public class ObjectDataRestV3Service {

    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;
    @Autowired
    private DataSnapshotLogicService dataSnapshotLogicService;
    @Autowired
    private MetaDataActionService metaDataActionService;

    public FindByIdV3.Result findById(FindByIdV3.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getDescribeApiName()) || Strings.isNullOrEmpty(arg.getDataId())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName() + "findById");
        String tenantId = RequestContextManager.getContext().getTenantId();
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(tenantId, arg.getDescribeApiName());

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(1);
        query.setNeedReturnCountNum(false);
        SearchTemplateQueryExt.of(query).searchInDB();
        SearchTemplateQueryExt.of(query).addFilter(Operator.EQ, IObjectData.ID, arg.getDataId());
        MetaDataFindService.QueryContext queryContext = arg.buildQueryContext();
        QueryResult<IObjectData> queryResult = metaDataService.findByQueryWithContext(queryContext, arg.getDescribeApiName(), query);
        IObjectData objectData = CollectionUtils.nullToEmpty(queryResult.getData()).stream().findFirst().orElse(null);
        stopWatch.lap("findByQuery");

        Map<String, Object> diffMap = useSnapshotForApproval(arg, stopWatch, describe, queryContext, objectData);
        if (arg.formatData()) {
            formatData(describe, objectData);
            stopWatch.lap("formatData");
        }
        FindByIdV3.Result result = FindByIdV3.Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .snapshot(diffMap)
                .build();
        if (arg.includeDescribe()) {
            if (arg.includeStatistics()) {
                Map<String, IObjectDescribe> describeExtMap = Maps.newHashMap();
                fieldRelationCalculateService.computeCalculateRelationWithExt(describe, null, describeExtMap);
                result.setObjectDescribeExt(ObjectDescribeDocument.of(describeExtMap.get(describe.getApiName())));
                stopWatch.lap("computeCalculateRelation");
            }
            result.setObjectDescribe(ObjectDescribeDocument.of(describe));
        }
        return result;
    }

    private void formatData(IObjectDescribe describe, IObjectData objectData) {
        if (Objects.isNull(objectData)) {
            return;
        }
        ObjectDataFormatter.builder()
                .describe(describe)
                .dataList(Lists.newArrayList(objectData))
                .build()
                .format();
    }

    private Map<String, Object> useSnapshotForApproval(FindByIdV3.Arg arg, StopWatch stopWatch, IObjectDescribe describe, MetaDataFindService.QueryContext queryContext, IObjectData objectData) {
        if (!arg.useSnapshotForApproval() || Objects.isNull(objectData)) {
            return null;
        }
        User user = queryContext.getUser();
        ObjectDataSnapshot snapshot = dataSnapshotLogicService.findAndMergeSnapshot(user.getTenantId(),
                arg.getDescribeApiName(), arg.getDataId(), ApprovalFlow.getCode(), RequestUtil.getOtherBizId());
        stopWatch.lap("findAndMergeSnapshot");
        if (Objects.isNull(snapshot)) {
            return null;
        }
        //计算变更之后的统计字段和计算字段
        IObjectData snapshotData = metaDataService.calculateForSnapshot(user, describe, objectData, snapshot);
        stopWatch.lap("calculateForSnapshot");
        //跟数据库里的数据做一次diff，diff结果供审批流展示
        Map<String, Object> diffMap = ObjectDataExt.of(objectData).diff(snapshotData, describe);
        // 补充查找关联、人员、部门、引用字段的显示信息
        if (queryContext.isFillExtendInfo()) {
            metaDataService.fillExtendFieldInfo(describe, Lists.newArrayList(ObjectDataExt.of(diffMap)), queryContext.getUser(),
                    queryContext.isNotFillQuote(), queryContext.isNotFillMask());
            stopWatch.lap("fillExtendFieldInfo");
        }
        return diffMap;
    }

    public FindByIdsV3.Result findByIds(FindByIdsV3.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getDescribeApiName()) || CollectionUtils.empty(arg.getDataIdList())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        int maxQueryLimitWithIds = AppFrameworkConfig.getMaxQueryLimitWithIds();
        if (arg.getDataIdList().size() > maxQueryLimitWithIds) {
            throw new ValidateException(String.format("query limit:%s with ids exceed max value:%s", arg.getDataIdList().size(), maxQueryLimitWithIds));
        }
        int maxQueryLimitWithIdsAndCalculateCount = AppFrameworkConfig.getMaxQueryLimitWithIdsAndCalculateCount();
        if (Boolean.TRUE.equals(arg.getCalculateCount()) && arg.getDataIdList().size() > maxQueryLimitWithIdsAndCalculateCount) {
            throw new ValidateException(String.format("query limit:%s with ids and calculateCount exceed max value:%s",
                    arg.getDataIdList().size(), maxQueryLimitWithIdsAndCalculateCount));
        }
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(arg.getDataIdList().size());
        query.setNeedReturnCountNum(false);
        SearchTemplateQueryExt.of(query).searchInDB();
        SearchTemplateQueryExt.of(query).addFilter(Operator.IN, IObjectData.ID, arg.getDataIdList());
        MetaDataFindService.QueryContext queryContext = arg.buildQueryContext();
        QueryResult<IObjectData> queryResult = metaDataFindService.findByQueryWithContext(queryContext, arg.getDescribeApiName(), query);
        return FindByIdsV3.Result.builder()
                .dataList(ObjectDataDocument.ofList(queryResult.getData()))
                .build();
    }

    public FindBySearchTemplateQueryV3.Result findByQuery(FindBySearchTemplateQueryV3.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getDescribeApiName()) || Strings.isNullOrEmpty(arg.getSearchQueryInfo())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        MetaDataFindService.QueryContext queryContext = arg.buildQueryContext();
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(queryContext.getUser().getTenantId(), arg.getDescribeApiName());
        SearchTemplateQuery query;
        if (arg.filterByDataRight()) {
            if (Strings.isNullOrEmpty(queryContext.getUser().getUserId())) {
                throw new ValidateException("header cannot be empty:x-fs-userInfo");
            }
            query = metaDataFindService.getSearchTemplateQuery(queryContext.getUser(), ObjectDescribeExt.of(describe),
                    "", arg.getSearchQueryInfo());
            arg.processSearchQuery(query);
        } else {
            query = arg.buildSearchQuery();
        }
        queryContext.setValidateFilterField(arg.validateFilterField());
        int maxQueryLimit = AppFrameworkConfig.getMaxQueryLimit();
        if (query.getLimit() <= 0 || query.getLimit() > maxQueryLimit) {
            throw new ValidateException(String.format("query limit:%s exceed max value:%s", query.getLimit(), maxQueryLimit));
        }
        //实时计算统计字段的话最多允许查询100条数据
        int maxQueryLimitWithCalculateCount = AppFrameworkConfig.getMaxQueryLimitWithCalculateCount();
        if (Boolean.TRUE.equals(arg.getCalculateCount()) && query.getLimit() > maxQueryLimitWithCalculateCount) {
            throw new ValidateException(String.format("query limit:%s with calculateCount exceed max value:%s",
                    query.getLimit(), maxQueryLimitWithCalculateCount));
        }
        QueryResult<IObjectData> queryResult = metaDataFindService.findByQueryWithContext(queryContext, arg.getDescribeApiName(), query);
        return FindBySearchTemplateQueryV3.Result.builder()
                .queryResult(FindBySearchTemplateQueryV3.QueryResult.builder()
                        .totalNumber(queryResult.getTotalNumber())
                        .dataList(ObjectDataDocument.ofList(queryResult.getData()))
                        .build())
                .build();
    }

    public FindOneV3.Result findOne(FindOneV3.Arg arg) {
        FindBySearchTemplateQueryV3.Result queryResult = findByQuery(arg);
        return FindOneV3.Result.builder()
                .objectData(queryResult.firstData())
                .build();
    }

    public AggregateQuery.Result aggregateQuery(AggregateQuery.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getDescribeApiName()) || Strings.isNullOrEmpty(arg.getSearchQueryInfo())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQueryExt.fromJsonString(arg.getSearchQueryInfo());
        if (Objects.isNull(query.getGroupByParameter()) || CollectionUtils.empty(query.getGroupByParameter().getAggFunctions())) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        RequestContext requestContext = RequestContextManager.getContext();
        String tenantId = requestContext.getUser().getTenantId();
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(tenantId, arg.getDescribeApiName());

        query.getGroupByParameter().getAggFunctions().forEach(aggFunctionArg -> {
            if (Strings.isNullOrEmpty(aggFunctionArg.getAggFunction())) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
            if (!SearchTemplateQueryExt.AGG_FUNCTIONS.contains(aggFunctionArg.getAggFunction())) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }
            if (!Count.TYPE_COUNT.equals(aggFunctionArg.getAggFunction()) && Strings.isNullOrEmpty(aggFunctionArg.getAggField())) {
                throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
            }

            // 非count场景，需要限制聚合字段的类型，只能是数字金额字段
            // 通过企业灰度控制这个功能
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.AGGREGATE_QUERY_FIELD_TYPE_CHECK, tenantId) && !Count.TYPE_COUNT.equals(aggFunctionArg.getAggFunction())
                    && !Strings.isNullOrEmpty(aggFunctionArg.getAggField())) {
                IFieldDescribe fieldDescribe = describe.getFieldDescribe(aggFunctionArg.getAggField());
                if (Objects.nonNull(fieldDescribe)) {
                    if (!FieldDescribeExt.of(fieldDescribe).isNumberTypeField()) {
                        throw new ValidateException(I18NExt.text(I18NKey.AGGREGATE_FIELD_TYPE_ERROR, aggFunctionArg.getAggField()));
                    }
                }
            }
        });
        //走数据权限需要加工query
        if (arg.filterByDataRight()) {
            if (Strings.isNullOrEmpty(requestContext.getUser().getUserId())) {
                throw new ValidateException("header cannot be empty:x-fs-userInfo");
            }
            query = metaDataFindService.getSearchTemplateQuery(requestContext.getUser(), ObjectDescribeExt.of(describe),
                    "", arg.getSearchQueryInfo());
        }
        //聚合查询不需要order_by
        SearchTemplateQueryExt.of(query).resetOrderBy(Lists.newArrayList());
        if (arg.validateFilterField()) {
            SearchTemplateQueryExt.of(query).validateWheresAndFilters(requestContext.getUser(), describe, true);
        }
        List<IObjectData> dataList = metaDataFindService.aggregateFindBySearchQuery(requestContext.getUser(), query, arg.getDescribeApiName());
        return AggregateQuery.Result.builder().dataList(ObjectDataDocument.ofList(dataList)).build();
    }

    public FillExtendInfo.Result fillExtendInfo(FillExtendInfo.Arg arg) {
        if (Strings.isNullOrEmpty(arg.getDescribeApiName())) {
            throw new ValidateException("describeApiName is empty");
        }
        if (CollectionUtils.empty(arg.getDataList())) {
            return FillExtendInfo.Result.builder().dataList(Collections.emptyList()).build();
        }
        if (CollectionUtils.size(arg.getDataList()) > 200) {
            throw new ValidateException("size of dataList over max value:200");
        }
        List<IObjectData> dataList = ObjectDataDocument.ofDataList(arg.getDataList());
        User user = RequestContextManager.getContext().getUser();
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), arg.getDescribeApiName());
        metaDataService.fillExtendFieldInfo(describe, dataList, user);
        return FillExtendInfo.Result.builder().dataList(ObjectDataDocument.ofList(dataList)).build();
    }

    /**
     * 批量更新创建时间
     *
     * @param arg 批量更新创建时间参数
     * @return 更新结果
     */
    public BatchUpdateCreateTime.Result batchUpdateCreateTime(BatchUpdateCreateTime.Arg arg) {
        // 1. 参数验证
        if (arg == null) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        if (Strings.isNullOrEmpty(arg.getDescribeApiName())) {
            throw new ValidateException("describeApiName cannot be empty");
        }
        if (CollectionUtils.empty(arg.getDataList())) {
            throw new ValidateException("dataList cannot be empty");
        }

        // 2. 获取用户上下文
        User user = RequestContextManager.getContext().getUser();

        // 3. 创建 ActionContext 并设置 specify_time=true
        IActionContext context = ActionContextExt.of(user)
                .setSpecifyCreateTime(true)
                .getContext();

        // 4. 数据转换：将 DTO 转换为 IObjectData 列表
        List<IObjectData> dataList = Lists.newArrayList();
        for (BatchUpdateCreateTime.Arg.DataItem item : arg.getDataList()) {
            if (Strings.isNullOrEmpty(item.getId())) {
                throw new ValidateException("data id cannot be empty");
            }
            if (item.getCreateTime() == null) {
                throw new ValidateException("createTime cannot be null");
            }

            IObjectData objectData = new ObjectData();
            objectData.setId(item.getId());
            objectData.setDescribeApiName(arg.getDescribeApiName());
            objectData.setCreateTime(item.getCreateTime());
            objectData.setTenantId(user.getTenantId());
            dataList.add(objectData);
        }

        // 5. 调用服务：批量更新字段
        metaDataActionService.batchUpdateByFields(context, dataList, Lists.newArrayList(IObjectData.CREATE_TIME));

        // 6. 返回结果
        return BatchUpdateCreateTime.Result.builder()
                .success(true)
                .build();
    }

}
