package com.facishare.paas.appframework.rest.dto.data;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.*;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2022/4/29.
 */
public interface FindBySearchTemplateQueryV3 {

    @Data
    @EqualsAndHashCode(callSuper = true)
    @ToString(callSuper = true)
    class Arg extends FindDataV3.Arg {
        //筛选条件
        private String searchQueryInfo;
        //是否返回数据总数
        private Boolean needCount;
        //是否强制es查询校验recentUpdate
        private Boolean esRecentUpdateCheck;
        //是否返回完整的富文本
        private Boolean searchRichTextExtra;
        //是否执行分页优化
        private Boolean paginationOptimization;
        //是否根据数据权限过滤数据
        private Boolean filterByDataRight;
        //是否校验筛选字段
        private Boolean validateFilterField;
        //查询超时时间
        private Integer timeoutNSecond;

        public boolean filterByDataRight() {
            return Boolean.TRUE.equals(filterByDataRight);
        }

        public boolean validateFilterField() {
            return Boolean.TRUE.equals(validateFilterField);
        }


        public boolean needCount() {
            return Boolean.TRUE.equals(needCount);
        }

        public SearchTemplateQuery buildSearchQuery() {
            SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQueryExt.fromJsonString(searchQueryInfo);
            processSearchQuery(query);
            return query;
        }

        public void processSearchQuery(SearchTemplateQuery query) {
            query.setNeedReturnCountNum(needCount());
        }

        @Override
        public MetaDataFindService.QueryContext buildQueryContext() {
            MetaDataFindService.QueryContext queryContext = super.buildQueryContext();
            queryContext.setEsRecentUpdateCheck(Boolean.TRUE.equals(esRecentUpdateCheck));
            queryContext.setSearchRichTextExtra(Boolean.TRUE.equals(searchRichTextExtra));
            queryContext.setPaginationOptimization(Boolean.TRUE.equals(paginationOptimization));
            queryContext.setTimeoutNSecond(timeoutNSecond);
            return queryContext;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private QueryResult queryResult;

        public ObjectDataDocument firstData() {
            if (Objects.isNull(queryResult) || CollectionUtils.empty(queryResult.getDataList())) {
                return null;
            }
            return queryResult.getDataList().stream().findFirst().get();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class QueryResult {
        private int totalNumber;
        private List<ObjectDataDocument> dataList;
    }

}
