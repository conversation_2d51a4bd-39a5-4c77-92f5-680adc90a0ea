package com.facishare.paas.appframework.rest.controller;

import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.rest.dto.data.*;
import com.facishare.paas.appframework.rest.service.ObjectDataRestV3Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

/**
 * Created by zhouwr on 2022/4/29.
 */
@RestAPI
@Controller
@Path("/v3/inner/rest/object_data")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Slf4j
public class ObjectDataRestV3Controller {

    @Autowired
    private ObjectDataRestV3Service objectDataRestV3Service;

    @POST
    @Path("/find_by_id")
    public FindByIdV3.Result findById(FindByIdV3.Arg arg) {
        return objectDataRestV3Service.findById(arg);
    }

    @POST
    @Path("/find_by_ids")
    public FindByIdsV3.Result findByIds(FindByIdsV3.Arg arg) {
        return objectDataRestV3Service.findByIds(arg);
    }

    @POST
    @Path("/find_by_query")
    public FindBySearchTemplateQueryV3.Result findByQuery(FindBySearchTemplateQueryV3.Arg arg) {
        return objectDataRestV3Service.findByQuery(arg);
    }

    @POST
    @Path("/find_one")
    public FindOneV3.Result findOne(FindOneV3.Arg arg) {
        return objectDataRestV3Service.findOne(arg);
    }

    @POST
    @Path("/aggregate_query")
    public AggregateQuery.Result aggregateQuery(AggregateQuery.Arg arg) {
        return objectDataRestV3Service.aggregateQuery(arg);
    }

    @POST
    @Path("/fill_extend_info")
    public FillExtendInfo.Result fillExtendInfo(FillExtendInfo.Arg arg) {
        return objectDataRestV3Service.fillExtendInfo(arg);
    }

    @POST
    @Path("/batch_update_create_time")
    public BatchUpdateCreateTime.Result batchUpdateCreateTime(BatchUpdateCreateTime.Arg arg) {
        return objectDataRestV3Service.batchUpdateCreateTime(arg);
    }

}
