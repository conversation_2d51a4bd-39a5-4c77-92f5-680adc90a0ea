package com.facishare.paas.appframework.rest.dto.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量更新创建时间的数据传输对象
 * Created for batch update create time functionality
 */
public interface BatchUpdateCreateTime {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        /**
         * 对象描述API名称
         */
        private String describeApiName;
        
        /**
         * 要更新的数据列表
         */
        private List<DataItem> dataList;
        
        /**
         * 数据项，包含ID和新的创建时间
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DataItem {
            /**
             * 数据ID
             */
            private String id;
            
            /**
             * 新的创建时间（时间戳）
             */
            private Long createTime;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        /**
         * 操作是否成功
         */
        private boolean success;
        
    }
}
