# fs-paas-appframework 单元测试项目计划

## 📋 项目概述

### 项目基本信息
- **项目名称**: fs-paas-appframework 核心模块单元测试编写
- **项目目标**: 为 `/fs-paas-app-core/src/main/java/com/facishare/paas/appframework/core/predef/action/` 目录中的Java类编写全面的JUnit5单元测试
- **测试覆盖范围**: 约120个Java类（Action类和抽象类）
- **技术栈**: JUnit 5 + Mockito + 项目现有测试基础设施

### 当前状态分析
- **现有Groovy测试**: 约30个文件
- **现有JUnit5测试**: 约40个文件  
- **测试覆盖率**: 约60%
- **待补充测试**: 约50个核心类

### 项目价值
- 提升代码质量和可维护性
- 确保业务逻辑的正确性和稳定性
- 为持续集成和部署提供可靠保障
- 建立标准化的测试规范和最佳实践

## 🎯 任务执行计划

### 任务依赖关系图
```
测试覆盖分析 → JUnit5规范分析 → AbstractStandardAction测试
                                      ↓
UI事件处理测试 ← AbstractCustomButtonAction测试 ← AbstractConvertAction测试
    ↓                    ↓                           ↓
    → 核心业务Action批量测试 → 测试质量验证与总结报告
```

### 阶段性任务详情

#### 🔍 阶段一：测试覆盖分析与优先级排序
**任务ID**: `9fe9d60d-7495-4bfa-bf66-0ef41b10e6f5`

**描述**: 系统分析目标目录中的120个Java类，对比现有测试，生成详细的测试覆盖缺口报告

**实施指南**:
1. 遍历目标目录下所有Java文件
2. 对比现有Groovy测试和JUnit5测试目录
3. 生成测试覆盖缺口清单
4. 按继承关系和业务重要性排序
5. 输出详细分析报告

**验收标准**:
- [ ] 完整的120个Java类清单
- [ ] 现有测试覆盖情况对比表
- [ ] 未测试类的优先级排序
- [ ] 详细的分析统计数据

**预估工时**: 8小时

---

#### 📚 阶段二：JUnit5测试规范和工具类深度分析
**任务ID**: `5f34f7f3-40f9-4a62-ba32-d1423d6667e0`

**描述**: 深入分析项目的JUnit5测试规范和现有测试基础设施，建立标准化模板

**实施指南**:
1. 分析 `t_paas-b-fr-junit5-mockito.mdc` 规范文件
2. 研究 BaseActionTest、ActionMockFactory、ActionTestUtils
3. 分析框架标准示例的对象构造规范
4. 制定标准化测试模板和编码指南
5. 确定Mock策略和依赖注入处理方式

**验收标准**:
- [ ] JUnit5快速通道流程详细步骤
- [ ] 测试基础设施使用方法文档
- [ ] Mock策略和对象构造标准模式
- [ ] 代码示例和最佳实践指南

**预估工时**: 12小时

---

#### 🏗️ 阶段三：AbstractStandardAction抽象基类测试编写
**任务ID**: `dae11f5b-96ce-457f-8788-ec60cf6d869f`

**描述**: 为最重要的抽象基类编写全面的JUnit5单元测试，作为其他Action类测试的参考模板

**实施指南**:
1. 执行JUnit5快速通道流程的强制性代码分析
2. 检查super.method()调用场景
3. 执行静态方法调用四层智能处理
4. 基于BaseActionTest创建测试类
5. 处理抽象方法的Mock和具体实现测试
6. 覆盖正常场景、边界条件和异常情况

**验收标准**:
- [ ] 完整的代码分析结果输出
- [ ] 所有public和protected方法的测试覆盖
- [ ] 模板方法模式的执行流程测试
- [ ] 异常场景和边界条件测试
- [ ] 符合项目规范的代码风格和注释

**预估工时**: 16小时

---

#### 🔄 阶段四：AbstractConvertAction转换动作类测试编写
**任务ID**: `d7e3bb2d-8222-40ad-b1f9-78b29ef72f4c`

**描述**: 为抽象转换动作类编写全面测试，重点测试数据转换逻辑和映射规则处理

**实施指南**:
1. 执行JUnit5快速通道流程的强制性代码分析
2. 检查现有StandardConvertActionGroovyTest，执行Groovy测试迁移
3. 分析转换规则、映射逻辑等核心业务方法
4. 测试不同转换场景：一对一、多对一、分组转换
5. 使用框架标准示例构造相关对象

**验收标准**:
- [ ] Groovy测试迁移报告
- [ ] 所有转换场景的测试覆盖
- [ ] 映射规则和数据验证的测试
- [ ] 异常处理和边界条件测试
- [ ] 符合框架标准示例的对象构造

**预估工时**: 20小时

---

#### 🔘 阶段五：AbstractCustomButtonAction自定义按钮动作类测试编写
**任务ID**: `f46a06c9-b86c-4fd1-852d-5dd4479ed4b6`

**描述**: 为抽象自定义按钮动作类编写全面测试，重点测试按钮执行逻辑和审批流程

**实施指南**:
1. 执行JUnit5快速通道流程的强制性代码分析
2. 分析自定义按钮的执行流程和审批逻辑
3. 测试按钮执行的不同场景
4. 处理掩码字段验证、验证规则触发等逻辑
5. 测试审批流程的启动和回调处理

**验收标准**:
- [ ] 按钮执行流程的完整测试
- [ ] 审批流程触发和处理的测试
- [ ] 权限验证和数据锁定的测试
- [ ] 掩码字段和验证规则的测试
- [ ] 各种异常场景的处理测试

**预估工时**: 18小时

---

#### 🎨 阶段六：UI事件处理类测试编写
**任务ID**: `a9a2b87a-43a9-421e-83c4-c1cb9c1af644`

**描述**: 为uievent子目录中的UI事件处理类编写JUnit5单元测试

**实施指南**:
1. 分析uievent目录下的所有处理器类
2. 检查现有的Groovy测试和JUnit5测试覆盖情况
3. 为AbstractProcessor抽象基类编写测试模板
4. 为具体处理器类编写专门的测试
5. 测试处理器的执行流程、数据处理和异常处理

**验收标准**:
- [ ] 处理器执行流程的测试
- [ ] 不同数据类型和场景的处理测试
- [ ] 异常处理和边界条件测试
- [ ] 处理器协作关系的测试

**预估工时**: 14小时

---

#### 📦 阶段七：核心业务Action类批量测试编写
**任务ID**: `c775b88d-c688-4ea6-bc76-9916f406b2e1`

**描述**: 为剩余的核心业务Action类批量编写JUnit5单元测试

**实施指南**:
1. 基于前面建立的测试模板和最佳实践
2. 批量分析剩余未测试的核心业务Action类
3. 按业务功能分组：增删改查、导入导出、审批流程等
4. 为每个类执行JUnit5快速通道流程
5. 使用标准化的Mock配置和对象构造

**验收标准**:
- [ ] 100%的类测试覆盖
- [ ] 高质量的测试代码和注释
- [ ] 统一的测试风格和规范遵循
- [ ] 完整的业务场景和异常处理测试

**预估工时**: 32小时

---

#### ✅ 阶段八：测试质量验证与总结报告
**任务ID**: `3717f848-8144-4eff-a9bd-2f49f5850bdc`

**描述**: 对所有生成的JUnit5测试进行质量验证，生成最终报告

**实施指南**:
1. 编译验证所有生成的测试代码
2. 运行测试套件，验证测试的可执行性
3. 检查测试覆盖率，确保达到预期目标
4. 验证测试代码的规范性
5. 生成测试覆盖报告和质量分析

**验收标准**:
- [ ] 测试编译和执行结果
- [ ] 测试覆盖率统计
- [ ] 代码质量分析
- [ ] Groovy测试迁移总结
- [ ] 测试维护指南和最佳实践总结

**预估工时**: 10小时

## 📝 待办事项清单

### 高优先级 (P0)
- [ ] **测试覆盖分析** | 负责人: 开发团队 | 工时: 8h | 截止: Week 1
- [ ] **JUnit5规范分析** | 负责人: 开发团队 | 工时: 12h | 截止: Week 1
- [ ] **AbstractStandardAction测试** | 负责人: 开发团队 | 工时: 16h | 截止: Week 2

### 中优先级 (P1)  
- [ ] **AbstractConvertAction测试** | 负责人: 开发团队 | 工时: 20h | 截止: Week 3
- [ ] **AbstractCustomButtonAction测试** | 负责人: 开发团队 | 工时: 18h | 截止: Week 3
- [ ] **UI事件处理类测试** | 负责人: 开发团队 | 工时: 14h | 截止: Week 4

### 低优先级 (P2)
- [ ] **核心业务Action批量测试** | 负责人: 开发团队 | 工时: 32h | 截止: Week 5-6
- [ ] **测试质量验证与总结** | 负责人: 开发团队 | 工时: 10h | 截止: Week 7

### 总计工时: 130小时 (约16个工作日)

## 🔧 技术规范要点

### JUnit5快速通道流程关键步骤

1. **自动加载核心规则**
   ```java
   // 读取 t_paas-b-fr-junit5-mockito.mdc 规范
   ```

2. **分层加载示例文件**
   ```java
   // 框架标准示例 > 业务自定义示例
   ```

3. **强制性场景检查**
   - [ ] super.method()调用检查
   - [ ] 静态方法调用智能检查  
   - [ ] 依赖注入检查

4. **代码修改（如需要）**
   - 场景1和场景2需要修改被测类代码
   - 抽取super调用为辅助方法

5. **生成单元测试**
   - 基于分析结果选择正确Mock策略
   - 遵循AAA模式组织测试代码

### Mock策略和对象构造标准模式

#### 基础Mock配置
```java
@ExtendWith(MockitoExtension.class)
class XxxActionTest extends BaseActionTest {
    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock  
    private InfraServiceFacade infraServiceFacade;
    
    @InjectMocks
    private XxxAction xxxAction;
}
```

#### 框架标准示例对象构造
```java
// User构造
User user = User.systemUser('74255');

// RequestContext构造  
RequestContext requestContext = RequestContext.builder()
    .tenantId('74255')
    .user(user)
    .requestSource(RequestContext.RequestSource.CEP)
    .build();

// IFieldDescribe构造
IFieldDescribe fieldDescribe = FieldDescribeFactory.newInstance([
    "api_name": "employeeField", 
    "type": IFieldType.EMPLOYEE
]);
```

#### 静态方法调用四层智能处理
1. **示例构造**（业务优先）- 直接使用框架示例
2. **必须Mock**（外部调用/时间/随机）- 使用MockedStatic
3. **工具方法**（直接调用）- 无需Mock
4. **渐进式处理**（先尝试后调整）

### 代码质量和规范要求

#### 命名规范
- **测试类名**: `被测类名 + Test`
- **测试方法名**: `test + 方法名 + 测试场景描述`
- **异常测试**: `test + 方法名 + Throws + 异常类型 + 场景描述`

#### 注释要求
```java
/**
 * GenerateByAI
 * 测试内容描述：具体说明该测试用例的目的和测试点
 */
@Test
@DisplayName("正常场景 - 描述测试场景")
void testMethodName_正常场景描述() {
    // AAA模式组织
}
```

#### 测试结构
- **Arrange**: 准备测试数据和Mock配置
- **Act**: 执行被测试方法
- **Assert**: 验证结果和Mock交互

## ⚠️ 风险和注意事项

### 潜在技术难点

#### 1. Super调用处理复杂性
**风险**: super.method()调用的三种场景处理策略复杂
**解决方案**: 
- 严格按照场景识别流程执行
- 场景1和场景2必须先修改被测类代码
- 使用spy对象和辅助方法Mock

#### 2. 静态方法调用处理
**风险**: 静态方法Mock策略选择困难
**解决方案**:
- 按四层智能处理优先级执行
- 优先使用框架标准示例构造
- 避免过度使用MockedStatic

#### 3. 复杂业务逻辑测试
**风险**: Action类业务逻辑复杂，测试场景多样
**解决方案**:
- 充分利用现有测试基础设施
- 参考现有测试类的最佳实践
- 重点关注核心业务流程和异常处理

### Groovy测试迁移注意事项

#### 迁移流程
1. **检测现有Groovy测试**
2. **重命名保护**: `XxxTest.groovy` → `XxxGroovyTest.groovy`
3. **分析映射**: 生成Groovy到JUnit5的方法映射表
4. **迁移验证**: 确保覆盖率不降低

#### 特性转换对照
| Groovy/Spock特性 | JUnit 5等价实现 |
|-----------------|----------------|
| `given:` `when:` `then:` | AAA模式 |
| `@Unroll` | `@ParameterizedTest` |
| `where:` 数据表 | `@MethodSource` |
| `thrown()` | `assertThrows()` |

### 质量保证检查点

#### 代码质量检查
- [ ] 编译通过，无语法错误
- [ ] 测试执行成功，无运行时异常
- [ ] Mock配置正确，验证逻辑完整
- [ ] 命名规范一致，注释完整清晰

#### 覆盖率检查
- [ ] 方法覆盖率 ≥ 90%
- [ ] 分支覆盖率 ≥ 80%  
- [ ] 异常场景覆盖完整
- [ ] 边界条件测试充分

#### 规范遵循检查
- [ ] 严格遵循JUnit5快速通道流程
- [ ] 正确应用框架标准示例
- [ ] 使用项目现有测试基础设施
- [ ] 符合项目代码风格和最佳实践

---

**文档版本**: v1.0  
**创建日期**: 2025-01-08  
**最后更新**: 2025-01-08  
**维护人员**: 开发团队
