package com.facishare.paas.appframework.button.action

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.button.dto.ButtonExecutor
import com.facishare.paas.appframework.button.dto.SendEmailPojo
import com.facishare.paas.appframework.common.service.SendEmailProxy
import com.facishare.paas.appframework.common.service.dto.SendEmailModel
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IUdefAction
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * SendEmailAction单元测试
 * 用于测试SendEmailAction类的所有功能
 */
@Unroll
class SendEmailActionTest extends Specification {

    SendEmailAction sendEmailAction

    // Mock dependencies
    SendEmailProxy sendEmailProxy = Mock()
    GetEmployeeManager getEmployeeManager = Mock()
    ParseVarService parseVarService = Mock()

    void setup() {
        sendEmailAction = new SendEmailAction()
        sendEmailAction.sendEmailProxy = sendEmailProxy
        sendEmailAction.getEmployeeManager = getEmployeeManager
        sendEmailAction.parseVarService = parseVarService
    }

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
    }

    /**
     * 测试getType方法返回正确的ActionExecutorType
     */
    def "getTypeTest"() {
        when:
        def result = sendEmailAction.getType()

        then:
        result == ActionExecutorType.SEND_MAIL
    }

    /**
     * 测试invoke方法正常调用流程
     */
    def "invokeTestNormal"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        ActionExecutorContext context = Mock(ActionExecutorContext)
        User user = User.systemUser("7768")
        IObjectData objectData = Mock(IObjectData)
        IUdefAction action = Mock(IUdefAction)
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        Map<String, List<IObjectData>> details = [:]

        when:
        arg.toDetails() >> details
        arg.getObjectData() >> objectData
        context.getUser() >> user
        context.getAction() >> action
        context.getButton() >> button
        context.getDescribe() >> describe

        // Mock action parameter
        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.template = "template123"
        sendEmailPojo.recipients = ["person": ["user1", "user2"]]
        sendEmailPojo.email_address = ["<EMAIL>"] as Set
        action.getActionParamter() >> JSON.toJSONString(sendEmailPojo)

        // Mock describe
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"

        // Mock email service dependencies
        getEmployeeManager.getReceives(user, describe.getApiName(), objectData.getId(), sendEmailPojo.recipients) >> ["1", "2"].toSet()
        sendEmailProxy.sendEmail(_, _) >> new SendEmailModel.Result()

        ButtonExecutor.Result result = sendEmailAction.invoke(arg, context)

        then:
        1 * sendEmailProxy.sendEmail(_, _)
        result == null // 方法返回null
    }

    /**
     * 测试getSendEmailPojo方法解析邮件配置
     */
    def "getSendEmailPojoTest"() {
        given:
        IUdefAction action = Mock(IUdefAction)
        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.template = "template123"
        sendEmailPojo.recipients = ["person": ["user1", "user2"]]
        sendEmailPojo.email_address = ["<EMAIL>"] as Set
        String jsonStr = JSON.toJSONString(sendEmailPojo)

        when:
        action.getActionParamter() >> jsonStr
        def result = sendEmailAction.getSendEmailPojo(action)

        then:
        result.template == "template123"
        result.recipients == ["person": ["user1", "user2"]]
        result.email_address == ["<EMAIL>"] as Set
    }

    /**
     * 测试getReceiveIds方法处理基本收件人
     */
    def "getReceiveIdsTestBasicRecipients"() {
        given:
        User user = User.systemUser("7768")
        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.recipients = ["person": ["user1", "user2"], "dept": ["dept1"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"
        getEmployeeManager.getReceives(user, "TestObj", "123", sendEmailPojo.recipients) >> ["1", "2", "3"].toSet()

        def result = sendEmailAction.getReceiveIds(user, sendEmailPojo, arg, objectData, describe, button)

        then:
        result == ["1", "2", "3"].toSet()
    }

        /**
     * 测试getReceiveIds方法处理变量收件人
     */
    def "getReceiveIdsTestVariableRecipients"() {
        given:
        User user = User.systemUser("7768")
        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.recipients = ["vars": ["emp_var", "dept_var"], "person": ["user1"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        // Mock variables
        Variable empVar = Mock(Variable)
        Variable deptVar = Mock(Variable)
        
        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"
        
        empVar.getFieldType() >> IFieldType.EMPLOYEE
        empVar.getValue() >> new ArrayList<String>(["emp1", "emp2"])
        
        deptVar.getFieldType() >> IFieldType.DEPARTMENT
        deptVar.getValue() >> new ArrayList<String>(["dept1", "dept2"])
        
        // Mock department employees call - first call for dept variables
        getEmployeeManager.getReceives(user, "TestObj", "123", ["dept": ["dept1", "dept2"]]) >> ["1", "2", "3"].toSet()
        // Mock final recipient call - second call for remaining recipients (person)
        getEmployeeManager.getReceives(user, "TestObj", "123", ["person": ["user1"]]) >> ["4", "5"].toSet()

        def result = sendEmailAction.getReceiveIds(user, sendEmailPojo, arg, objectData, describe, button)

        then:
        // Result should contain: emp1, emp2 (from employee variable) + 1,2,3 (from dept variable) + 4,5 (from person)
        result.containsAll(["emp1", "emp2", "1", "2", "3", "4", "5"])
        1 * parseVarService.getVarList(["emp_var", "dept_var"], [:], objectData, user, describe, button) >> [empVar, deptVar]
    }

    /**
     * 测试getEmailAddress方法处理基本邮件地址
     */
    def "getEmailAddressTestBasicEmails"() {
        given:
        User user = User.systemUser("7768")
        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.email_address = ["<EMAIL>", "<EMAIL>"]
        sendEmailPojo.recipients = [:]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        when:
        def result = sendEmailAction.getEmailAddress(user, sendEmailPojo, arg, objectData, describe, button)

        then:
        result == ["<EMAIL>", "<EMAIL>"].toSet()
    }

    /**
     * 测试getEmailAddress方法处理变量邮件地址
     */
    def "getEmailAddressTestVariableEmails"() {
        given:
        User user = User.systemUser("7768")
        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.email_address = ["<EMAIL>"]
        sendEmailPojo.recipients = ["vars": ["email_var"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        Variable emailVar = Mock(Variable)

        when:
        arg.getArgs() >> [:]

        emailVar.getFieldType() >> IFieldType.EMAIL
        emailVar.getVariableName() >> "\$customer_email\$"
        emailVar.getData() >> new ObjectData(["customer_email": "<EMAIL>"])

        def result = sendEmailAction.getEmailAddress(user, sendEmailPojo, arg, objectData, describe, button)

        then:
        result.contains("<EMAIL>")
        result.contains("<EMAIL>")
        1 * parseVarService.getVarList(["email_var"], [:], objectData, user, describe, button) >> [emailVar]
    }

    /**
     * 测试getEmailAddress方法处理空变量场景
     */
    def "getEmailAddressTestEmptyVariables"() {
        given:
        User user = User.systemUser("7768")
        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.email_address = ["<EMAIL>"]
        sendEmailPojo.recipients = null // 空的recipients
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        when:
        def result = sendEmailAction.getEmailAddress(user, sendEmailPojo, arg, objectData, describe, button)

        then:
        result == ["<EMAIL>"].toSet()
        0 * parseVarService.getVarList(_, _, _, _, _, _)
    }

    /**
     * 测试getEmailAddress方法处理recipients中不包含vars的场景
     */
    def "getEmailAddressTestNoVarsInRecipients"() {
        given:
        User user = User.systemUser("7768")
        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.email_address = ["<EMAIL>"]
        sendEmailPojo.recipients = ["person": ["user1"]] // 不包含vars
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        when:
        def result = sendEmailAction.getEmailAddress(user, sendEmailPojo, arg, objectData, describe, button)

        then:
        result == ["<EMAIL>"].toSet()
        0 * parseVarService.getVarList(_, _, _, _, _, _)
    }

    /**
     * 测试replaceVariableName方法替换变量名
     */
    def "replaceVariableNameTest"() {
        given:
        Variable variable = Mock(Variable)
        def data = new ObjectData(["customer_email": "<EMAIL>"])

        when:
        variable.getVariableName() >> "\$customer_email\$"
        variable.getData() >> data

        def result = sendEmailAction.replaceVariableName(variable)

        then:
        result == "<EMAIL>"
    }

    /**
     * 测试replaceVariableName方法处理null值
     */
    def "replaceVariableNameTestNullValue"() {
        given:
        Variable variable = Mock(Variable)
        def data = new ObjectData(["customer_email": null])

        when:
        variable.getVariableName() >> "\$customer_email\$"
        variable.getData() >> data

        def result = sendEmailAction.replaceVariableName(variable)

        then:
        result == null
    }

    /**
     * 测试startCustomButton方法完整流程
     */
    def "startCustomButtonTestCompleteFlow"() {
        given:
        IObjectData objectData = Mock(IObjectData)
        Map<String, List<IObjectData>> details = [:]
        User user = User.systemUser("7768")
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IUdefAction action = Mock(IUdefAction)
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)

        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.template = "template123"
        sendEmailPojo.recipients = ["person": ["user1"]]
        sendEmailPojo.email_address = ["<EMAIL>"]

        when:
        action.getActionParamter() >> JSON.toJSONString(sendEmailPojo)
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"
        arg.getArgs() >> [:]

        getEmployeeManager.getReceives(user, "TestObj", "123", sendEmailPojo.recipients) >> ["1"].toSet()
        sendEmailProxy.sendEmail(_, _) >> { args ->
            def emailArg = args[1]
            assert emailArg.template_id == "template123"
            assert emailArg.obj_api_name == "TestObj"
            assert emailArg.obj_data_id == "123"
            assert emailArg.userid_list == ["1"].toSet()
            assert emailArg.email_list.contains("<EMAIL>")
            assert emailArg.forCalc == true
            return new SendEmailModel.Result()
        }

        def result = sendEmailAction.startCustomButton(objectData, details, user, arg, action, button, describe)

        then:
        1 * sendEmailProxy.sendEmail(_, _) >> { args ->
            def emailArg = args[1]
            assert emailArg.template_id == "template123"
            assert emailArg.obj_api_name == "TestObj"
            assert emailArg.obj_data_id == "123"
            assert emailArg.userid_list == ["1"].toSet()
            assert emailArg.email_list.contains("<EMAIL>")
            assert emailArg.forCalc == true
            return new SendEmailModel.Result()
        }
        result == null
    }

    /**
     * 测试getReceiveIds方法处理变量值为null的场景
     */
    def "getReceiveIdsTestVariableNullValue"() {
        given:
        User user = User.systemUser("7768")
        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.recipients = ["vars": ["emp_var"], "person": ["user1"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        Variable empVar = Mock(Variable)

        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"

        empVar.getFieldType() >> IFieldType.EMPLOYEE
        empVar.getValue() >> null // null值

        getEmployeeManager.getReceives(user, "TestObj", "123", ["person": ["user1"]]) >> ["1"].toSet()

        def result = sendEmailAction.getReceiveIds(user, sendEmailPojo, arg, objectData, describe, button)

        then:
        result == ["1"].toSet()
        1 * parseVarService.getVarList(["emp_var"], [:], objectData, user, describe, button) >> [empVar]
    }

    /**
     * 测试getReceiveIds方法处理部门变量的场景
     */
    def "getReceiveIdsTestDepartmentVariable"() {
        given:
        User user = User.systemUser("7768")
        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.recipients = ["vars": ["dept_var"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        Variable deptVar = Mock(Variable)

        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"

        deptVar.getFieldType() >> IFieldType.DEPARTMENT
        deptVar.getValue() >> new ArrayList<String>(["dept1", "dept2"])

        // Mock for variable department employees
        getEmployeeManager.getReceives(user, "TestObj", "123", _) >> ["1", "2", "3"].toSet()
        // Mock for final call with updated recipients
        getEmployeeManager.getReceives(user, "TestObj", "123", _) >> [].toSet()

        def result = sendEmailAction.getReceiveIds(user, sendEmailPojo, arg, objectData, describe, button)

        then:
        result.containsAll(["1", "2", "3"])
        1 * parseVarService.getVarList(["dept_var"], [:], objectData, user, describe, button) >> [deptVar]
    }

    /**
     * 测试getEmailAddress方法处理非邮件类型变量的场景
     */
    def "getEmailAddressTestNonEmailVariable"() {
        given:
        User user = User.systemUser("7768")
        def sendEmailPojo = new SendEmailPojo()
        sendEmailPojo.email_address = ["<EMAIL>"]
        sendEmailPojo.recipients = ["vars": ["text_var"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        Variable textVar = Mock(Variable)

        when:
        arg.getArgs() >> [:]

        textVar.getFieldType() >> IFieldType.TEXT // 非邮件类型
        textVar.getVariableName() >> "\$customer_name\$"
        textVar.getData() >> ["customer_name": "John Doe"]

        def result = sendEmailAction.getEmailAddress(user, sendEmailPojo, arg, objectData, describe, button)

        then:
        result == ["<EMAIL>"].toSet() // 应该只包含原始邮件地址
        1 * parseVarService.getVarList(["text_var"], [:], objectData, user, describe, button) >> [textVar]
    }
} 