package com.facishare.paas.appframework.button.action

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.button.dto.ButtonExecutor
import com.facishare.paas.appframework.button.dto.SendCrmRemindPojo
import com.facishare.paas.appframework.common.service.CRMNotificationService
import com.facishare.paas.appframework.common.service.model.NewCrmNotification
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IUdefAction
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * SendCRMRemindAction单元测试
 * 用于测试SendCRMRemindAction类的所有功能
 */
@Unroll
class SendCRMRemindActionTest extends Specification {

    SendCRMRemindAction sendCRMRemindAction

    // Mock dependencies
    CRMNotificationService proxy = Mock()
    GetEmployeeManager getEmployeeManager = Mock()
    ParseVarService parseVarService = Mock()

    void setup() {
        sendCRMRemindAction = new SendCRMRemindAction()
        sendCRMRemindAction.proxy = proxy
        sendCRMRemindAction.getEmployeeManager = getEmployeeManager
        sendCRMRemindAction.parseVarService = parseVarService
    }

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
    }

    /**
     * 测试getType方法返回正确的ActionExecutorType
     */
    def "getTypeTest"() {
        when:
        def result = sendCRMRemindAction.getType()

        then:
        result == ActionExecutorType.SEND_CRM_REMIND
    }

    /**
     * 测试invoke方法正常调用流程
     */
    def "invokeTestNormal"() {
        given:
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        ActionExecutorContext context = Mock(ActionExecutorContext)
        User user = User.systemUser("7768")
        IObjectData objectData = Mock(IObjectData)
        IUdefAction action = Mock(IUdefAction)
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)
        Map<String, List<IObjectData>> details = [:]

        when:
        arg.toDetails() >> details
        arg.getObjectData() >> objectData
        context.getUser() >> user
        context.getAction() >> action
        context.getButton() >> button
        context.getDescribe() >> describe

        // Mock action parameter
        def sendCrmRemindPojo = new SendCrmRemindPojo()
        sendCrmRemindPojo.content = "Test content with \$name\$ variable"
        sendCrmRemindPojo.title = "Test title with \$company\$"
        sendCrmRemindPojo.recipients = ["person": ["user1", "user2"]]
        action.getActionParamter() >> JSON.toJSONString(sendCrmRemindPojo)

        // Mock variable replacement
        parseVarService.getDisplayData("name", [:], objectData, user, describe, button) >> "John Doe"
        parseVarService.getDisplayData("company", [:], objectData, user, describe, button) >> "ABC Corp"

        // Mock receive IDs - 确保返回非null值
        getEmployeeManager.getReceives(user, _, _, _) >> ["1", "2"].toSet()
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"

        ButtonExecutor.Result result = sendCRMRemindAction.invoke(arg, context)

        then:
        1 * proxy.sendNewCrmNotification(user, _) >> { args ->
            def notification = args[1] as NewCrmNotification
            assert notification.senderId == user.getUserId()
            assert notification.remindSender == true
            assert notification.fullContent.contains("John Doe")
            assert notification.title.contains("ABC Corp")
            assert notification.type == 92
            assert notification.receiverIDs == [1, 2].toSet()
        }
        result == null
    }

    /**
     * 测试getSendCrmRemindPojo方法解析CRM提醒配置
     */
    def "getSendCrmRemindPojoTest"() {
        given:
        IUdefAction action = Mock(IUdefAction)
        def sendCrmRemindPojo = new SendCrmRemindPojo()
        sendCrmRemindPojo.content = "Test content"
        sendCrmRemindPojo.title = "Test title"
        sendCrmRemindPojo.recipients = ["person": ["user1", "user2"]]
        String jsonStr = JSON.toJSONString(sendCrmRemindPojo)

        when:
        action.getActionParamter() >> jsonStr
        def result = sendCRMRemindAction.getSendCrmRemindPojo(action)

        then:
        result.content == "Test content"
        result.title == "Test title"
        result.recipients == ["person": ["user1", "user2"]]
    }

    /**
     * 测试getReceiveIds方法处理基本收件人
     */
    def "getReceiveIdsTestBasicRecipients"() {
        given:
        User user = User.systemUser("7768")
        def sendCrmRemindPojo = new SendCrmRemindPojo()
        sendCrmRemindPojo.recipients = ["person": ["user1", "user2"], "dept": ["dept1"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"
        getEmployeeManager.getReceives(user, "TestObj", "123", sendCrmRemindPojo.recipients) >> ["1", "2", "3"].toSet()

        def result = sendCRMRemindAction.getReceiveIds(user, sendCrmRemindPojo, arg, objectData, describe, button)

        then:
        result == [1, 2, 3].toSet()
    }

    /**
     * 测试getReceiveIds方法处理变量收件人 - 员工类型
     */
    def "getReceiveIdsTestEmployeeVariables"() {
        given:
        User user = User.systemUser("7768")
        def sendCrmRemindPojo = new SendCrmRemindPojo()
        sendCrmRemindPojo.recipients = ["vars": ["emp_var"], "person": ["user1"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        Variable empVar = Mock(Variable)

        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"

        empVar.getFieldType() >> IFieldType.EMPLOYEE
        empVar.getValue() >> new ArrayList<String>(["emp1", "emp2"])

        getEmployeeManager.getReceives(user, "TestObj", "123", _) >> ["1", "2", "3"].toSet()

        def result = sendCRMRemindAction.getReceiveIds(user, sendCrmRemindPojo, arg, objectData, describe, button)

        then:
        result == [1, 2, 3].toSet()
        1 * parseVarService.getVarList(["emp_var"], [:], objectData, user, describe, button) >> [empVar]
    }

    /**
     * 测试getReceiveIds方法处理变量收件人 - 多选员工类型
     */
    def "getReceiveIdsTestEmployeeManyVariables"() {
        given:
        User user = User.systemUser("7768")
        def sendCrmRemindPojo = new SendCrmRemindPojo()
        sendCrmRemindPojo.recipients = ["vars": ["emp_many_var"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        Variable empManyVar = Mock(Variable)

        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"

        empManyVar.getFieldType() >> IFieldType.EMPLOYEE_MANY
        empManyVar.getValue() >> new ArrayList<String>(["emp1", "emp2", "emp3"])

        getEmployeeManager.getReceives(user, "TestObj", "123", _) >> ["1", "2", "3"].toSet()

        def result = sendCRMRemindAction.getReceiveIds(user, sendCrmRemindPojo, arg, objectData, describe, button)

        then:
        result == [1, 2, 3].toSet()
        1 * parseVarService.getVarList(["emp_many_var"], [:], objectData, user, describe, button) >> [empManyVar]
    }

    /**
     * 测试getReceiveIds方法处理变量收件人 - 部门类型
     */
    def "getReceiveIdsTestDepartmentVariables"() {
        given:
        User user = User.systemUser("7768")
        def sendCrmRemindPojo = new SendCrmRemindPojo()
        sendCrmRemindPojo.recipients = ["vars": ["dept_var"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        Variable deptVar = Mock(Variable)

        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"

        deptVar.getFieldType() >> IFieldType.DEPARTMENT
        deptVar.getValue() >> new ArrayList<String>(["dept1", "dept2"])

        getEmployeeManager.getReceives(user, "TestObj", "123", _) >> ["1", "2", "3", "4"].toSet()

        def result = sendCRMRemindAction.getReceiveIds(user, sendCrmRemindPojo, arg, objectData, describe, button)

        then:
        result == [1, 2, 3, 4].toSet()
        1 * parseVarService.getVarList(["dept_var"], [:], objectData, user, describe, button) >> [deptVar]
    }

    /**
     * 测试getReceiveIds方法处理变量收件人 - 多选部门类型
     */
    def "getReceiveIdsTestDepartmentManyVariables"() {
        given:
        User user = User.systemUser("7768")
        def sendCrmRemindPojo = new SendCrmRemindPojo()
        sendCrmRemindPojo.recipients = ["vars": ["dept_many_var"], "dept": ["existing_dept"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        Variable deptManyVar = Mock(Variable)

        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"

        deptManyVar.getFieldType() >> IFieldType.DEPARTMENT_MANY
        deptManyVar.getValue() >> new ArrayList<String>(["dept1", "dept2"])

        getEmployeeManager.getReceives(user, "TestObj", "123", _) >> ["1", "2", "3", "4", "5"].toSet()

        def result = sendCRMRemindAction.getReceiveIds(user, sendCrmRemindPojo, arg, objectData, describe, button)

        then:
        result == [1, 2, 3, 4, 5].toSet()
        1 * parseVarService.getVarList(["dept_many_var"], [:], objectData, user, describe, button) >> [deptManyVar]
    }

    /**
     * 测试getReceiveIds方法处理变量值为null的场景
     */
    def "getReceiveIdsTestVariableNullValue"() {
        given:
        User user = User.systemUser("7768")
        def sendCrmRemindPojo = new SendCrmRemindPojo()
        sendCrmRemindPojo.recipients = ["vars": ["emp_var"], "person": ["user1"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        Variable empVar = Mock(Variable)

        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"

        empVar.getFieldType() >> IFieldType.EMPLOYEE
        empVar.getValue() >> null // null值

        getEmployeeManager.getReceives(user, "TestObj", "123", ["person": ["user1"]]) >> ["1"].toSet()

        def result = sendCRMRemindAction.getReceiveIds(user, sendCrmRemindPojo, arg, objectData, describe, button)

        then:
        result == [1].toSet()
        1 * parseVarService.getVarList(["emp_var"], [:], objectData, user, describe, button) >> [empVar]
    }

    /**
     * 测试replaceVars方法处理变量替换
     */
    def "replaceVarsTestNormal"() {
        given:
        User user = User.systemUser("7768")
        String content = "Hello \$name\$, welcome to \$company\$!"
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        when:
        arg.getArgs() >> [:]
        parseVarService.getDisplayData("name", [:], objectData, user, describe, button) >> "John Doe"
        parseVarService.getDisplayData("company", [:], objectData, user, describe, button) >> "ABC Corp"

        def result = sendCRMRemindAction.replaceVars(user, content, arg, objectData, describe, button)

        then:
        result == "Hello John Doe, welcome to ABC Corp!"
    }

    /**
     * 测试replaceVars方法处理null数据
     */
    def "replaceVarsTestNullData"() {
        given:
        User user = User.systemUser("7768")
        String content = "Hello \$name\$, your score is \$score\$"
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        when:
        arg.getArgs() >> [:]
        parseVarService.getDisplayData("name", [:], objectData, user, describe, button) >> "John Doe"
        parseVarService.getDisplayData("score", [:], objectData, user, describe, button) >> null

        def result = sendCRMRemindAction.replaceVars(user, content, arg, objectData, describe, button)

        then:
        result == "Hello John Doe, your score is "
    }

    /**
     * 测试replaceVars方法处理无变量的内容
     */
    def "replaceVarsTestNoVariables"() {
        given:
        User user = User.systemUser("7768")
        String content = "This is a simple message without variables"
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        when:
        def result = sendCRMRemindAction.replaceVars(user, content, arg, objectData, describe, button)

        then:
        result == "This is a simple message without variables"
        0 * parseVarService.getDisplayData(_, _, _, _, _, _)
    }

    /**
     * 测试startCustomButton方法完整流程
     */
    def "startCustomButtonTestCompleteFlow"() {
        given:
        IObjectData objectData = Mock(IObjectData)
        Map<String, List<IObjectData>> details = [:]
        User user = User.systemUser("7768")
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IUdefAction action = Mock(IUdefAction)
        IUdefButton button = Mock(IUdefButton)
        IObjectDescribe describe = Mock(IObjectDescribe)

        def sendCrmRemindPojo = new SendCrmRemindPojo()
        sendCrmRemindPojo.content = "Test content \$name\$"
        sendCrmRemindPojo.title = "Test title \$company\$"
        sendCrmRemindPojo.recipients = ["person": ["user1"]]

        when:
        action.getActionParamter() >> JSON.toJSONString(sendCrmRemindPojo)
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"
        arg.getArgs() >> [:]

        parseVarService.getDisplayData("name", [:], objectData, user, describe, button) >> "John"
        parseVarService.getDisplayData("company", [:], objectData, user, describe, button) >> "TestCorp"
        getEmployeeManager.getReceives(user, "TestObj", "123", sendCrmRemindPojo.recipients) >> ["1"].toSet()

        def result = sendCRMRemindAction.startCustomButton(objectData, details, user, arg, action, button, describe)

        then:
        1 * proxy.sendNewCrmNotification(user, _) >> { args ->
            def notification = args[1] as NewCrmNotification
            assert notification.senderId == user.getUserId()
            assert notification.remindSender == true
            assert notification.fullContent == "Test content John"
            assert notification.title == "Test title TestCorp"
            assert notification.type == 92
            assert notification.receiverIDs == [1].toSet()
            assert notification.appId != null
        }
        result == null
    }

    /**
     * 测试getReceiveIds方法处理混合变量类型
     */
    def "getReceiveIdsTestMixedVariables"() {
        given:
        User user = User.systemUser("7768")
        def sendCrmRemindPojo = new SendCrmRemindPojo()
        sendCrmRemindPojo.recipients = ["vars": ["emp_var", "dept_var"], "person": ["user1"]]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        Variable empVar = Mock(Variable)
        Variable deptVar = Mock(Variable)

        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"

        empVar.getFieldType() >> IFieldType.EMPLOYEE
        empVar.getValue() >> new ArrayList<String>(["emp1", "emp2"])

        deptVar.getFieldType() >> IFieldType.DEPARTMENT
        deptVar.getValue() >> new ArrayList<String>(["dept1"])

        getEmployeeManager.getReceives(user, "TestObj", "123", _) >> ["1", "2", "3", "4", "5"].toSet()

        def result = sendCRMRemindAction.getReceiveIds(user, sendCrmRemindPojo, arg, objectData, describe, button)

        then:
        result == [1, 2, 3, 4, 5].toSet()
        1 * parseVarService.getVarList(["emp_var", "dept_var"], [:], objectData, user, describe, button) >> [empVar, deptVar]
    }

    /**
     * 测试getReceiveIds方法处理空的recipients场景
     */
    def "getReceiveIdsTestEmptyRecipients"() {
        given:
        User user = User.systemUser("7768")
        def sendCrmRemindPojo = new SendCrmRemindPojo()
        sendCrmRemindPojo.recipients = [:]
        ButtonExecutor.Arg arg = Mock(ButtonExecutor.Arg)
        IObjectData objectData = Mock(IObjectData)
        IObjectDescribe describe = Mock(IObjectDescribe)
        IUdefButton button = Mock(IUdefButton)

        when:
        arg.getArgs() >> [:]
        describe.getApiName() >> "TestObj"
        objectData.getId() >> "123"
        getEmployeeManager.getReceives(user, "TestObj", "123", [:]) >> [].toSet()

        def result = sendCRMRemindAction.getReceiveIds(user, sendCrmRemindPojo, arg, objectData, describe, button)

        then:
        result == [].toSet()
        0 * parseVarService.getVarList(_, _, _, _, _, _)
    }
} 