package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ComponentActions;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.config.ButtonConfig;
import com.facishare.paas.appframework.metadata.layout.component.IComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ISummaryComponentInfo;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentInfo;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.IMultiTableComponent;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.ObjectAction.IMPORT_EXCEL;
import static com.facishare.paas.appframework.metadata.LayoutButtonExt.TERMINAL;
import static com.facishare.paas.appframework.metadata.LayoutButtonExt.TERMINAL_TYPE_WEB;

public class EditLayout {

    private static final Set<ObjectAction> MOBILE_HIDDEN_ACTIONS = Sets.newHashSet(ObjectAction.CREATE_SAVE,
            ObjectAction.CANCEL, ObjectAction.UPDATE_SAVE);
    public static final Set<String> MOBILE_HIDDEN_ACTION_CODES = MOBILE_HIDDEN_ACTIONS.stream()
            .map(ObjectAction::getActionCode).collect(Collectors.toSet());
    public static final Set<String> MOBILE_HIDDEN_BUTTON_NAMES = MOBILE_HIDDEN_ACTIONS.stream()
            .map(ObjectAction::getButtonApiName).collect(Collectors.toSet());
    public static final String EDIT_PAGE_LABEL = "(新建/编辑页)";

    @Getter
    @Delegate
    private ILayout layout;

    private EditLayout(ILayout layout) {
        this.layout = layout;
    }

    public static EditLayout of(ILayout layout) {
        return new EditLayout(layout);
    }

    public static EditLayout of(Map layoutDoc) {
        return new EditLayout(new Layout(layoutDoc));
    }

    public static String buildStatusKey(String describeApiName, String appId) {
        if (Strings.isNullOrEmpty(appId)) {
            return describeApiName + "|" + "edit_layout_status";
        }
        return describeApiName + "|" + "edit_layout_status" + "|" + appId;
    }

    public static String buildLocalStatusKey(String tenantId, String describeApiName, String appId) {
        if (Strings.isNullOrEmpty(appId)) {
            return tenantId + "|" + describeApiName;
        }
        return tenantId + "|" + describeApiName + "|" + appId;

    }

    private static String buildBatchAddButtonName(String lookupFieldName) {
        return ObjectAction.BATCH_LOOKUP_CREATE.getButtonApiName() + "_" + lookupFieldName;
    }

    private static IButton buildBatchAddButton(IObjectReferenceField lookupField) {
        IButton batchAddButton = ObjectAction.BATCH_LOOKUP_CREATE.createButton();
        batchAddButton.setName(buildBatchAddButtonName(lookupField.getApiName()));
        batchAddButton.setLabel(ObjectAction.BATCH_LOOKUP_CREATE.getActionLabel(lookupField.getLabel()));
        LayoutButtonExt.of(batchAddButton).setLookupFieldName(lookupField.getApiName());
        LayoutButtonExt.of(batchAddButton).setLookupObjectName(lookupField.getTargetApiName());
        return batchAddButton;
    }

    public static List<IButton> buildDefaultListNormalDetailObjButtons(IObjectDescribe detailDescribe) {
        return buildDefaultListNormalDetailObjButtons(detailDescribe, false);
    }

    public static List<IButton> buildDefaultListNormalDetailObjButtons(IObjectDescribe detailDescribe, boolean isMobileLayout) {
        List<IButton> detailButtons = getPresetButtonsByComponentActions(ComponentActions.DETAIL_WITH_MASTER_EDIT_PAGE_DETAIL_NORMAL, isMobileLayout);
        ObjectDescribeExt.of(detailDescribe).getActiveSingleReferenceFieldDescribes().forEach(lookupField ->
                detailButtons.add(buildBatchAddButton(lookupField)));
        detailButtons.forEach(x -> LayoutButtonExt.of(x).setRenderType(ButtonUsePageType.ListNormal.getId()));
        //根据配置过滤按钮
        Map<String, List<String>> removeButtons = ButtonConfig.MASTER_DETAIL_EDIT_DETAIL_LIST_NORMAL_REMOVE_BUTTONS;
        detailButtons.removeIf(x -> removeButtons.getOrDefault(detailDescribe.getApiName(),
                Collections.emptyList()).contains(x.getName()));

        //根据黑名单过滤按钮
        ButtonExt.filterButtonsByBlacklist(detailButtons, detailDescribe.getApiName());
        ButtonExt.filterButtonsByBlacklist(detailButtons, ObjectDescribeExt.of(detailDescribe).isBiObject());
        return detailButtons;
    }

    private static List<IButton> getPresetButtonsByComponentActions(ComponentActions detailWithMasterEditPageDetailNormal) {
        return getPresetButtonsByComponentActions(detailWithMasterEditPageDetailNormal, false);
    }

    private static List<IButton> getPresetButtonsByComponentActions(ComponentActions detailWithMasterEditPageDetailNormal, boolean isMobileLayout) {
        List<IButton> actionButtons = detailWithMasterEditPageDetailNormal.getActionButtons();
        if (detailWithMasterEditPageDetailNormal == ComponentActions.DETAIL_WITH_MASTER_EDIT_PAGE_DETAIL_NORMAL) {
            // 读取excel
            RequestContext context = RequestContextManager.getContext();
            if (Objects.nonNull(context) && StringUtils.isNotEmpty(context.getTenantId())
                    && !isMobileLayout
                    && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EXCEL_IMPORT_NORMAL_BUTTON_GRAY, context.getTenantId())) {
                actionButtons.forEach(button -> {
                    if (StringUtils.equals(button.getAction(), IMPORT_EXCEL.getActionCode())) {
                        button.set(TERMINAL, TERMINAL_TYPE_WEB);
                    }
                });
                return actionButtons;
            }
            actionButtons.removeIf(x -> StringUtils.equals(x.getAction(), IMPORT_EXCEL.getActionCode()));
        }
        return actionButtons;
    }

    public static List<IButton> buildDefaultListBatchDetailObjButtons(String detailObjApiName) {
        List<IButton> buttons = getPresetButtonsByComponentActions(ComponentActions.DETAIL_WITH_MASTER_EDIT_PAGE_DETAIL_BATCH);
        //根据配置过滤按钮
        Map<String, List<String>> removeButtons = ButtonConfig.MASTER_DETAIL_EDIT_DETAIL_LIST_BATCH_REMOVE_BUTTONS;
        buttons.removeIf(x -> removeButtons.getOrDefault(detailObjApiName, Collections.emptyList()).contains(x.getName()));

        ButtonExt.filterButtonsByBlacklist(buttons, detailObjApiName);
        return buttons;
    }

    public static List<IButton> buildDefaultListSingleDetailObjButtons(String tenantId, String detailObjApiName) {
        List<IButton> buttons = getPresetButtonsByComponentActions(ComponentActions.DETAIL_WITH_MASTER_EDIT_PAGE_DETAIL_SINGLE);
        //补充平铺按钮
        if (AppFrameworkConfig.isInMasterDetailEditPageDetailSingleButtonGray(tenantId, detailObjApiName)) {
            buttons.add(ObjectAction.TILE.createButton());
        }
        //补充插入按钮
        if (AppFrameworkConfig.isInMasterDetailEditPageDetailSingleButtonInsertGray(tenantId, detailObjApiName)) {
            buttons.add(ObjectAction.INSERT.createButton());
        }
        //根据配置过滤按钮
        Map<String, List<String>> removeButtons = ButtonConfig.MASTER_DETAIL_EDIT_DETAIL_LIST_SINGLE_REMOVE_BUTTONS;
        buttons.removeIf(x -> removeButtons.getOrDefault(detailObjApiName, Collections.emptyList()).contains(x.getName()));

        ButtonExt.filterButtonsByBlacklist(buttons, detailObjApiName);
        return buttons;
    }

    @Deprecated
    public static List<String> buildDefaultListNormalDetailObjButtonNames(IObjectDescribe detailDescribe) {
        return buildDefaultListNormalDetailObjButtonNames(detailDescribe, false);
    }

    public static List<String> buildDefaultListNormalDetailObjButtonNames(IObjectDescribe detailDescribe, boolean isMobileLayout) {
        return buildDefaultListNormalDetailObjButtons(detailDescribe, isMobileLayout)
                .stream()
                .map(IButton::getName)
                .collect(Collectors.toList());
    }

    public static List<String> buildDefaultListBatchDetailObjButtonNames(String detailObjApiName) {
        return buildDefaultListBatchDetailObjButtons(detailObjApiName)
                .stream()
                .map(x -> x.getName())
                .collect(Collectors.toList());
    }

    public static List<String> buildDefaultListSingleDetailObjButtonNames(String tenantId, String detailObjApiName) {
        return buildDefaultListSingleDetailObjButtons(tenantId, detailObjApiName)
                .stream()
                .map(x -> x.getName())
                .collect(Collectors.toList());
    }

    public static void processDetailRenderType(IComponent detailObjComponent, String tenantId, String masterObjectApiName) {
        if (!AppFrameworkConfig.isGrayMobileCardRenderType(tenantId, masterObjectApiName)) {
            return;
        }
        if (Strings.isNullOrEmpty(ComponentExt.of(detailObjComponent).getRenderType())) {
            ComponentExt.of(detailObjComponent).setRenderType(ComponentRenderTypes.CARD);
        }
    }

    public void processDetailRenderType(String tenantId, String masterObjectApiName) {
        if (!AppFrameworkConfig.isGrayMobileCardRenderType(tenantId, masterObjectApiName)) {
            return;
        }
        LayoutExt.of(layout).getMasterDetailComponents().forEach(x -> processDetailRenderType(x, tenantId, masterObjectApiName));
    }

    public void mergeDetailComponents(String tenantId, String masterObjectApiName, LayoutExt webLayout) {
        processDetailRenderType(tenantId, masterObjectApiName);
        processSummaryInfo(tenantId, webLayout);
    }

    public void processSummaryInfo(String tenantId, LayoutExt webLayout) {
        List<IComponent> webMasterDetailComponents = webLayout.getMasterDetailComponents();
        LayoutExt.of(layout).getMasterDetailComponents().forEach(component -> syncSummaryInfo(ListComponentExt.of(component), webMasterDetailComponents));
    }

    private void syncSummaryInfo(ListComponentExt detailObjComponent, List<IComponent> components) {
        if (!detailObjComponent.isDisplayTotalsFromMobile()) {
            detailObjComponent.setAllPageSummaryComponentInfo(Collections.emptyList());
            return;
        }
        Map<String, IComponent> componentMap = components.stream()
                .collect(Collectors.toMap(IComponent::getName, Function.identity()));
        Optional.ofNullable(componentMap.get(detailObjComponent.getName()))
                .ifPresent(component -> {
                    List<ISummaryComponentInfo> allPageSummaryComponentInfo = ListComponentExt.of(component).getAllPageSummaryComponentInfoByPageType(IComponentInfo.PAGE_TYPE_LIST);
                    detailObjComponent.setAllPageSummaryComponentInfo(allPageSummaryComponentInfo);
                });
    }

    @Deprecated
    public List<IButton> getListNormalDetailObjButtons(IObjectDescribe detailDescribe, ILayout detailLayout) {
        return getListNormalDetailObjButtons(detailDescribe, detailLayout, false);
    }

    public List<IButton> getListNormalDetailObjButtons(IObjectDescribe detailDescribe, ILayout detailLayout, boolean isMobileLayout) {
        List<IButton> result = getPresetButtonsByComponentActions(ComponentActions.DETAIL_WITH_MASTER_EDIT_PAGE_DETAIL_NORMAL, isMobileLayout);
        ObjectDescribeExt.of(detailDescribe).getActiveSingleReferenceFieldDescribes().stream()
                .filter(lookupField -> EditLayout.of(detailLayout).containsField(lookupField.getApiName())
                        && !FormFieldExt.of(LayoutExt.of(detailLayout).getField(lookupField.getApiName()).get()).readOnly())
                .forEach(lookupField -> result.add(buildBatchAddButton(lookupField)));
        //根据配置过滤按钮
        Map<String, List<String>> removeButtons = ButtonConfig.MASTER_DETAIL_EDIT_DETAIL_LIST_NORMAL_REMOVE_BUTTONS;
        result.removeIf(x -> removeButtons.getOrDefault(detailDescribe.getApiName(),
                Collections.emptyList()).contains(x.getName()));

        return filterAndSortDetailButtons(result, ListComponentInfo.ButtonRenderType.LIST_NORMAL, detailDescribe.getApiName());
    }

    public List<IButton> getListBatchDetailObjButtons(String detailObjApiName) {
        List<IButton> result = getPresetButtonsByComponentActions(ComponentActions.DETAIL_WITH_MASTER_EDIT_PAGE_DETAIL_BATCH);
        //根据配置过滤按钮
        Map<String, List<String>> removeButtons = ButtonConfig.MASTER_DETAIL_EDIT_DETAIL_LIST_BATCH_REMOVE_BUTTONS;
        result.removeIf(x -> removeButtons.getOrDefault(detailObjApiName,
                Collections.emptyList()).contains(x.getName()));
        return filterAndSortDetailButtons(result, ListComponentInfo.ButtonRenderType.LIST_BATCH, detailObjApiName);
    }

    public List<IButton> getListSingleDetailObjButtons(String tenantId, String detailObjApiName) {
        List<IButton> buttons = buildDefaultListSingleDetailObjButtons(tenantId, detailObjApiName);
        return filterAndSortDetailButtons(buttons, ListComponentInfo.ButtonRenderType.LIST_SINGLE, detailObjApiName);
    }

    public List<ISummaryComponentInfo> getAllPageSummaryInfo(String detailObjApiName) {
        return LayoutExt.of(layout).getComponentByApiName(ComponentExt.getDetailComponentName(detailObjApiName))
                .map(ListComponentExt::of)
                .map(it -> it.getAllPageSummaryComponentInfoByPageType(IComponentInfo.PAGE_TYPE_LIST))
                .orElse(Collections.emptyList());
    }

    private List<IButton> filterAndSortDetailButtons(List<IButton> buttons, ListComponentInfo.ButtonRenderType renderType, String detailObjApiName) {
        Optional<IComponent> detailObjComponent = LayoutExt.of(layout).getComponentByApiName(ComponentExt.
                getDetailComponentName(detailObjApiName));
        if (!detailObjComponent.isPresent()) {
            return buttons;
        }
        return ComponentExt.of(detailObjComponent.get()).filterAndSortButtons(buttons, renderType);
    }

    @Deprecated
    public static List<IButton> buildDefaultButtonsAdd(String tenantId, String objectApiName) {
        List<IButton> buttons = getPresetButtonsByComponentActions(ComponentActions.getAddPageActions(objectApiName));
        ButtonExt.filterButtonsByBlacklist(buttons, objectApiName);
        buttons.removeIf(x -> ObjectAction.CREATE_SAVE_DRAFT.getActionCode().equals(x.getAction())
                && !AppFrameworkConfig.isSupportSaveDraftButton(tenantId, objectApiName));
        buttons.removeIf(x -> ObjectAction.CREATE_SAVE_CONTINUE.getActionCode().equals(x.getAction())
                && !AppFrameworkConfig.isSupportSaveAndCreate(tenantId));

        buttons.stream()
                .filter(x -> MOBILE_HIDDEN_ACTION_CODES.contains(x.getAction()))
                .forEach(x -> LayoutButtonExt.of(x).setTerminal(Lists.newArrayList(LayoutAgentType.WEB.getCode())));
        buttons.stream()
                .filter(x -> ObjectAction.CREATE_SAVE.getActionCode().equals(x.getAction()))
                .forEach(x -> x.setLabel(I18N.text(I18NKey.action_submit)));
        return buttons;
    }

    /**
     * 构建新建页面的默认按钮列表（带转换规则过滤器）
     *
     * @param tenantId 租户ID
     * @param objectApiName 对象API名称
     * @param convertRuleChecker 转换规则检查器，用于判断是否支持拉单功能
     * @return 按钮列表
     */
    public static List<IButton> buildDefaultButtonsAdd(String tenantId, String objectApiName,
                                                       BiFunction<User, String, Boolean> convertRuleChecker) {
        List<IButton> buttons = buildDefaultButtonsAdd(tenantId, objectApiName);

        // 应用转换规则过滤器，过滤 REFERENCE_CREATE 按钮
        if (convertRuleChecker != null) {
            buttons.removeIf(x -> ObjectAction.REFERENCE_CREATE.getActionCode().equals(x.getAction())
                    && !convertRuleChecker.apply(User.systemUser(tenantId), objectApiName));
        }

        return buttons;
    }

    @Deprecated
    public static List<IButton> buildDefaultButtonsEdit(String tenantId, String objectApiName) {
        List<IButton> buttons = getPresetButtonsByComponentActions(ComponentActions.EDIT_PAGE);
        ButtonExt.filterButtonsByBlacklist(buttons, objectApiName);
        buttons.removeIf(x -> ObjectAction.EDIT_SAVE_DRAFT.getActionCode().equals(x.getAction())
                && !AppFrameworkConfig.isSupportEditDraftButton(tenantId, objectApiName));
        buttons.stream()
                .filter(x -> MOBILE_HIDDEN_ACTION_CODES.contains(x.getAction()))
                .forEach(x -> LayoutButtonExt.of(x).setTerminal(Lists.newArrayList(LayoutAgentType.WEB.getCode())));
        buttons.stream()
                .filter(x -> ObjectAction.UPDATE_SAVE.getActionCode().equals(x.getAction()))
                .forEach(x -> x.setLabel(I18N.text(I18NKey.action_submit)));
        return buttons;
    }

    /**
     * 构建编辑页面的默认按钮列表（带转换规则过滤器）
     *
     * @param tenantId 租户ID
     * @param objectApiName 对象API名称
     * @param convertRuleChecker 转换规则检查器，用于判断是否支持拉单功能
     * @return 按钮列表
     */
    public static List<IButton> buildDefaultButtonsEdit(String tenantId, String objectApiName,
                                                        BiFunction<User, String, Boolean> convertRuleChecker) {
        List<IButton> buttons = buildDefaultButtonsEdit(tenantId, objectApiName);

        // 应用转换规则过滤器，过滤 REFERENCE_CREATE 按钮
        if (convertRuleChecker != null) {
            buttons.removeIf(x -> ObjectAction.REFERENCE_CREATE.getActionCode().equals(x.getAction())
                    && !convertRuleChecker.apply(User.systemUser(tenantId), objectApiName));
        }

        return buttons;
    }

    public void mergeDesignerLayoutButton(Map<ButtonUsePageType, List<IButton>> customButtonMap, BiFunction<User, String, Boolean> convertRuleChecker) {
        if (LayoutExt.of(layout).isFlowLayout()) {
            return;
        }
        List<IButton> buttonsAdd = getButtonsAdd(customButtonMap);
        List<IButton> buttonsEdit = getButtonsEdit(customButtonMap);

        List<ListComponentInfo> buttonInfo = getButtonInfo();
        buttonInfo.stream().forEach(x -> {
            if (x.useForAddPage()) {
                x.mergeWithButtons(buttonsAdd);
            } else if (x.useForEditPage()) {
                x.mergeWithButtons(buttonsEdit);
            }
        });
        setButtonInfo(buttonInfo);
    }

    public List<IButton> getButtonsAdd(Map<ButtonUsePageType, List<IButton>> customButtonMap) {
        List<IButton> buttonsAdd = buildDefaultButtonsAdd(fetchTenantId(), getRefObjectApiName());
        if (CollectionUtils.notEmpty(customButtonMap)) {
            List<IButton> customButtonsAdd = customButtonMap.getOrDefault(ButtonUsePageType.Create, Collections.emptyList());
            buttonsAdd = CollectionUtils.addIfAbsent(buttonsAdd, customButtonsAdd, (x, y) -> Objects.equals(x.getName(), y.getName()));
        }
        return buttonsAdd;
    }

    /**
     * 获取新建页面按钮列表（带转换规则过滤器）
     *
     * @param customButtonMap 自定义按钮映射
     * @param convertRuleChecker 转换规则检查器
     * @return 按钮列表
     */
    public List<IButton> getButtonsAdd(Map<ButtonUsePageType, List<IButton>> customButtonMap,
                                       BiFunction<User, String, Boolean> convertRuleChecker) {
        List<IButton> buttonsAdd = buildDefaultButtonsAdd(fetchTenantId(), getRefObjectApiName(), convertRuleChecker);
        if (CollectionUtils.notEmpty(customButtonMap)) {
            List<IButton> customButtonsAdd = customButtonMap.getOrDefault(ButtonUsePageType.Create, Collections.emptyList());
            buttonsAdd = CollectionUtils.addIfAbsent(buttonsAdd, customButtonsAdd, (x, y) -> Objects.equals(x.getName(), y.getName()));
        }
        return buttonsAdd;
    }

    public List<IButton> getButtonsEdit(Map<ButtonUsePageType, List<IButton>> customButtonMap) {
        List<IButton> buttonsEdit = buildDefaultButtonsEdit(fetchTenantId(), getRefObjectApiName());
        if (CollectionUtils.notEmpty(customButtonMap)) {
            List<IButton> customButtonsEdit = customButtonMap.getOrDefault(ButtonUsePageType.Edit, Collections.emptyList());
            buttonsEdit = CollectionUtils.addIfAbsent(buttonsEdit, customButtonsEdit, (x, y) -> Objects.equals(x.getName(), y.getName()));
        }
        return buttonsEdit;
    }

    /**
     * 获取编辑页面按钮列表（带转换规则过滤器）
     *
     * @param customButtonMap 自定义按钮映射
     * @param convertRuleChecker 转换规则检查器
     * @return 按钮列表
     */
    public List<IButton> getButtonsEdit(Map<ButtonUsePageType, List<IButton>> customButtonMap,
                                        BiFunction<User, String, Boolean> convertRuleChecker) {
        List<IButton> buttonsEdit = buildDefaultButtonsEdit(fetchTenantId(), getRefObjectApiName(), convertRuleChecker);
        if (CollectionUtils.notEmpty(customButtonMap)) {
            List<IButton> customButtonsEdit = customButtonMap.getOrDefault(ButtonUsePageType.Edit, Collections.emptyList());
            buttonsEdit = CollectionUtils.addIfAbsent(buttonsEdit, customButtonsEdit, (x, y) -> Objects.equals(x.getName(), y.getName()));
        }
        return buttonsEdit;
    }

    private String fetchTenantId() {
        String tenantId = getTenantId();
        if (StringUtils.isNotEmpty(tenantId)) {
            return tenantId;
        }
        RequestContext context = RequestContextManager.getContext();
        if (Objects.nonNull(context)) {
            return context.getTenantId();
        }
        return null;
    }

    public List<ListComponentInfo> getButtonInfo() {
        Optional<IComponent> headerInfo = LayoutExt.of(layout).getHeadInfoComponent();
        if (!headerInfo.isPresent()) {
            return Lists.newArrayList();
        }
        return ComponentExt.of(headerInfo.get()).getButtonInfo();
    }

    public void setButtonInfo(List<ListComponentInfo> buttonInfo) {
        LayoutExt.of(layout).getHeadInfoComponent().ifPresent(x -> ComponentExt.of(x).setButtonInfo(buttonInfo));
    }

    public IComponent removeHeadInfo() {
        IComponent headInfo = LayoutExt.of(layout).getHeadInfoComponent().orElse(null);
        if (headInfo == null) {
            return null;
        }
        WebDetailLayout.of(layout).removeComponents(Lists.newArrayList(ComponentExt.HEAD_INFO_COMPONENT_NAME));
        List<Map> rows = (List<Map>) layout.getLayoutStructure().get(LayoutStructure.LAYOUT);
        Iterator<Map> iterator = rows.iterator();
        while (iterator.hasNext()) {
            Map row = iterator.next();
            List<List<String>> columns = (List<List<String>>) row.get(LayoutStructure.COMPONENTS);
            if (CollectionUtils.empty(columns) || columns.stream().allMatch(x -> CollectionUtils.empty(x))) {
                iterator.remove();
            }
        }
        return headInfo;
    }

    public void placeFormComponentToFirst() {
        List<IComponent> components = LayoutExt.of(layout).getComponentsSilently();
        if (components.get(0).getName().equals(ComponentExt.FORM_COMPONENT)) {
            return;
        }
        IComponent formComponent = components.stream().filter(x -> ComponentExt.FORM_COMPONENT.equals(x.getName())).findFirst().orElse(null);
        if (formComponent == null) {
            return;
        }
        components.removeIf(x -> formComponent.getName().equals(x.getName()));
        components.add(0, formComponent);
        setComponents(components);
    }

    public boolean containsDetailObjComponent(String detailDescribeApiName) {
        return LayoutExt.of(layout).getComponentsSilently().stream()
                .anyMatch(x -> ComponentExt.of(x).isMasterDetailComponent()
                        && detailDescribeApiName.equals(((IMultiTableComponent) x).getRefObjectApiName()));
    }

    public void mergeFormComponents(LayoutExt webLayout) {
        LayoutExt mobileLayout = LayoutExt.of(this.layout);
        List<String> webLayoutForms = webLayout.getFormComponents().stream().map(IComponent::getName).collect(Collectors.toList());
        List<String> mobileLayoutForms = mobileLayout.getFormComponents().stream().map(IComponent::getName).collect(Collectors.toList());
        List<String> toRemoveForms = mobileLayoutForms.stream().filter(x -> !webLayoutForms.contains(x)).collect(Collectors.toList());
        List<String> toAddForms = webLayoutForms.stream().filter(x -> !mobileLayoutForms.contains(x)).collect(Collectors.toList());
        WebDetailLayout.of(mobileLayout).removeComponents(toRemoveForms);
        List<IComponent> sortedToAddForms = webLayout.getComponentByApiNames(toAddForms).stream()
                .sorted(Comparator.comparingInt(x -> x.getOrder())).collect(Collectors.toList());
        WebDetailLayout.of(mobileLayout).addComponents(sortedToAddForms);
    }

    public boolean containsField(String fieldName) {
        if (LayoutExt.of(this.layout).getFormComponents().stream().anyMatch(x -> x.containsField(fieldName))) {
            return true;
        }
        return LayoutExt.of(this.layout).getFormTables().stream().anyMatch(x -> x.containsField(fieldName));
    }

    public void removeField(String fieldName) {
        if (Strings.isNullOrEmpty(fieldName)) {
            return;
        }
        removeFields(Sets.newHashSet(fieldName));
    }

    public void removeFields(Collection<String> fieldNames) {
        LayoutExt.of(layout).removeFields(fieldNames);
    }

    public void removeButtons(List<String> buttonApiNames) {
        removeButtonsInComponent(ComponentExt.HEAD_INFO_COMPONENT_NAME, buttonApiNames);
    }

    public void removeDetailObjButtons(String detailApiName, List<String> buttonApiNames) {
        removeButtonsInComponent(ComponentExt.getDetailComponentName(detailApiName), buttonApiNames);
    }

    private void removeButtonsInComponent(String componentName, List<String> buttonApiNames) {
        if (!LayoutExt.of(layout).isEditLayout() || CollectionUtils.empty(buttonApiNames)) {
            return;
        }
        LayoutExt.of(layout).getComponentByApiName(componentName).ifPresent(component ->
                ComponentExt.of(component).getButtonInfo().forEach(buttonInfo ->
                        buttonInfo.removeButtons(buttonApiNames)));
    }

    public List<IComponent> getComponentForDetailLayout() {
        return LayoutExt.of(layout).getComponentsSilently().stream()
                .filter(x -> ComponentExt.of(x).isFormTable()
                        || ComponentExt.of(x).isFormType()
                        || ComponentExt.of(x).isTextComponent())
                .collect(Collectors.toList());
    }

    public List<IComponent> getComponentsWithField() {
        List<IComponent> formFormTableTextComponents = getComponentForDetailLayout();
        List<String> textComponentNamesInTable = LayoutExt.of(layout).getTextComponentNamesInTable();
        formFormTableTextComponents.removeIf(x -> ComponentExt.of(x).isTextComponent()
                && !textComponentNamesInTable.contains(x.getName()));
        return formFormTableTextComponents;
    }

    public enum EditLayoutStatus {
        DISABLE(0), ENABLE(1);

        private int code;

        EditLayoutStatus(int code) {
            this.code = code;
        }

        public int getCode() {
            return code;
        }

        public static boolean isEnable(int code) {
            return ENABLE.getCode() == code;
        }

        public static boolean isDisable(int code) {
            return DISABLE.getCode() == code;
        }
    }

}
