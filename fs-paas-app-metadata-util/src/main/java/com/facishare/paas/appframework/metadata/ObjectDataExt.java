package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.DeptInfo;
import com.facishare.paas.appframework.common.service.dto.DimensionInfo;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds;
import com.facishare.paas.appframework.common.service.dto.UserInfo;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18<PERSON><PERSON>ey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderChangedStatus;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderEffectiveStatus;
import com.facishare.paas.appframework.metadata.dto.LocationInfo;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.dao.pg.entity.metadata.InvalidData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.support.JsonFieldHandler;
import com.facishare.paas.metadata.util.IdUtil;
import com.facishare.paas.multiRegion.MultiRegionContextHolder;
import com.facishare.paas.multiRegion.MultiRegionDateTimeFormatUtils;
import com.facishare.paas.multiRegion.MultiRegionNumberFormatUtils;
import com.facishare.paas.multiRegion.utils.RoundMode;
import com.facishare.paas.timezone.*;
import com.facishare.rest.core.util.JsonUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.MapDifference;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.ibm.icu.util.ULocale;
import lombok.*;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.bson.Document;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.ObjectUtils;

import java.lang.Number;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.function.ToLongFunction;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.i18n.I18NKey.DATA_FIELD;
import static com.facishare.paas.appframework.core.i18n.I18NKey.FIELD_DATA_TYPE_WRONG;
import static com.facishare.paas.appframework.metadata.ObjectDescribeExt.PARTNER_ID_API_NAME;
import static com.facishare.paas.metadata.api.describe.IFieldType.*;
import static com.facishare.paas.metadata.api.i18n.MetadataI18NKey.DATA_FILE_IS_REQUIRED_ERROR;
import static com.facishare.paas.metadata.api.i18n.MetadataI18NKey.PATTERN_ILLEGAL;
import static org.apache.commons.collections4.CollectionUtils.select;

/**
 * 对元数据ObjectData 的扩展包装类 <p> Created by liyiguang on 2017/8/24.
 */
@Slf4j
public class ObjectDataExt implements IObjectData {

    public static final String SPECIAL_RELEVANT_TEAM = "SPECIAL_RELEVANT_TEAM";
    public static final String SYNCHRONIZED_MAP_CLASS_NAME = "SynchronizedMap";
    public static int MAX_SIZE = 2000;
    public static String RELEVANT_TEAM = "relevant_team";
    public static String OUT_RELEVANT_TEAM = "out_relevant_team";
    public static String OWNER = "owner";

    public static String CHANGE_OWNER = "change_owner";
    public static String OWNER_DEPARTMENT = "owner_department";
    public static String DATA_OWN_DEPARTMENT = "data_own_department";
    public static String OUT_DATA_OWN_DEPARTMENT = "out_data_own_department";
    public static String DATA_OWN_ORGANIZATION = "data_own_organization";
    public static String OUT_DATA_OWN_ORGANIZATION = "out_data_own_organization";
    public static String EXTEND_OBJ_DATA_ID = "extend_obj_data_id";
    public static String CREATED_BY = "created_by";
    public static String LAST_MODIFIED_BY = "last_modified_by";
    public static String SFA_STATUS_INVALID = "99";
    public static String OUTER_OWNER = "out_owner";
    public static String OUTER_TENANT = "out_tenant_id";
    public static String USER_ID = "user_id";
    public static String PARTNER_ID = "partner_id";
    public static String OBJECT_DATA_ID = "object_data_id";
    public static String OBJECT_API_NAME = "object_api_name";
    public static String OBJECT_API_NAME_HUMP = "objectApiName";
    public static String OBJECT_DATA_ID_HUMP = "objectDataId";
    public static String OUT_RESOURCES = "out_resources";
    public static final String MARK_API_NAME = "__mark__";  // UI事件标识数据
    public static final String IS_CURRENT_EDITING = "__current__";  // UI事件标识当前编辑数据
    public static final String IS_BATCH_CURRENT_EDITING = "__batch_current__";  // UI事件标识批量编辑数据
    public static final String IS_NEW_DATA = "__new_data__";  // UI事件标识当前编辑数据
    public static final String TEMPORARY_ID = "__temp_data_id__";
    public static final String UPDATED_TAG = "__updated";
    public static final String DATA_INDEX = "dataIndex";

    public static final String DEPT_ID = "dept_id";
    /**
     * 变更单对象字段
     */
    public static final String ORIGINAL_DATA = "original_data";
    public static final String ORIGINAL_DETAIL_DATA = "original_detail_data";
    public static final String CHANGED_BY = "changed_by";
    public static final String CHANGED_TIME = "changed_time";
    public static final String CHANGED_REASON = "changed_reason";
    public static final String EFFECTIVE_STATUS = "effective_status";
    public static final String EFFECTIVE_BY = "effective_by";
    public static final String EFFECTIVE_TIME = "effective_time";
    public static final String CHANGED_DATA = "changed_data";
    public static final String CHANGED_TYPE = "changed_type";
    public static final String VERSION_NUMBER = "version_number";
    public static final String CHANGED_STATUS = "changed_status";
    public static final String CHANGE_ORDER_RULE = "change_order_rule";

    public static final String CHANGED_DATA_ID = "__changed_data_id__";

    public static final String MASTER_DATA_FLAG = "__master_data__";

    //    private static final Pattern SPACEPATTERN = Pattern.compile("^\\s+|\\s+$", Pattern.UNICODE_CHARACTER_CLASS);
    public static final Pattern SPECIAL_CHAR_REGX = Pattern.compile("(?![\\u000A\\u000D])[\\u0000-\\u001F\\u007F-\\u00A0]");
    /**
     * 匹配 全角空格\u3000和不间断空格(NO-BREAK SPACE)\u00A0
     */
    public static final Pattern SPACE_CHAR_REGX = Pattern.compile("[\\u00A0\\u3000]");
    private static final String DEFAULT_MASK_VALUE = "********";
    private static final Set<String> SPECIAL_SYSTEM_FIELDS = Sets.newHashSet(IObjectData.OUT_TENANT_ID, IObjectData.OUT_OWNER);
    private static final int WHAT_LIST_DATA_MAX_COUNT = 20;

    private final List<String> REQUIRED_DATA_KEY = Lists.newArrayList("path");

    /**
     * Delegate 注解目前只能用接口类型
     */
    @Getter
    @Delegate
    private IObjectData objectData;

    private ObjectDataExt(@NonNull IObjectData objectData) {
        if (objectData instanceof ObjectDataExt) {
            this.objectData = ((ObjectDataExt) objectData).getObjectData();
        } else {
            this.objectData = objectData;
        }
    }

    public static void removeGeoField(List<IObjectData> objectDataList, List<IDuplicatedSearch> duplicatedSearchList) {
        Set<String> locationFields = Sets.newHashSet();
        duplicatedSearchList.forEach(x -> locationFields.addAll(DuplicatedSearchExt.of(x).getGeoFields()));
        if (CollectionUtils.empty(locationFields)) {
            return;
        }
        for (IObjectData objectData : objectDataList) {
            Set<String> geoFields = locationFields.stream().map(FieldDescribeExt::getLocalizationNameByFieldName).collect(Collectors.toSet());
            ObjectDataExt.of(objectData).remove(geoFields);
        }
    }

    public static void removeTempFillId(List<IObjectData> objectDataList, Map<String, Set<String>> fillFieldValueMap) {
        if (CollectionUtils.empty(objectDataList) || CollectionUtils.empty(fillFieldValueMap)) {
            return;
        }
        for (IObjectData objectData : objectDataList) {
            Set<String> apiNameList = fillFieldValueMap.get(objectData.getId());
            if (CollectionUtils.empty(apiNameList)) {
                continue;
            }
            ObjectDataExt.of(objectData).remove(apiNameList);
        }

    }

    public LocationInfo getLocalValue(String fieldName) {
        String o = get(fieldName, String.class);
        if (StringUtils.isBlank(o)) {
            return new LocationInfo();
        }
        String[] split = o.split(",");
        if (CollectionUtils.empty(Arrays.asList(split))) {
            return new LocationInfo();
        }
        if (StringUtils.isAnyBlank(split[0], split[1]) || split.length > 2) {
            log.warn("getLocalValue param error! tenantId:{},dataId:{},fieldName:{},value:{}"
                    , getTenantId(), getId(), fieldName, o);
            return new LocationInfo();
        }
        if (split[0].equals(split[1]) && "0.0".equals(split[0])) {
            log.warn("locationInfo is null ! tenantId:{},dataId:{},fieldName:{},value:{}"
                    , getTenantId(), getId(), fieldName, o);
            return new LocationInfo();
        }
        return LocationInfo.builder().longitude(BigDecimal.valueOf(Double.parseDouble(split[0])))
                .latitude(BigDecimal.valueOf(Double.parseDouble(split[1])))
                .build();

    }
//
//    public LocationInfo getLocalValue(String fieldName) {
//        String o = get(fieldName, String.class);
//        if (StringUtils.isBlank(o)) {
//            return new LocationInfo();
//        }
//        String[] split = o.split("#%\\$");
//        if (CollectionUtils.empty(Arrays.asList(split))) {
//            return new LocationInfo();
//        }
//        if (StringUtils.isAnyBlank(split[0], split[1])) {
//            log.error("getLocalValue param error! tenantId:{},dataId:{},fieldName:{},longitude:{},latitude:{}"
//                    , getTenantId(), getId(), fieldName, split[0], split[1]);
//        }
//        return LocationInfo.builder().longitude(BigDecimal.valueOf(Double.parseDouble(split[0])))
//                .latitude(BigDecimal.valueOf(Double.parseDouble(split[1])))
//                .build();
//
//    }

    public String getOwnerOrOutOwnerId(User user) {
        return getOwnerOrOutOwnerIdOptional(user).orElse(null);
    }

    public Optional<String> getOwnerOrOutOwnerIdOptional(User user) {
        return user.isOutUser() ? getOutOwnerId() : getOwnerId();
    }

    private static String validateSuffix(String path, String ext) {
        if (StringUtils.endsWith(path, ext)) {
            path = StringUtils.substringBeforeLast(path, "." + ext);
            return path;
        }
        return path;
    }

    public static void sortByOrderBy(List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        dataList.sort(Comparator.comparingInt(it -> Objects.isNull(it.getOrderBy()) ? Integer.MAX_VALUE : it.getOrderBy()));
    }

    public static void removeParentDeptSymbol(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        Set<String> deptSymbolField = Sets.newHashSet();
        objectDataList.forEach(objectData -> deptSymbolField.addAll(ObjectDataExt.toMap(objectData).keySet().stream()
                .filter(ObjectDataExt::isParentDeptSymbolField).collect(Collectors.toSet())));
        ObjectDataExt.remove(objectDataList, deptSymbolField);
    }

    public static boolean isParentDeptSymbolField(String fieldApiName) {
        return StringUtils.isNotBlank(fieldApiName) && fieldApiName.startsWith("$parentDept") && fieldApiName.endsWith("#");
    }

    public static void fillDataIndex(Map<String, List<IObjectData>> detailDataMap, Map<String, List<IObjectData>> oldDetailDataMap) {
        if (CollectionUtils.empty(detailDataMap) || CollectionUtils.empty(oldDetailDataMap)) {
            return;
        }
        detailDataMap.forEach((objectApiName, objectDataList) -> {
            if (CollectionUtils.empty(objectDataList)) {
                return;
            }
            List<IObjectData> oldDataList = oldDetailDataMap.get(objectApiName);
            if (CollectionUtils.empty(oldDataList)) {
                return;
            }
            Map<String, IObjectData> oldDataMap = oldDataList.stream()
                    .collect(Collectors.toMap(IObjectData::getId, Function.identity()));
            for (IObjectData data : objectDataList) {
                IObjectData oldData = oldDataMap.get(data.getId());
                if (Objects.nonNull(oldData)) {
                    of(oldData).setDataIndex(of(data).getDataIndex());
                }
            }
        });
    }

    public static void fillCountMultiRegionWithCurrency(Object value, IObjectData objectData, IFieldDescribe fieldDescribe) {
        if (Objects.isNull(value) || Objects.isNull(objectData) || Objects.isNull(fieldDescribe)) {
            return;
        }
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        if (!FieldDescribeExt.of(fieldDescribe).countOrCurrencyRealTypeIsCurrency()) {
            return;
        }
        if (StringUtils.isEmpty(MultiRegionContextHolder.getUserRegion())) {
            return;
        }
        Object format = MultiRegionNumberFormatUtils.formatForRegion(value, null, fieldDescribeExt.getDecimalPlaces(), RoundMode.getByCode(fieldDescribeExt.getRoundMode()));
        if (Objects.isNull(format)) {
            return;
        }
        objectData.set(FieldDescribeExt.getLookupNameByFieldName(fieldDescribeExt.getApiName()), format);
    }

    public static void mergeWithDbData(Map<String, List<IObjectData>> detailObjectData, Map<String, List<IObjectData>> dbDetailDataMap) {
        if (CollectionUtils.empty(detailObjectData) || CollectionUtils.empty(dbDetailDataMap)) {
            return;
        }
        detailObjectData.forEach((describeApiName, dataList) -> {
            List<IObjectData> dbDataList = dbDetailDataMap.getOrDefault(describeApiName, Collections.emptyList());
            mergeWithDbData(dataList, dbDataList);
        });
    }

    public static void mergeWithDbData(List<IObjectData> dataList, List<IObjectData> dbDataList) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(dbDataList)) {
            return;
        }
        List<ObjectDataExt> editDataList = dataList.stream()
                .map(ObjectDataExt::of)
                .filter(ObjectDataExt::hasId)
                .collect(Collectors.toList());
        Map<String, IObjectData> dbDataMap = dbDataList.stream().filter(Objects::nonNull).collect(Collectors.toMap(IObjectData::getId, Function.identity()));
        if (CollectionUtils.empty(editDataList) || CollectionUtils.empty(dbDataMap)) {
            return;
        }
        for (ObjectDataExt objectData : editDataList) {
            IObjectData dbData = dbDataMap.get(objectData.getId());
            if (Objects.nonNull(dbData)) {
                objectData.merge(dbData);
            }
        }
    }

    public Map<String, Object> toMap() {
        if (this.objectData instanceof ObjectData) {
            return this.covert().getContainerDocument();
        }
        return (Map<String, Object>) JSON.parseObject(toJsonString(), Map.class);
    }

    public Map<String, Object> toMap(List<String> fieldNames) {
        if (CollectionUtils.empty(fieldNames)) {
            return Maps.newHashMap();
        }
        Map<String, Object> result = Maps.newHashMap();
        fieldNames.forEach(x -> result.put(x, get(x)));

        return result;
    }

    public static ObjectDataExt of(Map<String, Object> data) {
        return new ObjectDataExt(new ObjectData(data));
    }

    public static ObjectDataExt of(IObjectData data) {
        return new ObjectDataExt(data);
    }

    public static Map<String, Object> toMap(IObjectData objectData) {
        return new ObjectDataExt(objectData).covert().getContainerDocument();
    }

    public static List<Map<String, Object>> toMapList(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return Lists.newArrayList();
        }
        return objectDataList.stream().map(ObjectDataExt::toMap).collect(Collectors.toList());
    }

    public static boolean isDecimal(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        return NumberUtils.isParsable(value) || NumberUtils.isCreatable(value);
    }

    public static boolean isMaskValue(Object value) {
        return DEFAULT_MASK_VALUE.equals(value);
    }

    public static List<IObjectData> copyList(List<IObjectData> sourceDataList) {
        if (CollectionUtils.empty(sourceDataList)) {
            return Lists.newArrayList();
        }
        return sourceDataList.stream().map(x -> ObjectDataExt.of(x).copy()).collect(Collectors.toList());
    }

    public static Map<String, List<IObjectData>> copyMap(Map<String, List<IObjectData>> dataMap) {
        Map<String, List<IObjectData>> cpDataMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(dataMap)) {
            dataMap.forEach((k, v) -> cpDataMap.put(k, copyList(v)));
        }
        return cpDataMap;
    }

    public static IObjectData synchronize(IObjectData data) {
        Map<String, Object> map = of(data).toMap();
        // 通过类名判断是否已经是同步Map
        if (map.getClass().getName().contains(SYNCHRONIZED_MAP_CLASS_NAME)) {
            return of(map).getObjectData();
        }
        return of(Collections.synchronizedMap(map)).getObjectData();
    }

    public static List<IObjectData> synchronize(List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return Lists.newArrayList();
        }
        return dataList.stream().map(ObjectDataExt::synchronize).collect(Collectors.toList());
    }

    public static String generateId() {
        return IdUtil.generateId();
    }

    public static List<IObjectData> fillTemporaryId(List<IObjectData> dataList) {
        List<IObjectData> result = Lists.newArrayList();
        for (IObjectData data : dataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            if (dataExt.hasTemporaryId()) {
                continue;
            }
            dataExt.fillTemporaryId();
            result.add(dataExt.getObjectData());
        }
        return result;
    }

    public static List<IObjectData> fillTemporaryId(Map<String, List<IObjectData>> dataListMap) {
        if (CollectionUtils.empty(dataListMap)) {
            return Collections.emptyList();
        }
        List<IObjectData> result = Lists.newArrayList();
        dataListMap.forEach((apiName, dataList) -> result.addAll(fillTemporaryId(dataList)));
        return result;
    }

    public static void removeTemporaryId(List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        for (IObjectData data : dataList) {
            ObjectDataExt.of(data).removeTemporaryId();
        }
    }

    public static void removeTemporaryId(Map<String, List<IObjectData>> detailList) {
        if (CollectionUtils.empty(detailList)) {
            return;
        }
        detailList.forEach((apiName, dateList) -> {
            removeTemporaryId(dateList);
        });
    }

    public static List<IObjectData> fillDataId(List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return Lists.newArrayList();
        }
        List<IObjectData> dataListToFillId = dataList.stream().filter(x -> !ObjectDataExt.of(x).hasId()).collect(Collectors.toList());
        dataListToFillId.forEach(x -> x.setId(generateId()));

        return dataListToFillId;
    }

    public static List<IObjectData> fillDataId(Map<String, List<IObjectData>> dataListMap) {
        if (CollectionUtils.empty(dataListMap)) {
            return Lists.newArrayList();
        }
        List<IObjectData> result = Lists.newArrayList();
        dataListMap.forEach((apiName, dataList) -> result.addAll(fillDataId(dataList)));
        return result;
    }

    public static Tuple<List<IObjectData>, List<IObjectData>> fillDataId(List<IObjectData> detailDataList, IObjectData masterData, IObjectDescribe detailDescribe) {
        List<IObjectData> noIdDataList = fillDataId(detailDataList);
        List<IObjectData> noMasterIdDataList = Lists.newArrayList();
        if (masterData != null) {
            noIdDataList.addAll(fillDataId(Lists.newArrayList(masterData)));
            ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldName(masterData.getDescribeApiName()).ifPresent(x -> {
                detailDataList.stream().filter(d -> ObjectDataExt.isValueEmpty(d.get(x))).forEach(d -> {
                    d.set(x, masterData.getId());
                    noMasterIdDataList.add(d);
                });
            });
        }
        return Tuple.of(noIdDataList, noMasterIdDataList);
    }

    public static void removeDataId(List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        dataList.forEach(x -> ObjectDataExt.of(x).remove(IObjectData.ID));
    }

    public static void removeDataId(Tuple<List<IObjectData>, List<IObjectData>> noIdDataTuple, IObjectDescribe detailDescribe) {
        if (noIdDataTuple == null) {
            return;
        }
        removeDataId(noIdDataTuple.getKey());
        List<IObjectData> noMasterIdDataList = noIdDataTuple.getValue();
        if (CollectionUtils.notEmpty(noMasterIdDataList)) {
            noMasterIdDataList.forEach(x -> ObjectDataExt.of(x).remove(ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldDescribe().get().getApiName()));
        }
    }

    public static List<IObjectData> fromInvalidDataList(List<InvalidData> invalidDataList) {
        if (CollectionUtils.empty(invalidDataList)) {
            return Lists.newArrayList();
        }
        return invalidDataList.stream().map(x -> {
            IObjectData objectData = new ObjectData();
            objectData.setTenantId(x.getTenantId());
            objectData.setDescribeApiName(x.getObjectDescribeApiName());
            objectData.setId(x.getDataId());
            objectData.setName(x.getName());
            objectData.setLastModifiedBy(x.getInvalidBy());
            objectData.setLastModifiedTime(x.getInvalidTime());

            return objectData;
        }).collect(Collectors.toList());
    }

    public String getDisplayNameOrName(IObjectDescribe describe) {
        if (Objects.isNull(describe)) {
            return StringUtils.isNotBlank(getDisplayName()) ? getDisplayName() : getName();
        }
        if (BooleanUtils.isTrue(describe.isOpenDisplayName())) {
            return StringUtils.isNotBlank(getDisplayName()) ? getDisplayName() : getName();
        }
        return getName();
    }

    public static Map<String, List<String>> getRefObjectDataIds(IObjectDescribe describe, List<IObjectData> dataList) {
        List<IFieldDescribe> refFieldList = ObjectDescribeExt.of(describe).getAllRefFieldDescribes();
        return getRefObjectDataIds(refFieldList, dataList);
    }

    public static Map<String, List<String>> getRefObjectDataIds(List<IFieldDescribe> refFieldList, List<IObjectData> dataList) {
        if (CollectionUtils.empty(refFieldList) || CollectionUtils.empty(dataList)) {
            return Maps.newHashMap();
        }

        Map<String, List<String>> idListMap = Maps.newHashMap();
        for (IFieldDescribe fieldDescribe : refFieldList) {
            //支持what数据类型
            if (ObjectDescribeExt.isWhatField(fieldDescribe)) {
                WhatFieldDescribe what = (WhatFieldDescribe) fieldDescribe;
                String apiNameFieldApiName = what.getApiNameFieldApiName();
                String objectIdFieldApiName = what.getIdFieldApiName();
                dataList.stream().filter(Objects::nonNull).forEach(it -> {
                    String apiName = (String) it.get(apiNameFieldApiName);
                    List<String> ids = idListMap.get(apiName);
                    String objectId = (String) it.get(objectIdFieldApiName);
                    if (!Strings.isNullOrEmpty(apiName) && !Strings.isNullOrEmpty(objectId)) {
                        if (ids == null) {
                            idListMap.put(apiName, Lists.newArrayList(objectId));
                        } else {
                            ids.add(objectId);
                        }
                    }
                });
            } else if (ObjectDescribeExt.isWhatListField(fieldDescribe)) {
                WhatListFieldDescribe whatListFieldDescribe = (WhatListFieldDescribe) fieldDescribe;
                String whatListFieldApiName = whatListFieldDescribe.getApiName();
                dataList.stream().filter(Objects::nonNull).forEach(it -> {
                    Map<String, List<String>> relatedObjectDataMap = CollectionUtils.nullToEmpty(it.get(whatListFieldApiName, Map.class));
                    relatedObjectDataMap.keySet().stream()
                            .filter(apiName -> !AppFrameworkConfig.isDisplayNameSupportWhatListFieldBlackListObject(apiName))
                            .forEach(apiName -> {
                                List<String> whatListDataIds = relatedObjectDataMap.get(apiName);
                                if (CollectionUtils.notEmpty(whatListDataIds)) {
                                    idListMap.computeIfAbsent(apiName, k -> Lists.newArrayList()).addAll(whatListDataIds);
                                }
                            });
                });
            } else {
                List<String> idList;
                String lookUpFieldTargetApiName;
                String lookUpApiName;
                if (Objects.equals(IFieldType.OBJECT_REFERENCE, fieldDescribe.getType())) {
                    ObjectReferenceFieldDescribe lookupField = (ObjectReferenceFieldDescribe) fieldDescribe;
                    lookUpApiName = lookupField.getApiName();
                    lookUpFieldTargetApiName = lookupField.getTargetApiName();
                } else if (Objects.equals(IFieldType.OBJECT_REFERENCE_MANY, fieldDescribe.getType())) {
                    ObjectReferenceManyFieldDescribe lookupField = (ObjectReferenceManyFieldDescribe) fieldDescribe;
                    lookUpApiName = lookupField.getApiName();
                    lookUpFieldTargetApiName = lookupField.getTargetApiName();
                } else {
                    MasterDetailFieldDescribe lookupField = (MasterDetailFieldDescribe) fieldDescribe;
                    lookUpApiName = lookupField.getApiName();
                    lookUpFieldTargetApiName = lookupField.getTargetApiName();
                }
                if (Objects.equals(IFieldType.OBJECT_REFERENCE_MANY, fieldDescribe.getType())) {
                    idList = new ArrayList<>();
                    dataList.stream().filter(data -> Objects.nonNull(data.get(lookUpApiName))).forEach(data -> {
                        Object o = data.get(lookUpApiName);
                        if (o instanceof List) {
                            List<String> t = (List<String>) o;
                            if (CollectionUtils.notEmpty(t)) {
                                idList.addAll(t);
                            }
                        }
                    });
                } else {
                    idList = dataList.stream()
                            .filter(it -> Objects.nonNull(it.get(lookUpApiName)) && !Strings.isNullOrEmpty(String.valueOf(it.get(lookUpApiName))))
                            .map(it -> String.valueOf(it.get(lookUpApiName))).collect(Collectors.toList());
                }


                if (lookUpFieldTargetApiName != null && CollectionUtils.notEmpty(idList)) {
                    if (idListMap.get(lookUpFieldTargetApiName) == null) {
                        idListMap.put(lookUpFieldTargetApiName, idList);
                    } else {
                        idListMap.get(lookUpFieldTargetApiName).addAll(idList);
                    }
                }
            }

        }

        //对关联对象的id去重
        Lists.newArrayList(idListMap.keySet()).forEach(k -> {
            List<String> distinctIds = idListMap.get(k).stream().distinct().collect(Collectors.toList());
            idListMap.put(k, distinctIds);
        });

        return idListMap;
    }

    public static List<Map<String, String>> getDataIdName(List<IObjectData> dataList) {
        return dataList.stream().map(x -> ObjectDataExt.of(x).getIdName()).collect(Collectors.toList());
    }

    public static boolean checkIfDataVersionConflict(IObjectData newData, IObjectData oldData) {
        if (newData == null || oldData == null) {
            return false;
        }
        if (!AppFrameworkConfig.isSupportDataVersionCheck(oldData.getTenantId())) {
            return false;
        }
        if (ObjectDataExt.isValueEmpty(newData.get(IObjectData.VERSION))
                || ObjectDataExt.isValueEmpty(oldData.get(IObjectData.VERSION))) {
            return false;
        }
        return newData.getVersion() < oldData.getVersion();
    }

    public static void checkDataVersion(IObjectData newData, IObjectData oldData) {
        boolean isVersionConflict = checkIfDataVersionConflict(newData, oldData);
        if (isVersionConflict) {
            throw new ValidateException(I18N.text(I18NKey.DATA_EXPIRED));
        }
    }

    public static List<String> getDataId(List<IObjectData> dataList) {
        return dataList.stream().map(x -> ObjectDataExt.of(x).getId()).collect(Collectors.toList());
    }

    public static Map<String, List<IObjectData>> groupByDescribeApiName(List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return Maps.newHashMap();
        }
        return dataList.stream().collect(Collectors.groupingBy(x -> x.getDescribeApiName()));
    }

    public static Map<Set<String>, List<IObjectData>> groupByFields(List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return Maps.newHashMap();
        }
        return dataList.stream().collect(Collectors.groupingBy(x -> Sets.newHashSet(ObjectDataExt.of(x).toMap().keySet())));
    }

    public static void setDataOwner(String ownerId, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        dataList.stream().map(ObjectDataExt::of).forEach(dataExt -> dataExt.setOwnerId(ownerId));
    }

    public static IObjectData convertDateFieldValueToSystemZone(IObjectDescribe describe, IObjectData data) {
        ObjectDataExt.of(data).convertDateFieldValueToSystemZone(describe);
        return data;
    }

    public static List<IObjectData> convertDateFieldValueToSystemZone(IObjectDescribe describe,
                                                                      List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return dataList;
        }
        List<String> fieldName = ObjectDescribeExt.of(describe).getRealTypeIsDate();
        if (CollectionUtils.empty(fieldName)) {
            return dataList;
        }
        return dataList.stream()
                .peek(data -> ObjectDataExt.of(data).convertDateFieldValueToSystemZone(fieldName))
                .collect(Collectors.toList());
    }

    public static IObjectData convertDateFieldValueToCustomZone(IObjectDescribe describe, IObjectData data) {
        ObjectDataExt.of(data).convertDateFieldValueToCustomZone(describe);
        return data;
    }

    public static List<IObjectData> convertDateFieldValueToCustomZone(IObjectDescribe describe,
                                                                      List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return dataList;
        }
        List<String> fieldName = ObjectDescribeExt.of(describe).getRealTypeIsDate();
        if (CollectionUtils.empty(fieldName)) {
            return dataList;
        }
        return dataList.stream()
                .peek(data -> ObjectDataExt.of(data).convertDateFieldValueToCustomZone(fieldName))
                .collect(Collectors.toList());
    }

    public static Map<String, List<Map<String, Object>>> convertData2Map(Map<String, List<IObjectData>> dataMap) {
        if (dataMap == null) {
            return null;
        }
        Map<String, List<Map<String, Object>>> ret = Maps.newHashMap();
        dataMap.forEach((k, v) -> ret.put(k, convertData2Map(v)));
        return ret;
    }

    public static List<Map<String, Object>> convertData2Map(List<IObjectData> dataList) {
        if (dataList == null) {
            return null;
        }
        return dataList.stream()
                .map(it -> ObjectDataExt.of(it).toMap())
                .collect(Collectors.toList());
    }

    public static Map<String, List<IObjectData>> convertMap2Data(Map<String, List<Map<String, Object>>> documentMap) {
        Map<String, List<IObjectData>> ret = Maps.newHashMap();
        documentMap.forEach((k, v) -> ret.put(k, convertMap2Data(v)));
        return ret;
    }

    public static List<IObjectData> convertMap2Data(List<Map<String, Object>> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return Lists.newArrayList();
        }
        return dataList.stream().map(x -> ObjectDataExt.of(x).getObjectData()).collect(Collectors.toList());
    }

    public static void setOrderForDetailData(Map<String, List<IObjectData>> detailObjectData) {
        if (CollectionUtils.empty(detailObjectData)) {
            return;
        }
        detailObjectData.forEach((detailApiName, dataList) -> {
            int order = 0;
            for (IObjectData data : dataList) {
                order += 10;
                data.setOrderBy(order);
            }
        });
    }

    public void setIsNewData() {
        set(IS_NEW_DATA, true);
    }

    public boolean isNewData() {
        return !hasId() || Boolean.TRUE.equals(get(IS_NEW_DATA));
    }

    public void setUpdatedTag() {
        set(UPDATED_TAG, true);
    }

    public boolean hasUpdatedTag() {
        return Boolean.TRUE.equals(get(UPDATED_TAG));
    }

    public Map<String, String> getIdName() {
        Map<String, String> map = Maps.newHashMap();
        map.put(IObjectData.DESCRIBE_API_NAME, getDescribeApiName());
        map.put(IObjectData.ID, getId());
        map.put(IObjectData.NAME, getName());
        return map;
    }

    public void putAll(Map<String, Object> dataMap) {
        if (CollectionUtils.notEmpty(dataMap)) {
            toMap().putAll(dataMap);
        }
    }

    public void fillObjectInfo(IObjectDescribe describe) {
        setTenantId(describe.getTenantId());
        setDescribeApiName(describe.getApiName());
    }

    public boolean containsExtendObjDataId() {
        return toMap().containsKey(EXTEND_OBJ_DATA_ID);
    }

    public String getExtendObjDataId() {
        return get(EXTEND_OBJ_DATA_ID, String.class);
    }

    public void setExtendObjDataId(String extendObjDataId) {
        set(EXTEND_OBJ_DATA_ID, extendObjDataId);
    }

    public boolean hasId() {
        return StringUtils.isNotBlank(getId());
    }

    public boolean isLock() {
        return ObjectLockStatus.LOCK.getStatus().equals(getLockStatus());
    }

    public boolean isIneffective() {
        return ObjectLifeStatus.INEFFECTIVE.equals(getLifeStatus());
    }

    public boolean isUnderReview() {
        return ObjectLifeStatus.UNDER_REVIEW.equals(getLifeStatus());
    }

    public boolean isInChange() {
        return ObjectLifeStatus.IN_CHANGE.equals(getLifeStatus());
    }

    public boolean isNormal() {
        return ObjectLifeStatus.NORMAL.equals(getLifeStatus());
    }

    public boolean isInvalid() {
        Boolean isInvalid = isDeleted();
        return isInvalid != null && isInvalid;
    }

    public void setOldFieldValue(String fieldName, Object value) {
        set(FieldDescribeExt.getOldFieldName(fieldName), value);
    }

    public Object getOldFieldValue(String fieldName) {
        return get(FieldDescribeExt.getOldFieldName(fieldName));
    }

    public IObjectData filter(final Predicate<String> apiNameFilter) {
        Map<String, Object> document = covert().getContainerDocument();
        Map<String, Object> filtered = document.entrySet().stream().filter(x -> apiNameFilter.test(x.getKey()))
                .collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue()));
        return new ObjectData(new Document(filtered));
    }

    public List<String> getManyField(String apiName) {
        try {
            Object value = get(apiName);
            if (null == value) {
                return Lists.newArrayList();
            }
            String str;
            if (value instanceof String) {
                str = (String) value;
            } else {
                str = JSON.toJSONString(value);
            }
            return JSONObject.parseObject(str, List.class);
        } catch (Exception e) {
            log.warn("getManyField error: {}, {}", getId(), apiName, e);
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
    }

    public Optional<String> getOutOwnerId() {
        return parseEmployeeValue(OUTER_OWNER);
    }

    public Optional<String> getOwnerId() {
        return parseEmployeeValue(OWNER);
    }

    public Optional<String> getChangeOwnerId() {
        return parseEmployeeValue(CHANGE_OWNER);
    }

    public Integer getOwnerIdInt() {
        Optional<String> owner = parseEmployeeValue(OWNER);
        return owner.isPresent() ? Integer.valueOf(owner.get()) : null;
    }

    @NotNull
    private Optional<String> parseEmployeeValue(String apiName) {
        Object owner = objectData.get(apiName);
        String ret = null;
        if (Objects.nonNull(owner)) {
            if (owner instanceof List) {
                List ownerList = (List) owner;
                if (!ownerList.isEmpty()) {
                    ret = String.valueOf(ownerList.get(0));
                }
            } else if (owner instanceof String) {
                ret = (String) owner;
            } else if (owner instanceof Integer) {
                ret = String.valueOf(owner);
            } else {
                throw new MetaDataException(SystemErrorCode.METADATA_OWNER_ID_ERROR);
            }
        }
        return Optional.ofNullable(ret);
    }

    public boolean hasOwner() {
        Optional<String> ownerOpt = getOwnerId().filter(owner -> !Strings.isNullOrEmpty(owner) && !"null".equals(owner));
        return ownerOpt.isPresent();
    }

    public boolean hasOutOwner() {
        return getOutOwnerId()
                .filter(outOwner -> !Strings.isNullOrEmpty(outOwner) && !"null".equals(outOwner))
                .isPresent();
    }

    public void setOwnerId(String ownerId) {
        Objects.requireNonNull(ownerId);
        setOwnerId(Lists.newArrayList(ownerId));
    }

    public void setOutTenantAndOutOwner(String outTenantId, String outOwnerId) {
        // 校验为空的情况
        if (StringUtils.isAnyBlank(outOwnerId, outOwnerId)) {
            return;
        }
        objectData.setOutOwner(Lists.newArrayList(outOwnerId));
        objectData.setOutTenantId(outTenantId);
    }

    public void setOutUser(User user) {
        setOutUser(user, false);
    }

    public void setOutUser(User user, boolean allowOwnerFromOutUser) {
        setOutUser(user, allowOwnerFromOutUser, false);
    }

    public void setOutUser(User user, boolean allowOwnerFromOutUser, boolean outUserAssignOwner) {
        OwnerPolicy policy = OwnerPolicy.builder()
                .allowOutUserByArg(allowOwnerFromOutUser)
                .outUserAssignOwner(outUserAssignOwner)
                .build();
        setOutUser(user, policy);
    }

    /**
     * 按策略设置外部负责人、创建人、互联企业、数据负责人
     *
     * @param user   用户
     * @param policy 策略
     */
    public void setOutUser(User user, OwnerPolicy policy) {
        if (user.isOutUser()) {
            // 当外部负责人为空时，设置外部负责人为当前操作人
            if (!hasOutOwner() || !policy.isSetOutOwnerWhenEmpty()) {
                objectData.set(OUTER_OWNER, Lists.newArrayList(user.getOutUserId()));
            }
            objectData.setCreatedBy(user.getOutUserId());
            objectData.set(OUTER_TENANT, user.getOutTenantId());
            //objectData中没有owner && 是否让外部用户指定负责人：为true则外部用户指定，不需要补值，为false则系统指定
            if (Objects.nonNull(policy) && policy.isOutUserAssignOwner()) {
                return;
            }
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, user.getTenantId()) && hasOwner()) {
                return;
            }
            //如下场景需要强制设置数据负责人
            //1、不允许下游人员指定负责人（调用者允许且企业灰度允许）
            //2、未设置负责人
            if ((Objects.nonNull(policy) && !policy.isAllowOutUserByArg())
                    || !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ALLOW_OWNER_FROM_OUT_USER_IN_ADD_ACTION_EI, user.getTenantId())
                    || !hasOwner()) {
                setDataOwner(user, policy);
            }
        }
    }

    public void setDataOwner(User user) {
        setDataOwner(user, null);
    }

    /**
     * 按策略设置数据负责人
     *
     * @param user   用户（或下游）
     * @param policy 分配负责人策略
     */
    public void setDataOwner(User user, OwnerPolicy policy) {
        if (Objects.isNull(user)) {
            return;
        }

        if (user.isOutUser()) {
            // 下游用户（end user）创建数据负责人优化：默认数据负责人从互联服务获取，现在只从 AddAction 设置了 defaultDataOwnerId
            // upstreamOwnerIdOrUserId 设置为 dataOwner 变为 orElse
            String defaultOwnerId = Optional.ofNullable(policy)
                    .map(OwnerPolicy::getDefaultDataOwnerId)
                    .map(Supplier::get)
                    .filter(StringUtils::isNotBlank)
                    .orElse(user.getUpstreamOwnerIdOrUserId());
            if (!Strings.isNullOrEmpty(defaultOwnerId)) {
                objectData.setOwner(Lists.newArrayList(defaultOwnerId));
            }
        } else {
            if (!Strings.isNullOrEmpty(user.getUserId())) {
                objectData.setOwner(Lists.newArrayList(user.getUserId()));
            }
        }
    }

    public void setDataOwnerIfAbsent(User user) {
        if (!hasOwner()) {
            setDataOwner(user);
        }
    }

    public void setDataOwnerIfAbsent(User user, OwnerPolicy policy) {
        if (!hasOwner()) {
            setDataOwner(user, policy);
        }
    }

    public void setDefaultTeamMember() {
        setDefaultTeamMember(false);
    }

    public void setDefaultTeamMember(boolean isFromChangeOrder) {
        Optional<String> ownerId = getOwnerId();
        if (isFromChangeOrder) {
            ownerId = getChangeOwnerId();
        }
        ownerId.ifPresent(x -> {
            TeamMember teamMember = new TeamMember(x, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE);
            addTeamMembers(Lists.newArrayList(teamMember));
        });
    }

    public void setDefaultOutOwner2TeamMember() {
        getOutOwnerId().ifPresent(x -> {
            if (!Strings.isNullOrEmpty(getOutTenantId())) {
                TeamMember teamMember = new TeamMember(x, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, getOutTenantId());
                addTeamMembers(Lists.newArrayList(teamMember));
            }
        });
    }

    public boolean addTeamMembers(List<TeamMember> teamMembers) {
        return addTeamMembers(teamMembers, false);
    }


    /**
     * 添加相关团队成员
     *
     * @param teamMemberList        待添加的团队成员列表
     * @param isMergeTeamMemberRole 是否合并团队成员角色
     * @return 如果实际修改了相关团队返回true，否则返回false
     */
    public boolean addTeamMembers(List<TeamMember> teamMemberList, boolean isMergeTeamMemberRole) {
        List<TeamMember> currentTeamMembers = getTeamMembers();
        List<TeamMember> newTeamMember = currentTeamMembers.stream()
                .filter(x -> !teamMemberList.contains(x))
                .collect(Collectors.toList());

        if (isMergeTeamMemberRole) {
            List<TeamMember> oldTeamMembers = currentTeamMembers.stream()
                    .filter(x -> isNotExistRoleTeamMember(teamMemberList, x))
                    .collect(Collectors.toList());
            newTeamMember.addAll(oldTeamMembers);
        }

        // 如果新的相关团队包含负责人、清空原先相关团队中的负责人
        List<TeamMember> teamMemberOwners = teamMemberList.stream()
                .filter(teamMember -> TeamMember.Role.OWNER == teamMember.getRole())
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(teamMemberOwners)) {
            for (TeamMember teamMemberOwner : teamMemberOwners) {
                newTeamMember.removeIf(
                        teamMember -> TeamMember.Role.OWNER == teamMember.getRole()
                                && teamMemberOwner.isOutMember() == teamMember.isOutMember()
                                && TeamMember.MemberType.EMPLOYEE.equals(teamMember.getMemberType()));
            }
        }
        // 如果新的相关团队中包含互联部门，则清空原先相关团队中的互联部门
        List<TeamMember> teamMemberInterconnectDepartments = teamMemberList.stream()
                .filter(x -> TeamMember.MemberType.INTERCONNECT_DEPARTMENT == x.getMemberType())
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(teamMemberInterconnectDepartments)) {
            newTeamMember.removeIf(x -> TeamMember.MemberType.INTERCONNECT_DEPARTMENT == x.getMemberType());
        }

        newTeamMember.addAll(teamMemberList);
        List<TeamMember> teamMembers = getTeamMembersWithOutRepeat(newTeamMember);

        setTeamMembers(teamMembers);
        // 比较修改前后的团队成员是否有变化
        if (teamMembers.size() == currentTeamMembers.size()
                && Sets.newHashSet(teamMembers).equals(Sets.newHashSet(currentTeamMembers))) {
            return false;
        }

        return true;
    }

    /**
     * 移除相关团队中的互联部门
     *
     * @return 如果移除了互联部门返回true，否则返回false
     */
    public boolean removeInterconnectDepartmentTeamMembers() {
        List<TeamMember> currentTeamMembers = getTeamMembers();
        List<TeamMember> newTeamMembers = currentTeamMembers.stream()
                .filter(x -> TeamMember.MemberType.INTERCONNECT_DEPARTMENT != x.getMemberType())
                .collect(Collectors.toList());

        // 如果新旧列表大小相同，说明没有移除任何成员
        if (currentTeamMembers.size() == newTeamMembers.size()) {
            return false;
        }

        setTeamMembers(newTeamMembers);
        return true;
    }

    private List<TeamMember> getTeamMembersWithOutRepeat(List<TeamMember> newTeamMember) {
        //去重角色
        List<TeamMember> teamMembers = Lists.newArrayList();
        for (TeamMember teamMember : newTeamMember) {
            if (!isContainRoleTeamMember(teamMembers, teamMember)) {
                teamMembers.add(teamMember);
            }
        }
        return teamMembers;
    }

    public boolean isContainRoleTeamMember(List<TeamMember> teamMemberList, TeamMember teamMember) {
        return CollectionUtils.nullToEmpty(teamMemberList).stream()
                .filter(x -> Objects.equals(x, teamMember))
                .map(TeamMember::getRoleCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList())
                .contains(teamMember.getRoleCode());
    }

    public boolean isNotExistRoleTeamMember(List<TeamMember> teamMemberList, TeamMember teamMember) {
        List<TeamMember> existTeamMember = CollectionUtils.nullToEmpty(teamMemberList).stream()
                .filter(x -> Objects.equals(x, teamMember))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(existTeamMember)) {
            return false;
        }
        List<String> oldRoleCodes = existTeamMember.stream().map(TeamMember::getRoleCode).collect(Collectors.toList());
        return !oldRoleCodes.contains(teamMember.getRoleCode());
    }


    /**
     * 1、如果新增的相关团队角色是负责人、则判断是否和数据上的负责人相同，相同则替换相关团队，不相同则降级为普通成员角色
     * 2、如果新增的相关团队的当前已有的相关团队重复、保留权限更大的
     *
     * @param teamMembers
     */
    public void addOuterTeamMembers(List<TeamMember> teamMembers) {
        if (CollectionUtils.empty(teamMembers)) {
            return;
        }
        // 只保留外部相关团队、并将负责人角色转换为普通成员
        List<TeamMember> addTeamMembers = teamMembers.stream()
                .filter(TeamMember::isOutMember)
                .map(it -> {
                    if (TeamMember.Role.OWNER == it.getRole()) {
                        return new TeamMember(it.getEmployee(), TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READANDWRITE, it.getOutTenantId(), it.getMemberType());
                    }
                    return it;
                }).collect(Collectors.toList());
        List<TeamMember> currentTeamMembers = getTeamMembers();
        if (CollectionUtils.empty(currentTeamMembers)) {
            setTeamMembers(addTeamMembers);
            return;
        }

        // 判断新增的相关团队成员和已有的团队成员是否重复
        List<TeamMember> matchTeamMembers = Lists.newArrayList();
        List<TeamMember> newTeamMembers = Lists.newArrayList();
        select(currentTeamMembers, addTeamMembers::contains, matchTeamMembers, newTeamMembers);

        // 团队成员重复，使用合并后的数据
        // 团队成员不重复，使用新增的团队成员
        for (TeamMember addMember : addTeamMembers) {
            boolean isMatch = false;
            for (TeamMember matchMember : matchTeamMembers) {
                TeamMember newMember = matchMember.merge(addMember);
                if (Objects.nonNull(newMember)) {
                    newTeamMembers.add(newMember);
                    isMatch = true;
                    break;
                }
            }
            if (!isMatch) {
                newTeamMembers.add(addMember);
            }
        }
        setTeamMembers(newTeamMembers);
    }

    public List<TeamMember> getTeamMembers() {
        Object o = objectData.get(RELEVANT_TEAM);
        if (Objects.isNull(o) || !(o instanceof List)) {
            o = Collections.emptyList();
        }
        List<Object> teamMembers = (List) o;
        return teamMembers.stream()
                .filter(it -> it instanceof Map)
                .map(it -> new TeamMember((Map<String, Object>) it))
                .collect(Collectors.toList());
    }

    public void setTeamMembers(List<TeamMember> teamMembers) {
        List<Map<String, Object>> mapList = teamMembers.stream().map(TeamMember::toMap).collect(Collectors.toList());
        objectData.set(RELEVANT_TEAM, mapList);
    }

    private void setOwnerId(List<String> ownerId) {
        objectData.set(OWNER, ownerId);
    }

    public void setOwnerDepartment(String deptName) {
        objectData.set(OWNER_DEPARTMENT, deptName);
    }

    public void fillDepartmentFieldObject(String fieldApiName, Object data) {
        objectData.set(fieldApiName + "__r", data);
    }

    @Deprecated
    public String getObjectDataId() {
        String objectDataId = objectData.get(OBJECT_DATA_ID, String.class);
        if (!Strings.isNullOrEmpty(objectDataId)) {
            return objectDataId;
        }
        return objectData.get(OBJECT_DATA_ID_HUMP, String.class);
    }

    public String getWhatObjectDataId(What what) {
        Objects.requireNonNull(what);
        String idField = what.getIdFieldApiName();
        return get(idField, String.class);
    }

    @Deprecated
    public String getObjectApiName() {
        String objectApiName = objectData.get(OBJECT_API_NAME, String.class);
        if (!Strings.isNullOrEmpty(objectApiName)) {
            return objectApiName;
        }
        return objectData.get(OBJECT_API_NAME_HUMP, String.class);
    }

    public String getWhatObjectApiName(What what) {
        Objects.requireNonNull(what);
        return get(what.getApiNameFieldApiName(), String.class);
    }

    private Map<String, Object> addApiNamePrefix() {
        String describeApiName = objectData.getDescribeApiName();
        Map<String, Object> dataMap = Maps.newHashMap();
        toMap().forEach((key, value) -> dataMap.put(String.format("%s.%s", describeApiName, key), value));
        return dataMap;
    }

    public void mergeWhatData(@NonNull IObjectData data) {
        Map<String, Object> dataMap = of(data).addApiNamePrefix();
        toMap().putAll(dataMap);
    }

    @Deprecated
    public IObjectData getWhatObjectData() {
        Map<String, Object> data = Maps.newHashMap();
        String objectApiName = getObjectApiName();
        if (!Strings.isNullOrEmpty(objectApiName)) {
            toMap().forEach((key, value) -> {
                if (Objects.nonNull(key) && key.contains(objectApiName)) {
                    data.put(key.substring(key.lastIndexOf('.') + 1), value);
                }
            });
        }

        ObjectDataExt dataExt = of(data);
        dataExt.setId(getObjectDataId());
        dataExt.setDescribeApiName(objectApiName);
        return dataExt.getObjectData();
    }

    public IObjectData getWhatObjectData(What what) {
        Objects.requireNonNull(what);
        Map<String, Object> data = Maps.newHashMap();
        String objectApiName = getWhatObjectApiName(what);
        if (!Strings.isNullOrEmpty(objectApiName)) {
            toMap().forEach((key, value) -> {
                if (Objects.nonNull(key) && key.contains(objectApiName)) {
                    data.put(key.substring(key.lastIndexOf('.') + 1), value);
                }
            });
        }

        ObjectDataExt dataExt = of(data);
        dataExt.setId(getWhatObjectDataId(what));
        dataExt.setDescribeApiName(objectApiName);
        return dataExt.getObjectData();
    }


    private ObjectData covert() {
        return (ObjectData) objectData;
    }

    public void validate(IObjectDescribe objectDescribe) {
        //和产品确认不再校验，元数据本身也会校验
//        String name = objectData.getName();
//        if (name != null && name.length() > 100) {
//            throw new ValidateException(I18N.text(I18NKey.NAME_LENGTH_EXCEED, 100));
//        }
        //校验单选其他选项的必填
        checkSelectOneOtherOption(ObjectDescribeExt.of(objectDescribe), objectData);
        //校验多选其他选项的必填
        checkSelectManyOtherOption(ObjectDescribeExt.of(objectDescribe), objectData);
    }

    private void checkSelectManyOtherOption(ObjectDescribeExt objectDescribe, IObjectData objectData) {
        List<IFieldDescribe> selectManyList = objectDescribe.filter(a -> Objects.equals(a.getType(), IFieldType.SELECT_MANY));
        if (CollectionUtils.empty(selectManyList)) {
            return;
        }

        for (IFieldDescribe field : selectManyList) {
            Object o = objectData.get(field.getApiName());
            if (Objects.isNull(o)) {
                continue;
            }

            SelectMany selectMany = (SelectMany) field;
            if (Objects.equals(SelectMany.OPTION_OTHER_VALUE, String.valueOf(o))
                    && Objects.equals(Boolean.TRUE, selectMany.getOptionOtherIsRequired())
                    && Strings.isNullOrEmpty(objectData.get(field.getApiName() + "__o", String.class))) {
                throw new ValidateException(I18N.text(I18NKey.SELECT_MANY_NOT_EMPTY, field.getLabel()));
            }
        }
    }

    private void checkSelectOneOtherOption(ObjectDescribeExt objectDescribe, IObjectData objectData) {
        List<IFieldDescribe> selectOneList = objectDescribe.filter(a -> Objects.equals(a.getType(), IFieldType.SELECT_ONE));
        if (CollectionUtils.empty(selectOneList)) {
            return;
        }

        for (IFieldDescribe field : selectOneList) {
            Object o = objectData.get(field.getApiName());
            if (Objects.isNull(o)) {
                continue;
            }

            SelectOne selectOne = (SelectOne) field;
            if (Objects.equals(SelectOne.OPTION_OTHER_VALUE, String.valueOf(o))
                    && Objects.equals(Boolean.TRUE, selectOne.getOptionOtherIsRequired())
                    && Strings.isNullOrEmpty(objectData.get(field.getApiName() + "__o", String.class))) {
                throw new ValidateException(I18N.text(I18NKey.SELECT_ONE_NOT_EMPTY, field.getLabel()));
            }
        }
    }

    public void sortRelevantTeamMemberOfObjectData() {
        List<TeamMemberInfoPoJo> relevantTeamFromObjectData = getRelevantTeamFromObjectData();
        if (CollectionUtils.empty(relevantTeamFromObjectData)) {
            return;
        }
        Collections.sort(relevantTeamFromObjectData);
        setRelevantTeamIntoObjectData(relevantTeamFromObjectData);
    }

    private void setRelevantTeamIntoObjectData(List<TeamMemberInfoPoJo> relevantTeamFromObjectData) {
        String json = JsonUtil.toJson(relevantTeamFromObjectData);
        ArrayList arrayList = JsonUtil.fromJson(json, ArrayList.class);
        objectData.set(RELEVANT_TEAM, arrayList);
    }

    public IObjectData copy() {
        return of(Maps.newHashMap(toMap())).getObjectData();
    }

    public List<TeamMemberInfoPoJo> getRelevantTeamFromObjectData() {
        List<TeamMemberInfoPoJo> resultTeamMemberInfoPoJo = Lists.newArrayList();

        Object relevantTeamListObj = objectData.get(RELEVANT_TEAM);
        if (Objects.isNull(relevantTeamListObj)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> relevantTeamList = (List<Map<String, Object>>) relevantTeamListObj;

        Map<String, Map<Object, List<Map<String, Object>>>> relevantTeamMap = relevantTeamList.stream()
                .collect(Collectors.groupingBy(x -> {
                            String teamMemberType = null;
                            if (Objects.nonNull(x.get(TeamMember.TEAM_MEMBER_TYPE))) {
                                teamMemberType = String.valueOf(x.get(TeamMember.TEAM_MEMBER_TYPE));
                            }
                            return TeamMember.MemberType.of(teamMemberType).getValue();
                        },
                        Collectors.groupingBy(x -> x.get(TeamMember.TEAM_MEMBER_EMPLOYEE_API_NAME))));

        relevantTeamMap.forEach((memberType, relevantTeamInfo) ->
                relevantTeamInfo.forEach((employeeId, empInfo) -> {
                    List<String> employees = (List<String>) employeeId;

                    List<String> roleList = empInfo.stream()
                            .map(x -> Objects.isNull(x.get(TeamMember.TEAM_MEMBER_ROLE_API_NAME)) ?
                                    TeamMember.Role.NORMAL_STAFF.getValue() : String.valueOf(x.get(TeamMember.TEAM_MEMBER_ROLE_API_NAME)))
                            .collect(Collectors.toList());

                    boolean match = empInfo.stream()
                            .map(x -> Objects.isNull(x.get(TeamMember.TEAM_MEMBER_PERMISSION_TYPE_API_NAME)) ?
                                    TeamMember.Permission.READONLY.getValue() : String.valueOf(x.get(TeamMember.TEAM_MEMBER_PERMISSION_TYPE_API_NAME)))
                            .anyMatch(x -> StringUtils.equals(x, TeamMember.Permission.READANDWRITE.getValue()));

                    String permission = match ? TeamMember.Permission.READANDWRITE.getValue() : TeamMember.Permission.READONLY.getValue();

                    String outEI = empInfo.stream().map(x -> {
                        Object outEmployeeEi = x.get(TeamMember.TEAM_MEMBER_OUT_EMPLOYEE_EI);
                        if (Objects.nonNull(outEmployeeEi)) {
                            return String.valueOf(outEmployeeEi);
                        }
                        return "";
                    }).findFirst().get();

                    String sourceType = empInfo.stream().map(x -> {
                        Object outTeamMemberType = x.get(TeamMember.TEAM_MEMBER_EMPLOYEE_SOURCE_TYPE);
                        if (Objects.nonNull(outTeamMemberType)) {
                            return String.valueOf(outTeamMemberType);
                        }
                        return "";
                    }).findFirst().get();

                    String deptCascade = empInfo.stream().map(x -> {
                        Object teamMemberDeptCascade = x.get(TeamMember.TEAM_MEMBER_DEPT_CASCADE);
                        if (Objects.nonNull(teamMemberDeptCascade)) {
                            return String.valueOf(teamMemberDeptCascade);
                        }
                        return "0";
                    }).findFirst().get();

                    resultTeamMemberInfoPoJo.add(TeamMemberInfoPoJo.builder()
                            .teamMemberEmployee(employees)
                            .teamMemberRole(StringUtils.join(roleList, ","))
                            .teamMemberRoleList(roleList)
                            .teamMemberPermissionType(permission)
                            .teamMemberType(memberType)
                            .outTenantId(outEI)
                            .sourceType(sourceType)
                            .teamMemberDeptCascade(deptCascade)
                            .build()
                    );
                }));
        return resultTeamMemberInfoPoJo;
    }


    public List<TeamMemberInfoPoJo> getRelevantTeamFromObjectDataForImport() {
        Object relevantTeamListObj = objectData.get(RELEVANT_TEAM);
        if (Objects.isNull(relevantTeamListObj)) {
            return Collections.emptyList();
        }
        return (List<TeamMemberInfoPoJo>) relevantTeamListObj;
    }

    public List<String> modifyObjectDataWhenStartApprovalFlow(ApprovalFlowTriggerType type, ApprovalFlowStartResult result) {
        if (!getLifeStatus().startCallBack(objectData, type, result)) {
            return Lists.newArrayList();
        }

        List<String> fieldsToUpdate = Lists.newArrayList(ObjectLifeStatus.LIFE_STATUS_API_NAME);
        if (ApprovalFlowTriggerType.INVALID.equals(type)) {
            fieldsToUpdate.add(ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME);
        }
        return fieldsToUpdate;
    }

    public List<String> modifyObjectDataWhenStartApprovalFlow(ApprovalFlowStartResult result) {
        if (!getLifeStatus().startCallBack(objectData, ApprovalFlowTriggerType.CUSTOM_BUTTON, result)) {
            return Lists.newArrayList();
        }

        List<String> fieldsToUpdate = Lists.newArrayList(ObjectLifeStatus.LIFE_STATUS_API_NAME);
        return fieldsToUpdate;
    }

    public void createApprovalCompleteCallBack(boolean isPass) {
        //如果审批成功,变为正常, 如果审批失败,变为未生效
        if (isPass) {
            setLifeStatus(ObjectLifeStatus.NORMAL);
        } else {
            setLifeStatus(ObjectLifeStatus.INEFFECTIVE);
        }
    }

    public void invalidApprovalCompleteCallBack(boolean isPass) {
        //如果审批成功,变为作废,如果审批失败,变为正常
        if (isPass) {
            setLifeStatus(ObjectLifeStatus.INVALID);
        } else {
            setLifeStatus(ObjectLifeStatus.NORMAL);
            set(ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME, null);
        }
    }

    public void updateApprovalCompleteCallBack(boolean isPass) {
        setLifeStatus(ObjectLifeStatus.NORMAL);
    }

    public boolean createApprovalStartCallback(ApprovalFlowStartResult approvalType) {
        switch (approvalType) {
            case APPROVAL_NOT_EXIST:
                //如果没有审批流,则是正常。
                setLifeStatus(ObjectLifeStatus.NORMAL);
                break;
            case SUCCESS: {
                //如果触发成功或者已经存在审批流了
                //变为审核中
                setLifeStatus(ObjectLifeStatus.UNDER_REVIEW);
                //变为锁定状态
                setLockStatus(ObjectLockStatus.LOCK.getStatus());
                break;
            }
            default:
                return false;
        }
        return true;
    }

    public boolean updateApprovalStartCallback(ApprovalFlowStartResult approvalType) {
        if (approvalType == ApprovalFlowStartResult.SUCCESS) {
            //如果触发成功
            //变为变更中
            setLifeStatus(ObjectLifeStatus.IN_CHANGE);
            //变为锁定状态
            setLockStatus(ObjectLockStatus.LOCK.getStatus());
            return true;
        }
        return false;
    }

    public boolean invalidApprovalStartCallback(ApprovalFlowStartResult approvalType) {
        switch (approvalType) {
            case APPROVAL_NOT_EXIST:
                setLifeStatusBeforeInvalid(getLifeStatusText());
                setLifeStatus(ObjectLifeStatus.INVALID);
                break;
            case SUCCESS:
                //如果触发成功,或者已经存在了审批流
                setLifeStatusBeforeInvalid(getLifeStatusText());
                //变为变更中
                setLifeStatus(ObjectLifeStatus.IN_CHANGE);
                //变为锁定状态
                setLockStatus(ObjectLockStatus.LOCK.getStatus());
                break;
            default:
                return false;
        }
        return true;
    }

    public String getLifeStatusBeforeInvalid() {
        return objectData.get(ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME, String.class);
    }

    public void setLifeStatusBeforeInvalid(String lifeStatus) {
        objectData.set(ObjectLifeStatus.LIFE_STATUS_BEFORE_INVALID_API_NAME, lifeStatus);
    }

    public String getStringValue(String key) {
        Object value = objectData.get(key);
        if (Objects.nonNull(value)) {
            return value.toString();
        }
        return null;
    }

    public String getStringValueInImport(String apiName) {
        Object value = get(apiName);
        return formatValueInImport(value);
    }


    public void setCountryLabelInImport(String apiName, String name) {
        set(apiName + "__label", name);
    }

    public String getCountryLabelInImport(String apiName) {
        Object value = get(apiName + "__label");
        return formatValueInImport(value);
    }

    public static String formatValueInImport(Object value) {
        if (value instanceof List && CollectionUtils.empty((List) value)) {
            return "";
        } else {
            // return value == null ? "" : SPACEPATTERN.matcher(String.valueOf(value)).replaceAll("");
            if (value == null) {
                return "";
            }
            // 将全角空格\u3000和不间断空格(NO-BREAK SPACE)\u00A0 替换为 普通空格(\u0020)
            String str = SPACE_CHAR_REGX.matcher(String.valueOf(value)).replaceAll(" ");
            return SPECIAL_CHAR_REGX.matcher(str).replaceAll("").trim();
        }
    }

    public String getStringValueInImportForReference(String apiName) {
        Object value = get(apiName);
        if (value instanceof List && CollectionUtils.empty((List) value)) {
            return "";
        } else {
            return value == null ? "" : String.valueOf(value).replaceAll("^\\s+|\\s+$", "");
        }
    }

    public String getOldStatus() {
        return org.apache.commons.lang3.ObjectUtils.firstNonNull(objectData.get("account_status", String.class),
                objectData.get("leads_status", String.class));
    }

    public String getLifeStatusText() {
        return objectData.get(ObjectLifeStatus.LIFE_STATUS_API_NAME, String.class);
    }

    public ObjectLifeStatus getLifeStatus() {
        return ObjectLifeStatus.of(getLifeStatusText(), isInvalid());
    }

    public void setLifeStatus(ObjectLifeStatus lifeStatus) {
        objectData.set(ObjectLifeStatus.LIFE_STATUS_API_NAME, lifeStatus.getCode());
    }

    public void setLifeStatus(String lifeStatus) {
        setLifeStatus(ObjectLifeStatus.of(lifeStatus, isInvalid()));
    }

    public void setLockStatus(String lockStatus) {
        objectData.set(ObjectLockStatus.LOCK_STATUS_API_NAME, lockStatus);
    }

    public String getLockStatus() {
        return objectData.get(ObjectLockStatus.LOCK_STATUS_API_NAME, String.class);
    }

    public String getEmployeeFieldValue(String apiName) {
        Object o = objectData.get(apiName);
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof List) {
            if (CollectionUtils.empty((List) o)) {
                return null;
            }
            return String.valueOf(((List) o).get(0));
        }

        return getStringValue(apiName);
    }

    public List<String> getEmployeeFieldValueByFieldDescribe(IFieldDescribe fieldDescribe) {
        if (IFieldType.EMPLOYEE.equals(fieldDescribe.getType())) {
            String value = getEmployeeFieldValue(fieldDescribe.getApiName());
            if (StringUtils.isNotBlank(value)) {
                return Lists.newArrayList(value);
            }
            return Collections.emptyList();
        }
        if (fieldDescribe.getType().equals(IFieldType.EMPLOYEE_MANY)) {
            Object o = objectData.get(fieldDescribe.getApiName());
            if (Objects.isNull(o)) {
                return Collections.emptyList();
            }
            return (List<String>) o;
        }
        return Collections.emptyList();
    }


    public String getDepartmentFieldValue(String apiName) {
        Object o = objectData.get(apiName);
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof List) {
            if (CollectionUtils.empty((List) o)) {
                return null;
            }
            return String.valueOf(((List) o).get(0));
        }

        return getStringValue(apiName);
    }

    public List<String> getDepartmentFieldValueByFieldDescribe(IFieldDescribe fieldDescribe) {
        if (DEPARTMENT.equals(fieldDescribe.getType())) {
            String value = getDepartmentFieldValue(fieldDescribe.getApiName());
            if (StringUtils.isNotBlank(value)) {
                return Lists.newArrayList(value);
            }
            return Collections.emptyList();
        }
        if (fieldDescribe.getType().equals(DEPARTMENT_MANY)) {
            Object o = objectData.get(fieldDescribe.getApiName());
            if (Objects.isNull(o)) {
                return Collections.emptyList();
            }
            return (List<String>) o;
        }
        return Collections.emptyList();
    }


    public List<String> getRegionFieldValueByFieldDescribe(IFieldDescribe fieldDescribe) {
        Object o = objectData.get(fieldDescribe.getApiName());
        if (Objects.isNull(o)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(getStringValue(fieldDescribe.getApiName()));
    }

    public String getFieldValueByApiName(String apiName) {
        Object o = objectData.get(apiName);
        if (Objects.isNull(o)) {
            return null;
        }
        if (o instanceof List) {
            if (CollectionUtils.empty((List) o)) {
                return null;
            }
            return String.valueOf(((List) o).get(0));
        }

        return getStringValue(apiName);
    }

    public boolean isListSoloValue(String apiName) {
        Object o = objectData.get(apiName);
        if (Objects.isNull(o)) {
            return false;
        }
        if (o instanceof List) {
            List list = (List) o;
            return !CollectionUtils.empty(list) && Objects.equals(list.size(), 1);
        }

        return false;
    }

    public Map<String, Object> diff(IObjectData newData, IObjectDescribe objectDescribe, boolean isIgnoreFields, String sourceBiz) {
        Map<String, Object> oldDataMap = this.toMap();
        Map<String, Object> newDataMap = ObjectDataExt.of(newData).toMap();

        Map<String, Object> mapResult = Maps.newHashMap();
        MapDifference<String, Object> diffResult = Maps.difference(oldDataMap, newDataMap);
        Map<String, MapDifference.ValueDifference<Object>> differenceMap = diffResult.entriesDiffering();


        for (Map.Entry<String, MapDifference.ValueDifference<Object>> differenceEntry : differenceMap.entrySet()) {
            String key = differenceEntry.getKey();
            if (!isFieldNeedDiff(key, objectDescribe, isIgnoreFields, sourceBiz)) {
                continue;
            }
            Optional<IFieldDescribe> fieldOptional = ObjectDescribeExt.of(objectDescribe).getFieldDescribeSilently(key);
            String fieldType = !fieldOptional.isPresent() ? IFieldType.TEXT : fieldOptional.get().getType();
            Object leftValue = differenceEntry.getValue().leftValue();
            Object rightValue = differenceEntry.getValue().rightValue();
            if (isValueEqual(leftValue, rightValue, fieldType)) {
                continue;
            }
            mapResult.put(key, rightValue);
            //特殊处理单选和多选字段
            processSelectField(mapResult, newData, key, fieldType, objectDescribe);
            processMultiLangField(mapResult, newData, key, objectDescribe);
        }

        for (Map.Entry<String, Object> entriesOnlyOnRight : diffResult.entriesOnlyOnRight().entrySet()) {
            String key = entriesOnlyOnRight.getKey();
            if (!isFieldNeedDiff(key, objectDescribe, isIgnoreFields, sourceBiz)) {
                continue;
            }
            //原来没有的字段，如果新的值是空的话也认为没有变更
            Optional<IFieldDescribe> fieldOptional = ObjectDescribeExt.of(objectDescribe).getFieldDescribeSilently(key);
            String fieldType = !fieldOptional.isPresent() ? IFieldType.TEXT : fieldOptional.get().getType();
            Object value = entriesOnlyOnRight.getValue();
            if (isValueEmpty(value, fieldType)) {
                continue;
            }
            mapResult.put(key, value);
            //特殊处理单选和多选字段
            processSelectField(mapResult, newData, key, fieldType, objectDescribe);
            processMultiLangField(mapResult, newData, key, objectDescribe);
        }

        return mapResult;

    }

    private void processMultiLangField(Map<String, Object> mapResult, IObjectData newData, String fieldName, IObjectDescribe objectDescribe) {
        if (!AppFrameworkConfig.objectMultiLangGray(objectDescribe.getTenantId(), objectDescribe.getApiName())) {
            return;
        }
        if (isMultiLangField(objectDescribe, fieldName)) {
            String fieldApiName = FieldDescribeExt.getMultiLangFieldFromExtraField(fieldName);
            Object value = newData.get(fieldApiName);
            mapResult.put(fieldApiName, value);
            return;
        }
        if (ObjectDescribeExt.of(objectDescribe).isMultiLangField(fieldName)) {
            String multiLangFieldName = FieldDescribeExt.getMultiLangExtraFieldName(fieldName);
            mapResult.put(multiLangFieldName, newData.get(multiLangFieldName));
        }
    }

    private void processSelectField(Map<String, Object> mapResult, IObjectData newData, String fieldName,
                                    String fieldType, IObjectDescribe objectDescribe) {
        Object value = newData.get(fieldName);
        //根据other选项补充__o字段
        if (FieldDescribeExt.isSelectField(fieldType) && hasOtherValue(value)) {
            String otherKey = FieldDescribeExt.getSelectOther(fieldName);
            Object o = newData.get(otherKey);
            if (Objects.nonNull(o)) {
                mapResult.put(otherKey, o);
            }
        }
        //根据__o补充单选或多选字段
        if (FieldDescribeExt.isSelectOtherField(fieldName) && !RichTextExt.isRichTextAbstractField(fieldName, objectDescribe)) {
            String selectField = FieldDescribeExt.getSelectFieldFromOtherField(fieldName);
            mapResult.put(selectField, newData.get(selectField));
        }
    }

    public Map<String, Object> diff(IObjectData newData, IObjectDescribe objectDescribe) {
        return diff(newData, objectDescribe, true, null);
    }

    public Map<String, Object> diff(IObjectData newData, IObjectDescribe objectDescribe, boolean isIgnoreFields) {
        return diff(newData, objectDescribe, isIgnoreFields, null);
    }

    private boolean isFieldNeedDiff(String fieldName, IObjectDescribe objectDescribe, boolean isIgnoreFields, String sourceBiz) {
        //__o需要diff
        if (FieldDescribeExt.isSelectOtherField(fieldName) && !RichTextExt.isRichTextAbstractField(fieldName, objectDescribe)) {
            return true;
        }
        // 多语字段需要diff
        if (isMultiLangField(objectDescribe, fieldName)) {
            return true;
        }
        //系统字段不做diff
        if (ObjectDescribeExt.isSystemField(fieldName)) {
            return false;
        }
        //除了单选的其他选项，其他不在描述中的字段都忽略
        Optional<IFieldDescribe> fieldOptional = ObjectDescribeExt.of(objectDescribe).getFieldDescribeSilently(fieldName);
        if (!fieldOptional.isPresent()) {
            return false;
        }
        //系统字段不做diff
        IFieldDescribe fieldDescribe = fieldOptional.get();
        if (isNotNeedDiffFieldName(fieldDescribe, objectDescribe)
                && !(Objects.equals(IObjectData.OWNER, fieldName) && Objects.equals(sourceBiz, "UI_EVENT"))) {
            return false;
        }
        //虚拟字段不做diff
        if (BooleanUtils.isTrue(fieldDescribe.isAbstract()) && !FieldDescribeExt.of(fieldDescribe).isWhatListData()) {
            return false;
        }
        if (isIgnoreFields && AppFrameworkConfig.getIgnoreFieldTypesForDiff().contains(fieldDescribe.getType())) {
            return isRelevantTeamByImportValidate(sourceBiz, fieldDescribe);
        }
        return true;
    }

    private boolean isMultiLangField(IObjectDescribe objectDescribe, String fieldName) {
        return ObjectDescribeExt.of(objectDescribe).isMultiLangField(fieldName);
    }

    private boolean isNotNeedDiffFieldName(IFieldDescribe fieldDescribe, IObjectDescribe objectDescribe) {
        String fieldName = fieldDescribe.getApiName();
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_NEED_DIFF_SYSTEM_FIELD_NAME, objectDescribe.getTenantId())) {
            return IFieldDescribe.DEFINE_TYPE_SYSTEM.equals(fieldDescribe.getDefineType())
                    && !AppFrameworkConfig.needDiffFieldName(objectDescribe.getApiName(), fieldName)
                    && !SPECIAL_SYSTEM_FIELDS.contains(fieldName)
                    && !Objects.equals(IObjectData.NAME, fieldName)
                    && !Objects.equals("change_name", fieldName)
                    && !Objects.equals(IObjectData.ORDER_BY, fieldName);
        }
        return IFieldDescribe.DEFINE_TYPE_SYSTEM.equals(fieldDescribe.getDefineType())
                && !SPECIAL_SYSTEM_FIELDS.contains(fieldName)
                && !Objects.equals(IObjectData.NAME, fieldName)
                && !Objects.equals("change_name", fieldName)
                && !Objects.equals(IObjectData.ORDER_BY, fieldName);

    }

    private boolean isRelevantTeamByImportValidate(String sourceBiz, IFieldDescribe fieldDescribe) {
        return "import".equalsIgnoreCase(sourceBiz) && RELEVANT_TEAM.equals(fieldDescribe.getApiName());
    }

    public static boolean hasOtherValue(Object value) {
        if (value instanceof String) {
            return Objects.equals(value, "other");
        }
        if (value instanceof List) {
            return ((List) value).contains("other");
        }
        return false;
    }

    public static boolean isValueEqual(Object leftValue, Object rightValue, IFieldDescribe fieldDescribe) {
        String fieldType = fieldDescribe.getType();
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        if (fieldDescribeExt.isCountField()) {
            fieldType = ((Count) fieldDescribe).getReturnType();
        } else if (fieldDescribeExt.isFormula()) {
            fieldType = ((Formula) fieldDescribe).getReturnType();
        }
        return isValueEqual(leftValue, rightValue, fieldType);
    }

    public static boolean isValueEqual(Object leftValue, Object rightValue, String fieldType) {
        if (isValueEmpty(leftValue, fieldType)) {
            return isValueEmpty(rightValue, fieldType);
        }
        if (isValueEmpty(rightValue, fieldType)) {
            return false;
        }
        return isNotEmptyValueEqual(leftValue, rightValue, fieldType);
    }

    public static boolean isNotEmptyValueEqual(Object leftValue, Object rightValue, String fieldType) {

        if (FieldDescribeExt.isDateTypeField(fieldType)) {
            return isDateOrTimeEqual(leftValue, rightValue, fieldType);
        }
        if (FieldDescribeExt.isNumberTypeField(fieldType)) {
            return isNumberEqual(leftValue, rightValue);
        }
        if (FieldDescribeExt.isFileTypeField(fieldType)) {
            boolean ignoreOrder = !IMAGE.equals(fieldType);
            return isFileEqual(leftValue, rightValue, ignoreOrder);
        }
        if (FieldDescribeExt.isUseRangeField(fieldType)) {
            return isUseRangeEqual(leftValue, rightValue);
        }
        if (leftValue instanceof Collection && rightValue instanceof Collection) {
            return CollectionUtils.isEqual((Collection) leftValue, (Collection) rightValue);
        }
        if (leftValue instanceof Map && rightValue instanceof Map) {
            return Objects.equals(leftValue, rightValue);
        }

        return StringUtils.equals(leftValue.toString(), rightValue.toString());
    }

    private static boolean isUseRangeEqual(Object leftValue, Object rightValue) {
        JsonNode leftJsonNode = JacksonUtils.readTree(leftValue.toString());
        JsonNode rightJsonNode = JacksonUtils.readTree(rightValue.toString());
        if (leftJsonNode == null || rightJsonNode == null) {
            return false;
        }
        return Objects.equals(leftJsonNode, rightJsonNode);
    }

    public static boolean isValueEmpty(Object value) {
        if (Objects.isNull(value)) {
            return true;
        }
        if (Strings.isNullOrEmpty(value.toString())) {
            return true;
        }
        if (value instanceof Collection) {
            return CollectionUtils.empty((Collection) value);
        }
        if (value instanceof Map) {
            return CollectionUtils.empty((Map) value);
        }
        return false;
    }

    public static boolean isValueEmpty(Object value, String fieldType) {
        if (isValueEmpty(value)) {
            return true;
        }
        if (FieldDescribeExt.isNumberTypeField(fieldType)) {
            return "N/A".equals(value.toString());
        }
        return false;
    }

    private static boolean isDateOrTimeEqual(Object leftValue, Object rightValue, String fieldType) {
        return compareDateTime(leftValue, rightValue, fieldType) == 0;
    }

    public static int compareNumber(Object leftValue, Object rightValue) {
        BigDecimal leftNumber = new BigDecimal(leftValue.toString());
        BigDecimal rightNumber = new BigDecimal(rightValue.toString());

        return leftNumber.compareTo(rightNumber);
    }

    public static int compareDateTime(Object leftValue, Object rightValue, String fieldType) {
        Long leftLongValue = DateTimeUtils.parseISOLocateDateTime(leftValue.toString(), fieldType);
        Long rightLongValue = DateTimeUtils.parseISOLocateDateTime(rightValue.toString(), fieldType);
        if (DateTimeUtils.isGrayTimeZone()) {
            return leftLongValue.compareTo(rightLongValue);
        }
        if (DATE.equals(fieldType)) {
            String leftDate = DateFormatUtils.format(leftLongValue, "yyyyMMdd");
            String rightDate = DateFormatUtils.format(rightLongValue, "yyyyMMdd");
            return leftDate.compareTo(rightDate);
        }
        if (TIME.equals(fieldType)) {
            String leftTime = DateFormatUtils.format(leftLongValue, "HHmm");
            String rightTime = DateFormatUtils.format(rightLongValue, "HHmm");
            return leftTime.compareTo(rightTime);
        }
        if (IFieldType.DATE_TIME.equals(fieldType)) {
            String leftTime = DateFormatUtils.format(leftLongValue, "yyyyMMddHHmm");
            String rightTime = DateFormatUtils.format(rightLongValue, "yyyyMMddHHmm");
            return leftTime.compareTo(rightTime);
        }
        return leftLongValue.compareTo(rightLongValue);
    }

    public static void formatTimeFieldToLong(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        formatTimeFieldToString(objectDescribe, dataList, false);
    }

    public static void formatTimeFieldToString(IObjectDescribe objectDescribe, List<IObjectData> dataList, boolean formatString) {
        objectDescribe.getFieldDescribes().stream()
                .filter(field -> JsonFieldHandler.timeJsonField.contains(field.getType()))
                .forEach(field -> dataList.forEach(data -> {
                    Object value = data.get(field.getApiName());
                    Long longTime = null;

                    if (value instanceof Long) {
                        longTime = (Long) value;
                    } else if (value instanceof String && !Strings.isNullOrEmpty((String) value)) {
                        if (NumberUtils.isNumber((String) value)) {
                            longTime = new BigDecimal((String) value).longValue();
                            data.set(field.getApiName(), longTime);
                        }
                    } else if (value instanceof Timestamp) {
                        longTime = ((Timestamp) value).getTime();
                        data.set(field.getApiName(), longTime);
                    }

                    if (formatString && Objects.nonNull(longTime)) {
                        String time = formatTimeField(field.getType(), longTime);
                        data.set(field.getApiName(), time);
                    }
                }));
    }

    private static String formatTimeField(String fieldType, long longValue) {
        if (DateTimeUtils.isGrayTimeZone()) {
            return DateTimeFormat.get(fieldType).convertFromTimestamp(longValue);
        }
        if (DATE.equals(fieldType)) {
            return DateFormatUtils.format(longValue, "yyyy-MM-dd");
        }
        if (TIME.equals(fieldType)) {
            return DateFormatUtils.format(longValue, "HH:mm");
        }
        if (IFieldType.DATE_TIME.equals(fieldType)) {
            return DateFormatUtils.format(longValue, "yyyy-MM-dd HH:mm");
        }
        return null;
    }

    private static boolean isNumberEqual(Object leftValue, Object rightValue) {
        return compareNumber(leftValue, rightValue) == 0;
    }

    private static boolean isFileEqual(Object leftValue, Object rightValue, boolean ignoreOrder) {
        boolean needFileName = Optional.ofNullable(RequestContextManager.getContext())
                .map(RequestContext::getTenantId)
                .map(tenantId -> UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILE_EQUAL_WITH_FILE_NAME_GRAY, tenantId))
                .orElse(false);
        List<FileEqualHelper> leftFilePathList = ((List<Map>) leftValue).stream()
                .map(it -> FileEqualHelper.of(it, needFileName))
                .collect(Collectors.toList());
        List<FileEqualHelper> rightFilePathList = ((List<Map>) rightValue).stream()
                .map(it -> FileEqualHelper.of(it, needFileName))
                .collect(Collectors.toList());
        if (ignoreOrder) {
            return CollectionUtils.isEqual(leftFilePathList, rightFilePathList);
        } else {
            return leftFilePathList.equals(rightFilePathList);
        }
    }

    public ObjectDataExt removeInvalidFieldForApproval(IObjectDescribe objectDescribe) {
        Set<String> invalidFields = Lists.newArrayList(toMap().keySet()).stream()
                .filter(x -> !isFieldValidForApproval(x, objectDescribe)).collect(Collectors.toSet());
        remove(invalidFields);
        return this;
    }

    public ObjectDataExt removeCalculateField(IObjectDescribe objectDescribe) {
        Set<String> calculateFields = ObjectDescribeExt.of(objectDescribe)
                .stream()
                .map(x -> FieldDescribeExt.of(x))
                .filter(x -> x.isFormula() || x.isCountField() || x.hasCalculateValue())
                .map(x -> x.getApiName())
                .collect(Collectors.toSet());
        remove(calculateFields);
        return this;
    }

    public ObjectDataExt removeCalculateAndQuoteField(IObjectDescribe objectDescribe) {
        Set<String> calculateFields = ObjectDescribeExt.of(objectDescribe)
                .stream()
                .map(FieldDescribeExt::of)
                .filter(x -> x.isFormula() || x.isCountField() || x.hasCalculateValue() || x.isQuoteField())
                .map(FieldDescribeExt::getApiName)
                .collect(Collectors.toSet());
        remove(calculateFields);
        return this;
    }

    public void retainCalculateField(IObjectDescribe objectDescribe) {
        Set<String> otherFieldsExcludeCalculateFields = ObjectDescribeExt.of(objectDescribe)
                .stream()
                .map(FieldDescribeExt::of)
                .filter(x -> !(x.isFormula() || x.isCountField() || x.hasCalculateValue()))
                .map(FieldDescribeExt::getApiName)
                .collect(Collectors.toSet());
        remove(otherFieldsExcludeCalculateFields);
    }

    public ObjectDataExt removeFieldForLog(IObjectDescribe describe, List<String> updateFields) {
        Set<String> unSupportFields = Sets.newHashSet();
        for (String fieldApiName : getDataField()) {
            IFieldDescribe fieldDescribe = getLogFieldDescribe(describe, fieldApiName);
            if (Objects.isNull(fieldDescribe)) {
                continue;
            }
            if (CollectionUtils.notEmpty(updateFields) && !updateFields.contains(fieldDescribe.getApiName())) {
                unSupportFields.add(fieldApiName);
            }
            if (AppFrameworkConfig.unSupportFieldForModifyLog(describe.getApiName(), fieldDescribe.getApiName())
                    || AppFrameworkConfig.getIgnoreFieldTypesForDiff().contains(fieldDescribe.getType())) {
                unSupportFields.add(fieldApiName);
            }
        }
        removeCalculateField(describe);
        remove(unSupportFields);
        return this;
    }

    public ObjectDataExt removeFieldForLog(IObjectDescribe describe) {
        return removeFieldForLog(describe, Lists.newArrayList());
    }

    public static IFieldDescribe getLogFieldDescribe(IObjectDescribe describe, String fieldApiName) {
        if (StringUtils.isBlank(fieldApiName)) {
            return null;
        }
        if (fieldApiName.endsWith("__r")) {
            fieldApiName = StringUtils.substringBefore(fieldApiName, "__r");
        }
        if (fieldApiName.endsWith("__l")) {
            fieldApiName = StringUtils.substringBefore(fieldApiName, "__l");
        }
        if (fieldApiName.endsWith("__o")) {
            fieldApiName = StringUtils.substringBefore(fieldApiName, "__o");
        }
        if (ObjectDescribeExt.of(describe).isMultiLangField(fieldApiName)) {
            String apiName = FieldDescribeExt.getMultiLangFieldFromExtraField(fieldApiName);
            if (AppFrameworkConfig.objectMultiLangGray(describe.getTenantId(), describe.getApiName())) {
                fieldApiName = apiName;
            }
        }
        if (Objects.isNull(ObjectDescribeExt.of(describe).getFieldDescribe(fieldApiName))) {
            return null;
        }
        return ObjectDescribeExt.of(describe).getFieldDescribe(fieldApiName);
    }

    private boolean isFieldValidForApproval(String fieldName, IObjectDescribe objectDescribe) {
        //__o是审批流需要展示的
        if (FieldDescribeExt.isSelectOtherField(fieldName) && !RichTextExt.isRichTextAbstractField(fieldName, objectDescribe)) {
            return true;
        }
        if (SPECIAL_SYSTEM_FIELDS.contains(fieldName)) {
            return false;
        }

        Optional<IFieldDescribe> optional = ObjectDescribeExt.of(objectDescribe).getFieldDescribeSilently(fieldName);
        if (!optional.isPresent()) {
            return false;
        }
        if (!isFieldNeedDiff(fieldName, objectDescribe, true, null)) {
            return false;
        }
        IFieldDescribe fieldDescribe = optional.get();
        if (AppFrameworkConfig.isIgnoreFieldForApproval(objectDescribe.getApiName(), fieldName, fieldDescribe.getType())) {
            return false;
        }
        return true;
    }


    public ObjectDataExt removeDataOwner() {
        return remove(IObjectData.OWNER);
    }

    public ObjectDataExt remove(String fieldName) {
        toMap().remove(fieldName);
        return this;
    }

    public ObjectDataExt remove(Set<String> fieldNames) {
        if (CollectionUtils.notEmpty(fieldNames)) {
            fieldNames.forEach(x -> remove(x));
        }
        return this;
    }

    public ObjectDataExt remove(List<IFieldDescribe> fields) {
        if (CollectionUtils.notEmpty(fields)) {
            fields.forEach(x -> remove(x.getApiName()));
        }
        return this;
    }

    public ObjectDataExt merge(IObjectData sourceData) {
        Map<String, Object> sourceMap = ObjectDataExt.of(sourceData).toMap();
        sourceMap.keySet().stream().filter(x -> !toMap().containsKey(x)).forEach(x -> set(x, sourceData.get(x)));
        return this;
    }

    public void putAllNotEmptyValue(IObjectData sourceData) {
        Map<String, Object> sourceMap = ObjectDataExt.of(sourceData).toMap();
        sourceMap.keySet().stream()
                .filter(x -> !Strings.isNullOrEmpty(formatValueInImport(sourceData.get(x))))
                .forEach(x -> set(x, sourceData.get(x)));
    }

    public void putAllIgnoreFields(Map<String, Object> source, Collection<String> ignoreFields) {
        putAllIgnoreFields(source, ignoreFields, null);
    }

    public void putAllIgnoreFields(Map<String, Object> source, Collection<String> ignoreFields, String special) {
        if (CollectionUtils.empty(ignoreFields)) {
            putAll(source);
            return;
        }
        if (CollectionUtils.empty(source)) {
            return;
        }
        source.keySet().stream()
                .filter(it -> !ignoreFields.contains(it))
                .forEach(it -> putValue(source, it, special));
    }

    private void putValue(Map<String, Object> source, String fieldName, String special) {
        if (!SPECIAL_RELEVANT_TEAM.equals(special)) {
            set(fieldName, source.get(fieldName));
            return;
        }
        if (OWNER.equals(fieldName)) {
            set(fieldName, source.get(fieldName));
            setDefaultTeamMember();
            return;
        }
        if (RELEVANT_TEAM.equals(fieldName)) {
            // 待合并的数据，只保留非负责人角色的相关团队成员
            List<TeamMember> teamMembers = of(source).getTeamMembers().stream()
                    .filter(teamMember -> teamMember.getRole() != TeamMember.Role.OWNER)
                    .collect(Collectors.toList());

            // 原数据，移除除负责人角色外的其他相关团队成员
            List<TeamMember> teamMemberList = getTeamMembers();
            teamMemberList.removeIf(teamMember -> teamMember.getRole() != TeamMember.Role.OWNER);

            teamMemberList.addAll(teamMembers);
            setTeamMembers(teamMemberList);
            return;
        }
        set(fieldName, source.get(fieldName));
    }

    public byte[] toMessageData(ObjectAction type, String ea, String userId) {
        String ActionCode = type.getActionCode();
        //拷贝一下，防止其他线程同时修改objectData导致ConcurrentModificationException
        Map<String, Object> map = Maps.newHashMap(ObjectDataExt.of(objectData).toMap());
        Map<String, String> template = Maps.newHashMap();
        Map<String, Object> newMap = Maps.newHashMap();
        template.put(IObjectData.TENANT_ID, "TenantID");
        newMap.put("TenantAccount", ea);
        newMap.put("AppID", "CRM");
        template.put(IObjectData.PACKAGE, "Package");
        template.put(IObjectData.DESCRIBE_API_NAME, "ObjectApiName");
        template.put(IObjectData.ID, "ObjectID");
        newMap.put("ActionCode", ActionCode);
        newMap.put("ActionContent", JSON.parseObject(JSON.toJSONString(map)));
        newMap.put("OperatorID", Integer.parseInt(userId));
        newMap.put("ActionTime", System.currentTimeMillis());
        newMap.put("Source", "CRM");

        //透传eventId
        String eventId = RequestUtil.getEventId();
        if (!Strings.isNullOrEmpty(eventId)) {
            newMap.put("EventId", eventId);
        }

        for (Map.Entry<String, Object> newData : map.entrySet()) {
            if (template.containsKey(newData.getKey())) {
                newMap.put(template.get(newData.getKey()), newData.getValue());
            } else {
                newMap.put(newData.getKey(), newData.getValue());
            }
        }

        return JSON.toJSONString(newMap).getBytes(Charset.forName("UTF-8"));
    }

    public void fillEmployeeFieldObject(String fieldApiName, UserInfo data) {
        Map map = (Map) JSON.toJSON(data);
        objectData.set(fieldApiName + "__r", map);
    }

    public static Object parseVale(Object o, String type) {
        return parseVale(o, type, false);
    }

    public static Object parseVale(Object o, String type, boolean isUseMultiRegion) {
        ZoneId timeZone = TimeZoneContext.DEFAULT_TIME_ZONE;
        if (DateTimeUtils.isGrayTimeZone()) {
            if (isUseMultiRegion && StringUtils.isNotEmpty(MultiRegionContextHolder.getUserRegion())) {
                timeZone = TimeZoneContextHolder.getUserTimeZone();
            } else {
                return parseVale(o, type, TimeZoneContextHolder.getUserTimeZone());
            }
        }
        if (isUseMultiRegion && StringUtils.isNotEmpty(MultiRegionContextHolder.getUserRegion())) {
            return MultiRegionDateTimeFormatUtils.formatForRegion(o, timeZone, type,
                    new ULocale(MultiRegionContextHolder.getUserRegion()));
        }
        switch (type) {
            case DATE:
                try {
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                    return format.format(o);
                } catch (Exception e) {
                    log.error("datetimeDataCovert error, data is {}", o, e);
                }
            case DATE_TIME:
                try {
                    SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    return format.format(o);
                } catch (Exception e) {
                    log.error("datetimeDataCovert error, data is {}", o, e);
                }
            case TIME:
                try {
                    SimpleDateFormat format = new SimpleDateFormat("HH:mm");
                    return format.format(o);
                } catch (Exception e) {
                    log.error("datetimeDataCovert error, data is {}", o, e);
                }
        }
        return o;
    }

    public static Object parseVale(Object o, String type, ZoneId timeZone) {
        try {
            return DateTimeFormatUtils.format(o, timeZone, type);
        } catch (Exception e) {
            log.error("datetime data format error, data:{}, type:{}", o, type, e);
        }
        return o;
    }

    public String getPartnerId() {
        return objectData.get(PARTNER_ID_API_NAME, String.class);
    }

    public void setPartnerId(String partnerId) {
        objectData.set(PARTNER_ID_API_NAME, partnerId);
    }

    public void setRelevantTeamValue(Set<String> names) {
        if (CollectionUtils.empty(names)) {
            return;
        }
        objectData.set(RELEVANT_TEAM + "__r", Joiner.on(",").skipNulls().join(names));
    }

    public void setRelevantTeamValue(Set<String> names, long count) {
        if (CollectionUtils.empty(names)) {
            return;
        }
        if (count > 0) {
            objectData.set(RELEVANT_TEAM + "__r", Joiner.on(",").skipNulls().join(names) + ",...");
        } else {
            objectData.set(RELEVANT_TEAM + "__r", Joiner.on(",").skipNulls().join(names));
        }
    }

    public void setRelevantTeam(List<TeamMemberInfoPoJo> teamMemberInfoPoJos) {
        if (CollectionUtils.empty(teamMemberInfoPoJos)) {
            return;
        }
        objectData.set(RELEVANT_TEAM, teamMemberInfoPoJos);
    }

    public void removeAutoNumber(IObjectDescribe describe) {
        ObjectDescribeExt.of(describe).getAutoNumberFields()
                .forEach(autoNumber -> toMap().remove(autoNumber.getApiName()));
    }

    public void removeCount(IObjectDescribe describe) {
        ObjectDescribeExt.of(describe).getCountFields()
                .forEach(count -> toMap().remove(count.getApiName()));
    }

    public void removeByTypes(IObjectDescribe describe, String... types) {
        ObjectDescribeExt.of(describe)
                .getFieldByTypes(Sets.newHashSet(types)).stream()
                .map(IFieldDescribe::getApiName)
                .forEach(fieldName -> toMap().remove(fieldName));

    }

    public void setDataOwnDepartmentId(String deptId) {
        // 归属部门被清空，需要传一个空list
        if (Strings.isNullOrEmpty(deptId)) {
            objectData.set(DATA_OWN_DEPARTMENT, Collections.EMPTY_LIST);
        } else {
            objectData.set(DATA_OWN_DEPARTMENT, Lists.newArrayList(deptId));
        }
    }

    public String getDataOwnDepartmentId() {
        Object dataOwnDepartment = objectData.get(DATA_OWN_DEPARTMENT);
        return getDataOwnerDepartmentStr(dataOwnDepartment);
    }

    public void setDataOwnOrganizationId(String orgId) {
        //  归属组织被清空，需要传一个空list
        if (Strings.isNullOrEmpty(orgId)) {
            objectData.set(DATA_OWN_ORGANIZATION, Collections.EMPTY_LIST);
        } else {
            objectData.set(DATA_OWN_ORGANIZATION, Lists.newArrayList(orgId));
        }
    }

    public String getDataOwnOrganizationId() {
        Object dataOwnOrganization = objectData.get(DATA_OWN_ORGANIZATION);
        return getDataOwnerDepartmentStr(dataOwnOrganization);
    }

    public void setOutDataOwnDepartmentId(String deptId) {
        // 归属部门被清空，需要传一个空list
        if (Strings.isNullOrEmpty(deptId)) {
            objectData.set(OUT_DATA_OWN_DEPARTMENT, Collections.EMPTY_LIST);
        } else {
            objectData.set(OUT_DATA_OWN_DEPARTMENT, Lists.newArrayList(deptId));
        }
    }

    public String getOutDataOwnDepartmentId() {
        Object outDataOwnDepartment = objectData.get(OUT_DATA_OWN_DEPARTMENT);
        return getDataOwnerDepartmentStr(outDataOwnDepartment);
    }

    public void setOutDataOwnOrganizationId(String orgId) {
        //  归属组织被清空，需要传一个空list
        if (Strings.isNullOrEmpty(orgId)) {
            objectData.set(OUT_DATA_OWN_ORGANIZATION, Collections.EMPTY_LIST);
        } else {
            objectData.set(OUT_DATA_OWN_ORGANIZATION, Lists.newArrayList(orgId));
        }
    }

    public String getOutDataOwnOrganizationId() {
        Object dataOwnOrganization = objectData.get(OUT_DATA_OWN_ORGANIZATION);
        return getDataOwnerDepartmentStr(dataOwnOrganization);
    }

    private String getDataOwnerDepartmentStr(Object dataOwnDepartment) {
        String ret = null;
        if (Objects.nonNull(dataOwnDepartment)) {
            if (dataOwnDepartment instanceof List) {
                List ownerList = (List) dataOwnDepartment;
                if (!ownerList.isEmpty()) {
                    ret = String.valueOf(ownerList.get(0));
                }
            } else if (dataOwnDepartment instanceof String) {
                ret = (String) dataOwnDepartment;
            } else if (dataOwnDepartment instanceof Integer) {
                ret = String.valueOf(dataOwnDepartment);
            }
        }
        return ret;
    }

    public void setDataOwnOrgByDeptInfo(DeptInfo deptInfo) {
        if (Objects.isNull(deptInfo) || Strings.isNullOrEmpty(deptInfo.getDeptId())) {
            objectData.setDataOwnOrganization(Collections.emptyList());
            return;
        }
        objectData.setDataOwnOrganization(Lists.newArrayList(deptInfo.getDeptId()));
    }

    public static void filterUnauthorizedFieldsByDataList(List<IObjectData> dataList, Set<String> unauthorizedFields, IObjectDescribe describe) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(unauthorizedFields)) {
            return;
        }

        Set<String> extendNames = new HashSet<>();
        for (String fieldName : unauthorizedFields) {
            IFieldDescribe fieldDescribe = ObjectDescribeExt.of(describe).getFieldDescribe(fieldName);
            if (fieldDescribe == null) {
                continue;
            }
            String fieldExtendName = FieldDescribeExt.of(fieldDescribe).getFieldExtendName();
            if (!Strings.isNullOrEmpty(fieldExtendName)) {
                extendNames.add(fieldExtendName);
            }
        }

        unauthorizedFields.addAll(extendNames);
        dataList.stream().map(ObjectDataExt::new).forEach(data -> data.remove(unauthorizedFields));
    }

    @SuppressWarnings("unchecked")
    public Map<String, List<TeamMember.RoleWithPermission>> getRelevantTeamTemp() {
        Object obj = objectData.get(RELEVANT_TEAM);
        if (!(obj instanceof Map)) {
            return Maps.newHashMap();
        }
        return (Map<String, List<TeamMember.RoleWithPermission>>) obj;
    }

    public Map<String, List<String>> getRelevantTeamTempMap() {
        Object obj = objectData.get(RELEVANT_TEAM);
        if (!(obj instanceof Map)) {
            return Maps.newHashMap();
        }
        return (Map<String, List<String>>) obj;
    }

    public static void remove(List<IObjectData> dataList, Set<String> fieldNames) {
        if (CollectionUtils.notEmpty(fieldNames)) {
            dataList.forEach(x -> ObjectDataExt.of(x).remove(fieldNames));
        }
    }

    public static void removeByTypes(IObjectDescribe describe, List<IObjectData> dataList, String... types) {
        if (Objects.isNull(describe) || CollectionUtils.empty(dataList)) {
            return;
        }
        dataList.stream()
                .filter(Objects::nonNull)
                .map(ObjectDataExt::of)
                .forEach(dataExt -> dataExt.removeByTypes(describe, types));
    }

    public boolean isOutOwner(User user) {
        return !Objects.isNull(user)
                && Objects.equals(objectData.getOutOwner(), Lists.newArrayList(user.getOutUserId()))
                && Objects.equals(objectData.getOutTenantId(), user.getOutTenantId());
    }

    public boolean isSameDownStreamCorp(User user) {
        return !Objects.isNull(user) && Objects.equals(user.getOutTenantId(), objectData.getOutTenantId());
    }

    public void fillEmployeeFields(List<String> apiNames, List<UserInfo> userInfos) {
        if (CollectionUtils.empty(apiNames) || CollectionUtils.empty(userInfos)) {
            return;
        }
        Map<String, UserInfo> userInfoMap = userInfos.stream()
                .collect(Collectors.toMap(UserInfo::getId, Function.identity()));
        for (String apiName : apiNames) {
            List<String> userIds = getEmployeeValues(apiName);
            List<UserInfo> resultList = userIds.stream()
                    .map(userInfoMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(resultList)) {
                objectData.set(apiName + "__l", resultList);
            }
        }
    }

    public void fillDepartmentFields(List<String> apiNames, List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos) {
        if (CollectionUtils.empty(apiNames) || CollectionUtils.empty(deptInfos)) {
            return;
        }
        Map<String, QueryDeptInfoByDeptIds.DeptInfo> deptInfoMap = deptInfos.stream()
                .collect(Collectors.toMap(QueryDeptInfoByDeptIds.DeptInfo::getDeptId, Function.identity()));
        for (String apiName : apiNames) {
            List<String> userIds = getDepartmentValues(apiName);
            List<QueryDeptInfoByDeptIds.DeptInfo> resultList = userIds.stream()
                    .map(deptInfoMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(resultList)) {
                objectData.set(apiName + "__l", resultList);
            }
        }
    }

    public List<String> getDepartmentValues(String apiName) {
        Object o = objectData.get(apiName);
        if (o instanceof List) {
            return (List<String>) o;
        }
        return Collections.emptyList();
    }

    public List<String> getEmployeeValues(String apiName) {
        Object o = objectData.get(apiName);
        if (o instanceof List) {
            return (List<String>) o;
        }
        return Collections.emptyList();
    }

    public Optional<TeamMember> getOwnerTeamMemberOpt() {
        return getTeamMembers().stream()
                .filter(f -> f.getRole() == TeamMember.Role.OWNER
                        && f.getMemberType() == TeamMember.MemberType.EMPLOYEE)
                .filter(f -> !f.isOutMember())
                .findFirst();
    }

    public void fillMaskValue(IFieldDescribe field, boolean removeOrigValue, boolean overrideOrigValue) {
        if (!containsField(field.getApiName())) {
            return;
        }
        String maskValue = getMaskValue(field);
        if (overrideOrigValue) {
            set(field.getApiName(), maskValue);
            return;
        }
        set(FieldDescribeExt.getShowFieldName(field.getApiName()), maskValue);
        //不是特殊字段(端上反算会用到)的移除原值
        if (removeOrigValue && !AppFrameworkConfig.isSpecialMaskField(field.getDescribeApiName(), field.getApiName())) {
            remove(field.getApiName());
        }
    }

    public void fillMaskValue(List<IFieldDescribe> maskFields, boolean removeOrigValue, boolean overrideOrigValue) {
        if (CollectionUtils.empty(maskFields)) {
            return;
        }
        maskFields.forEach(field -> fillMaskValue(field, removeOrigValue, overrideOrigValue));
    }

    public String getMaskValue(IFieldDescribe field) {
        Object value = get(field.getApiName());
        if (ObjectDataExt.isValueEmpty(value)) {
            return "";
        }
        String strValue = value.toString();
        switch (field.getType()) {
            case IFieldType.PHONE_NUMBER:
                if (strValue.length() == 11) {
                    return StringUtils.substring(strValue, 0, 3) + "****" + StringUtils.substring(strValue, -4);
                } else {
                    return DEFAULT_MASK_VALUE;
                }
            default:
                return DEFAULT_MASK_VALUE;
        }
    }

    public void fillGeoDistance(IObjectDescribe objectDescribe) {
        List<IFieldDescribe> geoSearchFields = ObjectDescribeExt.of(objectDescribe).findGeoSearchField();
        CollectionUtils.nullToEmpty(geoSearchFields).forEach(f -> fillGeoDistance(f.getApiName()));
    }

    public void fillGeoDistance(String fieldApiName) {
        Object value = objectData.get(fieldApiName + "_distance");
        Map<String, String> map = Maps.newHashMap();
        map.put("api_name", fieldApiName);
        if (Objects.nonNull(value)) {
            map.put("distance", String.valueOf(value));
        }
        objectData.set("location_field", map);
    }

    /*
     * 同步外部相关团队负责人为ownerId
     */
    public void synchronizeOutTeamMemberOwner(String outTenantId, String outUserId) {
        List<TeamMember> teamMembers = getTeamMembers();
        // 清空外部负责任人
        if (Strings.isNullOrEmpty(outTenantId) && Strings.isNullOrEmpty(outUserId)) {
            teamMembers.removeIf(x -> x.isOutMember()
                    && TeamMember.Role.OWNER.equals(x.getRole())
                    && TeamMember.MemberType.EMPLOYEE.equals(x.getMemberType()));
            setTeamMembers(teamMembers);
            return;
        }
        // 创建新的外部相关团队负责人
        TeamMember newOutTeamMemberOwner = new TeamMember(outUserId, TeamMember.Role.OWNER, TeamMember.Permission.READANDWRITE, outTenantId);
        teamMembers.removeIf(teamMember -> teamMember.isOutMember()
                && teamMember.getMemberType() == TeamMember.MemberType.EMPLOYEE
                && (teamMember.getRole() == TeamMember.Role.OWNER
                || teamMember.getEmployee().equals(newOutTeamMemberOwner.getEmployee())));
        teamMembers.add(newOutTeamMemberOwner);
        setTeamMembers(teamMembers);
    }

    /**
     * 清空外部相关团队，除负责人外的其他团队成员
     */
    public void removeOutTeamMember() {
        List<TeamMember> teamMembers = getTeamMembers();
        teamMembers.removeIf(it -> it.isOutMember()
                && !(TeamMember.Role.OWNER.equals(it.getRole()) || TeamMember.MemberType.INTERCONNECT_DEPARTMENT == it.getMemberType()));
        setTeamMembers(teamMembers);
    }

    public List<String> getDimensionValues(String apiName) {
        Object o = objectData.get(apiName);
        if (o instanceof List) {
            return (List<String>) o;
        }
        return Collections.emptyList();
    }

    public void fillDimensionFields(List<String> dimensionFields, List<DimensionInfo> dimensionInfos) {
        if (CollectionUtils.empty(dimensionFields) || CollectionUtils.empty(dimensionInfos)) {
            return;
        }
        for (String field : dimensionFields) {
            List<String> dimensionIds = getDimensionValues(field);
            if (CollectionUtils.notEmpty(dimensionIds)) {
                List<DimensionInfo> dimensionInfoResult = dimensionInfos.stream()
                        .filter(it -> dimensionIds.contains(it.getId()))
                        .collect(Collectors.toList());
                List<Map<String, String>> dimensionValues = Lists.newArrayList();
                dimensionInfoResult.forEach(it -> {
                    Map<String, String> map = Maps.newHashMap();
                    map.put("id", it.getId());
                    map.put("name", it.getName());
                    dimensionValues.add(map);
                });
                objectData.set(field + "__r", dimensionValues);
            }
        }
    }

    public void removeLifeStatus() {
        remove(ObjectLifeStatus.LIFE_STATUS_API_NAME);
    }

    public String getCurrency() {
        return (String) get(FieldDescribeExt.CURRENCY_FIELD);
    }

    public void setCurrency(String currency) {
        set(FieldDescribeExt.CURRENCY_FIELD, currency);
    }

    public Object getExchangeRate() {
        return get(FieldDescribeExt.EXCHANGE_RATE_FIELD);
    }

    public void setExchangeRate(Object exchangeRate) {
        set(FieldDescribeExt.EXCHANGE_RATE_FIELD, exchangeRate);
    }

    public void setExchangeRateVersion(String exchangeRateVersion) {
        set(FieldDescribeExt.EXCHANGE_RATE_VERSION_FIELD, exchangeRateVersion);
    }

    public String getExchangeRateVersion() {
        return (String) get(FieldDescribeExt.EXCHANGE_RATE_VERSION_FIELD);
    }

    public String getFunctionalCurrency() {
        return (String) get(FieldDescribeExt.FUNCTIONAL_CURRENCY_FIELD);
    }

    public void setFunctionalCurrency(String functionalCurrency) {
        set(FieldDescribeExt.FUNCTIONAL_CURRENCY_FIELD, functionalCurrency);
    }

    public void checkCurrencyOption(IObjectDescribe describe) {
        String currency = getCurrency();
        if (Strings.isNullOrEmpty(currency)) {
            return;
        }
        Optional<IFieldDescribe> currencyFieldOpt = ObjectDescribeExt.of(describe).getCurrencyField();
        if (!currencyFieldOpt.isPresent()) {
            return;
        }
        SelectOne currencyField = (SelectOne) currencyFieldOpt.get();
        if (!currencyField.getOption(currency).isPresent()) {
            throw new ValidateException(I18N.text(I18NKey.CURRENCY_OPTION_NOT_EXIST, currency));
        }
    }

    public void checkCurrency() {
        String currency = getCurrency();
        Object exchangeRate = getExchangeRate();
        if (Strings.isNullOrEmpty(currency) && !ObjectDataExt.isValueEmpty(exchangeRate)) {
            throw new ValidateException(I18N.text(I18NKey.CURRENCY_CANNOT_BE_EMPTY));
        }
    }

    public void checkDetailCurrency(IObjectData masterData) {
        if (masterData == null) {
            return;
        }
        String masterCurrency = ObjectDataExt.of(masterData).getCurrency();
        String currency = getCurrency();
        if (!Strings.isNullOrEmpty(masterCurrency)) {
            if (Strings.isNullOrEmpty(currency)) {
                setCurrency(masterCurrency);
                setExchangeRate(ObjectDataExt.of(masterData).getExchangeRate());
                setExchangeRateVersion(ObjectDataExt.of(masterData).getExchangeRateVersion());
                setFunctionalCurrency(ObjectDataExt.of(masterData).getFunctionalCurrency());
                return;
            }
            if (Objects.equals(masterCurrency, currency)) {
                if (isValueEmpty(getExchangeRate())) {
                    setExchangeRate(ObjectDataExt.of(masterData).getExchangeRate());
                }
                if (Strings.isNullOrEmpty(getExchangeRateVersion())) {
                    setExchangeRateVersion(ObjectDataExt.of(masterData).getExchangeRateVersion());
                }
                if (Strings.isNullOrEmpty(getFunctionalCurrency())) {
                    setFunctionalCurrency(ObjectDataExt.of(masterData).getFunctionalCurrency());
                }
                return;
            }
        }
        if (!ObjectDataExt.isValueEqual(masterCurrency, currency, TEXT)) {
            throw new ValidateException(I18N.text(I18NKey.MASTER_DETAIL_CURRENCY_NOT_EQUAL));
        }
    }

    public void fillMultiCurrencyFields(List<MtCurrency> currencyList, String personCurrencyCode) {
        if (CollectionUtils.empty(currencyList)) {
            return;
        }
        String currency = getCurrency();
        Object exchangeRate = getExchangeRate();
        MtCurrency functionalCurrency = currencyList.stream().filter(MtCurrency::getIsFunctional).findFirst().orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.OBJECT_NOT_EXIST)));

        Map<String, MtCurrency> currencyMap = currencyList.stream().collect(Collectors.toMap(MtCurrency::getCurrencyCode, x -> x));

        MtCurrency finalCurrency = currencyList.stream()
                .filter(x -> Objects.equals(personCurrencyCode, x.getCurrencyCode()) && x.isLifeStatusByParam(DELETE_STATUS.NORMAL))
                .findFirst().orElse(functionalCurrency);

        if (Strings.isNullOrEmpty(currency)) {
            setCurrency(finalCurrency.getCurrencyCode());
            setExchangeRate(finalCurrency.getExchangeRate());
            setExchangeRateVersion(finalCurrency.getExchangeRateVersion());
            setFunctionalCurrency(functionalCurrency.getCurrencyCode());
        } else if (ObjectDataExt.isValueEmpty(exchangeRate)) {
            MtCurrency origCurrency = currencyMap.get(currency);
            if (Objects.isNull(origCurrency)) {
                throw new ValidateException(I18N.text(I18NKey.CURRENCY_OPTION_NOT_EXIST, currency));
            }
            setExchangeRate(origCurrency.getExchangeRate());
            setExchangeRateVersion(origCurrency.getExchangeRateVersion());
            setFunctionalCurrency(functionalCurrency.getCurrencyCode());
        } else if (ObjectDataExt.isValueEmpty(getFunctionalCurrency())) {
            setFunctionalCurrency(functionalCurrency.getCurrencyCode());
        }
    }

    public void removeEmptyMultiCurrencyFields() {
        if (containsField(FieldDescribeExt.CURRENCY_FIELD)) {
            String currency = getCurrency();
            if (Strings.isNullOrEmpty(currency)) {
                remove(FieldDescribeExt.CURRENCY_FIELD);
            }
        }
        if (containsField(FieldDescribeExt.EXCHANGE_RATE_FIELD)) {
            Object exchangeRate = getExchangeRate();
            if (isValueEmpty(exchangeRate)) {
                remove(FieldDescribeExt.EXCHANGE_RATE_FIELD);
            }
        }
        if (containsField(FieldDescribeExt.EXCHANGE_RATE_VERSION_FIELD)) {
            String exchangeRateVersion = getExchangeRateVersion();
            if (Strings.isNullOrEmpty(exchangeRateVersion)) {
                remove(FieldDescribeExt.EXCHANGE_RATE_VERSION_FIELD);
            }
        }
        if (containsField(FieldDescribeExt.FUNCTIONAL_CURRENCY_FIELD)) {
            String functionalCurrency = getFunctionalCurrency();
            if (Strings.isNullOrEmpty(functionalCurrency)) {
                remove(FieldDescribeExt.FUNCTIONAL_CURRENCY_FIELD);
            }
        }
    }

    public boolean isDataOutOwner(User user) {
        if (Objects.isNull(user) || !user.isOutUser()) {
            return false;
        }

        return Objects.equals(user.getOutTenantId(), objectData.getOutTenantId()) &&
                Objects.equals(Lists.newArrayList(user.getOutUserId()), objectData.getOutOwner());
    }

    public void convertRichTextFromMetadataWithField(Set<IFieldDescribe> richTextFields) {
        Set<String> list = getProcessableRichTextFields(richTextFields);
        convertRichTextFromMetadataWithApiName(list);
    }

    public void convertRichTextFromMetadataWithApiName(Set<String> richTextFields) {
        if (CollectionUtils.empty(richTextFields)) {
            return;
        }
        richTextFields.forEach(a -> {
            if (!containsField(a)) {
                return;
            }
            objectData.set(RichTextExt.getRichTextAbstractName(a), objectData.get(a));
            objectData.set(a, objectData.get(RichTextExt.getRichTextNameInMD(a)));
            remove(RichTextExt.getRichTextNameInMD(a));
        });
    }

    public void convertWhatListToMetadataWithField(List<WhatList> whatListFields) {
        CollectionUtils.nullToEmpty(whatListFields).forEach(whatList -> {
            if (!containsField(whatList.getIdFieldApiName())) {
                return;
            }
            Map<String, Set<String>> result = Maps.newHashMap();
            List<WhatListData> whatListDataList = parseWhatListData(whatList);
            CollectionUtils.nullToEmpty(whatListDataList).forEach(a -> {
                result.putIfAbsent(a.getDescribeApiName(), Sets.newLinkedHashSet());
                result.get(a.getDescribeApiName()).add(a.getId());
            });
            Map<String, List<String>> collect = result.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, b -> Lists.newArrayList(b.getValue())));
            set(whatList.getApiName(), collect);
            set(whatList.getApiNameFieldApiName(), Lists.newArrayList(result.keySet()));
        });

    }

    public void convertRichTextToMetadataWithField(Set<IFieldDescribe> richTextFields) {
        Set<String> list = getProcessableRichTextFields(richTextFields);
        convertRichTextToMetadataWithApiName(list);
    }

    private Set<String> getProcessableRichTextFields(Set<IFieldDescribe> richTextFields) {
        if (CollectionUtils.empty(richTextFields)) {
            return null;
        }

        //协同富文本不处理
        Set<String> list = richTextFields.stream()
                .filter(RichTextExt::isProcessableRichText)
                .filter(IFieldDescribe::isActive)
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        return list;
    }

    public void convertRichTextToMetadataWithApiName(Set<String> richTextFields) {
        if (CollectionUtils.empty(richTextFields)) {
            return;
        }
        extractAbstractOfRichText(richTextFields);
        convertAbstractFieldsToMetadata(richTextFields, IFieldType.HTML_RICH_TEXT);
    }

    private void convertAbstractFieldsToMetadata(Set<String> fields, String fieldType) {
        CollectionUtils.nullToEmpty(fields).forEach(field -> {
            if (!containsField(field)) {
                return;
            }
            boolean isBigText = StringUtils.equals(fieldType, BIG_TEXT);
            if (!isBigText && !containsField(RichTextExt.getRichTextAbstractName(field))) {
                return;
            }
            //原生字段提交的是带格式的文本
            Object raw = objectData.get(field);
            String strRaw = Objects.isNull(raw) ? null : String.valueOf(raw);
            //元数据__e存放带格式的文本
            objectData.set(RichTextExt.getRichTextNameInMD(field), strRaw);
            //__o提交的是不带格式的文本内容
            Object abstractValue = objectData.get(RichTextExt.getRichTextAbstractName(field));
            String strAbstract = getStrAbstract(isBigText, abstractValue, strRaw);
            //元数据原生字段存放不带格式的文本
            objectData.set(field, strAbstract);
            remove(RichTextExt.getRichTextAbstractName(field));
        });
    }

    private String getStrAbstract(boolean isBigText, Object abstractValue, String strRaw) {
        if (Objects.isNull(abstractValue)) {
            if (isBigText) {
                return StringUtils.substring(strRaw, 0, MAX_SIZE);
            }
            return null;
        }
        return String.valueOf(abstractValue);
    }

    public void extractAbstractOfRichText(IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (describeExt.isSlaveObject()) {
            return;
        }
        extractAbstractOfRichText(describeExt.getRichTextFields());
    }

    /**
     * 提取富文本的摘要信息
     */
    public void extractAbstractOfRichText(List<HtmlRichText> fields) {
        Set<String> apiNames = CollectionUtils.nullToEmpty(fields)
                .stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());

        extractAbstractOfRichText(apiNames);
    }

    public void extractAbstractOfRichText(Set<String> fieldApiNames) {
        CollectionUtils.nullToEmpty(fieldApiNames).forEach(fieldApiName -> {
            if (!containsField(fieldApiName)) {
                return;
            }
            Object raw = objectData.get(fieldApiName);
            String rawContent = Objects.isNull(raw) ? null : String.valueOf(raw);
            if (!Strings.isNullOrEmpty(rawContent)) {
                objectData.set(RichTextExt.getRichTextAbstractName(fieldApiName), RichTextExt.parseTextFromHtml(rawContent));
            } else {
                objectData.set(RichTextExt.getRichTextAbstractName(fieldApiName), null);
            }
        });
    }

    public void convertCooperativeRichTextToMetadataWithField(Set<IFieldDescribe> richTextFields) {
        Set<String> list = getProcessableCooperativeRichTextFields(richTextFields);
        convertCooperativeRichTextToMetadataWithApiName(list);
    }

    private Set<String> getProcessableCooperativeRichTextFields(Set<IFieldDescribe> richTextFields) {
        if (CollectionUtils.empty(richTextFields)) {
            return Collections.emptySet();
        }

        //只处理协同富文本
        return richTextFields.stream()
                .filter(RichTextExt::isProcessableCooperativeRichText)
                .filter(IFieldDescribe::isActive)
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
    }

    public void convertCooperativeRichTextToMetadataWithApiName(Set<String> richTextFields) {
        if (CollectionUtils.empty(richTextFields)) {
            return;
        }
        richTextFields.forEach(field -> {
            if (!containsField(field) || containsField(RichTextExt.getRichTextNameInMD(field))) {
                return;
            }
            Object raw = objectData.get(field);
            if (Objects.isNull(raw)) {
                String richTextAbstractName = RichTextExt.getRichTextAbstractName(field);
                if (containsField(richTextAbstractName)) {
                    objectData.set(field, null);
                    objectData.set(RichTextExt.getRichTextNameInMD(field), null);
                }
                return;
            }
            String xtRichTextJson = JSON.toJSONString(raw);
            JSONObject xtRichTextJsonObj = JSON.parseObject(xtRichTextJson);
            Object value = xtRichTextJsonObj.get("text");
            objectData.set(field, Objects.isNull(value) ? StringUtils.EMPTY : String.valueOf(value));
            //元数据__e存放带格式的文本
            objectData.set(RichTextExt.getRichTextNameInMD(field), xtRichTextJson);
            //原生字段提交的是带格式的文本
        });
    }

    public void convertCooperativeRichTextFromMetadataWithField(Set<IFieldDescribe> richTextFields) {
        Set<String> list = getProcessableCooperativeRichTextFields(richTextFields);
        convertCooperativeRichTextFromMetadataWithApiName(list);
    }

    public void convertCooperativeRichTextFromMetadataWithApiName(Set<String> richTextFields) {
        if (CollectionUtils.empty(richTextFields)) {
            return;
        }
        richTextFields.forEach(a -> {
            if (!containsField(a) || !containsField(RichTextExt.getRichTextNameInMD(a))) {
                return;
            }
            String xTRichTextStr = objectData.get(RichTextExt.getRichTextNameInMD(a), String.class);
            Object value = objectData.get(a);
            String valueStr = Objects.isNull(value) ? StringUtils.EMPTY : String.valueOf(value);
            objectData.set(RichTextExt.getRichTextAbstractName(a), valueStr);
            objectData.set(a, JSON.parseObject(xTRichTextStr));
            remove(RichTextExt.getRichTextNameInMD(a));
        });
    }

    public void convertBigTextFromMetadataWithField(Set<IFieldDescribe> richTextFields) {
        Set<String> list = getBigTextToMetadataWithFields(richTextFields);
        convertRichTextFromMetadataWithApiName(list);
    }


    public void convertBigTextToMetadataWithField(Set<IFieldDescribe> bigTextFields) {
        convertAbstractFieldsToMetadata(getBigTextToMetadataWithFields(bigTextFields), BIG_TEXT);
    }

    private Set<String> getBigTextToMetadataWithFields(Set<IFieldDescribe> bigTextFields) {
        if (CollectionUtils.empty(bigTextFields)) {
            return Collections.emptySet();
        }

        return bigTextFields.stream()
                .filter(x -> StringUtils.equals(x.getType(), BIG_TEXT))
                .filter(IFieldDescribe::isActive)
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
    }

    public void convertDateFieldValueToSystemZone(IObjectDescribe describe) {
        convertDateFieldValue(ObjectDescribeExt.of(describe).getRealTypeIsDate(), DateUtils::convertDateValueToSystem);
    }

    public void convertDateFieldValueToCustomZone(IObjectDescribe describe) {
        convertDateFieldValue(ObjectDescribeExt.of(describe).getRealTypeIsDate(), DateUtils::convertDateValueToCustom);
    }

    public void convertDateFieldValueToSystemZone(Collection<String> fieldNames) {
        convertDateFieldValue(fieldNames, DateUtils::convertDateValueToSystem);
    }

    public void convertDateFieldValueToCustomZone(Collection<String> fieldNames) {
        convertDateFieldValue(fieldNames, DateUtils::convertDateValueToCustom);
    }

    private void convertDateFieldValue(Collection<String> fieldNames, ToLongFunction<Long> function) {
        fieldNames.forEach(apiName -> {
            Object o = get(apiName);
            if (o instanceof Number) {
                long value = ((Number) o).longValue();
                set(apiName, function.applyAsLong(value));
            }
        });
    }

    /**
     * 1 过滤掉自定义字段
     * 2 过滤掉null和空字符串的字段
     *
     * @param dataList
     * @return
     */
    public static List<IObjectData> copyListNonNullAndNonCustomField(List<IObjectData> dataList) {
        List<IObjectData> result = Lists.newArrayList();
        CollectionUtils.nullToEmpty(dataList)
                .stream()
                .filter(Objects::nonNull)
                .forEach(data -> {
                    List<String> fieldList = ObjectDataExt.of(data).toMap().entrySet()
                            .stream()
                            .filter(entry -> !entry.getKey().endsWith("__c"))
                            .filter(entry -> !entry.getKey().endsWith("__r"))
                            .filter(entry -> !entry.getKey().endsWith("__l"))
                            .filter(entry -> !ObjectDataExt.isValueEmpty(entry.getValue()))
                            .map(Map.Entry::getKey)
                            .collect(Collectors.toList());
                    result.add(new ObjectData(ObjectDataExt.of(data).toMap(fieldList)));
                });
        return result;
    }

    public void setIfAbsent(String key, Object value) {
        if (!containsField(key)) {
            set(key, value);
        }
    }

    private static void removeDuplicatedValueInListValue(String tenantId, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        List<IFieldDescribe> fieldByTypes = ObjectDescribeExt.of(objectDescribe).getFieldByTypes(Sets.newHashSet(IFieldType.EMPLOYEE, DEPARTMENT,
                IFieldType.EMPLOYEE_MANY, IFieldType.DEPARTMENT_MANY, IFieldType.SELECT_MANY, IFieldType.OBJECT_REFERENCE_MANY,
                IFieldType.DIMENSION, IFieldType.OUT_EMPLOYEE, OUT_DEPARTMENT));
        Set<String> apiNames = CollectionUtils.nullToEmpty(fieldByTypes).stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
        CollectionUtils.nullToEmpty(objectDataList)
                .stream()
                .filter(Objects::nonNull)
                .forEach(data -> ObjectDataExt.of(data).removeDuplicatedListValue(apiNames));
    }

    public void removeDuplicatedListValue(Set<String> apiNames) {
        toMap().entrySet()
                .stream()
                .filter(entry -> apiNames.contains(entry.getKey()))
                .forEach(entry -> {
                    if (Objects.nonNull(entry.getValue()) && entry.getValue() instanceof List) {
                        List list = (List) entry.getValue();
                        if (list.size() < 2) {
                            return;
                        }
                        Set set = Sets.newLinkedHashSet(list);
                        entry.setValue(Lists.newArrayList(set));
                    }
                });
    }

    public List<WhatListData> parseWhatListData(WhatList whatList) {
        Object rawValue = get(whatList.getIdFieldApiName());
        if (ObjectUtils.isEmpty(rawValue) || !(rawValue instanceof List)) {
            return Lists.newArrayList();
        }

        List<Map> list = (List<Map>) rawValue;
        List<WhatListData> result = Lists.newArrayList();
        list.forEach(a -> {
            Object rawApiName = a.get("describe_api_name");
            if (ObjectUtils.isEmpty(rawApiName)) {
                return;
            }
            Object rawId = a.get("id");
            if (ObjectUtils.isEmpty(rawId)) {
                return;
            }
            String describeApiName = String.valueOf(rawApiName);
            String dataId = String.valueOf(rawId);

            result.add(WhatListData.builder().id(dataId).describeApiName(describeApiName).build());
        });
        return result;
    }

    public static void correctValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe) {
        removeDuplicatedValueInListValue(user.getTenantId(), objectDataList, describe);
        convertEmptyValueToNull(objectDataList, describe);
        convertDateTimeValue(objectDataList, describe);
        removePGNotSupportValue(objectDataList, describe);
        correctEmptyPublicDataFieldFlag(user, objectDataList, describe);
    }

    /**
     * 纠正公共对象的数据可见范围字段
     *
     * @param objectDataList
     * @param describe
     */
    private static void correctEmptyPublicDataFieldFlag(User user, List<IObjectData> objectDataList, IObjectDescribe describe) {
        if (!describe.isPublicObject()) {
            return;
        }

        CollectionUtils.nullToEmpty(objectDataList)
                .stream()
                .filter(Objects::nonNull)
                .map(ObjectDataExt::of)
                .filter(data -> data.privateData() && data.downstreamTenantIsEmpty())
                .forEach(data -> data.set(DOWNSTREAM_TENANT_ID, Lists.newArrayList(user.getTenantId())));
    }

    private boolean privateData() {
        Object publicDataType = get(IObjectData.PUBLIC_DATA_TYPE);
        return IObjectData.PRIVATE_DATA.equals(publicDataType);
    }

    private boolean downstreamTenantIsEmpty() {
        Object downstreamTenantId = get(IObjectData.DOWNSTREAM_TENANT_ID);
        return isValueEmpty(downstreamTenantId);
    }

    private static void removePGNotSupportValue(List<IObjectData> objectDataList, IObjectDescribe describe) {
        List<IFieldDescribe> fieldByTypes = ObjectDescribeExt.of(describe).getFieldByTypes(
                Sets.newHashSet(NUMBER, CURRENCY, PERCENTILE, TEXT, LONG_TEXT, PHONE_NUMBER, URL, HTML_RICH_TEXT, EMAIL,
                        SELECT_ONE, OBJECT_REFERENCE, MASTER_DETAIL, AUTO_NUMBER, LOCATION));
        Set<String> apiNames = CollectionUtils.nullToEmpty(fieldByTypes).stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
        CollectionUtils.nullToEmpty(objectDataList)
                .stream()
                .filter(Objects::nonNull)
                .forEach(data -> apiNames.forEach(field -> {
                    Object rawValue = data.get(field);
                    if (Objects.isNull(rawValue)) {
                        return;
                    }

                    if (rawValue instanceof String) {
                        String text = String.valueOf(rawValue);
                        if (StringUtils.contains(text, '\u0000')) {
                            String replace = StringUtils.replace(text, "\u0000", "");
                            data.set(field, replace);
                        }
                    }
                }));
    }

    private static void convertDateTimeValue(List<IObjectData> objectDataList, IObjectDescribe describe) {
        List<IFieldDescribe> fieldByTypes = ObjectDescribeExt.of(describe).getFieldByTypes(
                Sets.newHashSet(DATE_TIME, TIME, DATE));
        Set<String> apiNames = CollectionUtils.nullToEmpty(fieldByTypes).stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
        CollectionUtils.nullToEmpty(objectDataList)
                .stream()
                .filter(Objects::nonNull)
                .forEach(data -> apiNames.forEach(field -> {
                    Object rawValue = data.get(field);
                    if (Objects.isNull(rawValue)) {
                        return;
                    }

                    if (rawValue instanceof String && NumberUtils.isDigits(String.valueOf(rawValue))) {
                        BigDecimal bigInteger = new BigDecimal(String.valueOf(rawValue));
                        data.set(field, bigInteger.longValue());
                    }
                }));
    }

    private static void convertEmptyValueToNull(List<IObjectData> objectDataList, IObjectDescribe describe) {
        List<IFieldDescribe> fieldByTypes = ObjectDescribeExt.of(describe).getFieldByTypes(
                Sets.newHashSet(NUMBER, CURRENCY, PERCENTILE, TRUE_OR_FALSE, OBJECT_REFERENCE_MANY, SELECT_MANY,
                        EMPLOYEE_MANY, DEPARTMENT_MANY, EMPLOYEE, DEPARTMENT, IMAGE, FILE_ATTACHMENT, BIG_FILE_ATTACHMENT,
                        SIGNATURE, OUT_EMPLOYEE, OUT_DEPARTMENT, DIMENSION, DATE, DATE_TIME, TIME));
        Set<String> apiNames = CollectionUtils.nullToEmpty(fieldByTypes).stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
        CollectionUtils.nullToEmpty(objectDataList)
                .stream()
                .filter(Objects::nonNull)
                .forEach(data -> apiNames.forEach(field -> {
                    if (Objects.equals(data.get(field), "")) {
                        data.set(field, null);
                    }
                }));
    }

    public void validateFileFieldDataType(IFieldDescribe fieldDescribe) {
        if (Objects.isNull(fieldDescribe)) {
            return;
        }

        Object fieldValue = objectData.get(fieldDescribe.getApiName());
        if (Objects.isNull(fieldValue)) {
            return;
        }
        if (!(fieldValue instanceof Collection)) {
            throw new ValidateException(I18NExt.getOrDefault(FIELD_DATA_TYPE_WRONG, FIELD_DATA_TYPE_WRONG, fieldDescribe.getLabel() + fieldDescribe.getApiName(), fieldValue));
        }
        List fileDataList = (List) fieldValue;
        if (CollectionUtils.empty(fileDataList)) {
            return;
        }
        Set<String> fieldType = Sets.newHashSet(IMAGE, FILE_ATTACHMENT, BIG_FILE_ATTACHMENT, SIGNATURE);
        if (fieldType.contains(fieldDescribe.getType())) {
            String message = StringUtils.isEmpty(objectData.getName()) ? objectData.getId() : objectData.getName();
            String label = fieldDescribe.getLabel();
            fileDataList.forEach(data -> {
                if (!(data instanceof Map)) {
                    throw new ValidateException(I18NExt.getOrDefault(DATA_FIELD, DATA_FIELD, message, label) + I18NExt.getOrDefault(PATTERN_ILLEGAL, PATTERN_ILLEGAL));
                }
            });
            String result = validDataParameters(fileDataList);
            if (!result.equals("OK")) {
                throw new ValidateException(I18NExt.getOrDefault(DATA_FIELD, DATA_FIELD, message, label) + I18NExt.getOrDefault(DATA_FILE_IS_REQUIRED_ERROR, DATA_FILE_IS_REQUIRED_ERROR, result));
            }
        }
    }

    private String validDataParameters(List<Map> datas) {
        for (Map data : datas) {
            for (String key : REQUIRED_DATA_KEY) {
                if (!data.containsKey(key) || StringUtils.isBlank((String) data.get(key))) {
                    return key;
                }
            }
        }
        return "OK";
    }

    public void validateEmployeeFieldDataType(IFieldDescribe fieldDescribe) {
        if (Objects.isNull(fieldDescribe)) {
            return;
        }

        Object fieldValue = objectData.get(fieldDescribe.getApiName());
        if (Objects.isNull(fieldValue)) {
            return;
        }

        if (fieldValue instanceof List) {
            List<Object> list = (List) fieldValue;
            list.forEach(item -> {
                if (Objects.isNull(item)) {
                    throw new ValidateException(I18NExt.getOrDefault(FIELD_DATA_TYPE_WRONG, FIELD_DATA_TYPE_WRONG, fieldDescribe.getLabel() + fieldDescribe.getApiName(), fieldValue));
                }
                //校验内容是 数字的string
                if (!(item instanceof String)) {
                    throw new ValidateException(I18NExt.getOrDefault(FIELD_DATA_TYPE_WRONG, FIELD_DATA_TYPE_WRONG, fieldDescribe.getLabel() + fieldDescribe.getApiName(), fieldValue));
                }
                if (!NumberUtils.isParsable(String.valueOf(item)) && !StringUtils.equals(fieldDescribe.getType(), OUT_DEPARTMENT)) {
                    throw new ValidateException(I18NExt.getOrDefault(FIELD_DATA_TYPE_WRONG, FIELD_DATA_TYPE_WRONG, fieldDescribe.getLabel() + fieldDescribe.getApiName(), fieldValue));
                }
            });
        } else {
            throw new ValidateException(I18NExt.getOrDefault(FIELD_DATA_TYPE_WRONG, FIELD_DATA_TYPE_WRONG, fieldDescribe.getLabel() + fieldDescribe.getApiName(), fieldValue));
        }
    }

    /**
     * 当owner字段携带wheres条件时，不下发owner相关的默认值
     *
     * @param ownerField
     */
    @Deprecated
    public void removeOwnerIfWithWheres(Optional<IFieldDescribe> ownerField) {
        if (ownerField.isPresent() && CollectionUtils.notEmpty((List) ownerField.get().get("wheres"))) {
            remove(Sets.newHashSet(OWNER,
                    FieldDescribeExt.getLookupNameByFieldName(OWNER),
                    FieldDescribeExt.getEmployeeNameByFieldName(OWNER),
                    DATA_OWN_DEPARTMENT,
                    FieldDescribeExt.getLookupNameByFieldName(DATA_OWN_DEPARTMENT),
                    FieldDescribeExt.getEmployeeNameByFieldName(DATA_OWN_DEPARTMENT),
                    DATA_OWN_ORGANIZATION,
                    FieldDescribeExt.getLookupNameByFieldName(DATA_OWN_ORGANIZATION),
                    FieldDescribeExt.getEmployeeNameByFieldName(DATA_OWN_ORGANIZATION)));

        }
    }

    /**
     * 根据字段描述无法设置默认值时，清下默认值
     * 当前使用字段：负责人、归属部门、归属组织
     *
     * @param objectDescribe
     */
    public void removeDefaultValueIfInvalid(IObjectDescribe objectDescribe) {
        ObjectDescribeExt descExt = ObjectDescribeExt.of(objectDescribe);

        if ((descExt.containsField(OWNER) && FieldDescribeExt.of(descExt.getFieldDescribe(OWNER)).invalidDefaultValue())
                || (descExt.containsField(DATA_OWN_ORGANIZATION) && FieldDescribeExt.of(descExt.getFieldDescribe(DATA_OWN_ORGANIZATION)).invalidDefaultValue())
                || (descExt.containsField(DATA_OWN_DEPARTMENT) && FieldDescribeExt.of(descExt.getFieldDescribe(DATA_OWN_DEPARTMENT)).invalidDefaultValue())) {
            remove(Sets.newHashSet(OWNER,
                    FieldDescribeExt.getLookupNameByFieldName(OWNER),
                    FieldDescribeExt.getEmployeeNameByFieldName(OWNER),
                    DATA_OWN_DEPARTMENT,
                    FieldDescribeExt.getLookupNameByFieldName(DATA_OWN_DEPARTMENT),
                    FieldDescribeExt.getEmployeeNameByFieldName(DATA_OWN_DEPARTMENT),
                    DATA_OWN_ORGANIZATION,
                    FieldDescribeExt.getLookupNameByFieldName(DATA_OWN_ORGANIZATION),
                    FieldDescribeExt.getEmployeeNameByFieldName(DATA_OWN_ORGANIZATION)));

        }
    }

    public void removeFieldsNotSupportEdit(IObjectDescribe describe) {
        //不能直接编辑负责人
        removeDataOwner();
        // 不能更新计算和统计字段，灰度对象（流程的对象）可以更新
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ALLOW_UPDATE_FORMULA_GRAY_OBJECTS, describe.getApiName())) {
            removeCalculateField(describe);
        }
        //不能将币种、汇率更新为空
        removeEmptyMultiCurrencyFields();
    }

    public String getMark() {
        Object mark = get(MARK_API_NAME);
        if (Objects.isNull(mark)) {
            return null;
        }
        return mark.toString();
    }

    public void setMark(String mark) {
        set(MARK_API_NAME, mark);
    }

    public void setMasterDataFlag() {
        set(MASTER_DATA_FLAG, true);
    }

    public void clearMasterDataFlag() {
        remove(MASTER_DATA_FLAG);
    }

    public boolean hasMasterDataFlag() {
        return Boolean.TRUE.equals(get(MASTER_DATA_FLAG));
    }

    /**
     * 对象下的字段,优先使用的当前语种的值
     *
     * @param describe
     */
    public void handleMultiLangField(IObjectDescribe describe) {
        if (Objects.isNull(describe) || !AppFrameworkConfig.objectMultiLangGray(describe.getTenantId(), describe.getApiName())) {
            return;
        }
        Set<IFieldDescribe> enableMultiLangField = ObjectDescribeExt.of(describe).getEnableMultiLangField();
        if (CollectionUtils.empty(enableMultiLangField)) {
            return;
        }
        String lang = I18N.getContext().getLanguage();
        enableMultiLangField.forEach(fieldDescribe -> {
            String fieldName = fieldDescribe.getApiName();
            Object fieldValue = getMultiLangFieldValue(fieldName, lang);
            if (!isValueEmpty(fieldValue)) {
                set(FieldDescribeExt.getLookupNameByFieldName(fieldName), fieldValue);
            }
        });
    }


    public Map<String, Object> getMultiLangValue(String tenantId, String describeApiName, IFieldDescribe fieldDescribe) {
        if (!AppFrameworkConfig.objectMultiLangGray(tenantId, describeApiName) || !BooleanUtils.isTrue(fieldDescribe.getEnableMultiLang())) {
            return Collections.emptyMap();
        }
        String fieldName = fieldDescribe.getApiName();
        String langFieldName = FieldDescribeExt.getMultiLangExtraFieldName(fieldName);
        Map map = get(langFieldName, Map.class);
        if (CollectionUtils.empty(map)) {
            return Collections.emptyMap();
        }
        List<Lang> langs = AppFrameworkConfig.multiLangSupport(Lang.values(), fieldName, fieldName);
        Map<String, Object> result = Maps.newHashMap();
        for (Lang lang : langs) {
            Object value = map.get(lang.getValue());
            result.put(FieldDescribeExt.getMultiLangTempFieldName(fieldName, lang), value);
        }
        return result;
    }

    public Object getMultiLangFieldValue(String fieldName, String lang) {
        String langFieldName = FieldDescribeExt.getMultiLangExtraFieldName(fieldName);
        Map map = get(langFieldName, Map.class);
        if (CollectionUtils.empty(map)) {
            return null;
        }
        return map.get(lang);
    }

//    private void removeMultiLangFieldValue(String fieldName) {
//        String langFieldName = FieldDescribeExt.getMultiLangExtraFieldName(fieldName);
//        remove(langFieldName);
//    }

    public String getIdForCalculate() {
        String tempId = getTemporaryId();
        if (!Strings.isNullOrEmpty(tempId)) {
            return tempId;
        }
        String dataIndex = getDataIndex();
        if (!Strings.isNullOrEmpty(dataIndex)) {
            return dataIndex;
        }
        return getId();
    }

    public String getTemporaryId() {
        return get(TEMPORARY_ID, String.class);
    }

    public void setTemporaryId(String temporaryId) {
        set(TEMPORARY_ID, temporaryId);
    }

    public boolean hasTemporaryId() {
        return Objects.nonNull(getTemporaryId());
    }

    public void fillTemporaryId() {
        if (hasTemporaryId()) {
            return;
        }
        if (hasId()) {
            setTemporaryId(getId());
            return;
        }
        setTemporaryId(generateId());
    }

    public void removeTemporaryId() {
        remove(TEMPORARY_ID);
    }

    public String getDataIndex() {
        return get(DATA_INDEX, String.class);
    }

    public void setDataIndex(String temporaryId) {
        set(DATA_INDEX, temporaryId);
    }

    public String getChangeOrderRule() {
        return get(CHANGE_ORDER_RULE, String.class);
    }

    public String getOriginalDataId() {
        return get(ORIGINAL_DATA, String.class);
    }

    public String getOriginalDetailDataId() {
        return get(ORIGINAL_DETAIL_DATA, String.class);
    }

    public void setOriginalDetailDataId(String originalDetailDataId) {
        set(ORIGINAL_DETAIL_DATA, originalDetailDataId);
    }

    public void setChangedType(String changedType) {
        set(CHANGED_TYPE, changedType);
    }

    public String getChangedType() {
        return get(CHANGED_TYPE, String.class);
    }

    public void setChangeOrderEffectiveStatus(ChangeOrderEffectiveStatus status) {
        set(EFFECTIVE_STATUS, status.getCode());
    }

    public ChangeOrderEffectiveStatus getChangeOrderEffectiveStatus() {
        return ChangeOrderEffectiveStatus.of(get(EFFECTIVE_STATUS, String.class));
    }

    public ChangeOrderChangedStatus getChangeOrderChangedStatus() {
        return ChangeOrderChangedStatus.of(get(CHANGED_STATUS, String.class));
    }

    public List<String> validateDateRangeField(IObjectDescribe objectDescribe) {
        // 日期范围字段校验
        List<String> errorMsgList = Lists.newArrayList();
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
        List<IFieldDescribe> groupFields = objectDescribeExt.getFieldByTypes(Sets.newHashSet(IFieldType.GROUP));
        groupFields.stream()
                .map(x -> (GroupField) x)
                .filter(field -> StringUtils.equals(GroupField.GROUP_TYPE_DATE_TIME_RANGE, field.getGroupType()))
                .map(x -> (DateTimeRangeFieldDescribe) x)
                .forEach(fieldDescribe -> {
                    String startTimeFieldApiName = fieldDescribe.getStartTimeFieldApiName();
                    String endTimeFieldApiName = fieldDescribe.getEndTimeFieldApiName();
                    Long startTime = get(startTimeFieldApiName, Long.class);
                    Long endTime = get(endTimeFieldApiName, Long.class);
                    if (Objects.isNull(startTime) && Objects.isNull(endTime)) {
                        return;
                    }
                    if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
                        String fieldLabel = Objects.isNull(startTime) ? objectDescribeExt.getFieldLabelByName(startTimeFieldApiName)
                                : objectDescribeExt.getFieldLabelByName(endTimeFieldApiName);
                        errorMsgList.add(I18NExt.text(I18NKey.FIELD_NOT_NULL, fieldLabel));
                    } else if (endTime < startTime) {
                        String errorMsg = I18NExt.text(I18NKey.FIELD_NOT_LESS_THAN, objectDescribeExt.getFieldLabelByName(endTimeFieldApiName),
                                objectDescribeExt.getFieldLabelByName(startTimeFieldApiName));
                        errorMsgList.add(errorMsg);
                    }
                });
        return errorMsgList;
    }

    public static String formatNumberThousand(String numberValue) {
        return formatNumberThousand(numberValue, true);
    }

    public static String formatNumberThousand(String numberValue, boolean isFillMultiRegion) {
        if (Strings.isNullOrEmpty(numberValue)) {
            return numberValue;
        }
        if (isFillMultiRegion && StringUtils.isNotEmpty(MultiRegionContextHolder.getUserRegion())) {
            //考虑负数  增加单元测试
            //金额字段有小数位数 验证是否影响小数位数
            String multiRegionResult = MultiRegionNumberFormatUtils.formatForRegion(numberValue, true, null, null);
            if (StringUtils.isNotEmpty(multiRegionResult)) {
                return multiRegionResult;
            }
        }
        try {
            BigDecimal bigDecimal = new BigDecimal(numberValue);
            String plain = bigDecimal.toPlainString();
            if (plain.contains(".")) {
                String[] Number = plain.split("\\.");
                String intNumber = Number[0];
                String decimal = Number[1];
                String intThousandValue = addThousandSeparator(intNumber);
                return intThousandValue + "." + decimal;
            } else {
                return addThousandSeparator(plain);
            }
        } catch (Exception e) {
            log.warn("formatNumberThousand error,number:{}", numberValue, e);
        }
        return numberValue;
    }

    private static String addThousandSeparator(String numberString) {
        try {
            String prefix = null;
            if (StringUtils.startsWith(numberString, "-")) {
                prefix = "-";
                numberString = StringUtils.substringAfter(numberString, "-");
            }
            // 将数字字符串解析为长整型数值
            long number = Long.parseLong(numberString);
            // 创建DecimalFormat对象，指定千分位格式
            DecimalFormat decimalFormat = new DecimalFormat("#,###");
            // 使用DecimalFormat对象格式化数字，得到带有千分位的字符串
            String rowNum = decimalFormat.format(number);
            return StringUtils.isBlank(prefix) ? rowNum : prefix + rowNum;
        } catch (NumberFormatException e) {
            log.error("Invalid number format,value:{}", numberString, e);
            return numberString;
        }
    }

    public static Map<String, List<IObjectData>> diffDetailWithTemporaryId(Map<String, List<IObjectData>> oldDetails,
                                                                           Map<String, List<IObjectData>> newDetails,
                                                                           Map<String, IObjectDescribe> describeMap,
                                                                           boolean containTemporaryId) {
        Map<String, List<IObjectData>> result = Maps.newHashMap();
        if (CollectionUtils.empty(describeMap)) {
            return result;
        }
        CollectionUtils.nullToEmpty(newDetails).forEach((apiName, details) -> {
            List<IObjectData> oldDataList = CollectionUtils.nullToEmpty(oldDetails).get(apiName);
            if (CollectionUtils.empty(oldDataList)) {
                return;
            }
            List<IObjectData> diffList = Lists.newArrayList();
            Map<String, IObjectData> oldDetailsMap = oldDataList.stream()
                    .collect(Collectors.toMap(x -> ObjectDataExt.of(x).getTemporaryId(), Function.identity()));
            for (IObjectData detail : details) {
                String temporaryId = ObjectDataExt.of(detail).getTemporaryId();
                IObjectData oldData = oldDetailsMap.get(temporaryId);
                if (Objects.isNull(oldData)) {
                    continue;
                }
                Map<String, Object> diffMap = ObjectDataExt.of(oldData).diff(detail, describeMap.get(apiName));
                if (containTemporaryId) {
                    diffMap.put(TEMPORARY_ID, temporaryId);
                }
                diffList.add(of(diffMap).getObjectData());
            }
            result.put(apiName, diffList);
        });
        return result;
    }

    public static Map<String, List<IObjectData>> diffDetail(Map<String, List<IObjectData>> oldDetails, Map<String, List<IObjectData>> newDetails,
                                                            Map<String, IObjectDescribe> describeMap, boolean containsId) {
        Map<String, List<IObjectData>> result = Maps.newHashMap();
        if (CollectionUtils.empty(describeMap)) {
            return result;
        }
        CollectionUtils.nullToEmpty(newDetails).forEach((apiName, details) -> {
            List<IObjectData> oldDataList = CollectionUtils.nullToEmpty(oldDetails).get(apiName);
            if (CollectionUtils.empty(oldDataList)) {
                return;
            }
            List<IObjectData> diffList = Lists.newArrayList();
            Map<String, IObjectData> oldDetailsMap = oldDataList.stream()
                    .collect(Collectors.toMap(IObjectData::getId, Function.identity()));
            for (IObjectData detail : details) {
                IObjectData oldData = oldDetailsMap.get(detail.getId());
                if (Objects.isNull(oldData)) {
                    continue;
                }
                Map<String, Object> diffMap = ObjectDataExt.of(oldData).diff(detail, describeMap.get(apiName));
                if (containsId) {
                    diffMap.put(DBRecord.ID, oldData.getId());
                }
                diffList.add(of(diffMap).getObjectData());
            }
            result.put(apiName, diffList);
        });
        return result;
    }

    public String getFieldLangValue(String fieldApiName) {
        String langValue = "";
        if (Objects.nonNull(get(FieldDescribeExt.getMultiLangFieldFromExtraField(fieldApiName)))) {
            langValue = get(FieldDescribeExt.getLookupNameByFieldName(fieldApiName), String.class);
        }
        return StringUtils.isEmpty(langValue) ? get(fieldApiName, String.class) : langValue;
    }

    /**
     * 公共对象2个预制字段赋值
     * 1. 一期只能新建公有数据，不能创建私有数据
     * 2. 公有数据默认所有已升级公共对象的企业所有员工可见
     * <p>
     * publicDataType 为空，默认新建所有企业可见的公共数据
     * downstreamTenants 为空
     * publicDataType 为私有，downstreamTenants 补充当前企业的值
     * publicDataType 为公共，downstreamTenants 所有企业（-99999）
     */
    public void initPublicDataFlagBeforeCreate(User user) {
        // 设置创建企业为当前企业
        set(IObjectData.CREATE_ENTERPRISE, Lists.newArrayList(user.getTenantId()));
        Object publicDataType = get(IObjectData.PUBLIC_DATA_TYPE);
        Object downstreamTenants = get(IObjectData.DOWNSTREAM_TENANT_ID);
        if (ObjectDataExt.isValueEmpty(publicDataType)) {
            set(IObjectData.PUBLIC_DATA_TYPE, IObjectData.PUBLIC_DATA);
            if (ObjectDataExt.isValueEmpty(downstreamTenants)) {
                set(IObjectData.DOWNSTREAM_TENANT_ID, Lists.newArrayList(IObjectData.PUBLIC_DATA_DOWNSTREAM_TENANT_ID));
            }
            return;
        }

        if (!ObjectDataExt.isValueEmpty(downstreamTenants)) {
            return;
        }
        if (IObjectData.PRIVATE_DATA.equals(publicDataType)) {
            set(IObjectData.DOWNSTREAM_TENANT_ID, Lists.newArrayList(user.getTenantId()));
            return;
        }
        if (IObjectData.PUBLIC_DATA.equals(publicDataType)) {
            set(IObjectData.DOWNSTREAM_TENANT_ID, Lists.newArrayList(IObjectData.PUBLIC_DATA_DOWNSTREAM_TENANT_ID));
        }
    }

    public List convertSelectManyData(IFieldDescribe selectManyField) {
        Object o = get(selectManyField.getApiName());
        if (!(o instanceof List)) {
            return Lists.newArrayList();
        }
        Object[] array = ((List) o).toArray();
        List<ISelectOption> options = ((SelectManyFieldDescribe) selectManyField).getSelectOptions();
        if (array.length == 0 || CollectionUtils.empty(options)) {
            return Lists.newArrayList();
        }
        List<String> result = Lists.newArrayList();
        for (Object item : array) {
            for (ISelectOption option : options) {
                if (option.getValue().equals(String.valueOf(item))) {
                    if (Objects.equals(SelectMany.OPTION_OTHER_VALUE, item)) {
                        result.add((String) get(selectManyField.getApiName() + "__o"));
                        break;
                    }
                    result.add(option.getLabel());
                }
            }
        }
        return result;
    }

    public String convertSelectOneData(IFieldDescribe selectOneField) {
        List<ISelectOption> options = ((SelectOneFieldDescribe) selectOneField).getSelectOptions();
        Object oldData = get(selectOneField.getApiName(), Object.class);
        if (CollectionUtils.empty(options) || Objects.isNull(oldData) || Strings.isNullOrEmpty(oldData.toString())) {
            return "";
        }
        String value = String.valueOf(oldData);
        for (ISelectOption option : options) {
            if (option.getValue().equals(value)) {
                if (Objects.equals(SelectOne.OPTION_OTHER_VALUE, value)) {
                    String otherValue = (String) get(selectOneField.getApiName() + "__o");
                    if (Strings.isNullOrEmpty(otherValue)) {
                        return option.getLabel();
                    } else {
                        return option.getLabel() + ": " + otherValue;
                    }
                }
                return option.getLabel();
            }
        }
        return "";
    }


    /**
     * 删除 signedUrl
     *
     * @param objDesc 数据的描述
     */
    public void removeSignedUrl(IObjectDescribe objDesc) {
        List<IFieldDescribe> signedFields = ObjectDescribeExt.of(objDesc).getFieldByRenderTypes(Sets.newHashSet(IMAGE, FILE_ATTACHMENT));
        if (Objects.isNull(signedFields)) {
            return;
        }
        signedFields.forEach(field -> {
            Object value = objectData.get(field.getApiName());
            if (ObjectUtils.isEmpty(value)) {
                return;
            }
            if (value instanceof Collection) {
                ((Collection<?>) value).forEach(item -> {
                    if (item instanceof Map) {
                        ((Map) item).remove(ImageExt.SIGNATURE_URL);
                    }
                });
            }
        });
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WhatListData {
        String id;
        String describeApiName;
    }

    @Data
    @AllArgsConstructor
    private static class FileEqualHelper {
        private final String filePath;
        private final String fileName;
        private final Boolean recapture;

        public static FileEqualHelper of(Map map, boolean needFileName) {
            String filePath = validateSuffix((String) map.get("path"), (String) map.get("ext"));
            String fileName = null;
            if (needFileName) {
                fileName = (String) map.get("filename");
            }
            Boolean recapture = (Boolean) map.get("recapture");
            return new FileEqualHelper(filePath, fileName, recapture);
        }
    }

    /**
     * 数据负责人分配策略及默认值
     */
    @Data
    @Builder
    public static class OwnerPolicy {
        /**
         * 该对象负责人字段指定了下游人员新建数据负责人指定方式为手动（负责人可以为空）
         */
        private boolean outUserAssignOwner;
        /**
         * 允许下游人员新建数据的时候指定负责人
         * 1、调用者允许
         */
        private boolean allowOutUserByArg;

        /**
         * 为下游人员分配的默认数据负责人
         */
        private Supplier<String> defaultDataOwnerId;

        /**
         * 当外部负责人为空时，设置外部负责人为当前操作人
         * 否则设置当前操作人为外部负责人
         */
        private boolean setOutOwnerWhenEmpty;

    }
}
