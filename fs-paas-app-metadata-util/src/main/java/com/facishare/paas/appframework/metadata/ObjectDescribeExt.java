package com.facishare.paas.appframework.metadata;

import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig;
import com.facishare.paas.appframework.metadata.exception.FieldNotExistException;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation.RelateField;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.dao.pg.entity.metadata.RelevantTeam;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.base.Equivalence;
import com.google.common.base.Strings;
import com.google.common.collect.*;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.Date;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.crm.openapi.Utils.*;
import static com.facishare.paas.common.util.UdobjConstants.*;
import static com.facishare.paas.metadata.api.describe.IFieldType.*;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;

/**
 * 对元数据ObjectDescribe的扩展包装类
 * <p>
 * Created by liyiguang on 2017/8/25.
 */
@Slf4j
public class ObjectDescribeExt implements IObjectDescribe {

    public final static String WORKFLOW_INSTANCE_ID = "workflow_instance_id";

    //付款记录的apiName
    public final static String PAYMENT_DESCRIBE_API_NAME = "payment_record";
    //人员对象的apiName
    public final static String PERSONNEL_OBJ_API_NAME = "PersonnelObj";
    //人员字段的对象虚拟apiName(计算公式里为了区分人员字段和查找关联到人员的特殊逻辑)
    public final static String EMPLOYEE_OBJ_API_NAME = "__EmployeeObj";
    // 部门字段的对象虚拟apiName(计算公式里为了区分部门字段和查找关联到部门的特殊逻辑)
    public final static String CALCULATE_DEPARTMENT_OBJ_API_NAME = "__DepartmentObj";

    public final static String PARTNER_ID_API_NAME = "partner_id";

    public final static String CHANGE_ORDER_SUFFIX = "__changeObj__c";

    public final static Set<String> SYSTEM_FIELD_SET = Sets.newHashSet(IObjectDescribe.CREATED_BY,
            IObjectDescribe.CREATE_TIME, IObjectDescribe.LAST_MODIFIED_BY, IObjectDescribe.LAST_MODIFIED_TIME,
            "life_status", "lock_status", "life_status_before_invalid", "lock_user", "lock_rule", IObjectDescribe.ID,
            IObjectData.DESCRIBE_API_NAME);

    public final static Set<String> NUMBER_TYPE_FIELD = Sets.newHashSet(IFieldType.NUMBER, IFieldType.CURRENCY, IFieldType.PERCENTILE, IFieldType.COUNT);

    public final static Set<String> VALIDATE_IS_INDEX_TYPE_FIELD = Sets.newHashSet(IFieldType.FORMULA, IFieldType.QUOTE);


    public final static Set<String> DATE_TYPE_FIELD = Sets.newHashSet(IFieldType.DATE, IFieldType.DATE_TIME, IFieldType.TIME);

    public final static List<String> CANNOT_LOOKUP_PREDEFINE_CUSTOM_OBJECTS = Lists.newArrayList(
            Utils.GOAL_RULE_API_NAME,
            Utils.GOAL_VALUE_API_NAME,
//            Utils.APPROVAL_TASK_API_NAME,  920流程要求放开
            Utils.APPROVAL_INSTANCE_API_NAME,
            Utils.BPM_INSTANCE_API_NAME,
//            Utils.BPM_TASK_API_NAME, 920流程要求放开
            "GoalRuleDetailObj",
            "org_dept",
            "GoalRuleApplyCircleObj",
            "AttachObj"
    );

    public final static List<String> PREDEFINE_CUSTOM_OBJECTS = Lists.newArrayList(
            Utils.CUSTOMER_ACCOUNT_API_NAME,
            Utils.ENTERPRISE_INFO_API_NAME,
            Utils.PREPAY_DETAIL_API_NAME,
            Utils.REBATE_INCOME_DETAIL_API_NAME,
            Utils.REBATE_USE_RULE_OBJ,
            Utils.PRICE_BOOK_API_NAME,
            Utils.PRICE_BOOK_PRODUCT_API_NAME,
            Utils.PAYMENT_PLAN_API_NAME,
            Utils.CUSTOMER_PAYMENT_API_NAME,
            Utils.ORDER_PAYMENT_API_NAME,
            Utils.QUOTE_API_NAME,
            Utils.QUOTE_LINES_API_NAME,
            Utils.CHECKINS_API_NAME,
            Utils.CHECKINS_IMG_API_NAME,
            Utils.PROMOTION_API_NAME,
            Utils.PROMOTION_PRODUCT_API_NAME,
            Utils.PROMOTION_RULE_API_NAME,
            Utils.ADVERTISEMENT_OBJ,
            Utils.DELIVERY_NOTE_API_NAME,
            Utils.DELIVERY_NOTE_PRODUCT_API_NAME,
            Utils.STOCK_API_NAME,
            GOODS_RECEIVED_NOTE_API_NAME,
            Utils.WAREHOUSE_API_NAME,
            Utils.GOODS_RECEIVED_NOTE_PRODUCT_API_NAME,
            Utils.ERP_STOCK_API_NAME,
            Utils.ERP_WAREHOUSE_API_NAME,
            Utils.PARTNER_API_NAME,
            Utils.CASES_API_NAME,
            OUTBOUND_DELIVER_NOTE_API_NAME,
            Utils.OUTBOUND_DELIVER_NOTE_PRODUCT_API_NAME,
            Utils.REQUISTITION_NOTE_API_NAME,
            Utils.REQUISTITION_NOTE_PRODUCT_API_NAME,
            Utils.PERSONNEL_OBJ_API_NAME,
            Utils.SIGN_TENANT_CERTIFY_API_NAME,
            Utils.SIGN_USER_CERTIFY_API_NAME,
            Utils.SIGN_RECORD_API_NAME,
            Utils.SIGNER_API_NAME,
            Utils.WECHAT_FAN_API_NAME,
            Utils.DEVICE_API_NAME,
            Utils.DEVICE_PART_API_NAME,
            Utils.DEVICE_CHECK_RECORD_API_NAME,
            //Utils.ORDER_MESSAGE_API_NAME,
            Utils.STOCK_CHECK_NOTE_API_NAME,
            Utils.STOCK_CHECK_NOTE_PRODUCT_API_NAME,
            Utils.STOCK_DETAILS_API_NAME,
            Utils.NEW_OPPORTUNITY_API_NAME,
            Utils.NEW_OPPORTUNITY_LINES_API_NAME,
            Utils.NEW_OPPORTUNITY_CONTACTS_API_NAME,
            Utils.CREDIT_FILE_API_NAME,
//            Utils.APPROVAL_INSTANCE_API_NAME,
            //Utils.APPROVAL_TASK_API_NAME,
            Utils.STATEMENT,
            Utils.STATEMENT_DETAIL,
            Utils.INDUSTRY_PRICE_BOOK_API_NAME,
            Utils.INDUSTRY_PRICE_BOOK_PRODUCT_API_NAME,
            Utils.SALES_SCOPE_API_NAME,
            Utils.SALES_SCOPE_PRODUCT_API_NAME,
            Utils.SERVICE_RECORD_API_NAME,
            Utils.BATCH_API_NAME,
            Utils.SERIAL_NUMBER_API_NAME,
            Utils.SPU_API_NAME,
            Utils.SPECIFICATION_API_NAME,
            Utils.SPECIFICATION_VALUE_API_NAME,
            Utils.PRODUCT_API_NAME,
            Utils.FEE_DETAIL_API_NAME,
            Utils.FEE_SETTLEMENT_BILL_API_NAME,
            Utils.FEE_SETTLEMENT_BILL_DETAIL_API_NAME,
            Utils.CHARGE_APPROVE_API_NAME,
            Utils.MEMBER_API_NAME, //会员
            Utils.MEMBER_EQUITY_API_NAME, //会员权益
            Utils.MEMBER_GRADE_API_NAME, //会员等级
            Utils.MEMBER_GRADE_EQUITY_API_NAME,  //会员等级权益规则
            Utils.MEMBER_GROWTH_VALUE_DETAIL_API_NAME,   //会员成长值明细
            Utils.MEMBER_INTEGRAL_DETAIL_API_NAME,   //会员积分明细
            Utils.AI_MAIN_API_NAME,
            Utils.AI_REF_API_NAME,
            Utils.APPRAISE_API_NAME,
            Utils.CASES_ACCESSORY_USE_INFO_API_NAME, //工单配件使用产品
            Utils.RECEIVE_MATERIAL_BILL_API_NAME, //领料单
            Utils.RECEIVE_MATERIAL_BILL_PRODUCT_API_NAME, //领料单产品
            Utils.REFUND_MATERIAL_BILL_API_NAME, //退料单
            Utils.REFUND_MATERIAL_BILL_PRODUCT_API_NAME, //退料单产品
            Utils.EMPLOYEE_WAREHOUSE_API_NAME, //员工个人库
            Utils.MARKET_ACTIVITY_API_NAME,
            Utils.DEALER_STOCK_API_NAME, //经销商库存
            Utils.STORE_STOCK_API_NAME, //门店库存
            Utils.CUSTOMER_RECEIVING_NOTE_API_NAME, //客户收货单
            Utils.CUSTOMER_RECEIVING_NOTE_PRODUCT_API_NAME, //客户收货单产品

            Utils.PURCHASE_ORDER_API_NAME, //采购订单
            Utils.PURCHASE_ORDER_PRODUCT_API_NAME, //采购订单产品
            Utils.SUPPLIER_API_NAME,
            Utils.SUBPRODUCT_API_NAME,
            Utils.MARKET_ACTIVITY_API_NAME,
            Utils.CUSTOMER_RECEIVING_NOTE_API_NAME, //客户收货单
            Utils.CUSTOMER_RECEIVING_NOTE_PRODUCT_API_NAME, //客户收货单产品
            Utils.BATCH_STOCK_API_NAME,
            Utils.DEALER_ORDER_API_NAME,
            Utils.DEALER_ORDER_PRODUCT_API_NAME,
            Utils.DEALER_DELIVERY_NOTE_API_NAME,
            Utils.DEALER_DELIVERY_NOTE_PRODUCT_API_NAME,
            Utils.DEALER_RECEIVED_NOTE_API_NAME,
            Utils.DEALER_RECEIVED_NOTE_PRODUCT_API_NAME,
            Utils.DEALER_RETURN_ORDER_API_NAME,
            Utils.DEALER_RETURN_ORDER_PRODUCT_API_NAME,
            Utils.STORE_SALES_VOLUME_API_NAME,
            Utils.STORE_SALES_VOLUME_PRODUCT_API_NAME,
            Utils.CUSTOMER_RECEIVED_NOTE_API_NAME, //客户收货单
            Utils.CUSTOMER_RECEIVED_NOTE_PRODUCT_API_NAME //客户收货单产品


    );

//    public final static List<String> PREDEFINE_CUSTOM_DETAIL_OBJECTS = Lists.newArrayList(
////            Utils.PREPAY_DETAIL_API_NAME,
////            Utils.REBATE_INCOME_DETAIL_API_NAME,
//            Utils.PRICE_BOOK_PRODUCT_API_NAME,
//            Utils.QUOTE_LINES_API_NAME,
//            Utils.ORDER_PAYMENT_API_NAME,
//            Utils.REQUISTITION_NOTE_PRODUCT_API_NAME,
//            Utils.OUTBOUND_DELIVER_NOTE_PRODUCT_API_NAME,
//            Utils.SPECIFICATION_VALUE_API_NAME,
//            Utils.CUSTOMER_ACCOUNT_API_NAME,
//            Utils.GOODS_RECEIVED_NOTE_PRODUCT_API_NAME,
//            Utils.STOCK_CHECK_NOTE_PRODUCT_API_NAME,
    /// /            Utils.PROMOTION_PRODUCT_API_NAME,
    /// /            Utils.PROMOTION_RULE_API_NAME,
    /// /            Utils.DELIVERY_NOTE_API_NAME,
    /// /            Utils.DELIVERY_NOTE_PRODUCT_API_NAME
//            Utils.PROMOTION_PRODUCT_API_NAME,
//            Utils.PROMOTION_RULE_API_NAME,
//            Utils.DELIVERY_NOTE_PRODUCT_API_NAME
//
//    );

    public final static List<String> CAN_NOT_MAPPING_OBJECTS = Lists.newArrayList(
            PERSONNEL_OBJ_API_NAME,
            Utils.CHECKINS_IMG_API_NAME,
            Utils.CHECKINS_API_NAME,
            Utils.APPROVAL_INSTANCE_API_NAME,
            Utils.APPROVAL_TASK_API_NAME,
            Utils.SCHEDULE_API_NAME
    );

    public final static List<String> IGNORE_FILL_FIELDS = Lists.newArrayList(
            "stageRuntime"
    );

    public static final Set<String> INTERNAL_BLACK_OBJECTS = Sets.newHashSet(
            "EmployeeLoginUsageObj", "EmployeeObjectUsageObj"
    );

    public final static List<String> BLACK_LIST = Lists.newArrayList(
            Utils.ACCOUNT_ATT_API_NAME,
            "ObjectFollowDealSettingObj",
            "SaleEventObj",
            "ExportCustomerSalesEventObj",
            "MenuItemObj",
            "MenuObj",
            "MenuWorkbenchObj",
            "CommonlyUsedMenuItem",
            "RoleSourceObj",
            "SpuSkuSpecValueRelateObj",
            "sign_in_info",
            "SubProductCatalogObj",
            "OpportunityContactRelationObj",
            "OpportunityProductRelationObj",
            "ContactProductRelationObj",
            Utils.MULTI_UNIT_RELATED_API_NAME,
            Utils.BOM_PATH_API_NAME,
            Utils.BEHAVIOR_INTEGRAL_DETAIL_API_NAME,
            Utils.ORDER_OCCUPY_API_NAME,
            Utils.POLICY_OCCUPY_API_NAME,
//            Utils.FORECAST_TASK_API_NAME,
            Utils.FORECAST_TASK_DETAIL_API_NAME,
            "SaleActionNewObj",
            "SaleActionStageNewObj"
    );


    public final static List<String> TRANSFERED_OLD_OBJ_LIST = Lists.newArrayList(
            Utils.CUSTOMER_PAYMENT_API_NAME,
            Utils.PRODUCT_API_NAME,
            Utils.ACCOUNT_API_NAME,
            Utils.LEADS_API_NAME,
            Utils.CONTACT_API_NAME,
            Utils.CONTRACT_API_NAME,
            Utils.OPPORTUNITY_API_NAME,
            Utils.SALES_ORDER_API_NAME,
            Utils.SALES_ORDER_PRODUCT_API_NAME,
            Utils.RETURN_GOODS_INVOICE_API_NAME,
            Utils.RETURN_GOODS_INVOICE_Product_API_NAME,
            Utils.REFUND_API_NAME,
            Utils.INVOICE_APPLICATION_API_NAME,
            Utils.MARKETING_EVENT_API_NAME
    );
    public static final int DESCRIBE_API_NAME_LENGTH = 50;

    public static List<String> UnsupportedImportRecordTypeDescribes = Lists.newArrayList(GOODS_RECEIVED_NOTE_API_NAME,
            DELIVERY_NOTE_API_NAME, OUTBOUND_DELIVER_NOTE_API_NAME, GOAL_VALUE_API_NAME);
    public static final String LOG_ANALYSIS_IS_OPEN = "log_analysis_is_open_";
    public static final String NEW_CASCADE_PARENT_API_NAME = "new_cascade_parent_api_name";
    public static final String CASCADE_DETAIL_API_NAME = "cascade_detail_api_name";
    public static final String CASCADE_PARENT_API_NAME = "cascade_parent_api_name";
    public static final String EMAIl_PATTERN = "^\\w+([-+.]*\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";

    public final static String DEPARTMENT_OBJ_API_NAME = "DepartmentObj";

    public final static String ICON_SLOT = "icon_slot";

    /**
     * 国家省市区相关的字段类型集合
     */
    private static final Set<String> COUNTRY_AREA_FIELD_TYPES = Sets.newHashSet(
            IFieldType.COUNTRY,
            IFieldType.PROVINCE,
            IFieldType.DISTRICT,
            IFieldType.CITY,
            IFieldType.TOWN,
            IFieldType.VILLAGE
    );

    public static final String SUPPORT_CROSS_FILTER = "support_cross_filter";

    public static final List<String> EMPLOYEE_FIELD_TYPES = ImmutableList.of(IFieldType.EMPLOYEE, IFieldType.EMPLOYEE_MANY);
    public static final List<String> DEPARTMENT_FIELD_TYPES = ImmutableList.of(IFieldType.DEPARTMENT, IFieldType.DEPARTMENT_MANY);

    @Getter
    @Delegate
    private IObjectDescribe objectDescribe;

    public boolean isSFAObject() {
        return ObjectAPINameMapping.isSFAObject(getApiName());
    }

    public static boolean isAutoNumber(IFieldDescribe fieldDescribe) {
        Boolean ret = fieldDescribe.get("is_auto_number", Boolean.class);
        return ret == null ? false : ret;
    }


    private ObjectDescribeExt(@NonNull IObjectDescribe objectDescribe) {
        if (objectDescribe instanceof ObjectDescribeExt) {
            this.objectDescribe = ((ObjectDescribeExt) objectDescribe).getObjectDescribe();
        } else {
            this.objectDescribe = objectDescribe;
        }
    }

    public static ObjectDescribeExt of(IObjectDescribe objectDescribe) {
        ObjectDescribeExt objectDescribeExt = new ObjectDescribeExt(objectDescribe);
//        objectDescribeExt.addExtProperty();
        return objectDescribeExt;
    }

    public static ObjectDescribeExt of(Map map) {
        ObjectDescribeExt objectDescribeExt = new ObjectDescribeExt(new ObjectDescribe(map));
//        objectDescribeExt.addExtProperty();
        return objectDescribeExt;
    }

    /**
     * 该方法效率较低，只有需要修改describe中的属性才需要使用此方法
     *
     * @param objectDescribe
     * @return
     */
    public static ObjectDescribeExt copy(IObjectDescribe objectDescribe) {
        ObjectDescribeExt objectDescribeExt = new ObjectDescribeExt(objectDescribe.copy());
//        objectDescribeExt.addExtProperty();
        return objectDescribeExt;
    }

    public static String getFieldRenderType(IFieldDescribe fieldDescribe) {
        if (fieldDescribe instanceof Formula) {
            return ((Formula) fieldDescribe).getReturnType();
        }
        if (fieldDescribe instanceof Count) {
            return ((Count) fieldDescribe).getReturnType();
        }
        return fieldDescribe.getType();
    }

    public void mergeWhatObjectFields(IObjectDescribe whatObjectDescribe) {
        if (Objects.nonNull(whatObjectDescribe)) {
            List<IFieldDescribe> fieldDescribes = whatObjectDescribe.getFieldDescribes().stream()
                    .peek(fieldDescribe -> convertField(whatObjectDescribe, fieldDescribe)).collect(Collectors.toList());
            this.addFieldDescribeList(fieldDescribes);
        }
    }

    private void convertField(IObjectDescribe whatObjectDescribe, IFieldDescribe fieldDescribe) {
        fieldDescribe.setApiName(WhatComponentExt.getWhatFieldName(whatObjectDescribe.getApiName(), fieldDescribe.getApiName()));
        if (IFieldType.QUOTE.equals(fieldDescribe.getType())) {
            fieldDescribe.setQuoteField(WhatComponentExt.getWhatFieldName(whatObjectDescribe.getApiName(), ((Quote) fieldDescribe).getQuoteField()));
        }
    }

    public List<Department> getDepartmentFields() {
//        return filter(x -> IFieldType.DEPARTMENT.equals(x.getType())).stream().map(fieldDescribe -> (Department)
// fieldDescribe).collect(Collectors.toList());
        return objectDescribe.getFieldDescribes().stream()
                .filter(fieldDescribe -> IFieldType.DEPARTMENT.equals(fieldDescribe.getType()))
                .map(fieldDescribe -> (Department) fieldDescribe)
                .collect(Collectors.toList());
    }

    public static boolean isSystemField(String fieldName) {
        return SYSTEM_FIELD_SET.contains(fieldName);
    }

    public static boolean isSignInField(IFieldDescribe fieldDescribe) {
        if (IFieldType.GROUP.equals(fieldDescribe.getType()) && fieldDescribe instanceof GroupField) {
            if (GroupField.GROUP_TYPE_SIGN_IN.equals(((GroupField) fieldDescribe).getGroupType())) {
                return true;
            }
        }
        return false;
    }

    public static boolean isWhatField(IFieldDescribe fieldDescribe) {
        return Objects.equals(fieldDescribe.getType(), IFieldType.GROUP) &&
                Objects.equals(fieldDescribe.get(GroupField.GROUP_TYPE, String.class), What.GROUP_TYPE_WHAT);
    }

    public static boolean isWhatListField(IFieldDescribe fieldDescribe) {
        return Objects.equals(fieldDescribe.getType(), IFieldType.GROUP) &&
                Objects.equals(fieldDescribe.get(GroupField.GROUP_TYPE, String.class), WhatList.GROUP_TYPE_WHAT_LIST);
    }

    public static boolean isDateTimeRangeField(IFieldDescribe fieldDescribe) {
        return Objects.equals(fieldDescribe.getType(), IFieldType.GROUP) &&
                Objects.equals(fieldDescribe.get(GroupField.GROUP_TYPE, String.class), DateTimeRange.GROUP_TYPE_DATE_TIME_RANGE);
    }

    public void convertDescribeFieldFilter2Custom(Function<String, IObjectDescribe> findDescribe) {
        stream().map(FieldDescribeExt::of)
                .forEach(it -> it.convert2CustomZone(findDescribe));
    }

    public void convertDescribeFieldFilter2System(Function<String, IObjectDescribe> findDescribe) {
        stream().map(FieldDescribeExt::of)
                .forEach(it -> it.convert2SystemZone(findDescribe));
    }

    public Optional<What> getWhatField() {
        // related_object
        return filterOne(ObjectDescribeExt::isWhatField).map(fieldDescribe -> (What) fieldDescribe);
    }

    public Optional<IFieldDescribe> getWhatIdField() {
        return getWhatField().map(it -> getFieldDescribe(it.getIdFieldApiName()));
    }

    public Optional<IFieldDescribe> getWhatApiNameField() {
        return getWhatField().map(it -> getFieldDescribe(it.getApiNameFieldApiName()));
    }

    public static boolean isPayment(String apiName) {
        return PAYMENT_DESCRIBE_API_NAME.equals(apiName);
    }

    public static ObjectDescribeExt buildPaymentDescribe() {
        IObjectDescribe describe = new ObjectDescribe();
        describe.setApiName("payment_record");
        describe.setDisplayName(I18N.text(I18NKey.RECEIPT));

        IFieldDescribe amountField = new TextFieldDescribe();
        amountField.setApiName("amount");
        amountField.setLabel(I18N.text(I18NKey.RECEIPT_AMOUNT));
        IFieldDescribe feeField = new TextFieldDescribe();
        feeField.setApiName("fee");
        feeField.setLabel(I18N.text(I18NKey.SERVICE_FEE));
        IFieldDescribe payEnterpriseNameField = new TextFieldDescribe();
        payEnterpriseNameField.setApiName("payEnterpriseName");
        payEnterpriseNameField.setLabel(I18N.text(I18NKey.PAY_ENTERPRISE_NAME));
        IFieldDescribe remarkField = new TextFieldDescribe();
        remarkField.setApiName("remark");
        remarkField.setLabel(I18N.text(I18NKey.REMARK));
        IFieldDescribe payTypeField = new TextFieldDescribe();
        payTypeField.setApiName("payType");
        payTypeField.setLabel(I18N.text(I18NKey.PAY_TYPE));
        IFieldDescribe finishTimeField = new TextFieldDescribe();
        finishTimeField.setApiName("finishTime");
        finishTimeField.setLabel(I18N.text(I18NKey.PAY_FINISH_TIME));
        IFieldDescribe transTimeField = new TextFieldDescribe();
        transTimeField.setApiName("transTime");
        transTimeField.setLabel(I18N.text(I18NKey.TRANSACTION_TIME));
        IFieldDescribe relatedObjectField = new TextFieldDescribe();
        relatedObjectField.setApiName("relatedObject");
        relatedObjectField.setLabel(I18N.text(I18NKey.RELATED_OBJECT));
        IFieldDescribe relatedObjectNameField = new TextFieldDescribe();
        relatedObjectNameField.setApiName("relatedObjectName");
        relatedObjectNameField.setLabel(I18N.text(I18NKey.RELATED_OBJECT_NAME));
        IFieldDescribe payStatusField = new TextFieldDescribe();
        payStatusField.setApiName("payStatus");
        payStatusField.setLabel(I18N.text(I18NKey.STATUS));
        IFieldDescribe detailUrlField = new TextFieldDescribe();
        detailUrlField.setApiName("detailUrl");
        IFieldDescribe orderNoField = new TextFieldDescribe();
        orderNoField.setApiName("name");
        orderNoField.setLabel(I18N.text(I18NKey.RECEIPT_ORDER_NO));

        describe.setFieldDescribes(Lists.newArrayList(amountField, feeField, payEnterpriseNameField, remarkField,
                payTypeField, finishTimeField, transTimeField, relatedObjectField, relatedObjectNameField,
                payStatusField, detailUrlField, orderNoField));

        return ObjectDescribeExt.of(describe);
    }

    public Map<String, Object> toMap() {
        return this.convert().getContainerDocument();
    }

    public IObjectDescribe mergeFields(List<IFieldDescribe> fieldDescribes) {
        IObjectDescribe newDescribe = copy();
        fieldDescribes.forEach(x -> {
            if (newDescribe.containsField(x.getApiName())) {
                newDescribe.updateFieldDescribe(x);
            } else {
                newDescribe.addFieldDescribe(x);
            }
        });
        return newDescribe;
    }

    public String getFieldLabelByName(String fieldApiName) {
        return getFieldDescribeSilently(fieldApiName).map(x -> x.getLabel()).orElse(fieldApiName);
    }

    public List<IFieldDescribe> getFieldByApiNames(Collection<String> fieldApiNames) {
        if (CollectionUtils.empty(fieldApiNames)) {
            return Lists.newArrayList();
        }
        return fieldApiNames.stream().distinct()
                .map(x -> getFieldDescribe(x))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public void addFieldIfAbsent(IFieldDescribe fieldDescribe) {
        if (getFieldDescribeSilently(fieldDescribe.getApiName()).isPresent()) {
            return;
        }
        addFieldDescribe(fieldDescribe);
    }

    public void setFieldCalculateRelation(String fieldName, CalculateRelation calculateRelation) {
        if (calculateRelation == null) {
            return;
        }
        getFieldDescribeSilently(fieldName).ifPresent(x -> FieldDescribeExt.of(x).setCalculateRelation(calculateRelation));
    }

    public void setCalculateRelation(CalculateRelation calculateRelation) {
        if (calculateRelation == null) {
            return;
        }
        set(CalculateRelation.CALCULATE_RELATION, calculateRelation.toMap());
    }

    public CalculateRelation getCalculateRelation() {
        Map<String, Object> map = get(CalculateRelation.CALCULATE_RELATION, Map.class);
        if (map == null) {
            return null;
        }
        return new CalculateRelation(map);
    }

    public void addCalculateField(String objectApiName, String fieldName) {
        addCalculateFields(objectApiName, Lists.newArrayList(fieldName));
    }

    public void addCalculateFields(String objectApiName, Collection<String> fieldNames) {
        if (CollectionUtils.empty(fieldNames)) {
            return;
        }
        CalculateRelation calculateRelation = getCalculateRelation();
        if (calculateRelation == null) {
            calculateRelation = new CalculateRelation();
            setCalculateRelation(calculateRelation);
        }
        calculateRelation.addCalculateFields(objectApiName, fieldNames);
    }

    public void addRelateField(String objectApiName, RelateField relateField) {
        addRelateFields(objectApiName, Lists.newArrayList(relateField));
    }

    public void addRelateFields(String objectApiName, Collection<RelateField> relateFields) {
        if (CollectionUtils.empty(relateFields)) {
            return;
        }
        CalculateRelation calculateRelation = getCalculateRelation();
        if (calculateRelation == null) {
            calculateRelation = new CalculateRelation();
            setCalculateRelation(calculateRelation);
        }
        List<String> relateFieldNames = relateFields.stream().map(x -> x.getFieldName()).collect(Collectors.toList());
        calculateRelation.addCalculateFields(objectApiName, relateFieldNames);
        calculateRelation.addRelateFields(objectApiName, relateFields);
    }

    public Map<String, Set<RelateField>> getRelateFields() {
        CalculateRelation calculateRelation = getCalculateRelation();
        if (calculateRelation == null) {
            return Maps.newHashMap();
        }
        return calculateRelation.getRelateFields();
    }

    public Map<String, Set<RelateField>> getAllRelateFields() {
        Map<String, Set<RelateField>> result = Maps.newHashMap();
        CalculateRelation describeCalcRelation = getCalculateRelation();
        if (describeCalcRelation != null) {
            result.putAll(describeCalcRelation.getRelateFields());
        }
        getFieldDescribes().forEach(field -> {
            Map<String, Set<RelateField>> fieldRelateFieldMap = FieldDescribeExt.of(field).getRelateFields();
            fieldRelateFieldMap.forEach((objectApiName, relateFields) -> {
                result.putIfAbsent(objectApiName, Sets.newHashSet());
                result.get(objectApiName).addAll(relateFields);
            });
        });

        return result;
    }

    public boolean isBiObject() {
        return IObjectDescribe.VISIBLE_SCOPE_BI.equals(getVisibleScope());
    }

    public boolean isBigTreeList() {
        String bigData = "bigData";
        return bigData.equals(getTreeType());
    }


    public Optional<String> getRefFieldName(String targetApiName, String targetRelatedListName) {
        Optional<ObjectReferenceWrapper> refField = getReferenceField(targetApiName, targetRelatedListName);
        if (refField.isPresent()) {
            return Optional.of(refField.get().getApiName());
        }
        Optional<MasterDetailFieldDescribe> masterDetailField = getMasterDetailFieldDescribe();
        if (masterDetailField.isPresent()) {
            return Optional.of(masterDetailField.get().getApiName());
        }
        Optional<What> whatField = getWhatField();
        if (whatField.isPresent()) {
            return Optional.of(whatField.get().getApiName());
        }
        return Optional.empty();
    }

    public Optional<String> getRefFieldName(Optional<ObjectReferenceWrapper> refField) {
        if (refField.isPresent()) {
            return Optional.of(refField.get().getApiName());
        }
        Optional<MasterDetailFieldDescribe> masterDetailField = getMasterDetailFieldDescribe();
        if (masterDetailField.isPresent()) {
            return Optional.of(masterDetailField.get().getApiName());
        }

        return Optional.empty();
    }

    public List<SelectOne> getActiveSelectOneFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isSelectOne() && x.isActive())
                .map(x -> (SelectOne) x)
                .collect(Collectors.toList());
    }

    public boolean hasActiveReferenceFieldDescribes() {
        return stream().anyMatch(x -> (FieldDescribeExt.of(x).isLookupField() || FieldDescribeExt.of(x).isMasterDetailField()) && x.isActive());
    }

    public List<ObjectReferenceFieldDescribe> getActiveSingleReferenceFieldDescribes() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isObjectReferenceField() && x.isActive())
                .map(x -> (ObjectReferenceFieldDescribe) x)
                .collect(Collectors.toList());
    }

    public List<ObjectReferenceWrapper> getActiveReferenceFieldDescribes() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isLookupField() && x.isActive())
                .map(x -> ObjectReferenceWrapper.of(x))
                .collect(Collectors.toList());
    }

    public List<FileAttachment> getActiveOcrFieldDescribes() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isFileAttachFileOrImageField() && x.isActive())
                .map(x -> (FileAttachment) x)
                .filter(x -> BooleanUtils.isTrue(x.getIsOcrRecognition()))
                .collect(Collectors.toList());
    }

    public void checkMDFieldForOcr() {
        List<FileAttachment> ocrFieldDescribes = getActiveOcrFieldDescribes();
        if (CollectionUtils.notEmpty(ocrFieldDescribes)) {
            throw new ValidateException(I18NExt.text(I18NKey.OCR_MD_FIELD_ERROR));
        }
    }


    public List<IFieldDescribe> getActiveLookupFieldDescribes() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isLookupField() && x.isActive())
                .collect(Collectors.toList());
    }

    public List<ObjectReferenceWrapper> getNotAddToLayoutReferenceFieldDescribes(IObjectDescribe describeInDb) {
        return stream()
                .filter(x -> Objects.isNull(describeInDb) || !describeInDb.containsField(x.getApiName()))
                .filter(x -> !FieldDescribeExt.of(x).isRelatedListAddToLayout() && x.isActive())
                .map(ObjectReferenceWrapper::of)
                .collect(Collectors.toList());
    }

    public Optional<ObjectReferenceWrapper> getSystemReferenceFieldDescribesByTargetApiName(String targetApiName) {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isLookupField() && !FieldDescribeExt.of(x).isCustomField())
                .map(x -> ObjectReferenceWrapper.of(x))
                .filter(x -> targetApiName.equals(x.getTargetApiName()))
                .findFirst();
    }

    public List<IFieldDescribe> getReferenceFieldDescribes(Collection<String> fieldNames) {
        return fieldNames.stream()
                .filter(x -> FieldDescribeExt.of(getFieldDescribe(x)).isLookupField())
                .map(x -> getFieldDescribe(x))
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getAllNeedCleanFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isLookupField() || FieldDescribeExt.of(x).isAutoNumber()
                        || FieldDescribeExt.of(x).isGeneralOptions() || FieldDescribeExt.of(x).isMasterDetailField()
                        || FieldDescribeExt.of(x).isWhatListField())
                .collect(Collectors.toList());
    }

    public List<ObjectReferenceWrapper> getReferenceFieldDescribes() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isLookupField())
                .map(ObjectReferenceWrapper::of)
                .collect(Collectors.toList());
    }

    public List<ObjectReferenceWrapper> getLookupFieldDescribes() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isLookupField())
                .map(ObjectReferenceWrapper::of)
                .collect(Collectors.toList());
    }

    public List<ObjectReferenceWrapper> getRefFieldAndHaveLookupRoles() {
        return stream()
                .filter(x -> StringUtils.equals(IFieldType.OBJECT_REFERENCE, x.getType())
                        || StringUtils.equals(IFieldType.OBJECT_REFERENCE_MANY, x.getType()))
                .filter(x -> CollectionUtils.notEmpty(ObjectReferenceWrapper.of(x).getLookupRoles()))
                .map(ObjectReferenceWrapper::of)
                .collect(Collectors.toList());
    }

    public ObjectReferenceWrapper getRefFieldAndHaveLookupRolesByApiName(String apiName) {
        IFieldDescribe fieldDescribe = getFieldDescribe(apiName);
        if (!(fieldDescribe instanceof ObjectReferenceFieldDescribe)) {
            return null;
        }
        return ObjectReferenceWrapper.of(fieldDescribe);
    }

    public Optional<MasterDetailFieldDescribe> getMasterDetailFieldDescribe() {
        return filterOne(x -> FieldDescribeExt.of(x).isMasterDetailField())
                .map(ObjectDescribeExt::convert);
    }

    public Optional<MasterDetail> getMasterDetailField() {
        return filterOne(x -> FieldDescribeExt.of(x).isMasterDetailField())
                .map(ObjectDescribeExt::convert);
    }

    public Optional<ObjectReferenceFieldDescribe> getRelatedDetailFieldDescribe(String apiName) {
        return filterOne(x -> FieldDescribeExt.of(x).isRelatedDetailFieldAndFollowLookDataRights(apiName))
                .map(ObjectDescribeExt::convert);
    }

    public Optional<ObjectReferenceWrapper> getRelatedFieldAndLookupRolesDescribe(String teamMemberRole,
                                                                                  String apiName) {
        return filterOne(x -> FieldDescribeExt.of(x).isRelatedDetailAndLookupRoles(teamMemberRole, apiName))
                .map(ObjectDescribeExt::convert);
    }

    public Optional<String> getMasterDetailFieldName(String targetApiName) {
        Optional<MasterDetail> ret = filterOne(x -> FieldDescribeExt.of(x).isMasterDetailField())
                .map(ObjectDescribeExt::convert);

        return ret.filter(x -> x.getTargetApiName().equals(targetApiName)).map(IFieldDescribe::getApiName);
    }

    public String getMasterDetailFieldNameOrThrowException(String targetApiName) {
        Optional<MasterDetail> ret = filterOne(x -> FieldDescribeExt.of(x).isMasterDetailField())
                .map(ObjectDescribeExt::convert);

        return ret.filter(x -> x.getTargetApiName().equals(targetApiName)).map(IFieldDescribe::getApiName)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.MASTER_DETAIL_FIELD_NOT_EXIST, getDisplayName())));
    }

    public boolean isCreateWithMaster(String masterAPIName) {
        Optional<MasterDetailFieldDescribe> ret = filterOne(x -> FieldDescribeExt.of(x).isMasterDetailField())
                .map(ObjectDescribeExt::convert);
        return ret.filter(x -> x.getTargetApiName().equals(masterAPIName)
                && (x.getIsCreateWhenMasterCreate() != null && x.getIsCreateWhenMasterCreate())).isPresent();
    }

    public boolean isCreateWithMaster() {
        Optional<MasterDetailFieldDescribe> ret = filterOne(x -> FieldDescribeExt.of(x).isMasterDetailField())
                .map(ObjectDescribeExt::convert);
        return ret.filter(x -> TRUE.equals(x.getIsCreateWhenMasterCreate())).isPresent();
    }

    public List<DataVisibilityRange> getDataVisibilityRangeFields() {
        return stream()
                .filter(IFieldDescribe::isActive)
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isDataVisibilityRange)
                .map(FieldDescribeExt::<DataVisibilityRange>getFieldDescribe)
                .collect(Collectors.toList());
    }

    public List<Quote> getQuoteFieldDescribes() {
        return stream()
                .filter(IFieldDescribe::isActive)
                .filter(x -> FieldDescribeExt.of(x).isQuoteField())
                .map(x -> (Quote) x)
                .collect(Collectors.toList());
    }

    public List<Quote> getQuoteFieldDescribes(String refFieldName) {
        List<Quote> quoteList = getQuoteFieldDescribes();
        if (CollectionUtils.empty(quoteList)) {
            return quoteList;
        }
        return quoteList.stream().filter(x -> refFieldName.equals(QuoteExt.of(x).parseQuoteField().getKey()))
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getAllRefFieldDescribes() {
        return stream()
                .filter(a -> FieldDescribeExt.of(a).isLookupField()
                        || FieldDescribeExt.of(a).isMasterDetailField()
                        || ObjectDescribeExt.isWhatField(a)
                        || ObjectDescribeExt.isWhatListField(a))
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getAllActiveRefFieldDescribesExcludeWhatField() {
        return stream()
                .filter(a -> (FieldDescribeExt.of(a).isLookupField()
                        || FieldDescribeExt.of(a).isMasterDetailField())
                        && a.isActive())
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getAllObjectRefManyFieldDescribes() {
        return stream().filter(field -> field instanceof IObjectReferenceMany).collect(Collectors.toList());
    }

    public List<IObjectReferenceMany> getActiveObjectRefManyFieldDescribesByTargetApiName(String targetApiName) {
        return stream()
                .filter(IObjectReferenceMany.class::isInstance)
                .filter(IFieldDescribe::isActive)
                .map(IObjectReferenceMany.class::cast)
                .filter(field -> Objects.equals(targetApiName, field.getTargetApiName()))
                .collect(Collectors.toList());
    }

    /**
     * 将对象新的describe和老的describe的统计字段做diff
     *
     * @param oldDescribe
     * @return 新增和变更的统计字段列表
     */
    public List<Count> diffCountField(IObjectDescribe oldDescribe) {
        MapDifference<String, IFieldDescribe> difference = diffCount(oldDescribe);
        if (Objects.isNull(difference)) {
            return Lists.newArrayList();
        }
        List<Count> changedCountList = Lists.newArrayList();
        difference.entriesOnlyOnLeft().forEach((k, v) -> changedCountList.add((Count) v));
        difference.entriesDiffering().forEach((k, v) -> changedCountList.add((Count) v.leftValue()));

        return changedCountList;
    }

    public List<IFieldDescribe> getNewCount(IObjectDescribe oldDescribe) {
        MapDifference<String, IFieldDescribe> difference = diffCount(oldDescribe);
        if (Objects.isNull(difference)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(difference.entriesOnlyOnLeft().values());
    }

    public MapDifference<String, IFieldDescribe> diffCount(IObjectDescribe oldDescribe) {
        Map<String, IFieldDescribe> countMap =
                getCountFields().stream().collect(Collectors.toMap(IFieldDescribe::getApiName, x -> x));
        if (CollectionUtils.empty(countMap)) {
            return null;
        }
        Map<String, IFieldDescribe> oCountMap = ObjectDescribeExt.of(oldDescribe).getCountFields().stream()
                .collect(Collectors.toMap(IFieldDescribe::getApiName, x -> x));
        return Maps.difference(countMap, oCountMap, Equivalence.equals().onResultOf(count -> CountExt.of((Count) count)));
    }

    @Deprecated
    public List<IFieldDescribe> diffFieldChange(IObjectDescribe oldDescribe) {
        Map<String, IFieldDescribe> fieldMap = getFieldDescribes().stream()
                .collect(Collectors.toMap(IFieldDescribe::getApiName, it -> it));
        Map<String, IFieldDescribe> oldFieldMap = oldDescribe.getFieldDescribes().stream()
                .collect(Collectors.toMap(IFieldDescribe::getApiName, it -> it));
        MapDifference<String, IFieldDescribe> difference = Maps.difference(fieldMap, oldFieldMap);

        List<IFieldDescribe> result = Lists.newArrayList();
        difference.entriesDiffering().forEach((key, value) -> result.add(value.leftValue()));
        difference.entriesOnlyOnLeft().forEach((key, value) -> result.add(value));
        return result;
    }

    @Deprecated
    public List<IFieldDescribe> getChangedField(List<IFieldDescribe> fieldDescribes) {
        if (CollectionUtils.empty(fieldDescribes)) {
            return Lists.newArrayList();
        }
        Map<String, IFieldDescribe> fieldMap = fieldDescribes.stream()
                .collect(Collectors.toMap(IFieldDescribe::getApiName, it -> it));
        Map<String, IFieldDescribe> oldfieldMap = getFieldDescribes().stream()
                .collect(Collectors.toMap(IFieldDescribe::getApiName, it -> it));

        MapDifference<String, IFieldDescribe> difference = Maps.difference(fieldMap, oldfieldMap);

        return difference.entriesDiffering().values().stream().map(MapDifference.ValueDifference::leftValue).collect(Collectors.toList());
    }

    public List<Count> getCountFields(String detailObjectAPIName) {
        List<Count> ret = stream()
                .filter(x -> x instanceof Count)
                .filter(x -> x.isActive())
                .map(x -> (Count) x)
                .filter(x -> detailObjectAPIName.equals(x.getSubObjectDescribeApiName()))
                .collect(Collectors.toList());
        return ret;
    }

    public List<Count> getMasterDetailCountFields(String detailObjectAPIName, String fieldApiName) {
        return getCountFields(detailObjectAPIName).stream()
                .filter(x -> Strings.isNullOrEmpty(x.getFieldApiName()) || fieldApiName.equals(x.getFieldApiName()))
                .peek(x -> x.setFieldApiName(fieldApiName))
                .collect(Collectors.toList());
    }

    public List<Count> getCountFields(String detailObjectAPIName, String fieldApiName) {
        return getCountFields(detailObjectAPIName).stream()
                .filter(x -> fieldApiName.equals(x.getFieldApiName()))
                .collect(Collectors.toList());
    }

    public List<Count> getCountFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isCountField())
                .filter(x -> x.isActive())
                .map(x -> (Count) x)
                .collect(Collectors.toList());
    }

    public List<Count> getCountFields(boolean isActive) {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isCountField())
                .filter(x -> x.isActive())
                .map(x -> (Count) x)
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getFormulaFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isFormula())
                .filter(x -> x.isActive())
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getCalculateFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isCalculateField())
                .filter(x -> x.isActive())
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getCalculateFieldsAndMultiCurrencyCalculateFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isCalculateField() || FieldDescribeExt.of(x).isMultiCurrencyCalculateFields())
                .filter(x -> x.isActive())
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getCalculateFieldsExcludeDefaultValue() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isFormula() || FieldDescribeExt.of(x).hasCalculateValue())
                .filter(x -> x.isActive())
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getCountAndFormulaFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isCountField() || FieldDescribeExt.of(x).isFormula())
                .filter(x -> x.isActive())
                .collect(Collectors.toList());
    }

    /**
     * 获取所有国家省市区字段
     *
     * @return 国家省市区字段列表
     */
    public List<IFieldDescribe> getCountryAreaFields() {
        return stream()
                .filter(x -> COUNTRY_AREA_FIELD_TYPES.contains(x.getType()))
                .filter(IFieldDescribe::isActive)
                .collect(Collectors.toList());
    }

    /**
     * 判断字段是否为国家省市区字段
     *
     * @param fieldApiName 字段API名称
     * @return 是否为国家省市区字段
     */
    public boolean isCountryAreaField(String fieldApiName) {
        Optional<IFieldDescribe> field = getFieldDescribeSilently(fieldApiName);
        return field.filter(iFieldDescribe ->
                COUNTRY_AREA_FIELD_TYPES.contains(iFieldDescribe.getType())
        ).isPresent();
    }

    public List<IFieldDescribe> getGroupFields() {
        return stream()
                .filter(x -> IFieldType.GROUP.equals(x.getType()))
                .filter(x -> x.isActive())
                .collect(Collectors.toList());
    }

    public List<Area> getAreaGroupFields() {
        return stream()
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isAreaField)
                .filter(FieldDescribeExt::isActive)
                .map(FieldDescribeExt::<Area>getFieldDescribe)
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getTownFields() {
        return stream()
                .filter(x -> IFieldType.TOWN.equals(x.getType()))
                .filter(IFieldDescribe::isActive)
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getVillageFields() {
        return stream()
                .filter(x -> VILLAGE.equals(x.getType()))
                .filter(IFieldDescribe::isActive)
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getFormulaOrCurrencyRealTypeCurrencyFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).formulaOrCurrencyRealTypeIsCurrency())
                .filter(x -> x.isActive())
                .collect(Collectors.toList());
    }


    public List<IFieldDescribe> getGroupFieldList(String fieldApiName) {
        Optional<IFieldDescribe> groupFieldDescribe = getGroupFields().stream().filter(x -> Objects.equals(x.getApiName(), fieldApiName)).findFirst();
        if (groupFieldDescribe.isPresent()) {
            GroupField groupField = (GroupField) groupFieldDescribe.get();
            try {
                return groupField.getFieldList(objectDescribe);
            } catch (MetadataServiceException e) {
                log.warn("getGroupFieldList error,fieldApiName:{}", fieldApiName, e);
                return Lists.newArrayList();
            }
        }
        return Lists.newArrayList();
    }


    public List<IFieldDescribe> getMaskFields() {
        return stream()
                .filter(x -> x.isActive())
                .filter(x -> FieldDescribeExt.of(x).isShowMask())
                .collect(Collectors.toList());
    }

    public List<What> getWhatFields() {
        return getGroupFields().stream()
                .filter(IFieldDescribe::isActive)
                .filter(ObjectDescribeExt::isWhatField)
                .map(it -> (WhatFieldDescribe) it)
                .collect(Collectors.toList());
    }

    public List<DateTimeRangeFieldDescribe> getDateTimeRangeFields() {
        return getGroupFields().stream()
                .filter(IFieldDescribe::isActive)
                .filter(ObjectDescribeExt::isDateTimeRangeField)
                .map(it -> (DateTimeRangeFieldDescribe) it)
                .collect(Collectors.toList());
    }

    /**
     * 补充日期范围组结束时间
     *
     * @param fieldList
     */
    public void addDateTimeRangeEndTime(List<String> fieldList) {
        if (CollectionUtils.empty(fieldList)) {
            return;
        }
        getDateTimeRangeFields().forEach(field -> {
            if (fieldList.contains(field.getStartTimeFieldApiName())) {
                int index = fieldList.indexOf(field.getStartTimeFieldApiName());
                fieldList.add(index + 1, field.getEndTimeFieldApiName());
            }
        });
    }

    public List<SelectOne> getSelectOneFields() {
        return stream()
                .filter(x -> FieldDescribeExt.CASCADE_CHILD_API_NAME_FIELD_TYPE.contains(x.getType()))
                .map(x -> (SelectOne) x)
                .collect(Collectors.toList());
    }

    /**
     * 获取已启用的多选和单选字段
     *
     * @param enableClone true: 字段值可复制，false: 字段值不可复制
     * @return 已经启用的多选和单选字段
     */
    public List<SelectOne> getActiveSelectFields(boolean enableClone) {
        return stream()
                .filter(f -> FieldDescribeExt.CASCADE_CHILD_API_NAME_FIELD_TYPE.contains(f.getType()))
                .filter(IFieldDescribe::isActive)
                .filter(f -> Objects.equals(enableClone, f.getEnableClone()))
                .map(x -> (SelectOne) x)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有非系统字段
     *
     * @return
     */
    public List<IFieldDescribe> getNonSystemField() {
        return stream()
                .filter(IFieldDescribe::isActive)
                .filter(fieldDescribe -> !Objects.equals(fieldDescribe.getDefineType(),
                        IFieldDescribe.DEFINE_TYPE_SYSTEM))
                .collect(Collectors.toList());
    }

    public Optional<String> getMasterAPIName() {
        return getMasterDetailFieldDescribe().map(MasterDetailFieldDescribe::getTargetApiName);
    }

    public String getMasterAPINameOrThrowException() {
        return getMasterDetailFieldDescribe().map(MasterDetailFieldDescribe::getTargetApiName)
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.MASTER_DETAIL_FIELD_NOT_EXIST, getDisplayName())));
    }

    public boolean isSlaveObject() {
        return getMasterDetailFieldDescribe().isPresent();
    }

    public boolean isInternalObject() {
        return IObjectDescribe.DEFINE_TYPE_INTERNAL.equals(getDefineType());
    }

    public boolean isSlaveObjectCreateWithMaster() {
        MasterDetailFieldDescribe masterDetail = getMasterDetailFieldDescribe().orElse(null);
        return masterDetail != null && !FALSE.equals(masterDetail.getIsCreateWhenMasterCreate());
    }

    public boolean isShowDetailButton() {
        MasterDetailFieldDescribe masterDetail = getMasterDetailFieldDescribe().orElse(null);
        return masterDetail != null && !FALSE.equals(masterDetail.getShowDetailButton());
    }

    public boolean isSlaveObjectCreateWithMasterAndHiddenDetailButton() {
        MasterDetailFieldDescribe masterDetail = getMasterDetailFieldDescribe().orElse(null);
        return masterDetail != null
                && !FALSE.equals(masterDetail.getIsCreateWhenMasterCreate())
                && Boolean.FALSE.equals(masterDetail.getShowDetailButton());
    }

    public boolean isSlaveObjectCreateWithMasterAndInGrayList() {
        return AppFrameworkConfig.isInMasterDetailApprovalGrayList(getTenantId()) && isSlaveObjectCreateWithMaster();
    }

    //只校验cep的请求
    public boolean needCheckMasterStatus() {
        return RequestUtil.isCepRequest() && isSlaveObjectCreateWithMasterAndInGrayList();
    }

    public boolean isRelatedSlaveObject(String apiName) {
        return getRelatedDetailFieldDescribe(apiName).isPresent();
    }

    public boolean isRelatedAndLookupRolesObject(IObjectData data, User user, String apiName) {
        if (Objects.isNull(data)) {
            return false;
        }
        Optional<TeamMember> any = ObjectDataExt.of(data).getTeamMembers().stream()
                .filter(x -> x.getEmployee().contains(user.getUserId()))
                .filter(x -> getRelatedFieldAndLookupRolesDescribe(x.getRole().getValue(), apiName).isPresent())
                .findAny();
        return any.isPresent();
    }

    public boolean isRelatedAndLookupRolesObject(List<RelevantTeam> relevantTeam, User user, String apiName) {
        if (CollectionUtils.empty(relevantTeam)) {
            return false;
        }

        Optional<RelevantTeam> optional = relevantTeam.stream()
                .filter(x -> StringUtils.equals(x.getMemberId(), user.getUserId()))
                .filter(x -> getRelatedFieldAndLookupRolesDescribe(x.getRoleType(), apiName).isPresent())
                .findAny();
        return optional.isPresent();
    }

    public boolean isEmployeeField(String fieldName) {
        Optional<IFieldDescribe> field = getFieldDescribeSilently(fieldName);
        if (!field.isPresent()) {
            return false;
        }
        String renderType = LayoutExt.getRenderType(getApiName(), fieldName, field.get().getType());
        return Objects.equals(renderType, IFieldType.EMPLOYEE);
    }

    public boolean isDepartmentField(String fieldName) {
        Optional<IFieldDescribe> field = getFieldDescribeSilently(fieldName);
        return field.filter(iFieldDescribe -> FieldDescribeExt.of(iFieldDescribe).isDepartmentField()).isPresent();
    }

    public boolean isSpecifiedFieldType(String fieldName, List<String> fieldTypes) {
        Optional<IFieldDescribe> field = getFieldDescribeSilently(fieldName);
        return field.filter(iFieldDescribe -> fieldTypes.contains(iFieldDescribe.getType())).isPresent();
    }

    public boolean isReference(String fieldName) {
        Optional<IFieldDescribe> field = getFieldDescribeSilently(fieldName);
        if (!field.isPresent()) {
            return false;
        }
        return Objects.equals(field.get().getType(), IFieldType.OBJECT_REFERENCE);
    }

    public boolean isInGroupField(String fieldApiName) {
        return getFieldDescribes().stream()
                .filter(x -> FieldDescribeExt.of(x).isGroupField())
                .anyMatch(x -> {
                    Map fields = (Map) FieldDescribeExt.of(x).toMap().get("fields");
                    return CollectionUtils.notEmpty(fields) && fields.containsValue(fieldApiName);
                });
    }

    public Optional<RecordTypeFieldDescribe> getRecordTypeField() {
        return getFieldDescribeSilently(MultiRecordType.RECORD_TYPE).map(ObjectDescribeExt::convert);
    }

    public List<String> getRecordTypeNames() {
        return getRecordTypeField()
                .map(RecordTypeFieldDescribe::getRecordTypeOptions)
                .orElse(Collections.emptyList())
                .stream()
                .map(IRecordTypeOption::getApiName)
                .distinct()
                .collect(Collectors.toList());
    }

    public Optional<SignInFieldDescribe> getSignInFieldDescribe() {
        Optional<SignInFieldDescribe> ret = filterOne(x -> IFieldType.GROUP.equals(x.getType())
                && GroupField.GROUP_TYPE_SIGN_IN.equals(((GroupField) x).getGroupType()))
                .map(ObjectDescribeExt::convert);
        return ret;
    }

    public Optional<PaymentFieldDescribe> getPaymentFieldDescribe() {
        Optional<PaymentFieldDescribe> ret = filterOne(x -> IFieldType.GROUP.equals(x.getType())
                && GroupField.GROUP_TYPE_PAYMENT.equals(((GroupField) x).getGroupType()))
                .map(ObjectDescribeExt::convert);
        return ret;
    }

    public Optional<DepartmentFieldDescribe> getDepartmentFieldDescribe() {
        Optional<DepartmentFieldDescribe> ret = filterOne(x -> IFieldType.DEPARTMENT.equals(x.getType()))
                .map(ObjectDescribeExt::convert);
        return ret;
    }

    public Optional<IRecordTypeOption> getRecordTypeOption(String optionAPIName) {
        Optional<RecordTypeFieldDescribe> recordTypeFieldDescribe = getRecordTypeField();
        return recordTypeFieldDescribe.map(x -> x.getRecordTypeOption(optionAPIName));
    }

    public Optional<ObjectReferenceWrapper> getReferenceField(String targetApiName,
                                                              String targetRelatedListName) {

        Optional<ObjectReferenceWrapper> ret = stream()
                .filter(x -> FieldDescribeExt.of(x).isLookupField())
                .map(x -> ObjectReferenceWrapper.of(x)).filter(x -> targetApiName.equals(x.getTargetApiName())
                        && Objects.equals(targetRelatedListName, x.getTargetRelatedListName())).findFirst();
        return ret;
    }

    public List<AutoNumber> getAutoNumberFields() {
        return stream().filter(x -> x instanceof AutoNumber)
                .filter(IFieldDescribe::isActive)
                .map(x -> (AutoNumber) x)
                .collect(Collectors.toList());
    }

    public List<AutoNumber> getAutoNumberFieldsByCondition(String condition) {
        return stream().filter(x -> x instanceof AutoNumber)
                .filter(IFieldDescribe::isActive)
                .map(x -> (AutoNumber) x)
                .filter(x -> Objects.equals(condition, x.getCondition()))
                .collect(Collectors.toList());
    }

    public List<AutoNumber> getAutoNumberFieldsNoCondition(String condition) {
        return stream().filter(x -> x instanceof AutoNumber)
                .filter(IFieldDescribe::isActive)
                .map(x -> (AutoNumber) x)
                .filter(x -> !Objects.equals(condition, x.getCondition()))
                .collect(Collectors.toList());
    }

    public List<AutoNumber> getAutoNumberFieldsIsNone() {
        return stream().filter(x -> x instanceof AutoNumber)
                .filter(IFieldDescribe::isActive)
                .map(x -> (AutoNumber) x)
                .filter(x -> AutoNumberExt.ConditionEnum.of(x.getCondition()) == AutoNumberExt.ConditionEnum.NONE)
                .filter(it -> !AutoNumberExt.of(it).isFunctionAutoNumber())
                .collect(Collectors.toList());
    }

    public List<AutoNumber> getAutoNumberFieldsNotNone() {
        return stream().filter(x -> x instanceof AutoNumber)
                .filter(IFieldDescribe::isActive)
                .map(x -> (AutoNumber) x)
                .filter(x -> AutoNumberExt.ConditionEnum.of(x.getCondition()) != AutoNumberExt.ConditionEnum.NONE)
                .filter(it -> !AutoNumberExt.of(it).isFunctionAutoNumber())
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> filter(Predicate<IFieldDescribe> filter) {
        return stream().filter(filter).collect(Collectors.toList());
    }

    public Stream<IFieldDescribe> stream() {
        return getFieldDescribesSilently().stream();
    }

    public Optional<IFieldDescribe> filterOne(Predicate<IFieldDescribe> filter) {
        List<IFieldDescribe> ret = filter(filter);
        return ret.isEmpty() ? Optional.empty() : Optional.of(ret.get(0));
    }

    public List<IFieldDescribe> getWhatObjectFieldDescribes() {
        return getFieldDescribes().stream()
                .filter(IFieldDescribe::isActive)
                // 设计器可配置的字段
                .filter(field -> CollectionUtils.notEmpty(field.getConfig()))
                // 老对象的虚拟字段不存库
                .filter(x -> !TRUE.equals(x.isAbstract()))
                // 相关团队
                .filter(field -> !ObjectDataExt.RELEVANT_TEAM.equals(field.getApiName()))
                .filter(field -> !IFieldType.GROUP.equals(field.getType()))
                .filter(field -> !IFieldType.EMBEDDED_OBJECT_LIST.equals(field.getType()))
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getActiveFieldDescribes() {
        return getFieldDescribes().stream().filter(x -> x.isActive()).collect(Collectors.toList());
    }

    public List<IFieldDescribe> getDisableFieldDescribes() {
        return getFieldDescribes().stream().filter(x -> !x.isActive()).collect(Collectors.toList());
    }

    public List<IFieldDescribe> getFieldDescribesSilently() {
        return getFieldDescribes();
    }

    public Optional<IFieldDescribe> getFieldDescribeSilently(String fieldAPIName) {
        return Optional.ofNullable(getFieldDescribe(fieldAPIName));
    }

    public Optional<IFieldDescribe> getActiveFieldDescribeSilently(String fieldAPIName) {
        return Optional.ofNullable(getFieldDescribe(fieldAPIName)).filter(x -> x.isActive());
    }

    public IFieldDescribe getActiveFieldDescribe(String fieldAPIName) {
        return getActiveFieldDescribeSilently(fieldAPIName)
                .orElseThrow(() -> new FieldNotExistException(getDisplayName() + "." + getFieldLabelByName(fieldAPIName)));
    }

    public IFieldDescribe getActiveFieldDescribe(String fieldAPIName, String expressionLabel) {
        return getActiveFieldDescribeSilently(fieldAPIName)
                .orElseThrow(() -> new FieldNotExistException(expressionLabel,
                        getDisplayName() + "." + getFieldLabelByName(fieldAPIName)));
    }

    public IFieldDescribe getActiveFieldDescribeByValidate(String fieldAPIName, String i18nKey, String label) {
        return getActiveFieldDescribeSilently(fieldAPIName)
                .orElseThrow(() -> new ValidateException(I18NExt.text(i18nKey, label, getDisplayName() + "." + getFieldLabelByName(fieldAPIName))));
    }

    private DocumentBasedBean convert() {
        return (DocumentBasedBean) objectDescribe;
    }

    private static <T extends IFieldDescribe> T convert(IFieldDescribe field) {
        return (T) field;
    }

    public static List<Wheres> getWheresBy(List<LinkedHashMap> list) {
        return WheresExt.castToWheresList(list);
    }

    public void fillSignInInfoField() {
        getSignInFieldDescribe().ifPresent(signInFieldDescribe -> {
            if (getSignInInfoListFieldSilently(signInFieldDescribe).isPresent()) {
                return;
            }
            signInFieldDescribe.setSignInInfoListFieldApiName("sign_in_info__c");
            EmbeddedObjectList fieldDescribe = generateSignInfoListField();

            objectDescribe.addFieldDescribe(fieldDescribe);
        });
    }

    @Deprecated
    public void checkFieldLabelDuplicate() {
        Set<String> fieldLabels = Sets.newHashSet();
        getFieldDescribesSilently().forEach(fieldDescribe -> {
            if (!fieldLabels.add(fieldDescribe.getLabel())) {
                throw new ValidateException(I18N.text(I18NKey.FIELD_LABEL_DUPLICATE, fieldDescribe.getLabel()));
            }
        });
    }

    public void setFieldDescribeCreateTime() {
        //i用来保证每个fieldDescribe的创建时间都不一样,原因是为了排序。
        int i = 0;
        for (IFieldDescribe fieldDescribe : getFieldDescribesSilently()) {
            if (fieldDescribe.getCreateTime() == null) {
                fieldDescribe.setCreateTime(System.currentTimeMillis() + (i++));
            }
        }
    }

    public static EmbeddedObjectList generateSignInfoListField() {
        EmbeddedObjectList fieldDescribe = new EmbeddedObjectListFieldDescribe();
        fieldDescribe.setApiName("sign_in_info__c");
        fieldDescribe.setLabel(I18N.text(I18NKey.SIGN_IN_INFO));
        fieldDescribe.setActive(true);
        fieldDescribe.setDefineType(IFieldDescribe.DEFINE_TYPE_CUSTOM);
        fieldDescribe.setIndex(false);
        fieldDescribe.setStatus(IFieldDescribe.STATUS_NEW);
        fieldDescribe.set("used_in", "component");
        DateTime time = new DateTimeFieldDescribe();
        time.setApiName("sign_time__c");
        time.setLabel(I18N.text(I18NKey.TIME));
        time.setActive(true);
        time.setDefineType(IFieldDescribe.DEFINE_TYPE_CUSTOM);
        time.setIndex(false);
        time.setStatus(IFieldDescribe.STATUS_NEW);

        Location location = new LocationFieldDescribe();
        location.setApiName("location__c");
        location.setLabel(I18N.text(I18NKey.LOCATION));
        location.setActive(true);
        location.setDefineType(IFieldDescribe.DEFINE_TYPE_CUSTOM);
        location.setIndex(false);
        location.setStatus(IFieldDescribe.STATUS_NEW);

        SelectOne status = new SelectOneFieldDescribe();
        status.setApiName("status__c");
        status.setLabel(I18N.text(I18NKey.STATUS));
        status.setActive(true);
        status.setDefineType(IFieldDescribe.DEFINE_TYPE_CUSTOM);
        status.setIndex(false);
        status.setStatus(IFieldDescribe.STATUS_NEW);
        ISelectOption signInOption = new SelectOption();
        signInOption.setValue("sign_in_complete");
        signInOption.setLabel(I18N.text(I18NKey.SIGN_IN_OPTION));
        ISelectOption signOutOption = new SelectOption();
        signOutOption.setValue("sign_out_complete");
        signOutOption.setLabel(I18N.text(I18NKey.SIGN_OUT_OPTION));
        status.setSelectOptions(Lists.newArrayList(signInOption, signOutOption));

        SelectOne bizType = new SelectOneFieldDescribe();
        bizType.setApiName("biz_type__c");
        bizType.setLabel(I18N.text(I18NKey.BIZ_TYPE));
        bizType.setActive(true);
        bizType.setDefineType(IFieldDescribe.DEFINE_TYPE_CUSTOM);
        bizType.setIndex(false);
        bizType.setStatus(IFieldDescribe.STATUS_NEW);
        ISelectOption bizSignInOption = new SelectOption();
        bizSignInOption.setValue("sign_in");
        bizSignInOption.setLabel(I18N.text(I18NKey.SIGN_IN));
        ISelectOption bizSignOutOption = new SelectOption();
        bizSignOutOption.setValue("sign_out");
        bizSignOutOption.setLabel(I18N.text(I18NKey.SIGN_OUT));
        bizType.setSelectOptions(Lists.newArrayList(bizSignInOption, bizSignOutOption));

        Text device = new TextFieldDescribe();
        device.setApiName("device_no__c");
        device.setLabel(I18N.text(I18NKey.SIGN_IN_DEVICE));
        device.setActive(true);
        device.setDefineType(IFieldDescribe.DEFINE_TYPE_CUSTOM);
        device.setIndex(false);
        device.setStatus(IFieldDescribe.STATUS_NEW);

        Text systemRisk = new TextFieldDescribe();
        systemRisk.setApiName("system_risk__c");
        systemRisk.setLabel(I18N.text(I18NKey.SYSTEM_RISK));
        systemRisk.setActive(true);
        systemRisk.setDefineType(IFieldDescribe.DEFINE_TYPE_CUSTOM);
        systemRisk.setIndex(false);
        systemRisk.setStatus(IFieldDescribe.STATUS_NEW);

        List<IFieldDescribe> fieldDescribeList = Lists.newArrayList(time, location, status, bizType, device,
                systemRisk);
        fieldDescribe.setFieldDescribes(fieldDescribeList);
        return fieldDescribe;
    }

    private Optional<EmbeddedObjectList> getSignInInfoListFieldSilently(SignInFieldDescribe signInFieldDescribe) {
        try {
            return Optional.ofNullable(signInFieldDescribe.getSignInInfoListField(objectDescribe));
        } catch (MetadataServiceException e) {
            throw new MetaDataException(SystemErrorCode.METADATA_SIGN_INFO_ERROR, e);
        }
    }

    /**
     * 去掉系统字段的帮助文本
     */
    public void removeHelpTextForSysField() {
        List<IFieldDescribe> fieldDescribes = filter(a -> Objects.equals(IFieldDescribe.DEFINE_TYPE_SYSTEM,
                a.getDefineType()));
        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            fieldDescribe.setHelpText("");
        }
    }

    // 按照查找关联字段的创建顺序排序
    public List<RelatedObjectDescribeStructure> getRelatedObjectDescribeStructuresOrderByFieldCreateTime(List<IObjectDescribe> relatedDescribeList) {
        if (CollectionUtils.empty(relatedDescribeList)) {
            return Lists.newArrayList();
        }
        List<RelatedObjectDescribeStructure> referenceStructureList = Lists.newArrayList();
        List<ObjectReferenceWrapper> referenceFields = Lists.newArrayList();
        Map<String, IObjectDescribe> objectDescribeStructureMap = Maps.newHashMap();
        relatedDescribeList.stream().filter(IObjectDescribe::isActive).forEach(x -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(x);
            referenceFields.addAll(describeExt.getActiveReferenceFieldDescribes()
                    .stream()
                    .filter(ref -> {
                        if (Objects.equals(ref.getTargetApiName(), objectDescribe.getApiName())) {
                            objectDescribeStructureMap.put(getRefObjFieldKey(ref), x);
                            return true;
                        }
                        return false;
                    })
                    .collect(Collectors.toList()));
        });
        referenceFields.sort(Comparator.comparingLong(o -> Optional.ofNullable(o.getCreateTime()).orElse(0L)));
        referenceFields.forEach(y -> {
            referenceStructureList.add(RelatedObjectDescribeStructure.builder()
                    .fieldApiName(y.getApiName())
                    .fieldLabel(y.getLabel())
                    .relatedObjectDescribe(objectDescribeStructureMap.get(getRefObjFieldKey(y)))
                    .relatedListLabel(y.getTargetRelatedListLabel())
                    .relatedListName((y.getTargetRelatedListName()))
                    .build());
        });
        return referenceStructureList;
    }

    //将所有的从对象加到列表中
    public List<RelatedObjectDescribeStructure> getRelatedObjectDescribeStructures(List<IObjectDescribe> relatedDescribeList) {
        if (CollectionUtils.empty(relatedDescribeList)) {
            return Lists.newArrayList();
        }

        List<RelatedObjectDescribeStructure> referenceStructureList = Lists.newArrayList();
        relatedDescribeList.stream().filter(x -> x.isActive()).forEach(x -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(x);
            List<ObjectReferenceWrapper> referenceFields = describeExt.getActiveReferenceFieldDescribes()
                    .stream()
                    .filter(ref -> Objects.equals(ref.getTargetApiName(), objectDescribe.getApiName())).collect(Collectors.toList());
            referenceFields.forEach(y -> {
                referenceStructureList.add(RelatedObjectDescribeStructure.builder()
                        .fieldApiName(y.getApiName())
                        .fieldLabel(y.getLabel())
                        .relatedObjectDescribe(x)
                        .relatedListLabel(y.getTargetRelatedListLabel())
                        .relatedListName((y.getTargetRelatedListName()))
                        .build());
            });
        });

        return referenceStructureList;
    }

    public List<RelatedObjectDescribeStructure> getDetailObjectDescribeStructuresOrderByFieldCreateTime(List<IObjectDescribe> detailDescribeList) {
        if (CollectionUtils.empty(detailDescribeList)) {
            return Lists.newArrayList();
        }
        List<RelatedObjectDescribeStructure> masterDetailStructureList = Lists.newArrayList();
        List<IFieldDescribe> masterDetailFields = Lists.newArrayList();
        Map<String, IObjectDescribe> detailDescribeMap = Maps.newHashMap();
        detailDescribeList.stream().filter(IObjectDescribe::isActive).forEach(x -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(x);
            masterDetailFields.addAll(describeExt.stream().filter(a -> {
                        if (a.isActive() && Objects.equals(a.getType(), IFieldType.MASTER_DETAIL)
                                && Objects.equals(((MasterDetail) a).getTargetApiName(), objectDescribe.getApiName())) {
                            detailDescribeMap.put(getRefObjFieldKey(a), x);
                            return true;
                        }
                        return false;
                    })
                    .collect(Collectors.toList()));
        });
        masterDetailFields.sort(Comparator.comparingLong(o -> Optional.ofNullable(o.getCreateTime()).orElse(0L)));
        masterDetailFields.forEach(y ->
                masterDetailStructureList
                        .add(ObjectDescribeExt.of(detailDescribeMap.get(getRefObjFieldKey(y))).toDetailRelatedStructure((MasterDetail) y)));
        return masterDetailStructureList;
    }

    private String getRefObjFieldKey(IFieldDescribe fieldDescribe) {
        return fieldDescribe.getDescribeApiName() + "_" + fieldDescribe.getApiName();
    }

    public List<RelatedObjectDescribeStructure> getDetailObjectDescribeStructures(List<IObjectDescribe> detailDescribeList) {
        if (CollectionUtils.empty(detailDescribeList)) {
            return Lists.newArrayList();
        }

        List<RelatedObjectDescribeStructure> masterDetailStructureList = Lists.newArrayList();
        detailDescribeList.stream().filter(x -> x.isActive()).forEach(x -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(x);
            List<IFieldDescribe> masterDetailFields = describeExt.filter(a -> a.isActive()
                    && Objects.equals(a.getType(), IFieldType.MASTER_DETAIL)
                    && Objects.equals(((MasterDetail) a).getTargetApiName(), objectDescribe.getApiName()));
            masterDetailFields.forEach(y ->
                    masterDetailStructureList.add(ObjectDescribeExt.of(x).toDetailRelatedStructure((MasterDetail) y)));
        });

        return masterDetailStructureList;
    }

    public RelatedObjectDescribeStructure toDetailRelatedStructure() {
        Optional<MasterDetail> masterDetailField = getMasterDetailField();
        if (!masterDetailField.isPresent()) {
            return null;
        }
        MasterDetail masterDetail = masterDetailField.get();
        return toDetailRelatedStructure(masterDetail);
    }

    private RelatedObjectDescribeStructure toDetailRelatedStructure(MasterDetail masterDetail) {
        return RelatedObjectDescribeStructure.builder()
                .fieldApiName(masterDetail.getApiName())
                .fieldLabel(masterDetail.getLabel())
                .relatedObjectDescribe(objectDescribe)
                .relatedListLabel(masterDetail.getTargetRelatedListLabel())
                .relatedListName(masterDetail.getTargetRelatedListName())
                .build();
    }

    public boolean isFieldInGroup(IFieldDescribe fieldDescribe, GroupField groupField, IObjectDescribe describe) {
        List<IFieldDescribe> fieldList = null;
        try {
            fieldList = groupField.getFieldList(describe);
        } catch (MetadataServiceException e) {

        }
        if (CollectionUtils.empty(fieldList)) {
            return false;
        }

        for (IFieldDescribe field : fieldList) {
            if (Objects.equals(field.getApiName(), fieldDescribe.getApiName())) {
                return true;
            }
        }
        return false;
    }

    //描述层：生成默认的团队成员的EmbeddedObjectListFieldDescribe
    public static EmbeddedObjectListFieldDescribe generateDefaultTeamMemberFieldDescribe() {
        EmbeddedObjectListFieldDescribe teamMemberField = new EmbeddedObjectListFieldDescribe();
        teamMemberField.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        teamMemberField.setLabel(UdobjConstants.getRelevantTeamLabel());
        teamMemberField.setHelpText(UdobjConstants.getRelevantTeamLabel());
        teamMemberField.setApiName(UdobjConstants.RELEVANT_TEAM_API_NAME);
        teamMemberField.setIndex(TRUE);
        teamMemberField.setRequired(FALSE);
        // teamMemberEmployee	成员员工（员工ID)
        EmployeeFieldDescribe teamMemberEmployee = new EmployeeFieldDescribe();
        teamMemberEmployee.setApiName(UdobjConstants.TEAM_MEMBER_EMPLOYEE_API_NAME);
        teamMemberEmployee.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        teamMemberEmployee.setDescription(UdobjConstants.getTeamMemberEmployeeLabel());
        teamMemberEmployee.setHelpText(UdobjConstants.getTeamMemberEmployeeLabel());
        teamMemberEmployee.setLabel(UdobjConstants.getTeamMemberEmployeeLabel());
        teamMemberEmployee.setIndex(TRUE);
        teamMemberEmployee.setRequired(FALSE);
        teamMemberEmployee.setIsNeedConvert(TRUE);
        // teamMemberTypeList	成员角色
        SelectOneFieldDescribe teamMemberRole = new SelectOneFieldDescribe();
        teamMemberRole.setApiName(UdobjConstants.TEAM_MEMBER_ROLE_API_NAME);
        teamMemberRole.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        teamMemberRole.setDescription(UdobjConstants.getTeamMemberRoleLabel());
        teamMemberRole.setHelpText(UdobjConstants.getTeamMemberRoleLabel());
        teamMemberRole.setLabel(UdobjConstants.getTeamMemberRoleLabel());
        teamMemberRole.setIndex(TRUE);
        teamMemberRole.setRequired(FALSE);
        teamMemberRole.setSelectOptions(
                Lists.newArrayList(
                        new SelectOption(
                                DefObjConstants.DATA_PRIVILEGE_ROLE_TYPE.OWNER.getLabel(),
                                DefObjConstants.DATA_PRIVILEGE_ROLE_TYPE.OWNER.getValue(), null),
                        new SelectOption(
                                DefObjConstants.DATA_PRIVILEGE_ROLE_TYPE.NORMAL_STAFF.getLabel(),
                                DefObjConstants.DATA_PRIVILEGE_ROLE_TYPE.NORMAL_STAFF.getValue(), null)
                ));
        // teamMemberPermissionType	成员权限类型
        SelectOneFieldDescribe teamMemberPermissionType = new SelectOneFieldDescribe();
        teamMemberPermissionType.setApiName(UdobjConstants.TEAM_MEMBER_PERMISSION_TYPE_API_NAME);
        teamMemberPermissionType.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        teamMemberPermissionType.setDescription(UdobjConstants.getTeamMemberPermissionTypeLabel());
        teamMemberPermissionType.setHelpText(UdobjConstants.getTeamMemberPermissionTypeLabel());
        teamMemberPermissionType.setLabel(UdobjConstants.getTeamMemberPermissionTypeLabel());
        teamMemberPermissionType.setIndex(TRUE);
        teamMemberPermissionType.setRequired(FALSE);
        teamMemberPermissionType.setSelectOptions(
                Lists.newArrayList(
                        new SelectOption(
                                DefObjConstants.DATA_PRIVILEGE_PERMISSION.READONLY.getLabel(),
                                DefObjConstants.DATA_PRIVILEGE_PERMISSION.READONLY.getValue(), null),
                        new SelectOption(
                                DefObjConstants.DATA_PRIVILEGE_PERMISSION.READANDWRITE.getLabel(),
                                DefObjConstants.DATA_PRIVILEGE_PERMISSION.READANDWRITE.getValue(), null)
                ));
        teamMemberField.setFieldDescribes(Lists.newArrayList(teamMemberEmployee, teamMemberRole,
                teamMemberPermissionType));
        return teamMemberField;
    }

    public static boolean isSFAObject(String apiName) {
        return ObjectAPINameMapping.isSFAObject(apiName);
    }

    public static boolean isSFANotStandardObject(String apiName) {
        return ObjectAPINameMapping.isSFANotStandardObject(apiName);
    }

    public static boolean isPredefineCustomObject(String apiName) {
        return PREDEFINE_CUSTOM_OBJECTS.contains(apiName);
    }

    public void fillValueToRecordType() {
        Optional<RecordTypeFieldDescribe> recordTypeField = getRecordTypeField();
        if (!recordTypeField.isPresent()) {
            return;
        }

        List<IRecordTypeOption> recordTypeOptions = recordTypeField.get().getRecordTypeOptions();
        for (IRecordTypeOption option : recordTypeOptions) {
            ((DocumentBasedBean) option).getContainerDocument().put(ISelectOption.OPTION_VALUE, option.getApiName());
        }
    }

    /**
     * 清理被删除选项的依赖关系
     */
    public List<IFieldDescribe> cleanOptionRelation(SelectOne selectOne, ISelectOption optionToBeDeleted) {
        // 当前选项字段使用了通用选项集,不处理选项的依赖关系
        if (FieldDescribeExt.of(selectOne).isGeneralOptions()) {
            return Collections.emptyList();
        }
        //处理父单选
        List<IFieldDescribe> fieldDescribes = cleanParentOptionRelation(selectOne, optionToBeDeleted);
        //处理子单选
        List<IFieldDescribe> childfieldDescribes = cleanChildOptionRelation(selectOne, optionToBeDeleted);
        List<IFieldDescribe> result = Lists.newArrayList();
        result.addAll(fieldDescribes);
        result.addAll(childfieldDescribes);

        return result;
    }

    private List<IFieldDescribe> cleanChildOptionRelation(SelectOne selectOne, ISelectOption optionToBeDeleted) {
        List<Map<String, List<String>>> childOptions = optionToBeDeleted.getChildOptions();
        if (CollectionUtils.empty(childOptions)) {
            //无依赖此选项的其他单选
            return Lists.newArrayList();
        }

        //检查当前单选的所有选项, 如果没有
        List<String> childSelectOne = Lists.newArrayList();
        for (Map<String, List<String>> map : childOptions) {
            childSelectOne.addAll(map.keySet());
        }

        List<IFieldDescribe> updatedFields = Lists.newArrayList();
        //当前selectone下其他选项
        List<ISelectOption> selectOptions = selectOne.getSelectOptions();
        for (String childApiName : childSelectOne) {
            for (ISelectOption selectOption : selectOptions) {
                if (Objects.equals(selectOption.getValue(), optionToBeDeleted.getValue())) {
                    continue;
                }
                List<Map<String, List<String>>> optionChildOptions = selectOption.getChildOptions();
                if (CollectionUtils.empty(optionChildOptions)) {
                    continue;
                }

                updateParentApiName(updatedFields, childApiName, optionChildOptions);
            }
        }
        return updatedFields;
    }

    private List<IFieldDescribe> cleanParentOptionRelation(SelectOne selectOne, ISelectOption optionToBeDeleted) {
        String parentApiName = selectOne.getCascadeParentApiName();
        if (Strings.isNullOrEmpty(parentApiName)) {
            return Lists.newArrayList();
        }
        Optional<IFieldDescribe> parent = getFieldDescribeSilently(parentApiName);
        if (!parent.isPresent()) {
            return Lists.newArrayList();
        }

        // 父字段使用了选项集,不处理级联关系
        if (FieldDescribeExt.of(parent.get()).isGeneralOptions()) {
            return Lists.newArrayList();
        }

        if (Objects.equals(parentApiName, MultiRecordType.RECORD_TYPE)) {
            Optional<RecordTypeFieldDescribe> recordTypeField = getRecordTypeField();
            if (!recordTypeField.isPresent()) {
                return Lists.newArrayList();
            }
            List<IRecordTypeOption> recordTypeOptions = recordTypeField.get().getRecordTypeOptions();
            for (IRecordTypeOption option : recordTypeOptions) {
                List<Map<String, List<String>>> childOptions = option.getChildOptions();
                removeChildOption(childOptions, selectOne.getApiName(), optionToBeDeleted.getValue());
                option.setChildOptions(childOptions);
            }
        } else {
            SelectOne parentSelectOne = (SelectOne) parent.get();
            List<ISelectOption> selectOptions = parentSelectOne.getSelectOptions();

            for (ISelectOption option : selectOptions) {
                List<Map<String, List<String>>> childOptions = option.getChildOptions();
                removeChildOption(childOptions, selectOne.getApiName(), optionToBeDeleted.getValue());
                option.setChildOptions(childOptions);
            }
        }
        return Lists.newArrayList(parent.get());
    }

    private void removeChildOption(List<Map<String, List<String>>> childOptions, String apiName, String optionValue) {
        if (CollectionUtils.empty(childOptions)) {
            return;
        }

        //childOptions.stream().filter(a -> a.containsKey(apiName)).map(a -> a.get(apiName).remove(optionValue));
        for (Map<String, List<String>> map : childOptions) {
            if (!map.containsKey(apiName)) {
                continue;
            }

            map.get(apiName).remove(optionValue);
        }
    }

    public List<IFieldDescribe> cleanChildOptionRelationForRecordType(IRecordTypeOption optionToBeDeleted) {
        Optional<RecordTypeFieldDescribe> recordTypeField = getRecordTypeField();
        if (!recordTypeField.isPresent()) {
            return Lists.newArrayList();
        }
        RecordTypeFieldDescribe selectOne = recordTypeField.get();

        List<Map<String, List<String>>> childOptions = optionToBeDeleted.getChildOptions();
        if (CollectionUtils.empty(childOptions)) {
            //无依赖此选项的其他单选
            return Lists.newArrayList();
        }

        //检查当前单选的所有选项, 如果没有
        List<String> childSelectOne = Lists.newArrayList();
        for (Map<String, List<String>> map : childOptions) {
            childSelectOne.addAll(map.keySet());
        }

        List<IFieldDescribe> updatedFields = Lists.newArrayList();
        //当前selectone下其他选项
        List<IRecordTypeOption> selectOptions = selectOne.getRecordTypeOptions();
        for (String childApiName : childSelectOne) {
            for (IRecordTypeOption selectOption : selectOptions) {
                if (Objects.equals(selectOption.getApiName(), optionToBeDeleted.getApiName())) {
                    continue;
                }
                List<Map<String, List<String>>> optionChildOptions = selectOption.getChildOptions();
                if (CollectionUtils.empty(optionChildOptions)) {
                    continue;
                }

                updateParentApiName(updatedFields, childApiName, optionChildOptions);
            }
        }
        return updatedFields;
    }

    private void updateParentApiName(List<IFieldDescribe> updatedFields, String childApiName, List<Map<String,
            List<String>>> optionChildOptions) {
        Optional<Boolean> any = optionChildOptions.stream().map(a -> a.containsKey(childApiName)).findAny();
        if (!any.isPresent()) {
            Optional<IFieldDescribe> one = getFieldDescribeSilently(childApiName);
            one.ifPresent(iFieldDescribe -> {
                ((SelectOne) iFieldDescribe).setCascadeParentApiName(null);
                updatedFields.add(iFieldDescribe);
            });
        }
    }

    public void fillParentLookup() {
        fillParentLookupWithExt(null);
    }

    public void fillParentLookupWithExt(Map<String, IObjectDescribe> describeExtMap) {
        List<ObjectReferenceWrapper> refFieldList = getReferenceFieldDescribes();
        refFieldList.forEach(f -> {
            Set<String> fieldNameSet = Sets.newHashSet();
            List<Wheres> wheres = f.getWheresBy();
            if (!CollectionUtils.empty(wheres)) {
                for (Wheres where : wheres) {
                    List<IFilter> filters = where.getFilters();
                    if (CollectionUtils.empty(filters)) {
                        continue;
                    }
                    for (IFilter filter : filters) {
                        if (!FilterExt.of(filter).hasRefObjectVariableValueType() || CollectionUtils.empty(filter.getFieldValues())) {
                            continue;
                        }
                        //替换表达式的值
                        String expression = filter.getFieldValues().get(0).replace("$", "");
                        // 只下发三角关系
                        if (expression.contains(".")) {
                            continue;
                        }
                        //只处理条件字段是lookup类型的filter
                        IFieldDescribe fieldDescribe = getFieldDescribe(expression);
                        if (fieldDescribe == null || !FieldDescribeExt.of(fieldDescribe).isLookupField()) {
                            continue;
                        }
                        fieldNameSet.add(expression);
                    }
                }
            }
            if (!CollectionUtils.empty(fieldNameSet)) {
                if (describeExtMap != null) {
                    copyField4Ext(describeExtMap, f).set(CASCADE_PARENT_API_NAME, fieldNameSet);
                } else {
                    f.set(CASCADE_PARENT_API_NAME, fieldNameSet);
                }
            }
        });

    }

    public Map<String, Tuple<String, List<String>>> fillCascadeDetailLookup() {
        return fillCascadeDetailLookupWithExt(null);
    }

    public Map<String, Tuple<String, List<String>>> fillCascadeDetailLookupWithExt(Map<String, IObjectDescribe> describeExtMap) {
        List<ObjectReferenceFieldDescribe> refFieldList = getActiveSingleReferenceFieldDescribes();
        Map<String, ObjectReferenceFieldDescribe> refFieldMap = refFieldList.stream().collect(Collectors.toMap(IFieldDescribe::getApiName, x -> x));
        Map<Tuple<Boolean, String>, List<String>> fieldNameMap = Maps.newHashMap();

        refFieldList.forEach(field -> {
            List<Wheres> wheres = getWheresBy(field.getWheres());
            wheres.forEach(where -> where.getFilters().stream()
                    .filter(filter -> CollectionUtils.notEmpty(filter.getFieldValues()) &&
                            (FilterExt.of(filter).hasRefObjectVariableValueType() || FilterExt.of(filter).hasRelatedChainObjectVariableValueType()))
                    .forEach(filter -> {
                        Tuple<Boolean, String> tuple = getFieldApiNameTuple(filter);
                        //value_type=2的filter只处理条件字段是lookup类型的场景
                        FilterExt filterExt = FilterExt.of(filter);
                        if (filterExt.hasRefObjectVariableValueType() && !filterExt.isTriangleRelation() && !filterExt.isRectangleRelation()) {
                            IFieldDescribe fieldDescribe = getFieldDescribe(tuple.getValue());
                            if (fieldDescribe == null || !FieldDescribeExt.of(fieldDescribe).isLookupField()) {
                                return;
                            }
                        }
                        fieldNameMap.computeIfAbsent(tuple, x -> Lists.newArrayList()).add(field.getApiName());
                    }));
        });

        // 只有fieldNameMap 中存储的是本对象的字段时才下发 cascade_detail_api_name
        fieldNameMap.entrySet().stream()
                .filter(x -> TRUE.equals(x.getKey().getKey()))
                .filter(x -> refFieldMap.containsKey(x.getKey().getValue()))
                .forEach(x -> {
                    Map<String, Set<String>> resultMap = Maps.newHashMap();
                    resultMap.put(getApiName(), Sets.newHashSet(x.getValue()));

                    if (describeExtMap == null) {
                        refFieldMap.get(x.getKey().getValue()).set(CASCADE_DETAIL_API_NAME, resultMap);
                    } else {
                        copyField4Ext(describeExtMap, refFieldMap.get(x.getKey().getValue())).set(CASCADE_DETAIL_API_NAME, resultMap);
                    }

                    // 本对象从对象下发new_cascade_parent_api_name
                    fillNewCascadeParentApiName(refFieldMap, x, getApiName(), describeExtMap);
                    copyCascadeParentToNewCascadeParent(refFieldMap.values(), getApiName(), describeExtMap);
                });
        // 主从一起新建的从对象，将四角、五角的关系返回
        return handleNewCascadeParentCreateWithMaster(refFieldMap, fieldNameMap, describeExtMap);
    }

    public IObjectDescribe copy4Ext(Map<String, IObjectDescribe> describeExtMap) {
        return describeExtMap.computeIfAbsent(getApiName(), apiName -> {
            IObjectDescribe describe = new ObjectDescribe();
            describe.setApiName(apiName);
            return describe;
        });
    }

    public IFieldDescribe copyField4Ext(Map<String, IObjectDescribe> describeExtMap, IFieldDescribe field) {
        IObjectDescribe describe = copy4Ext(describeExtMap);
        if (!describe.containsField(field.getApiName())) {
            describe.addFieldDescribe(FieldDescribeExt.of(field).copy());
        }
        return describeExtMap.get(getApiName()).getFieldDescribe(field.getApiName());
    }

    public IFieldDescribe getOrCopyField(IFieldDescribe field) {
        if (!containsField(field.getApiName())) {
            addFieldDescribe(FieldDescribeExt.of(field).copy());
        }
        return getFieldDescribe(field.getApiName());
    }

    private void copyCascadeParentToNewCascadeParent(Collection<ObjectReferenceFieldDescribe> refFieldSet,
                                                     String apiName, Map<String, IObjectDescribe> describeExtMap) {
        refFieldSet.stream()
                .map(fieldDescribe -> {
                    if (describeExtMap == null) {
                        return fieldDescribe;
                    } else {
                        return (ObjectReferenceFieldDescribe) copyField4Ext(describeExtMap, fieldDescribe);
                    }
                })
                .filter(fieldDescribe -> fieldDescribe.containsKey(CASCADE_PARENT_API_NAME))
                .forEach(fieldDescribe -> {
                    Collection<String> cascadeParentApiName = getCascadeParentApiName(fieldDescribe);
                    Map<String, Set<String>> cascadeParentMap = computeIfAbsentByFieldDescribe(fieldDescribe);
                    cascadeParentMap.computeIfAbsent(apiName, t -> Sets.newHashSet()).addAll(cascadeParentApiName);
                });
    }

    @SuppressWarnings("unchecked")
    private Collection<String> getCascadeParentApiName(ObjectReferenceFieldDescribe fieldDescribe) {
        Object o = fieldDescribe.get(ObjectDescribeExt.CASCADE_PARENT_API_NAME);
        if (o instanceof Collection) {
            return (Collection) o;
        }
        return Lists.newArrayList(fieldDescribe.get(ObjectDescribeExt.CASCADE_PARENT_API_NAME, String.class));
    }

    @SuppressWarnings("unchecked")
    private Map<String, Set<String>> computeIfAbsentByFieldDescribe(ObjectReferenceFieldDescribe fieldDescribe) {
        return (Map<String, Set<String>>) fieldDescribe.getContainerDocument()
                .computeIfAbsent(NEW_CASCADE_PARENT_API_NAME, x -> Maps.newHashMap());
    }

    private Tuple<Boolean, String> getFieldApiNameTuple(IFilter filter) {
        Tuple<Boolean, String> tuple;
        //替换表达式的值
        if (FilterExt.of(filter).isTriangleRelation()) {// 处理三角
            tuple = Tuple.of(true, FilterExt.of(filter).getFieldApiNameWithTriangleRelation());
        } else if (FilterExt.of(filter).isRectangleRelation()) {// 处理四角
            tuple = Tuple.of(false, FilterExt.of(filter).getFieldApiNameWithRectangleRelation());
        } else if (FilterExt.of(filter).hasRelatedChainObjectVariableValueType()) { // 处理五角
            tuple = Tuple.of(false, FilterExt.of(filter).getFieldApiNameWithPentagonRelation());
        } else {
            tuple = Tuple.of(false, StringUtils.replace(filter.getFieldValues().get(0), "$", ""));
        }
        return tuple;
    }

    private Map<String, Tuple<String, List<String>>> handleNewCascadeParentCreateWithMaster(
            Map<String, ObjectReferenceFieldDescribe> refFieldMap,
            Map<Tuple<Boolean, String>, List<String>> fieldNameMap,
            Map<String, IObjectDescribe> describeExtMap) {
        if (!isCreateWithMaster()) {
            return Maps.newHashMap();
        }
        Map<String, Tuple<String, List<String>>> resultMap = Maps.newHashMap();

        fieldNameMap.entrySet().stream()
                .filter(x -> FALSE.equals(x.getKey().getKey()))
                .forEach(x -> {
                    String key = x.getKey().getValue();
                    Tuple<String, List<String>> tuple = resultMap.putIfAbsent(key, Tuple.of(getApiName(),
                            Lists.newArrayList(x.getValue())));
                    if (Objects.nonNull(tuple)) {
                        tuple.getValue().addAll(x.getValue());
                    }
                    // 主从同时新建的从对象下发new_cascade_parent_api_name
                    fillNewCascadeParentApiName(refFieldMap, x, getMasterAPIName().orElse(""), describeExtMap);
                });
        return resultMap;
    }

    private void fillNewCascadeParentApiName(Map<String, ObjectReferenceFieldDescribe> refFieldMap,
                                             Map.Entry<Tuple<Boolean, String>, List<String>> entry, String apiName,
                                             Map<String, IObjectDescribe> describeExtMap) {
        entry.getValue().stream()
                .map(x -> {
                    if (describeExtMap == null) {
                        return refFieldMap.get(x);
                    } else {
                        return (ObjectReferenceFieldDescribe) copyField4Ext(describeExtMap, refFieldMap.get(x));
                    }
                })
                .forEach(fieldDescribe -> {
                    Map<String, Set<String>> cascadeParentMap = computeIfAbsentByFieldDescribe(fieldDescribe);
                    cascadeParentMap.computeIfAbsent(apiName, t -> Sets.newHashSet()).add(entry.getKey().getValue());
                });
    }

    @SuppressWarnings("unchecked")
    public void fillCascadeDetailLookup(List<IObjectDescribe> detailDescribes) {
        fillCascadeDetailLookupWithExt(detailDescribes, null);
    }

    public void fillCascadeDetailLookupWithExt(List<IObjectDescribe> detailDescribes, Map<String, IObjectDescribe> describeExtMap) {
        if (CollectionUtils.empty(detailDescribes)) {
            return;
        }
        Map<String, ObjectReferenceFieldDescribe> refFieldMap = getActiveSingleReferenceFieldDescribes().stream()
                .collect(Collectors.toMap(IFieldDescribe::getApiName, x -> x));
        detailDescribes.forEach(describe -> {
            Map<String, Tuple<String, List<String>>> detailLookup = ObjectDescribeExt.of(describe).fillCascadeDetailLookupWithExt(describeExtMap);
            detailLookup.entrySet().stream()
                    .filter(x -> refFieldMap.containsKey(x.getKey()))
                    .forEach(x -> {
                        ObjectReferenceFieldDescribe fieldDescribe = describeExtMap == null ? refFieldMap.get(x.getKey()) :
                                (ObjectReferenceFieldDescribe) copyField4Ext(describeExtMap, refFieldMap.get(x.getKey()));
                        Map<String, List<String>> detailApiNameMap = (Map<String, List<String>>) fieldDescribe.get(
                                CASCADE_DETAIL_API_NAME, Map.class);
                        if (CollectionUtils.empty(detailApiNameMap)) {
                            Map<String, List<String>> map = Maps.newHashMap();
                            map.put(x.getValue().getKey(), x.getValue().getValue());
                            fieldDescribe.set(CASCADE_DETAIL_API_NAME, map);
                            return;
                        }

                        String describeApiName = x.getValue().getKey();
                        if (detailApiNameMap.containsKey(describeApiName)) {
                            detailApiNameMap.get(describeApiName).addAll(x.getValue().getValue());
                        } else {
                            detailApiNameMap.put(describeApiName, x.getValue().getValue());
                        }

                    });
        });

    }

    public IFieldDescribe getNameField() {
        return getFieldDescribeSilently(IObjectData.NAME)
                .orElseThrow(() -> new FieldNotExistException(getDisplayName() + "." + getFieldLabelByName(IObjectData.NAME)));
    }

    public static SelectOneFieldDescribe generateDefaultLifeStatusField() {
        SelectOneFieldDescribe selectOneFieldDescribe = new SelectOneFieldDescribe();
        selectOneFieldDescribe.setRequired(true);
        selectOneFieldDescribe.setApiName(LIFE_STATUS_API_NAME);
        selectOneFieldDescribe.setDescription(UdobjConstants.getLifeStatusLabel());
        selectOneFieldDescribe.setLabel(UdobjConstants.getLifeStatusLabel());
        selectOneFieldDescribe.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        selectOneFieldDescribe.setIndex(true);
        selectOneFieldDescribe.setCreateTime(System.currentTimeMillis());
        selectOneFieldDescribe.setSelectOptions(
                Lists.newArrayList(new SelectOption(I18N.text(I18NKey.LIFE_STATUS_INEFFECTIVE),
                                LIFE_STATUS_VALUE_INEFFECTIVE, null),
                        new SelectOption(I18N.text(I18NKey.LIFE_STATUS_UNDER_REVIEW), LIFE_STATUS_VALUE_UNDER_REVIEW, null),
                        new SelectOption(I18N.text(I18NKey.LIFE_STATUS_NORMAL), LIFE_STATUS_VALUE_NORMAL, null),
                        new SelectOption(I18N.text(I18NKey.LIFE_STATUS_IN_CHANGE), LIFE_STATUS_VALUE_IN_CHANGE, null),
                        new SelectOption(I18N.text(I18NKey.LIFE_STATUS_INVALID), LIFE_STATUS_VALUE_INVALID, null)));
        selectOneFieldDescribe.setDefaultValue(LIFE_STATUS_VALUE_NORMAL);
        return selectOneFieldDescribe;
    }

    public static TextFieldDescribe generateLifeStatusBeforeInvalidField() {
        TextFieldDescribe textFieldDescribe = new TextFieldDescribe();
        textFieldDescribe.setRequired(true);
        textFieldDescribe.setApiName(LIFE_STATUS_BEFORE_INVALID_API_NAME);
        String lifeStatusBeforeInvalidLabel = getLifeStatusBeforeInvalidLabel();
        textFieldDescribe.setDescription(lifeStatusBeforeInvalidLabel);
        textFieldDescribe.setLabel(lifeStatusBeforeInvalidLabel);
        textFieldDescribe.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        textFieldDescribe.setIndex(false);
        textFieldDescribe.setCreateTime(System.currentTimeMillis());
        textFieldDescribe.setRequired(false);
        return textFieldDescribe;
    }

    public static EmployeeFieldDescribe generateLockUserField() {
        EmployeeFieldDescribe employeeFieldDescribe = new EmployeeFieldDescribe();
        employeeFieldDescribe.setRequired(false);
        employeeFieldDescribe.setApiName(LOCK_USER_API_NAME);
        employeeFieldDescribe.setDescription(UdobjConstants.getLockUserLabel());
        employeeFieldDescribe.setLabel(UdobjConstants.getLockUserLabel());
        employeeFieldDescribe.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        employeeFieldDescribe.setIndex(false);
        employeeFieldDescribe.setIsSingle(true);
        employeeFieldDescribe.setCreateTime(System.currentTimeMillis());
        return employeeFieldDescribe;
    }

    public static SelectOneFieldDescribe generateLockStatusField() {
        SelectOneFieldDescribe selectOneFieldDescribe = new SelectOneFieldDescribe();
        selectOneFieldDescribe.setRequired(false);
        selectOneFieldDescribe.setApiName(LOCK_STATUS_API_NAME);
        selectOneFieldDescribe.setDescription(UdobjConstants.getLockStatusLabel());
        selectOneFieldDescribe.setLabel(UdobjConstants.getLockStatusLabel());
        selectOneFieldDescribe.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        selectOneFieldDescribe.setIndex(true);
        selectOneFieldDescribe.setCreateTime(System.currentTimeMillis());
        selectOneFieldDescribe.setSelectOptions(
                Lists.newArrayList(new SelectOption(I18N.text(I18NKey.LOCK_STATUS_UNLOCK), LOCK_STATUS_VALUE_UNLOCK, null),
                        new SelectOption
                                (I18N.text(I18NKey.LOCK_STATUS_LOCKED), LOCK_STATUS_VALUE_LOCK, null)));
        selectOneFieldDescribe.setDefaultValue(LOCK_STATUS_VALUE_UNLOCK);
        return selectOneFieldDescribe;
    }

    public static LockRuleFieldDescribe generateLockRuleField() {
        LockRuleFieldDescribe lockRuleFieldDescribe = new LockRuleFieldDescribe();
        lockRuleFieldDescribe.setRequired(false);
        lockRuleFieldDescribe.setApiName(LOCK_RULE_API_NAME);
        lockRuleFieldDescribe.setDescription(UdobjConstants.getLockRuleLabel());
        lockRuleFieldDescribe.setLabel(UdobjConstants.getLockRuleLabel());
        lockRuleFieldDescribe.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        lockRuleFieldDescribe.setIndex(false);
        lockRuleFieldDescribe.setCreateTime(System.currentTimeMillis());
        lockRuleFieldDescribe.setDefaultValue(LOCK_RULE_DEFAULT_API_NAME);
        LockRuleEachDetail lockRuleEachDetail = new LockRuleEachDetail();
        lockRuleEachDetail.setApiName("default_lock_rule");
        lockRuleEachDetail.setLabel(I18N.text(I18NKey.DEFAULT_LOCK_RULE));
        lockRuleEachDetail.setDescription(I18N.text(I18NKey.DEFAULT_LOCK_RULE));
        lockRuleEachDetail.setIsActive(true);
        lockRuleEachDetail.setCreateTime(new Date(System.currentTimeMillis()));
        lockRuleEachDetail.setLastModifiedTime(new Date(System.currentTimeMillis()));
        lockRuleEachDetail.setCreatedBy("system");
        lockRuleEachDetail.setLastModifiedBy("system");
        lockRuleEachDetail.setLockActions(
                Lists.newArrayList(
                        new LockAction(ObjectAction.INVALID.getActionCode()),
                        new LockAction(ObjectAction.UPDATE.getActionCode()),
                        new LockAction(ObjectAction.CHANGE_OWNER.getActionCode()),
                        new LockAction(ObjectAction.ADD_TEAM_MEMBER.getActionCode()),
                        new LockAction(ObjectAction.EDIT_TEAM_MEMBER.getActionCode()),
                        new LockAction(ObjectAction.DELETE_TEAM_MEMBER.getActionCode())
                ));
        lockRuleEachDetail.setUsersExceptional(
                Lists.newArrayList(
                        new UserExceptional(DefObjConstants.SUPER_PRIVILEGE_USER_ID)
                )
        );
        lockRuleFieldDescribe.setLockRuleEachDetailList(Lists.newArrayList(lockRuleEachDetail));
        return lockRuleFieldDescribe;
    }

    /**
     * 校验布局中的单选字段级联关系
     *
     * @param layout 布局
     * @throws MetadataServiceException 检查出有级联关系极联关系的父级单选字段被隐藏抛出提示信息
     */
    public void checkSelectOneFiledWithLayout(ILayout layout) throws MetaDataBusinessException {
        List<IFieldDescribe> fieldDescribes = objectDescribe.getFieldDescribes();
        List<SelectOneFieldDescribe> selectOneList =
                fieldDescribes.stream().filter(x -> IFieldType.SELECT_ONE.equals(x.getType()) &&
                                "custom".equals(x.getDefineType())).
                        map(iFieldDescribe -> (SelectOneFieldDescribe) iFieldDescribe).
                        collect(Collectors.toList());
        LayoutExt layoutExt = LayoutExt.of(layout);
        List<SelectOneFieldDescribe> showList = Lists.newArrayList();
        List<SelectOneFieldDescribe> hiddenList = Lists.newArrayList();
        selectOneList.forEach(selectOne -> {
            Optional<IFormField> optional = layoutExt.getField(selectOne.getApiName());
            if (optional.isPresent()) {
                showList.add(selectOne);
            } else {
                hiddenList.add(selectOne);
            }
        });
        if (hiddenList.size() > 0) {
            List<SelectOneFieldDescribe> shouldNotHiddenList =
                    hiddenList.stream().filter(hidden -> showList.stream().map(show -> show.getCascadeParentApiName())
                            .collect(Collectors.toList()).contains(hidden.getApiName())
                    ).collect(Collectors.toList());
            if (!CollectionUtils.empty(shouldNotHiddenList)) {
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < shouldNotHiddenList.size(); i++) {
                    buffer.append(shouldNotHiddenList.get(i).getLabel());
                    if (i < shouldNotHiddenList.size() - 1) {
                        buffer.append("、");
                    }
                }
                String message = I18N.text(I18NKey.SELECT_ONE_HIDE_FORBID, buffer.toString());
                throw new MetaDataBusinessException(message, AppFrameworkErrorCode.VALIDATION_ERROR);
            }

        }
    }

    public void checkFieldUnique(List<IFieldDescribe> fieldDescribes) {
        //库里是可重复的
        Set<String> repeatableFieldDescribe = objectDescribe.getFieldDescribes().stream()
                .filter(x -> !x.isUnique())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        //当前为不可重复的
        fieldDescribes.stream()
                .filter(IFieldDescribe::isUnique)
                .map(IFieldDescribe::getApiName)
                .forEach(x -> {
                    if (repeatableFieldDescribe.contains(x)) {
                        throw new MetaDataBusinessException(I18N.text(I18NKey.FIELD_UNIQUE_VALID, x));
                    }
                });
    }

    public boolean isFieldActive(String filedAPIName) {
        if (!containsField(filedAPIName)) {
            return false;
        }
        return objectDescribe.getFieldDescribe(filedAPIName).isActive();
    }

    public IRecordTypeOption getDefaultRecordType() {
        Optional<RecordTypeFieldDescribe> recordTypeField = getRecordTypeField();
        return recordTypeField
                .map(fieldDescribe -> fieldDescribe.getRecordTypeOption(MultiRecordType.RECORD_TYPE_DEFAULT))
                .orElse(null);

    }

    public void setUpdateDescribeDefaultValue(User user) {
        objectDescribe.setTenantId(user.getTenantId());
        objectDescribe.setLastModifiedBy(user.getUserId());

        // 多语言之后，不校验字段label重复
//        checkFieldLabelDuplicate();
        setFieldDescribeCreateTime();

        if (IGNORE_FILL_FIELDS.contains(getApiName())) {
            return;
        }

        fillSignInInfoField();
        if (!containsField(UdobjConstants.RELEVANT_TEAM_API_NAME)) {
            IFieldDescribe field = ObjectDescribeExt.generateDefaultTeamMemberFieldDescribe();
            addFieldDescribe(field);
        }

        if (!containsField(UdobjConstants.LIFE_STATUS_BEFORE_INVALID_API_NAME)) {
            IFieldDescribe field = ObjectDescribeExt.generateLifeStatusBeforeInvalidField();
            addFieldDescribe(field);
        }

        if (!containsField(UdobjConstants.LOCK_USER_API_NAME)) {
            IFieldDescribe field = ObjectDescribeExt.generateLockUserField();
            addFieldDescribe(field);
        }

        if (!containsField(UdobjConstants.LOCK_RULE_API_NAME)) {
            IFieldDescribe field = ObjectDescribeExt.generateLockRuleField();
            addFieldDescribe(field);
        }

        if (!containsField(ObjectLockStatus.LOCK_STATUS_API_NAME)) {
            IFieldDescribe field = ObjectDescribeExt.generateLockStatusField();
            addFieldDescribe(field);
        }
    }

    public boolean isPRMEnabled() {
        return containsField(PARTNER_ID_API_NAME);
    }

    public static List<String> getObjectOrderList() {
        return AppFrameworkConfig.getObjectOrder();
    }

    /**
     * 将对象新的describe和老的describe的计算字段做diff
     *
     * @param oldDescribe
     * @return 新增和变更的计算字段列表
     */
    public List<Formula> diffFormulaField(IObjectDescribe oldDescribe) {
        Map<String, FormulaExt> formulaMap =
                getFormulaFields().stream().map(x -> FormulaExt.of((Formula) x)).collect(Collectors.toMap(x -> x.getApiName(), x -> x));
        if (CollectionUtils.empty(formulaMap)) {
            return Lists.newArrayList();
        }

        Map<String, FormulaExt> oFormulaMap =
                ObjectDescribeExt.of(oldDescribe).getFormulaFields().stream().map(x -> FormulaExt.of((Formula) x))
                        .collect(Collectors.toMap(x -> x.getApiName(), x -> x));

        MapDifference<String, FormulaExt> difference = Maps.difference(formulaMap, oFormulaMap);

        List<Formula> changedFormulaList = Lists.newArrayList();
        difference.entriesOnlyOnLeft().forEach((k, v) -> changedFormulaList.add(v.getFormula()));
        difference.entriesDiffering().forEach((k, v) -> changedFormulaList.add(v.leftValue().getFormula()));

        return changedFormulaList;
    }

    /**
     * 将对象新的describe和老的describe的默认值字段做diff
     *
     * @param oldDescribe
     * @return 新增和变更的默认值字段列表
     */
    public List<IFieldDescribe> diffDefaultValueField(IObjectDescribe oldDescribe) {
        Map<String, IFieldDescribe> newDefaultMap = getDefaultValueFields().stream()
                .collect(Collectors.toMap(x -> x.getApiName(), x -> x));
        if (CollectionUtils.empty(newDefaultMap)) {
            return Lists.newArrayList();
        }

        Map<String, IFieldDescribe> oldDefaultMap = ObjectDescribeExt.of(oldDescribe).getDefaultValueFields().stream()
                .collect(Collectors.toMap(x -> x.getApiName(), x -> x));

        List<IFieldDescribe> changedDefaultList = Lists.newArrayList();
        if (CollectionUtils.empty(oldDefaultMap)) {
            changedDefaultList.addAll(newDefaultMap.values());
            return changedDefaultList;
        }
        newDefaultMap.forEach((apiName, value) -> {
            IFieldDescribe oblFieldDescribe = oldDefaultMap.get(apiName);
            if (Objects.nonNull(oblFieldDescribe)) {
                if (!Objects.equals(value.getDefaultValue(), oblFieldDescribe.getDefaultValue())) {
                    changedDefaultList.add(value);
                }
            } else {
                changedDefaultList.add(value);
            }
        });
        return changedDefaultList;
    }

    public List<IFieldDescribe> getDefaultValueFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).hasFormulaDefaultValue())
                .filter(x -> x.isActive())
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getNeedCalculateDefaultValueFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).hasFormulaDefaultValue() || FieldDescribeExt.of(x).isMultiCurrencyCalculateFields())
                .filter(IFieldDescribe::isActive)
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getNeedCalculateConstantDefaultValue() {
        return stream()
                .filter(x -> !FieldDescribeExt.of(x).hasFormulaDefaultValue())
                .filter(x -> x.isActive())
                .filter(a -> !isSystemField(a.getApiName()))
                .filter(a -> !IFieldType.AUTO_NUMBER.equals(a.getType()))
                .filter(it -> FieldDescribeExt.of(it).defaultValueNotEmpty())
                .collect(Collectors.toList());
    }

    public List<AutoNumber> diffAutoNumberFields(IObjectDescribe describeInDb) {
        Map<String, AutoNumberExt> autoNumberExtMap = getAutoNumberFields().stream()
                .map(AutoNumberExt::of).collect(Collectors.toMap(AutoNumberExt::getApiName, x -> x));
        if (CollectionUtils.empty(autoNumberExtMap)) {
            return Lists.newArrayList();
        }
        Map<String, AutoNumberExt> oldAutoNumberExtMap = ObjectDescribeExt.of(describeInDb).getAutoNumberFields().stream()
                .map(AutoNumberExt::of).collect(Collectors.toMap(AutoNumberExt::getApiName, x -> x));
        MapDifference<String, AutoNumberExt> difference = Maps.difference(autoNumberExtMap, oldAutoNumberExtMap);

        List<AutoNumber> changeAutoNumberList = Lists.newArrayList();
        difference.entriesOnlyOnLeft().forEach((k, v) -> changeAutoNumberList.add(v.getAutoNumber()));
        difference.entriesDiffering().forEach((k, v) -> changeAutoNumberList.add(v.leftValue().getAutoNumber()));

        return changeAutoNumberList;
    }

    public List<IFieldDescribe> getIndexedFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isIndex())
                .filter(IFieldDescribe::isActive)
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getFilterableFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isIndex() || x.isEncrypted())
                .filter(IFieldDescribe::isActive)
                .collect(Collectors.toList());
    }

    public void removeAbstractField() {
        List<String> apiNameList = stream().filter(x -> FieldDescribeExt.of(x).isAbstractField())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        removeFieldDescribeList(apiNameList);
    }

    public IObjectDescribe retainFields(Collection<String> fieldNames) {
        IObjectDescribe copy = objectDescribe.copy();
        Map fieldMap = copy.get(IObjectDescribe.FIELDS, Map.class);
        fieldMap.keySet().removeIf(it -> !fieldNames.contains(it));
        return copy;
    }

    public Optional<IFieldDescribe> getFieldIsUsedByStage() {
        return getFieldDescribes().stream().filter(x -> FieldDescribeExt.of(x).isUsedByStage()).findFirst();
    }

    public boolean isCustomObject() {
        return Objects.equals(this.objectDescribe.getDefineType(), DEFINE_TYPE_CUSTOM);
    }

    public void descriptionNullToEmpty() {
        if (Objects.nonNull(objectDescribe) && Objects.isNull(getDescription())) {
            setDescription("");
        }
        getFieldDescribes().forEach(x -> FieldDescribeExt.of(x).descriptionNullToEmpty());
    }

    public void addExtProperty() {
        addExtPropertyWithExt(null);
    }

    public void addExtPropertyWithExt(Map<String, IObjectDescribe> describeExtMap) {
        int maxNum = FieldManyMaxConfig.getEmployeeManyMaxLimit(getTenantId(), getApiName());
        int objectRefManyNum = FieldManyMaxConfig.getObjectReferenceManyMaxLimit(getTenantId(), getApiName());
        this.getFieldDescribes().forEach(x -> {
            int num = -1;
            if (x.getType().equals(EMPLOYEE_MANY) || x.getType().equals(DEPARTMENT_MANY)) {
                num = maxNum;
            } else if (x.getType().equals(OBJECT_REFERENCE_MANY)) {
                num = objectRefManyNum;
            }
            if (num != -1) {
                if (describeExtMap == null) {
                    FieldDescribeExt.of(x).addMaxNum(num);
                } else {
                    FieldDescribeExt.of(copyField4Ext(describeExtMap, x)).addMaxNum(num);
                }
            }
        });
    }

    public static boolean isCustomObject(String apiName) {
        return Objects.nonNull(apiName) && apiName.endsWith("__c");
    }

    public Map<String, Object> getSimpleFieldInfos(String fieldName) {
        Map<String, Object> resultMap = Maps.newHashMap();
        IFieldDescribe fieldDescribe = getFieldDescribe(fieldName);
        resultMap.put(IFieldDescribe.LABEL, fieldDescribe.getLabel());
        resultMap.put(IFieldDescribe.API_NAME, fieldDescribe.getApiName());
        resultMap.put(IFieldDescribe.IS_ACTIVE, fieldDescribe.isActive());
        return resultMap;
    }

    /**
     * 替换邮箱字段的 pattern
     */
    public void handelEmailFields() {
        handleEmailFieldsWithExt(null);
    }

    public void handleEmailFieldsWithExt(Map<String, IObjectDescribe> describeExtMap) {
        stream().filter(x -> IFieldType.EMAIL.equals(x.getType()))
                .map(x -> {
                    if (describeExtMap == null) {
                        return x;
                    } else {
                        return copyField4Ext(describeExtMap, x);
                    }
                })
                .forEach(x -> FieldDescribeExt.of(x).setPattern(EMAIl_PATTERN));
    }

    public void hideButton() {
        set("hide_button", true);
    }

    public boolean isHideButton() {
        return TRUE.equals(get("hide_button"));
    }

    public void fillDefaultShowDetailButton() {
        getMasterDetailFieldDescribe().ifPresent(x -> FieldDescribeExt.of(x).fillDefaultShowDetailButton());
    }

    public List<String> findCascadeParentFields(String fieldApiName) {
        Map<String, List<String>> fieldCascadeMap = getFieldCascadeMap();
        List<String> cascadeParentFields = Lists.newArrayList();
        findCascadeParentField(cascadeParentFields, fieldApiName, fieldCascadeMap);
        return cascadeParentFields;
    }

    private void findCascadeParentField(List<String> cascadeParentFields, String fieldApiName, Map<String, List<String>> fieldCascadeMap) {
        if (CollectionUtils.empty(fieldCascadeMap) || !fieldCascadeMap.containsKey(fieldApiName)) {
            return;
        }
        List<String> cascadeParentApiNames = fieldCascadeMap.get(fieldApiName);
        cascadeParentApiNames.forEach(cascadeParentApiName -> {
            if (!fieldApiName.equals(cascadeParentApiName) && !cascadeParentFields.contains(cascadeParentApiName)) {
                findCascadeParentField(cascadeParentFields, cascadeParentApiName, fieldCascadeMap);
                cascadeParentFields.add(cascadeParentApiName);
            }
        });

    }

    public List<String> findCascadeDetailFields(String fieldApiName) {
        Map<String, List<String>> fieldCascadeMap = getFieldCascadeMap();
        List<String> cascadeDetailFields = Lists.newArrayList();
        findCascadeDetailField(cascadeDetailFields, fieldApiName, fieldCascadeMap);
        return cascadeDetailFields;
    }

    private Map<String, List<String>> getFieldCascadeMap() {
        Map<String, List<String>> fieldCascadeMap = Maps.newHashMap();
        getActiveFieldDescribes().forEach(x -> {
            List<String> cascadeParentApiNames = FieldDescribeExt.of(x).getCascadeParentApiNames();
            if (CollectionUtils.notEmpty(cascadeParentApiNames)) {
                fieldCascadeMap.put(x.getApiName(), cascadeParentApiNames);
            }
        });
        return fieldCascadeMap;
    }

    private void findCascadeDetailField(List<String> cascadeDetailFields, String fieldApiName, Map<String, List<String>> fieldCascadeMap) {
        if (CollectionUtils.empty(fieldCascadeMap)) {
            return;
        }
        fieldCascadeMap.forEach((k, v) -> {
            if (v.contains(fieldApiName) && !cascadeDetailFields.contains(k)) {
                cascadeDetailFields.add(k);
                findCascadeDetailField(cascadeDetailFields, k, fieldCascadeMap);
            }
        });
    }

    public List<WhatListData> getWhatListDatas() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isWhatListData())
                .filter(IFieldDescribe::isActive)
                .map(it -> (WhatListData) it)
                .collect(Collectors.toList());
    }

    public List<WhatList> getWhatListFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isWhatListField())
                .filter(IFieldDescribe::isActive)
                .map(it -> (WhatList) it)
                .collect(Collectors.toList());
    }

    public static boolean allCustomObject(List<IObjectDescribe> relateds) {
        return CollectionUtils.nullToEmpty(relateds).stream().allMatch(it -> ObjectDescribeExt.of(it).isCustomObject());
    }

    public Optional<AutoNumber> getFunctionAutoNumber() {
        return stream()
                .filter(it -> BooleanUtils.isTrue(it.isActive()))
                .filter(it -> FieldDescribeExt.of(it).isAutoNumber())
                .map(it -> (AutoNumber) it)
                .filter(it -> AutoNumberExt.of(it).isFunctionAutoNumber())
                .findFirst();
    }

    public List<IFieldDescribe> findGeoSearchField() {
        return stream()
                .filter(a -> FieldDescribeExt.of(a).isSupportGeoSearch())
                .filter(IFieldDescribe::isActive)
                .collect(Collectors.toList());
    }

    public void validateGeoLocationField(List<IFieldDescribe> changedFields) {
        List<IFieldDescribe> existFields = objectDescribe.getFieldDescribes();
        List<IFieldDescribe> changedFieldList = CollectionUtils.nullToEmpty(changedFields);
        //去掉已存在的字段中旧的字段内容
        existFields.removeIf(a -> changedFieldList.stream().anyMatch(b -> Objects.equals(a.getApiName(), b.getApiName())));
        existFields.addAll(changedFieldList);
        long count = existFields.stream().filter(a -> FieldDescribeExt.of(a).isSupportGeoSearch()).count();
        if (count > 1) {
            throw new ValidateException(I18N.text(I18NKey.GEO_LOCATION_FIELD_COUNT));
        }
    }

    public void validateRichTextFieldInDescribe(List<IFieldDescribe> changedFields) {
        List<IFieldDescribe> existFields = objectDescribe.getFieldDescribes();
        List<IFieldDescribe> changedFieldList = CollectionUtils.nullToEmpty(changedFields);
        //去掉已存在的字段中旧的字段内容
        existFields.removeIf(a -> changedFieldList.stream().anyMatch(b -> Objects.equals(a.getApiName(), b.getApiName())));
        existFields.addAll(changedFieldList);

        // HTML富文本校验
        long htmlRichTextCount = existFields.stream().filter(RichTextExt::isProcessableRichText).count();
        String describeApiName = getApiName();
        int htmlRichTextLimit = AppFrameworkConfig.getHtmlRichTextFieldMaxLimitByObject(describeApiName, objectDescribe.getTenantId());
        if (htmlRichTextCount > htmlRichTextLimit) {
            throw new ValidateException(I18NExt.text(I18NKey.VALIDATE_RICH_TEXT_FIELD_COUNT, htmlRichTextLimit));
        }

        // 协同富文本校验
        long richTextCount = existFields.stream().filter(RichTextExt::isProcessableCooperativeRichText).count();
        int richTextLimit = AppFrameworkConfig.getRichTextFieldMaxLimitByObject(describeApiName, objectDescribe.getTenantId());
        if (richTextCount > richTextLimit) {
            throw new ValidateException(I18NExt.text(I18NKey.VALIDATE_COOPERATIVE_RICH_TEXT_FIELD_COUNT, richTextLimit));
        }
    }

    //检查单选字段是否存在成环依赖
    public void checkSelectCascadeCircle() {
        Map<String, IFieldDescribe> fieldMap = getFieldDescribeMap();
        Set<String> checked = Sets.newLinkedHashSet();
        fieldMap.values().stream()
                .filter(x -> FieldDescribeExt.of(x).isSelectOne())
                .map(x -> (SelectOne) x)
                .forEach(x -> {
                    if (checked.contains(x.getApiName())) {
                        return;
                    }
                    if (Strings.isNullOrEmpty(x.getCascadeParentApiName())) {
                        checked.add(x.getApiName());
                        return;
                    }
                    Set<String> path = Sets.newLinkedHashSet();
                    path.add(x.getApiName());
                    while (!Strings.isNullOrEmpty(x.getCascadeParentApiName())
                            && fieldMap.containsKey(x.getCascadeParentApiName())
                            && FieldDescribeExt.of(fieldMap.get(x.getCascadeParentApiName())).isSelectOne()) {
                        if (!path.add(x.getCascadeParentApiName())) {
                            throw new ValidateException(I18N.text(I18NKey.FIELD_CASCADE_CIRCLE, getFieldLabels(path)));
                        }
                        x = (SelectOne) fieldMap.get(x.getCascadeParentApiName());
                    }
                    checked.addAll(path);
                });
    }

    private String getFieldLabels(Set<String> fieldNames) {
        List<String> fieldLabels = getFieldByApiNames(Lists.newArrayList(fieldNames)).stream()
                .map(x -> x.getLabel()).collect(Collectors.toList());
        return StringUtils.join(fieldLabels, ",");
    }

    public List<Image> getImageFieldList() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isImageField())
                .map(x -> (Image) x)
                .collect(Collectors.toList());
    }

    /**
     * 校验前提：存在级联关系字段才进行校验，否则不校验
     * 1. 多选或单选存在级联字段，去校验父属性是否包含当前子选项
     * 2. 单选或业务类型包含子选项，校验子选项是否存在
     */
    public void checkFieldCascade() {
        // TODO: 2022/8/2 关联字段有一个使用了选项集,不需要校验依赖关系
        getFieldDescribes().stream()
                .filter(x -> FieldDescribeExt.of(x).isSelectMany() || FieldDescribeExt.of(x).isSelectOne())
                .map(x -> (SelectOne) x)
                .forEach(x -> {
                    Optional<IFieldDescribe> parentField = getActiveFieldDescribeSilently(x.getCascadeParentApiName());
                    if (parentField.isPresent()) {
                        boolean isExistCascadeRelation = false;
                        if (FieldDescribeExt.of(parentField.get()).isSelectOne()) {
                            SelectOne selectOneParentField = (SelectOne) parentField.get();
                            isExistCascadeRelation = selectOneParentField.getSelectOptions().stream()
                                    .map(ISelectOption::getChildOptions)
                                    .anyMatch(y -> {
                                        if (CollectionUtils.empty(y)) {
                                            return false;
                                        }
                                        return y.stream().anyMatch(z -> z.containsKey(x.getApiName()));
                                    });
                        }
                        if (FieldDescribeExt.of(parentField.get()).isRecordType()) {
                            RecordType recordTypeParentField = (RecordType) parentField.get();
                            isExistCascadeRelation = recordTypeParentField.getRecordTypeOptions().stream()
                                    .map(IRecordTypeOption::getChildOptions)
                                    .anyMatch(y -> {
                                        if (CollectionUtils.empty(y)) {
                                            return false;
                                        }
                                        return y.stream().anyMatch(z -> z.containsKey(x.getApiName()));
                                    });
                        }
                        if (!isExistCascadeRelation) {
                            log.warn("parent field does not contain child field,parent_api_name={},child_api_name={}", x.getCascadeParentApiName(), x.getApiName());
                            //throw new MetaDataBusinessException(I18N.text(I18NKey.CASCADE_PARENT_FIELD, x.getApiName()));
                        }
                    }
                });
        getFieldDescribes().stream()
                .filter(x -> FieldDescribeExt.of(x).isSelectOne())
                .map(x -> (SelectOne) x)
                .forEach(x -> x.getSelectOptions().stream()
                        .map(ISelectOption::getChildOptions).filter(Objects::nonNull)
                        .forEach(y -> y.stream().filter(Objects::nonNull)
                                .forEach(z -> z.keySet().forEach(k -> {
                                    Optional<IFieldDescribe> childField = getFieldDescribeSilently(k);
                                    if (childField.isPresent()) {
                                        SelectOne one = (SelectOne) childField.get();
                                        if (!StringUtils.equals(x.getApiName(), one.getCascadeParentApiName())) {
                                            log.warn("child field does not match parent field,parent_api_name={},child_api_name={}", x.getApiName(), one.getApiName());
                                        }
                                    } else {
                                        log.warn("child field not exist,child_api_name={}", k);
                                    }
                                }))));
        getFieldDescribes().stream()
                .filter(x -> FieldDescribeExt.of(x).isRecordType())
                .map(x -> (RecordType) x)
                .forEach(x -> x.getRecordTypeOptions().stream()
                        .map(IRecordTypeOption::getChildOptions).filter(Objects::nonNull)
                        .forEach(y -> y.stream().filter(Objects::nonNull)
                                .forEach(z -> z.keySet().forEach(k -> {
                                    Optional<IFieldDescribe> childField = getFieldDescribeSilently(k);
                                    if (childField.isPresent()) {
                                        SelectOne one = (SelectOne) childField.get();
                                        if (!StringUtils.equals(x.getApiName(), one.getCascadeParentApiName())) {
                                            log.warn("child field does not match parent field,parent_api_name={},child_api_name={}", x.getApiName(), one.getApiName());
                                        }
                                    } else {
                                        log.warn("child field not exist,child_api_name={}", k);
                                    }
                                }))));
        //校验国家省市区
        getFieldDescribes().stream()
                .filter(x -> FieldDescribeExt.of(x).isProvinceField() || FieldDescribeExt.of(x).isCityField() || FieldDescribeExt.of(x).isDistrictField())
                .map(x -> (SelectOne) x)
                .forEach(x -> {
                    Optional<IFieldDescribe> parentField = getActiveFieldDescribeSilently(x.getCascadeParentApiName());
                    if (!parentField.isPresent()) {
                        log.warn("parent field not exist,field_api_name={}", x.getApiName());
                    }
                });
    }

    public void formatLabel(List<IFieldDescribe> fieldDescribes) {
        objectDescribe.setDisplayName(ObjectDataExt.formatValueInImport(objectDescribe.getDisplayName()));
        fieldDescribes.forEach(x -> x.setLabel(ObjectDataExt.formatValueInImport(x.getLabel())));
        fieldDescribes.stream()
                .filter(x -> FieldDescribeExt.of(x).isSelectOne() || FieldDescribeExt.of(x).isSelectMany())
                .map(x -> (SelectOne) x)
                .forEach(x -> x.getSelectOptions().forEach(y -> y.setLabel(ObjectDataExt.formatValueInImport(y.getLabel()))));
        fieldDescribes.stream()
                .filter(x -> FieldDescribeExt.of(x).isTrueOrFalseField())
                .map(x -> (TrueOrFalse) x)
                .forEach(x -> x.getSelectOptions().forEach(y -> y.setLabel(ObjectDataExt.formatValueInImport(y.getLabel()))));
        fieldDescribes.stream()
                .filter(x -> FieldDescribeExt.of(x).isRecordType())
                .map(x -> (RecordType) x)
                .forEach(x -> x.getRecordTypeOptions().forEach(y -> y.setLabel(ObjectDataExt.formatValueInImport(y.getLabel()))));
    }

    public List<IFieldDescribe> getDimensionFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isDimension())
                .collect(Collectors.toList());
    }

    public List<String> getDimensionFieldApiNames() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).isDimension())
                .map(x -> x.getApiName())
                .collect(Collectors.toList());
    }

    public void changeIsIndexWithOuter(User user) {
        if (!user.isOutUser()) {
            return;
        }
        stream()
                .filter(it -> FieldDescribeExt.of(it).isNotIndexFieldForOutUser())
                .forEach(it -> it.setIndex(false));
    }

    public Optional<IFieldDescribe> getExchangeRateField() {
        return getActiveFieldDescribeSilently(FieldDescribeExt.EXCHANGE_RATE_FIELD);
    }

    public Optional<IFieldDescribe> getCurrencyField() {
        return getActiveFieldDescribeSilently(FieldDescribeExt.CURRENCY_FIELD);
    }

    public boolean containsMultiCurrencyField() {
        return containsField(FieldDescribeExt.CURRENCY_FIELD);
    }

    public List<IFieldDescribe> getMultiCurrencyCalculateFields() {
        return stream().filter(x -> FieldDescribeExt.of(x).isMultiCurrencyCalculateFields()).collect(Collectors.toList());
    }

    public boolean isOpenOrganization() {
        return filterOne(it -> ObjectDataExt.DATA_OWN_ORGANIZATION.equals(it.getApiName())).isPresent();
    }

    public boolean isExistOutDataOwnOrganization() {
        return filterOne(it -> ObjectDataExt.OUT_DATA_OWN_ORGANIZATION.equals(it.getApiName())).isPresent();
    }

    public boolean isExistOutDataOwnDepartment() {
        return filterOne(it -> ObjectDataExt.OUT_DATA_OWN_DEPARTMENT.equals(it.getApiName())).isPresent();
    }

    public boolean isExistBigText() {
        return filterOne(it -> BIG_TEXT.equals(it.getType())).isPresent();
    }


    public void validateObjectReferenceField(List<IFieldDescribe> fieldDescribes) {
        Set<String> targetRelatedListName = Sets.newHashSet();
        Map<String, Set<String>> fieldMap = Maps.newHashMap();
        Set<String> repeatApiNames = Sets.newHashSet();
        List<IFieldDescribe> fieldDescribeList = getFieldDescribes();
        if (CollectionUtils.notEmpty(fieldDescribes)) {
            fieldDescribeList.addAll(fieldDescribes);
        }
        fieldDescribeList.stream()
                .filter(x -> FieldDescribeExt.of(x).isLookupField())
                .map(x -> ObjectReferenceWrapper.of(x))
                .forEach(field -> {
                    String key = field.getTargetRelatedListName() + "_" + field.getTargetApiName();
                    if (targetRelatedListName.contains(key)
                            && !fieldMap.get(key).contains(field.getApiName())) {
                        repeatApiNames.add(field.getApiName());
                    }
                    targetRelatedListName.add(key);
                    fieldMap.compute(key, (k, v) -> {
                        Set<String> fieldApiNames = Sets.newHashSet();
                        if (CollectionUtils.notEmpty(v)) {
                            fieldApiNames.addAll(v);
                        }
                        fieldApiNames.add(field.getApiName());
                        return fieldApiNames;
                    });
                });
        fieldDescribeList.stream()
                .filter(x -> FieldDescribeExt.of(x).isMasterDetailField())
                .map(x -> (MasterDetail) x)
                .forEach(field -> {
                    String key = field.getTargetRelatedListName() + "_" + field.getTargetApiName();
                    if (targetRelatedListName.contains(key)
                            && !fieldMap.get(key).contains(field.getApiName())) {
                        repeatApiNames.add(field.getApiName());
                    }
                    targetRelatedListName.add(key);
                    fieldMap.compute(key, (k, v) -> {
                        Set<String> fieldApiNames = Sets.newHashSet();
                        if (CollectionUtils.notEmpty(v)) {
                            fieldApiNames.addAll(v);
                        }
                        fieldApiNames.add(field.getApiName());
                        return fieldApiNames;
                    });
                });
        if (CollectionUtils.notEmpty(repeatApiNames)) {
            throw new ValidateException(I18N.text(I18NKey.TARGET_RELATED_LIST_NAME_DUPLICATION, repeatApiNames.toString()));
        }
    }


    public void validateGroupField(List<IFieldDescribe> fieldDescribes, String tenantId) {
        if (!UdobjGrayConfig.isAllow("validateGroupField", tenantId)) {
            return;
        }
        // 收集所有字段
        List<IFieldDescribe> allFields = Lists.newArrayList();
        if (CollectionUtils.notEmpty(getFieldDescribes())) {
            allFields.addAll(getFieldDescribes());
        }
        if (CollectionUtils.notEmpty(fieldDescribes)) {
            allFields.addAll(fieldDescribes);
        }

        if (CollectionUtils.empty(allFields)) {
            return;
        }

        // 找出所有组件字段的字段名，并统计出现次数
        Map<String, Long> fieldNameCounts = allFields.stream()
                .filter(field -> FieldDescribeExt.of(field).isGroupField() && FieldDescribeExt.of(field).isCustomField())
                .flatMap(field -> {
                    List<String> fields = Lists.newArrayList();
                    if (StringUtils.isNotBlank(field.getApiName())) {
                        fields.add(field.getApiName());
                    }
                    List<String> innerFields = getGroupInnerFields(field);
                    if (CollectionUtils.notEmpty(innerFields)) {
                        fields.addAll(innerFields);
                    }
                    return fields.stream();
                })
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.groupingBy(
                        fieldApiName -> fieldApiName,
                        Collectors.counting()
                ));

        // 找出重复的字段名
        Set<String> duplicateFields = fieldNameCounts.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        if (!duplicateFields.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKey.GROUP_FIELD_DUPLICATED,
                    String.join(",", duplicateFields)));
        }
    }

    public List<String> getGroupInnerFields(IFieldDescribe fieldDescribe) {
        if (!(fieldDescribe instanceof GroupField)) {
            return Lists.newArrayList();
        }
        Map<String, String> groupFields = (Map) fieldDescribe.get("fields");
        if (CollectionUtils.empty(groupFields)) {
            return Lists.newArrayList();
        }
        return groupFields
                .values()
                .stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    public void fillOtherOptionInSelectOneFields() {
        getFieldDescribes().stream()
                .filter(x -> FieldDescribeExt.of(x).isSelectOne() || FieldDescribeExt.of(x).isSelectMany())
                .filter(x -> FieldDescribeExt.of(x).isCustomField() && !isInGroupField(x.getApiName()))
                .forEach(x -> FieldDescribeExt.of(x).addDefaultOtherOption());
    }

    public List<HtmlRichText> getRichTextFields() {
        return stream()
                .filter(RichTextExt::isProcessableRichText)
                .filter(IFieldDescribe::isActive)
                .map(x -> (HtmlRichText) x)
                .collect(Collectors.toList());
    }

    public List<RichText> getCooperativeRichTextFields() {
        return stream()
                .filter(RichTextExt::isProcessableCooperativeRichText)
                .filter(IFieldDescribe::isActive)
                .map(x -> (RichText) x)
                .collect(Collectors.toList());
    }

    public List<BigTextFieldDescribe> getBigTextFields() {
        return stream()
                .filter(x -> StringUtils.equals(x.getType(), BIG_TEXT))
                .filter(IFieldDescribe::isActive)
                .map(x -> (BigTextFieldDescribe) x)
                .collect(Collectors.toList());
    }

    public List<String> getRealTypeIsDate() {
        return stream()
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::realTypeIsDate)
                .map(FieldDescribeExt::getApiName)
                .collect(Collectors.toList());
    }

    public boolean isSupportDisplayName() {
        return TRUE.equals(isOpenDisplayName());
    }

    public boolean isSupportTreeViewObject() {
        return AppFrameworkConfig.supportTreeView(getTenantId(), getApiName()) && TRUE.equals(isSupportTreeView());
    }

    public Optional<IFieldDescribe> getOwnerField() {
        return getFieldDescribeSilently(ObjectDataExt.OWNER);
    }

    public Optional<IObjectReferenceField> getSupportRelationOuterOwnerField() {
        return getSupportRelationOuterOwnerFieldStream().findFirst();
    }

    public List<IObjectReferenceField> getSupportRelationOuterOwnerFields() {
        return getSupportRelationOuterOwnerFieldStream()
                .collect(Collectors.toList());
    }

    public List<IObjectReferenceField> getSupportRelationFields() {
        return stream()
                .filter(IFieldDescribe::isActive)
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isObjectReferenceField)
                .filter(FieldDescribeExt::isSupportRelation)
                .map(FieldDescribeExt::<IObjectReferenceField>getFieldDescribe)
                .collect(Collectors.toList());
    }

    private Stream<IObjectReferenceField> getSupportRelationOuterOwnerFieldStream() {
        return stream()
                .filter(IFieldDescribe::isActive)
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isObjectReferenceField)
                .filter(FieldDescribeExt::isSupportRelationOuterOwner)
                .map(FieldDescribeExt::<IObjectReferenceField>getFieldDescribe)
                .sorted(Comparator.comparingInt(this::sortedSupportRelationOuterDataPrivilegeField));
    }

    /**
     * 支持『关联外部数据权限』的字段排序
     * <p>
     * 关联『合作伙伴』在前，关联『客户』在后
     *
     * @param fieldDescribe 字段描述
     * @return
     */
    private int sortedSupportRelationOuterDataPrivilegeField(IFieldDescribe fieldDescribe) {
        String targetApiName = fieldDescribe.get(IObjectReference.TARGET_API_NAME, String.class);
        if (Utils.PARTNER_API_NAME.equals(targetApiName)) {
            return 0;
        }
        if (Utils.ACCOUNT_API_NAME.equals(targetApiName)) {
            return 1;
        }
        return Integer.MAX_VALUE;
    }

    public List<IFieldDescribe> getFieldByTypes(Set<String> fieldTypes) {
        return stream()
                .filter(x -> CollectionUtils.nullToEmpty(fieldTypes).contains(x.getType()))
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getFieldByRenderTypes(Set<String> renderTypes) {
        if (CollectionUtils.empty(renderTypes)) {
            return Lists.newArrayList();
        }
        return stream()
                .filter(x -> renderTypes.contains(FieldDescribeExt.of(x).getTypeOrReturnType()))
                .collect(Collectors.toList());
    }

    public void handleNumberStepValue(List<IFieldDescribe> allList) {
        CollectionUtils.nullToEmpty(allList).stream()
                .filter(a -> Objects.equals(a.getType(), IFieldType.NUMBER))
                .filter(a -> Objects.nonNull(a.get("step_value")))
                .forEach(a -> {
                    Object rawValue = a.get("step_value");
                    String value = String.valueOf(rawValue);
                    if (!NumberUtils.isDigits(value)) {
                        a.set("step_value", null);
                    }
                    if (new BigDecimal(value).compareTo(new BigDecimal(AppFrameworkConfig.getNumberStepMaxValue())) > 0) {
                        throw new ValidateException(I18NExt.text(I18NKey.NUMBER_FIELD_STEP_MORE_LIMIT, a.getApiName(), AppFrameworkConfig.getNumberStepMaxValue()));
                    }
                });
    }

    public void handleNumberStepValue() {
        List<IFieldDescribe> list = getFieldDescribesSilently();
        handleNumberStepValue(list);
    }

    /**
     * 纠正描述信息
     */
    public void correctDescribe() {
        List<Area> areaGroupFields = getAreaGroupFields();
        if (CollectionUtils.empty(areaGroupFields)) {
            return;
        }
        areaGroupFields.stream()
                .map(FieldDescribeExt::of)
                .forEach(fieldDescribeExt -> fieldDescribeExt.correctAreaDetailAddress(this));
    }

    public Optional<IFieldDescribe> getTreeViewObjectParentField() {
        return getActiveLookupFieldDescribes().stream()
                .filter(IFieldDescribe::isActive)
                .filter(field -> FieldDescribeExt.of(field).isTreeViewSelfAssociatedField())
                .findFirst();
    }

    public List<String> getTreeViewObjectFieldsToReturn() {
        Optional<IFieldDescribe> treeViewObjectParentField = getTreeViewObjectParentField();
        if (!treeViewObjectParentField.isPresent()) {
            return Lists.newArrayList();
        }
        IFieldDescribe field = treeViewObjectParentField.get();
        String parentApiName = field.getApiName();
        String treeViewField = ((IObjectReferenceField) field).getTreeViewField();
        List<String> fieldApiNames = Lists.newArrayList();
        fieldApiNames.add(IObjectData.ID);
        fieldApiNames.add(IObjectData.NAME);
        fieldApiNames.add(IObjectData.DESCRIBE_API_NAME);
        fieldApiNames.add(parentApiName);
        fieldApiNames.add(treeViewField);
        if (isSupportDisplayName()) {
            fieldApiNames.add(IObjectData.DISPLAY_NAME);
        }
        return fieldApiNames;
    }

    public MapDifference<String, IFieldDescribe> diffFieldDescribe(@NonNull List<IFieldDescribe> fieldDescribes) {

        Map<String, IFieldDescribe> left = fieldDescribes.stream()
                .collect(Collectors.toMap(IFieldDescribe::getApiName, it -> it));
        Map<String, IFieldDescribe> right = getFieldDescribes().stream()
                .collect(Collectors.toMap(IFieldDescribe::getApiName, it -> it));

        return Maps.difference(left, right);
    }

    public Set<IFieldDescribe> getEnableMultiLangField() {
        return stream()
                .filter(it -> BooleanUtils.isTrue(it.getEnableMultiLang()))
                .collect(Collectors.toSet());
    }

    public boolean enableMultiLangField() {
        return stream()
                .anyMatch(it -> BooleanUtils.isTrue(it.getEnableMultiLang()));
    }

    public boolean isMultiLangField(String apiName) {
        if (FieldDescribeExt.isMultiLangExtraField(apiName)) {
            apiName = FieldDescribeExt.getMultiLangFieldFromExtraField(apiName);
        }
        return getFieldDescribeSilently(apiName)
                .filter(it -> BooleanUtils.isTrue(it.getEnableMultiLang()))
                .isPresent();
    }

    public IObjectDescribe copyOnWrite() {
        return ((ObjectDescribe) objectDescribe).copyOnWrite();
    }

    public List<String> getCopyObjectNeedToDelFieldApiNames() {
        List<String> needToDelFieldApiNames = Lists.newArrayList();
        needToDelFieldApiNames.addAll(getDimensionFieldApiNames());
        List<String> countApiName = getCountFields().stream()
                .map(IFieldDescribe::getApiName).collect(Collectors.toList());
        needToDelFieldApiNames.addAll(countApiName);
        if (enabledChangeOrder()) {
            needToDelFieldApiNames.addAll(getOriginalDescribeFieldNames());
        }
        return needToDelFieldApiNames;
    }

    /**
     * 是变更单对象
     *
     * @return
     */
    public boolean isChangeOrderObject() {
        return !Strings.isNullOrEmpty(getOriginalDescribeApiName());
    }

    /**
     * 开启了变更单的原单对象
     *
     * @return
     */
    public boolean enabledChangeOrder() {
        Set<String> fields = getOriginalDescribeFieldNames();
        return fields.stream()
                .allMatch(this::containsField);
    }

    private Set<String> getOriginalDescribeFieldNames() {
        List<IFieldDescribe> fields = ChangeOrderConfig.getOriginalDescribeFields(null);
        if (CollectionUtils.empty(fields)) {
            return Sets.newHashSet(ObjectDataExt.CHANGED_STATUS);
        }
        return fields.stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
    }

    public boolean isChangeOrderOrOriginalObj() {
        return isChangeOrderObject() || enabledChangeOrder();
    }

    /**
     * 在 describeLayout，计算默认值时，需要按需要按 AUTO 处理，补充默认的负责人
     * 在 describeLayout，处理布局时，需要按需要按 MANUAL 处理，展示负责人字段
     * 在 Add 接口保存数据的时候，需要按需要按 MANUAL 处理，不补充默认的负责人
     *
     * <p>
     * 这里根据灰度，返回不通场景期望的结果
     * <p>
     * 没有灰度调用 {@link #isManualByOuterAllocateOwner(User)}
     *
     * @param user
     * @param expectResult
     * @return
     */
    public boolean getExpectEmployeeAllocateRuleByGray(User user, boolean expectResult) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, user.getTenantId()) && user.isOutUser()) {
            return expectResult;
        }
        return isManualByOuterAllocateOwner(user);
    }

    /**
     * 下游人员新建数据负责人指定方式
     * 自动auto 手动manual
     *
     * @param user
     * @return
     */
    public boolean isManualByOuterAllocateOwner(User user) {
        return EmployeeAllocateRuleType.MANUAL.getType().equals(getOwnerAllocateType(user));
    }

    /**
     * 获取人员新建负责人指定方式，上游人员默认为auto，下游人员根据人员字段的属性判断
     *
     * @param user
     * @return
     */
    public String getOwnerAllocateType(User user) {
        if (!user.isOutUser()) {
            return EmployeeAllocateRuleType.AUTO.getType();
        }
        IFieldDescribe fieldDescribe = getFieldDescribe(ObjectData.OWNER);
        if (Objects.isNull(fieldDescribe)) {
            return EmployeeAllocateRuleType.AUTO.getType();
        }
        Employee employee = (Employee) fieldDescribe;
        return employee.getPeopleAllocateRule();
    }

    /**
     * 字段是否支持复制
     *
     * @return
     */
    public void fillEnabledCloneDefaultValueToField() {
        Set<String> fieldTypes = AppFrameworkConfig.getFieldEnableCloneFieldTypes(getTenantId());
        if (CollectionUtils.empty(fieldTypes)) {
            return;
        }
        List<IFieldDescribe> hasEnableClonePropField = getFieldByTypes(fieldTypes);
        if (CollectionUtils.empty(hasEnableClonePropField)) {
            return;
        }
        hasEnableClonePropField.forEach(field -> {
            FieldDescribeExt fieldExt = FieldDescribeExt.of(field);
            if (!fieldExt.isCustomField() && !AppFrameworkConfig.isNeedEnableClone(field.getDescribeApiName(), field.getApiName())) {
                return;
            }
            if (Objects.isNull(fieldExt.getEnableCloneActual())) {
                field.setEnableClone(true);
            }
        });
    }

    public void filterFieldByType(List<String> fieldTypes) {
        if (CollectionUtils.empty(fieldTypes)) {
            return;
        }
        List<IFieldDescribe> fieldDescribeList = getFieldDescribesSilently().stream().filter(x -> fieldTypes.contains(x.getType())).collect(Collectors.toList());
        setFieldDescribes(fieldDescribeList);
    }

    public void setPublicObjectFlag(String upstreamTenantId) {
        if (isPublicObject()) {
            return;
        }
        if (isBigObject()) {
            setVisibleScope(VISIBLE_SCOPE_PUBLIC_BIG);
            setUpstreamTenantId(upstreamTenantId);
            return;
        }
        String visibleScope = getVisibleScope();
        if (isVisibleToUpstreamTenant(visibleScope, upstreamTenantId)) {
            setVisibleScope(VISIBLE_SCOPE_PUBLIC);
            setUpstreamTenantId(upstreamTenantId);
            return;
        }
        log.warn("set public object flag fail! ei:{}, apiName:{}, visibleScope:{}", getTenantId(), getApiName(), visibleScope);
        throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
    }

    private boolean isVisibleToUpstreamTenant(String visibleScope, String upstreamTenantId) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SET_FIELD_PUBLIC_FLAG_BEFORE_ADD, upstreamTenantId)) {
            return Strings.isNullOrEmpty(visibleScope) || IObjectDescribe.VISIBLE_SCOPE_CONFIG.equals(visibleScope);
        }
        return Strings.isNullOrEmpty(visibleScope);
    }

    public List<IFieldDescribe> getPublicFields() {
        if (!isPublicObject()) {
            return Collections.emptyList();
        }
        return stream()
                .filter(field -> FieldDescribeExt.of(field).isPublicField())
                .collect(Collectors.toList());
    }

    /**
     * 公共对象私有数据字段
     *
     * @return
     */
    public List<IFieldDescribe> getPublicFieldPrivateDataFields() {
        if (!isPublicObject()) {
            return Collections.emptyList();
        }
        return stream()
                .filter(field -> FieldDescribeExt.of(field).isPublicFieldPrivateData())
                .collect(Collectors.toList());
    }

    public Optional<IFieldDescribe> getPublicField(String apiName) {
        if (!isPublicObject()) {
            return Optional.empty();
        }
        return Optional.ofNullable(getFieldDescribe(apiName))
                .filter(field -> FieldDescribeExt.of(field).isPublicField());
    }

    public Optional<IFieldDescribe> getPublicFieldPrivateData(String apiName) {
        if (!isPublicObject()) {
            return Optional.empty();
        }
        return Optional.ofNullable(getFieldDescribe(apiName))
                .filter(field -> FieldDescribeExt.of(field).isPublicFieldPrivateData());
    }

    public Optional<IFieldDescribe> getPublicFieldPublicData(String apiName) {
        if (!isPublicObject()) {
            return Optional.empty();
        }
        return Optional.ofNullable(getFieldDescribe(apiName))
                .filter(field -> FieldDescribeExt.of(field).isPublicFieldPublicData());
    }

    /**
     * 找出该对象中的所有查找关联（不包括主从关系）对象和使用到的相关对象（目前只有统计字段可以使用相关对象「包括从对象」）
     *
     * @return targetObjAPISet, relatedObjAPISet
     */
    public Set<String> findTargetObjAndUsedRelatedObj() {
        Set<String> targetObjAndUsedRelated = Sets.newHashSet();

        targetObjAndUsedRelated.addAll(findTargetObj());
        Set<String> usedCountObjAPISet = stream()
                .filter(x -> FieldDescribeExt.of(x).isCountField())
                .map(x -> (Count) x)
                .map(Count::getSubObjectDescribeApiName)
                .collect(Collectors.toSet());
        targetObjAndUsedRelated.addAll(usedCountObjAPISet);
        return targetObjAndUsedRelated;
    }

    public Set<String> findTargetObj() {
        return getReferenceFieldDescribes().stream()
                .map(ObjectReferenceWrapper::getTargetApiName)
                .collect(Collectors.toSet());
    }

    public boolean isDownstreamTenantWithPublicObject() {
        return isPublicObject() && !Objects.equals(getTenantId(), getUpstreamTenantId());
    }

    public static String getChangeOrderApiName(String describeApiName) {
        return describeApiName + CHANGE_ORDER_SUFFIX;
    }

    public List<Image> getImageFields() {
        return stream().filter(x -> Objects.equals(x.getType(), IMAGE))
                .filter(IFieldDescribe::isActive)
                .map(x -> (Image) x)
                .collect(Collectors.toList());
    }

    public List<IFieldDescribe> getCountOrCurrencyRealTypeCurrencyFields() {
        return stream()
                .filter(x -> FieldDescribeExt.of(x).countOrCurrencyRealTypeIsCurrency())
                .filter(x -> x.isActive())
                .collect(Collectors.toList());
    }


    public Stream<FieldDescribeExt> getRelatedFieldForFormalStream() {
        return stream()
                .map(FieldDescribeExt::of)
                .filter(x -> x.isObjectReferenceField() || x.isMasterDetailField())
                .filter(FieldDescribeExt::isActive);
    }

    public Set<IFieldDescribe> getRelatedFieldForFormalFiled() {
        return getRelatedFieldForFormalStream()
                .map(FieldDescribeExt::getExtData)
                .collect(Collectors.toSet());
    }

    // 指向 可配置在计算公式的【关联对象】下字段 的 字段 指向的obj， 暂时先写查找关联（单选，不含多选）和主从， 还有人员、部门类型的字段
    public Set<String> getRelatedFieldForFormal() {
        return getRelatedFieldForFormalStream()
                .map(FieldDescribeExt::getRefObjTargetApiName)
                .collect(Collectors.toSet());
    }

    public List<IFieldDescribe> getFieldDescribes(List<String> fieldApiNames) {
        if (CollectionUtils.empty(fieldApiNames)) {
            return Lists.newArrayList();
        }
        return stream()
                .filter(a -> fieldApiNames.contains(a.getApiName()))
                .collect(Collectors.toList());
    }
}
