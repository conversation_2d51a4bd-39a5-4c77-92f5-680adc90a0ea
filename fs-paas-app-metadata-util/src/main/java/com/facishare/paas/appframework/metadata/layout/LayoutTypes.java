package com.facishare.paas.appframework.metadata.layout;

import com.google.common.collect.ImmutableSet;

import java.util.Set;

public interface LayoutTypes {
    String DETAIL = "detail";   // 详情页/流程布局(后者包含namespace)
    String ADD = "add";
    String EDIT = "edit";   // 新建编辑页
    String LIST_LAYOUT = "list_layout"; // 列表页-列表页布局
    String LIST = "list";   // 列表页-移动端摘要布局
    String WHAT_LIST = "what_list"; // 待办-all-移动端摘要布局
    String FLOW_TASK_LIST = "flow_task_list";   // 待办-all-列表页布局
    String SIDEBAR = "sidebar";   // 侧边栏布局
    String MOBILE = "mobile";   // 移动端布局



    Set<String> ALL_TYPES = ImmutableSet.of(DETAIL, LIST, LIST_LAYOUT, ADD, EDIT, WHAT_LIST, FLOW_TASK_LIST);
    Set<String> TODO_TYPES = ImmutableSet.of(WHAT_LIST, FLOW_TASK_LIST);   // 待办布局
}
