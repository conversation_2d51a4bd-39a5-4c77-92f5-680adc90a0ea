<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>fs-paas-appframework</artifactId>
    <groupId>com.facishare</groupId>
    <version>dev-stage-SNAPSHOT</version>
  </parent>

  <modelVersion>4.0.0</modelVersion>
  <artifactId>fs-paas-app-prm</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-paas-app-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-paas-app-metadata-util</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-enterprise-relation-outapi</artifactId>
      <version>1.0.4-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <groupId>com.github.zhxing</groupId>
          <artifactId>retrofit-spring</artifactId>
        </exclusion>
        <exclusion>
          <artifactId>validation-api</artifactId>
          <groupId>javax.validation</groupId>
        </exclusion>
          <exclusion>
            <artifactId>fs-paas-app-metadata-util</artifactId>
            <groupId>com.facishare</groupId>
          </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-metadata-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.facishare</groupId>
          <artifactId>fs-paas-foundation-boot</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.esotericsoftware</groupId>
          <artifactId>kryo</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mybatis</groupId>
          <artifactId>mybatis</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.mybatis</groupId>
          <artifactId>mybatis-spring</artifactId>
        </exclusion>
        <exclusion>
          <groupId>mysql</groupId>
          <artifactId>mysql-connector-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fxiaoke</groupId>
          <artifactId>retrofit-spring2</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.elasticsearch.client</groupId>
          <artifactId>transport</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>

</project>
