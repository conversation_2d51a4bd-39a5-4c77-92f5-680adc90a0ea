package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.OutDataPrivilege;
import com.facishare.paas.appframework.privilege.dto.*;
import com.facishare.paas.appframework.privilege.dto.TemporaryRights.RuleConfig;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by zhouwr on 2017/10/16
 */
public interface DataPrivilegeService {


    Integer USER_TYPE = 0;//用户类型
    Integer USER_GROUP_TYPE = 1;//用户组类型
    Integer DEPART_TYPE = 2;//部门类型
    Integer ROLE_TYPE = 4;//角色类型
    Integer OUT_TENANT_TYPE = 5; //外部企业类型
    Integer OUT_TENANT_GROUP_TYPE = 6;//外部企业组
    Integer ALL_OUT_TENANT_TYPE = 7;//全部外部企业
    Integer ORGANIZATION_TYPE = 8;//组织

    String ALL_OUT_TENANT_ID = "-100000000";

    Integer DATA_SHARE_USER_STATUS_NORMAL = 0;//启用状态

    Integer DATA_SHARE_USER_STATUS_STOP = 1;//停用状态

    Map<String, Permissions> checkDataPrivilege(User user, List<String> idList, String apiName);

    Boolean delDataRights(User user, String apiName);

    boolean delFieldShare(User user, List<String> shareIds, String describeApiName, int status);

    boolean changeFieldShareStatus(User user, List<String> shareIds, int status);

    CreateEntityFieldShareModel.Result addFieldShare(User user, String describeApiName, String ruleName, List<Receive> receives, String ruleParse, List<Rule> rules);

    CreateEntityFieldShareModel.Result addFieldShareRule(User user, String describeApiName, String ruleName, List<Receive> receives, String ruleParse, List<Rule> rules, boolean isCheckRule);

    boolean updateFieldShare(User user, String id, String describeApiName, String ruleName, List<Receive> receives, String ruleParse, List<Rule> rules);

    boolean updateFieldShareRule(User user, String id, String describeApiName, String ruleName, List<Receive> receives, String ruleParse, List<Rule> rules, boolean isCheckRule);

    QueryAllEntityFieldShareModel.Result getAllFieldShareList(User user,
                                                              List ruleCodes,
                                                              Integer status,
                                                              Integer permissionType,
                                                              List receives,
                                                              Map receivesWithType,
                                                              Set<String> createIds,
                                                              Set<String> modifyIds,
                                                              Map<String, Long> createTimeRange,
                                                              Map<String, Long> modifyTimeRange,
                                                              String ruleName,
                                                              Integer pageNumber,
                                                              Integer pageSize,
                                                              List<String> entices,
                                                              Boolean outReceive);

    QueryEntityFieldShareModel.Result getFieldShares(User user, String describeApiName, List shareIds, Integer status, Integer permissionType, List receives, Map receivesWithType, String ruleName, Integer pageNumber, Integer pageSize, List<String> entices,Boolean outReceive);

    Map<String, Object> queryDimensionIntersectionStatus(User user, Map<String, String> queryContent);

    List<ObjectDataPermissionInfo> getCommonPrivilegeListResult(User user, List<IObjectDescribe> objectDescribeList);

    ObjectDataPermissionInfo getCommonPrivilege4DefObjResult(User user, IObjectDescribe objectDescribe);


    void addCommonPrivilegeListResult(User user, List<ObjectDataPermissionInfo> objectDataPermissionInfos);

    void initCommonPrivilegeListResult(User user, List<ObjectDataPermissionInfo> objectDataPermissionInfos);

    void updateCommonPrivilegeList(User user, List<ObjectDataPermissionInfo> objectDataPermissionInfos);

    List<String> addOrUpdateShareRules(User user, int permissionType, List<String> describeApiNameList,
                                       List<Integer> sourceCircleIDList, List<Integer> sourceEmployeeIDList,
                                       List<String> sourceUserGroupIDList, List<String> sourceRoleIDList, List<Integer> targetCircleIDList,
                                       List<Integer> targetEmployeeIDList, List<String> targetUserGroupIDList, List<String> targetRoleIDList);

    List<String> addOrUpdateShareRules(User user, DataSharing dataSharing, Set<String> queryAllEntityIds);

    List<String> addOrUpdateEntityShareRules(User user, DataSharing dataSharing, Set<String> queryAllEntityIds, boolean isApiOperation);

    boolean updateEntityShareRulePermission(User user, List<String> entityShareIds, int permission, boolean isApiOperation);

    List<String> addOrUpdateShareRuleGroups(User user, DataShareRuleGroup dataShareRuleGroup);

    boolean delShareRuleGroups(User user, Set<String> sharedRuleGroupIds);

    boolean delShareRules(User user, List<String> sharedRuleIds);

    boolean enableOrDisableShareRule(User user, List<String> sharedRuleIds, int status);

    boolean enableOrDisableShareRuleGroup(User user, List<String> sharedRuleGroupIds, int status);

    CreateDimensionRuleGroupModel.Result createDimensionRuleGroup(User user,
                                                                  String entityId,
                                                                  String ruleParse,
                                                                  int ruleType,
                                                                  int permission,
                                                                  String remark,
                                                                  List<DimensionRulePojo> rules,
                                                                  List<DimensionRuleGroupReceivePojo> receives);

    CreateDimensionRuleGroupModel.Result createDimensionRuleGroupInfo(User user,
                                                                      String entityId,
                                                                      String ruleParse,
                                                                      int ruleType,
                                                                      int permission,
                                                                      String remark,
                                                                      Map<String, String> properties,
                                                                      List<DimensionRulePojo> rules,
                                                                      List<DimensionRuleGroupReceivePojo> receives);

    UpdateDimensionRuleGroupModel.Result updateDimensionRuleGroup(User user,
                                                                  String ruleCode,
                                                                  String ruleParse,
                                                                  int permission,
                                                                  String remark,
                                                                  List<DimensionRulePojo> rules,
                                                                  List<DimensionRuleGroupReceivePojo> receives);

    UpdateDimensionRuleGroupModel.Result updateDimensionRuleGroupInfo(User user,
                                                                      String ruleCode,
                                                                      String ruleParse,
                                                                      int permission,
                                                                      String remark,
                                                                      Map<String, String> properties,
                                                                      List<DimensionRulePojo> rules,
                                                                      List<DimensionRuleGroupReceivePojo> receives);

    QueryDimensionRuleGroupModel.Result queryDimensionRuleGroup(User user,
                                                                Set<String> receiveIds,
                                                                Integer receiveType,
                                                                String receiveTenantId,
                                                                int permissionType,
                                                                Map<String, Long> createTimeRange,
                                                                Map<String, Long> modifyTimeRange,
                                                                int sortType,
                                                                int sortOrder,
                                                                int pageNumber,
                                                                int pageSize);

    QueryDimensionRuleGroupModel.Result queryDimensionRuleGroupInfo(User user,
                                                                    Set<String> receiveIds,
                                                                    Integer receiveType,
                                                                    String receiveTenantId,
                                                                    int permissionType,
                                                                    Map<String, String> properties,
                                                                    Map<String, Long> createTimeRange,
                                                                    Map<String, Long> modifyTimeRange,
                                                                    int sortType,
                                                                    int sortOrder,
                                                                    int pageNumber,
                                                                    int pageSize);

    QueryDimensionRuleCodeListModel.Result queryDimensionRuleCodeList(User user, Set<String> receiveIds, Integer receiveType, String receiveTenantId);

    boolean deleteDimensionRuleGroup(User user, Set<String> ruleCodes);

    boolean deleteDimensionRuleGroupInfo(User user, Set<String> ruleCodes, Map<String, String> properties);

    QueryEntityShareModel.Result getEntitySharePojoResult(GetShareRules.Arg arg, List<String> describeApiNameList, User user);

    QueryAllEntityShareModel.Result getAllEntitySharePojoList(GetAllShareRules.Arg arg, List<String> describeApiNameList, User user);

    QueryAllEntityShareModel.Result getAllEntitySharePojoListByIds(GetAllShareRules.Arg arg, List<String> describeApiNameList, User user);

    QueryEntityShareGroupModel.Result getEntityShareGroupPojoResult(GetShareRuleGroups.Arg arg, List<String> describeApiNameList, User user);

    int findEntityShareCount(User user, Set<String> queryAllEntityIds);

    int findEntityShareCountByScope(User user, Set<String> queryAllEntityIds, Integer queryScope);

    QueryTemporaryPrivilegeList.Result getTemporaryPrivilegeList(User user, String describeApiName,
                                                                 String ownerId, Integer pageSize, Integer pageNumber,
                                                                 String userId, String appId);

    UpdateTemporaryRights.Result updateTemporaryRights(User user, String describeApiName, String dataId, Set<String> ownerId, String scene);

    DeleteTemporaryRights.Result deleteTemporaryRights(User user);

    DeleteTemporaryRights.Result deleteTemporaryRights(User user, String sourceId, String dataId, String owner, String apiName);

    DeleteTemporaryRights.Result batchDeleteTemporaryRights(User user, Set<String> temporaryRightsIds);

    BatchDeleteTemporaryPrivilegeModel.Result batchDeleteTemporaryPrivilege(User user, BatchDeleteTemporaryPrivilege.Arg arg, String lang);

    QueryTemporaryRightsList.Result queryTemporaryRights(User user, String describeApiName, List<String> entityIdList, String dataId, Integer pageSize, Integer pageNumber,
                                                         String userId, String scene);

    QueryTemporaryRightsList.Result queryTemporaryRightsByTimeRange(User user,
                                                                    String describeApiName,
                                                                    List<String> entityIdList,
                                                                    String dataId,
                                                                    Integer pageSize,
                                                                    Integer pageNumber,
                                                                    String userId,
                                                                    String scene,
                                                                    Map<String, Map<String, Long>> createTimeRange,
                                                                    Map<String, Map<String, Long>> expiryTimeRange);

    void enableTemporaryRights(User user, String describeApiName, boolean enable, String AppId);

    void asyncUpdateTemporaryRights(User user, String describeApiName, RuleConfig beforeRule, RuleConfig afterRule, String AppId);

    void asyncDeleteTemporaryRights(User user, String describeApiName, String AppId);

    OutDataPrivilege getOutDataPrivilege(User user, String appId, String ObjectAPIName);

    Map<String,Object> obtainDataAuth(QueryDataAuth.Arg arg);

    Map<String,Set<String>> queryTeamRoleDescribeList(User user);

    Object queryTeamRoleMaxNumber(User user);

    CreateTeamRole.Result createTeamRole(User user, CreateTeamRole.Arg arg);

    QueryTeamRole.Result queryTeamRole(User user, QueryTeamRole.Arg arg, String lang);

    UpdateTeamRole.Result updateTeamRole(User user, UpdateTeamRole.Arg arg, String lang);

    UpdateTeamRoleStatus.Result updateTeamRoleStatus(User user, UpdateTeamRoleStatus.Arg arg);

    UpdateTeamRoleStatus.Result deleteTeamRole(User user, UpdateTeamRoleStatus.Arg arg);
}
