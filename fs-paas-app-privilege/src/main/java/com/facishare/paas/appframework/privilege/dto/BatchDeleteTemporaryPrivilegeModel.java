package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;
import java.util.Set;

public interface BatchDeleteTemporaryPrivilegeModel {

  @Data
  class Arg {
    private String tenantId;

    private String operatorId;

    private String lang;

    private Set<String> userIds;

    private Set<String> outUserIds;

    private Set<String> describeApiNames;

    private Set<String> scenes;

    private Set<String> dataIds;

    private Map<String, Map<String, Long>> createTimeRange;

    private Map<String, Map<String, Long>> expiryTimeRange;
  }


  @Data
  @EqualsAndHashCode(callSuper = true)
  class Result extends BasePrivilegeResult {
    BatchDeleteTemporaryPrivilegeModel.DeleteResult result;
  }


  @Data
  class DeleteResult {

  }
}
