package com.facishare.paas.appframework.privilege.dto;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import lombok.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.privilege.dto.QueryTemporaryRightsList.TemporaryRightSourceEnum.getSourceNameBySource;

/**
 * create by z<PERSON><PERSON> on 2018/11/13
 */
public interface QueryTemporaryRightsList {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    class Arg extends BasePrivilegeArg {
        private String entityId;
        private List<String> entityIdList;
        private String scene;
        private String sourceId;
        private Integer permission;
        private String status;
        private String withdrawalWay;
        private String owner;
        private String dataIds;
        private Map<String, Map<String, Long>> createTimeRange;
        private Map<String, Map<String, Long>> expiryTimeRange;
        private PageInfo pageInfo;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        QueryResult result;

        public void fillSceneName() {
            if (Objects.isNull(result) || CollectionUtils.empty(result.getContent())) {
                return;
            }

            result.getContent().forEach(a -> a.setSceneName(getSourceNameBySource(a.getScene())));
        }
    }

    @Data
    class QueryResult {
        List<TemporaryPermissionInfo> content;
        PageInfo page;
    }

    @Data
    class TemporaryPermissionInfo {
        String id;
        String tenantId;
        String appId;
        String entityId;
        Integer permission;
        String dataId;
        String owner;
        String status;
        String withdrawalWay;
        String scene;
        String sceneName;
        String sourceId;
        Long createTime;
        Long expiryTime;
    }

    enum TemporaryRightSourceEnum {
        APPROVAL("approval", I18NKey.APPROVAL_FLOW),
        BPM("bpm", I18NKey.BUSINESS_FLOW),
        STAGE("stage", I18NKey.STAGE_PROPELLER),
        FREE_FLOW("freeflow", I18NKey.FREE_PROCESS),
        FREE_APPROVALFLOW("free_approvalflow", I18NKey.FREE_APPROVAL_PROCESS),
        CALL_CENTER("CALL_CENTER", I18NKey.CALL_CENTER_TEMPORARY_RIGHTS_SCENE),
        QIXIN("QIXIN", I18NKey.QIXIN);

        @Getter
        private final String source;
        private final String sourceName;

        public String getSourceName() {
            return I18N.text(sourceName);
        }

        TemporaryRightSourceEnum(String source, String sourceName) {
            this.source = source;
            this.sourceName = sourceName;
        }

        private static final Map<String, TemporaryRightSourceEnum> sceneMap;

        static {
            sceneMap = Stream.of(values()).collect(Collectors.toMap(TemporaryRightSourceEnum::getSource, x -> x));
        }

        public static TemporaryRightSourceEnum getSourceEnum(String source) {
            return sceneMap.get(source);
        }

        public static String getSourceNameBySource(String source) {
            if (Objects.isNull(getSourceEnum(source))) {
                return null;
            }
            return getSourceEnum(source).getSourceName();
        }
    }
}
