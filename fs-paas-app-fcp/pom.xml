<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-paas-appframework</artifactId>
        <groupId>com.facishare</groupId>
        <version>dev-stage-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>fs-paas-app-fcp</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fcp-biz-server</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-core</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>