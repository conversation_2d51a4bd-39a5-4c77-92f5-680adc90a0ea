<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:spring/common.xml"></import>
    <!-- move to fs-paas-app-common/spring/common.xml -->
    <!-- <import resource="classpath:spring/fs-fsc-rest-client.xml"/> -->

    <bean id="recordTypeAuthProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.RecordTypeAuthProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="userExtensionProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.UserExtensionProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>


    <bean id="crmRestServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.CRMRestServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="newCrmRestServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.NewCRMRestServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="duplicateSearchProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.DuplicateSearchProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="customComponentProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.component.CustomComponentProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="layoutDesignerButtonProviderProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.button.LayoutDesignerButtonProviderProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="specialButtonProviderProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.button.SpecialButtonProviderProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="ocrService" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.ocr.api.service.OcrService"
          p:factory-ref="restServiceProxyFactory"/>

    <bean id="importObjectProviderProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.importobject.ImportObjectProviderProxy"
          p:factory-ref="restServiceProxyFactory"/>

    <bean id="i18nSettionProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.I18nSettingProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="findAndFilterButtonsByDataProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.button.FindAndFilterButtonsByDataProxy"
          p:factory-ref="restServiceProxyFactory"/>

    <bean id="findButtonsByDescribeApiNameProviderProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.button.FilterButtonsProviderProxy"
          p:factory-ref="restServiceProxyFactory"/>


    <bean id="brushToolsTransferProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.tools.BrushToolsTransferProxy"
          p:factory-ref="restServiceProxyFactory"/>

    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
    </bean>

    <bean name="okHttpClient" id="okHttpClient" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"/>
    <bean id="appFrameworkExclusiveClient" class="com.fxiaoke.stone.commons.impl.AppFrameworkExclusiveClient">
        <constructor-arg ref="okHttpClient"/>
    </bean>

    <bean id="aiRestServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.ai.AiRestServiceProxy"
          p:factory-ref="restServiceProxyFactory"/>

    <bean id="biRestServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.bi.BIRestServiceProxy"
          p:factory-ref="biServiceProxyFactory"/>

    <bean id="biCRMRestServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.bi.BICRMRestServiceProxy"
          p:factory-ref="biServiceProxyFactory"/>


</beans>