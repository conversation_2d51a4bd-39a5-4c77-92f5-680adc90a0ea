package com.facishare.paas.appframework.metadata.dto.tools;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

public interface BrushProgress {
    @Data
    class Arg {
        private String taskDataId;
        private String taskRecordType;
        private int action;
    }

    @Data
    @Builder
    class Result {
        private Long totalCount;
        private Long successCount;
        private Long failureCount;
        private List<String> successEnterpriseIds;
        private List<String> failureEnterpriseIds;
    }
}
