package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.TagRestServiceProxy;
import com.facishare.paas.appframework.common.service.dto.FindAllTags;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.dto.tag.TagGroupTag;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.util.TranslateI18nUtils;
import com.facishare.paas.metadata.api.DataAndSubTag;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.ISubTagDescribe;
import com.facishare.paas.metadata.api.describe.ITagDescribe;
import com.facishare.paas.metadata.api.search.Ranges;
import com.facishare.paas.metadata.api.service.ISubTagDescribeService;
import com.facishare.paas.metadata.api.service.ITagDataRelationService;
import com.facishare.paas.metadata.api.service.ITagDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.describe.SubTagDescribe;
import com.facishare.paas.metadata.impl.describe.TagDescribe;
import com.facishare.paas.metadata.impl.search.TagQueryInfo;
import com.facishare.paas.metadata.support.GDSHandler;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.core.util.RequestUtil.VERSION_710;

/**
 * <AUTHOR>
 * @date 2020/2/11 5:49 下午
 */
@Slf4j
@Service("tagLogicService")
public class TagLogicServiceImpl implements TagLogicService {

    /**
     * 标签服务
     */
    @Resource
    private ISubTagDescribeService subTagDescribeService;

    /**
     * 标签分组服务
     */
    @Resource
    private ITagDescribeService tagDescribeService;

    @Autowired
    private TagRestServiceProxy tagRestServiceProxy;

    @Autowired
    GDSHandler gdsHandler;

    @Autowired
    LicenseService licenseService;

    @Autowired
    I18nSettingService i18nSettingService;

    /**
     * 标签数据关联接口
     */
    @Autowired
    private ITagDataRelationService tagDataRelationService;

    @Override
    public List<ISubTagDescribe> findAllTagsFilterByRole(String objectApiName, String keyword, Boolean isActive, IActionContext context) {
        try {
            List<ISubTagDescribe> subTagDescribeList = subTagDescribeService.findSubTagListByNameAndDescribe(objectApiName, keyword, isActive, context);
            subTagDescribeList = translateSubTagDescribeList(subTagDescribeList, false);
            return subTagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("find all tags error, tenantId:{}, objectApiName:{}", context.getEnterpriseId(), objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ISubTagDescribe> findAllTags(String objectApiName, String tenantId, String keyword, Boolean isActive) {
        try {
            List<ISubTagDescribe> subTagDescribeList = subTagDescribeService.findSubTagListByNameAndDescribe(tenantId, objectApiName, keyword, isActive);
            subTagDescribeList = translateSubTagDescribeList(subTagDescribeList, false);
            return subTagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("find all tags error, tenantId:{}, objectApiName:{}", tenantId, objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ITagDescribe> findAllTagGroups(String objectApiName, String tenantId) {
        try {
            // TODO: 2020/4/15 需要元数据提供获取指定对象描述下的所有标签分组
            // 需要先查询出所有的标签分组
            int count = tagDescribeService.findTagCount(tenantId, objectApiName);
            if (count <= 0) {
                return Lists.newArrayList();
            }
            List<ITagDescribe> tagGroups = tagDescribeService.findAllTag(tenantId, objectApiName, 1, count);
            tagGroups = translateTagDescribeList(tagGroups, false);
            if (CollectionUtils.notEmpty(tagGroups)) {
                return Lists.newArrayList();
            }
            return tagGroups;
        } catch (MetadataServiceException e) {
            log.warn("Find all tags fail,tenantId:{}, apiName;{}", tenantId, objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ISubTagDescribe> findTagsByName(String objectApiName, String tenantId, String name) {
        try {
            // 先查询子标签数量
            int count = subTagDescribeService.findSubTagCountByDescribeApiName(tenantId, objectApiName);
            // 模糊查询子标签
            QueryResult<ISubTagDescribe> resultQuery = subTagDescribeService.findSubTagByTagId(tenantId, null,
                    objectApiName, name, 1, count);
            List<ISubTagDescribe> subTagDescribeList = resultQuery.getData();
            subTagDescribeList = translateSubTagDescribeList(subTagDescribeList, false);
            return subTagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("find tags by name, tenantId:{}, describeApiName:{}, name:{}", tenantId, objectApiName, name, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ITagDescribe> findTagGroupsByIds(String objectApiName, String tenantId, Collection<String> groupIds) {
        try {
            List<ITagDescribe> tagGroups = CollectionUtils.nullToEmpty(tagDescribeService
                    .findTagsByIds(tenantId, objectApiName, Lists.newArrayList(groupIds)));
            tagGroups = translateTagDescribeList(tagGroups, false);
            return tagGroups;
        } catch (MetadataServiceException e) {
            log.warn("find tag groups by ids error, tenantId:{}, describeApiName:{}", tenantId, objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ITagDescribe> findTagGroupsByIds(String tenantId, Collection<String> groupIds) {
        try {
            List<ITagDescribe> tagGroups = CollectionUtils.nullToEmpty(tagDescribeService.findTagsByIds(tenantId, Lists.newArrayList(groupIds)));
            tagGroups = translateTagDescribeList(tagGroups, false);
            return tagGroups;
        } catch (MetadataServiceException e) {
            log.warn("find tag groups by ids error, tenantId:{}", tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ITagDescribe> findTagGroupsByIds(String tenantId, Collection<String> groupIds, boolean onTime) {
        try {
            List<ITagDescribe> tagGroups = CollectionUtils.nullToEmpty(tagDescribeService.findTagsByIds(tenantId, Lists.newArrayList(groupIds)));
            tagGroups = translateTagDescribeList(tagGroups, onTime);
            return tagGroups;
        } catch (MetadataServiceException e) {
            log.warn("find tag groups by ids error, tenantId:{}", tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<TagGroupTag> findTagsByRest(String name, String tenantId, String userId) {
        try {
            Map<String, String> header = Maps.newHashMap();
            header.put("x-fs-userid", userId);
            header.put("x-fs-ea", gdsHandler.getEAByEI(tenantId));
            Map<String, String> queryMap = Maps.newHashMap();
            if (StringUtils.isNotBlank(name)) {
                queryMap.put("keyword", name);
            }
            FindAllTags.Result result = tagRestServiceProxy.findAllTags(header, queryMap);
            if (CollectionUtils.empty(result.getData())) {
                return Lists.newArrayList();
            }
            List<TagGroupTag> tags = Lists.newArrayList();
            for (FindAllTags.Tag tag : result.getData()) {
                TagGroupTag t = new TagGroupTag();
                t.setTagGroupName(tag.getTagGroupName());
                t.setTagNames(tag.getTagDatas());
                tags.add(t);
            }
            return tags;
        } catch (Exception e) {
            log.error("find all tags error, tenantId:{}, keyword:{}", tenantId, name, e);
            return Lists.newArrayList();
        }
    }

    @Override
    public List<ISubTagDescribe> findTagsByDataId(String describeApiName, String dataId, IActionContext context) {
        try {
            List<ISubTagDescribe> subTagDescribeList = tagDataRelationService.findAllTagByDataId(context, dataId, describeApiName);
            subTagDescribeList = translateSubTagDescribeList(subTagDescribeList, false);
            return subTagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("find tags by data id error, tenantId:{}, describeApiName:{}, dataId:{}", context.getEnterpriseId(),
                    describeApiName, dataId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ISubTagDescribe findTagById(String id, User user) {
        try {
            List<ISubTagDescribe> subTagDescribeList = Lists.newArrayList();
            subTagDescribeList.add(subTagDescribeService.findSubTagById(user.getTenantId(), id));
            subTagDescribeList = translateSubTagDescribeList(subTagDescribeList, false);
            return subTagDescribeList.get(0);
        } catch (MetadataServiceException e) {
            log.warn("find tag by id error, tagId:{}, user:{}", id, user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ISubTagDescribe findTagById(String id, User user, boolean isOnTime) {
        try {
            List<ISubTagDescribe> subTagDescribeList = Lists.newArrayList();
            subTagDescribeList.add(subTagDescribeService.findSubTagById(user.getTenantId(), id));
            subTagDescribeList = translateSubTagDescribeList(subTagDescribeList, isOnTime);
            return subTagDescribeList.isEmpty() ? null : subTagDescribeList.get(0);
        } catch (MetadataServiceException e) {
            log.warn("find tag by id error, tagId:{}, user:{}", id, user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean batchRemoveTagsForData(User user, String dataId, List<String> tagIds, String tenantId, String describeApiName) {
        try {
            if (CollectionUtils.notEmpty(tagIds)) {
                return false;
            }
            Map<String, List<String>> dataTagMap = Maps.newHashMap();
            dataTagMap.put(dataId, tagIds);
            return tagDataRelationService.bulkRemoveTagForData(dataTagMap, tenantId, describeApiName, ActionContextExt.of(user).getContext());
        } catch (MetadataServiceException e) {
            log.warn("batch remove tags for data error, tenantId:{}, describeApiName:{}, tagIds:{},dataId:{}",
                    tenantId, describeApiName, tagIds, dataId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean batchAddTagsByTagName(String dataId, Map<String, List<String>> tagNames, User user, String describeApiName) {
        try {
            String tenantId = user.getTenantId();
            List<String> tagIdForData = Lists.newArrayList();
            // 未同步的标签，需要在paas中创建
            for (Map.Entry<String, List<String>> entry : tagNames.entrySet()) {
                // 先同步标签组
                ITagDescribe group = tagDescribeService.findTagByType(tenantId, describeApiName, entry.getKey());
                if (null == group) {
                    ITagDescribe tagGroupDescribe = new TagDescribe();
                    tagGroupDescribe.setTenantId(tenantId);
                    tagGroupDescribe.setType(entry.getKey());
                    tagGroupDescribe.setDescribeApiName(describeApiName);
                    tagGroupDescribe.setTagDefineType("custom");
                    group = tagDescribeService.create(tagGroupDescribe);
                }
                // 查询该标签组下所有标签
                List<ISubTagDescribe> tagsInPaas = subTagDescribeService.findSubTagByTagId(tenantId, group.getId(),
                        describeApiName).getData();
                // 不存在的标签需要创建，已存在于Paas的标签，关联数据
                for (String tagName : entry.getValue()) {
                    boolean flag = true;
                    for (ISubTagDescribe tag : tagsInPaas) {
                        if (tagName.equals(tag.getName())) {
                            // Paas中已存在的标签
                            tagIdForData.add(tag.getId());
                            flag = false;
                            break;
                        }
                    }
                    // 没有在Paas中找到标签，需要同步
                    if (flag) {
                        ISubTagDescribe subTagDescribe = new SubTagDescribe();
                        subTagDescribe.setTenantId(tenantId);
                        subTagDescribe.setTagId(group.getId());
                        subTagDescribe.setDescribeApiName(describeApiName);
                        subTagDescribe.setName(tagName);
                        subTagDescribe.setGrade(1);
                        subTagDescribe.setSupTag(null);
                        ISubTagDescribe tagIdForData1 = subTagDescribeService.createSubTag(subTagDescribe,
                                ActionContextExt.of(user).getContext());
                        tagIdForData.add(tagIdForData1.getId());
                    }
                }
            }
            // 因为元数据bulkHangTagForDataById方法虽然是全量操作，但是又不能不传标签(即不支持删除全部标签)，所以如果前端
            // 没有传任何标签，需要单独调用删除数据全部标签接口
            if (CollectionUtils.empty(tagIdForData)) {
                List<String> dataIds = Lists.newArrayList(dataId);
                return tagDataRelationService.bulkRemoveTagForData(dataIds, tenantId, describeApiName, ActionContextExt.of(user).getContext());
            }
            // 批量关联标签到指定数据
            Map<String, List<String>> map = Maps.newHashMap();
            map.put(dataId, tagIdForData);
            return tagDataRelationService.bulkHangTagForDataById(map, tenantId, describeApiName, ActionContextExt.of(user).getContext());
        } catch (MetadataServiceException e) {
            log.warn("batchAddTagsByTagName error, tenantId:{}, describeApiName:{}, dataId:{}",
                    user.getTenantId(), describeApiName, dataId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    public boolean batchBoundTags(String dataId, Collection<String> tagIds, String describeApiName, User user) {
        try {
            if (CollectionUtils.empty(tagIds)) {
                // 因为元数据bulkHangTagForDataById方法虽然是全量操作，但是又不能不传标签(即不支持删除全部标签)，所以如果前端
                // 没有传任何标签，需要单独调用删除数据全部标签接口
                return tagDataRelationService.bulkRemoveTagForData(Lists.newArrayList(dataId), user.getTenantId(), describeApiName, ActionContextExt.of(user).getContext());
            }
            // 批量关联标签到指定数据
            Map<String, List<String>> map = Maps.newHashMap();
            map.put(dataId, Lists.newArrayList(tagIds));
            return tagDataRelationService.bulkHangTagForDataById(map, user.getTenantId(), describeApiName, ActionContextExt.of(user).getContext());
        } catch (MetadataServiceException e) {
            log.warn("batchBoundTags error, tenantId:{}, describeApiName:{}, dataId:{}", user.getTenantId(),
                    describeApiName, dataId, e);
            throw new MetaDataBusinessException(e);
        }

    }


    public Map<String, List<String>> multiDataBatchBoundTags(Map<String, List<String>> dataIdTagMap, String describeApiName, User user) {
        Map<String, List<String>> failDataMap = Maps.newHashMap();
        if (CollectionUtils.empty(dataIdTagMap)) {
            return failDataMap;
        }
        List<List<String>> partition = Lists.partition(Lists.newArrayList(dataIdTagMap.keySet()), 500);
        for (List<String> list : partition) {
            Map<String, List<String>> dataIdMap = Maps.newHashMap();
            list.forEach(x -> dataIdMap.put(x, dataIdTagMap.get(x)));
            Map<String, List<String>> partitionMap = bulkAppendTagForData(dataIdMap, describeApiName, user);
            failDataMap = Stream.concat(failDataMap.entrySet().stream(), partitionMap.entrySet().stream()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }
        return failDataMap;
    }

    private Map<String, List<String>> bulkAppendTagForData(Map<String, List<String>> dataIdTagMap, String describeApiName, User user) {
        try {
            boolean success = tagDataRelationService.bulkAppendTagForData(dataIdTagMap, user.getTenantId(), describeApiName, ActionContextExt.of(user).getContext());
            if (!success) {
                return dataIdTagMap;
            }
        } catch (Exception e) {
            log.warn("multiDataBatchBoundTags error, tenantId:{}, describeApiName:{}, dataIdList:{}", user.getTenantId(),
                    describeApiName, dataIdTagMap, e);
            return dataIdTagMap;
        }
        return Maps.newHashMap();
    }

    @Override
    public boolean bulkUpdateTags(List<String> dataIds, Collection<String> tagIds, String describeApiName, User user) {
        try {
            if (CollectionUtils.empty(dataIds)) {
                return true;
            }
            if (CollectionUtils.empty(tagIds)) {
                // 因为元数据bulkHangTagForDataById方法虽然是全量操作，但是又不能不传标签(即不支持删除全部标签)，所以如果前端
                // 没有传任何标签，需要单独调用删除数据全部标签接口
                return tagDataRelationService.bulkRemoveTagForData(dataIds, user.getTenantId(), describeApiName, ActionContextExt.of(user).getContext());
            }
            Map<String, List<String>> dataId2Tags = Maps.newHashMap();
            dataIds.forEach(dataId ->
                    dataId2Tags.put(dataId, Lists.newArrayList(tagIds)));
            return tagDataRelationService.bulkHangTagForDataById(dataId2Tags, user.getTenantId(), describeApiName, ActionContextExt.of(user).getContext());
        } catch (MetadataServiceException e) {
            log.warn("bulkUpdateTags error, tenantId:{}, describeApiName:{}, dataIds:{}", user.getTenantId(),
                    describeApiName, dataIds, e);
            throw new MetaDataBusinessException(e);
        }
    }


    @Override
    public ITagDescribe findTagGroupByName(String describeApiName, String tagGroupName, String tenantId) {
        try {
            List<ITagDescribe> tagDescribeList = Lists.newArrayList();
            tagDescribeList.add(tagDescribeService.findTagByType(tenantId, describeApiName, tagGroupName));
            tagDescribeList = translateTagDescribeList(tagDescribeList, false);
            return tagDescribeList.get(0);
        } catch (MetadataServiceException e) {
            log.warn("find tag group by name, tenantId:{}, describeApiName:{}, tagGroupName:{}", tenantId,
                    describeApiName, tagGroupName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ISubTagDescribe> findTagsByTagGroupId(String tagGroupId, String tenantId) {
        try {
            List<ISubTagDescribe> tagResult = subTagDescribeService.findSubTagByTagId(tenantId, tagGroupId);
            tagResult = translateSubTagDescribeList(tagResult, false);
            return CollectionUtils.nullToEmpty(tagResult);
        } catch (MetadataServiceException e) {
            log.warn("find tags by group id error, tenantId:{}, groupId:{}", tenantId, tagGroupId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    private static final List<String> supportedApiNames = Lists.newArrayList("AccountObj", "LeadsObj");

    @Override
    public boolean isSupportTag(String describeApiName, User user) {
        // 710前版本终端，客户和线索且开通营销通才显示标签按钮
        if (RequestUtil.isMobileRequestBeforeVersion(VERSION_710)) {
            Map<String, Boolean> map = licenseService.existModule(user.getTenantId(), Sets.newHashSet("marketing_strategy_stan_app", "marketing_strategy_pro_app"));
            return supportedApiNames.contains(describeApiName) &&
                    (map.getOrDefault("marketing_strategy_stan_app", false)
                            || map.getOrDefault("marketing_strategy_pro_app", false));
        }
        // 710后全部支持
        return true;
    }

    @Override
    public boolean hasAnyTagData(String describeApiName, User user) {
        try {
            // 使用元数据服务提供的方法检查该对象类型下是否存在标签数据
            return tagDataRelationService.existsDataTag(buildContext(user), user.getTenantId(), describeApiName);
        } catch (Exception e) {
            log.warn("check has any tag data error, tenantId:{}, describeApiName:{}",
                    user.getTenantId(), describeApiName, e);
            // 出现异常时返回true，保持原有逻辑
            return true;
        }
    }

    @Override
    public Map<String, Long> batchQuerySubTagCount(String tenantId, TagQueryInfo tagQueryInfo) {
        try {
            return subTagDescribeService.batchAggFindSubTagCountByQueryInfo(tenantId, tagQueryInfo);
        } catch (MetadataServiceException e) {
            log.warn("find sub tag count map by query error,tenantId:{},queryInfo:{}", tenantId, tagQueryInfo, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ISubTagDescribe> findSubTagByQueryInfo(String tenantId, String tagId, TagQueryInfo tagQueryInfo) {
        try {
            List<ISubTagDescribe> subTagDescribeList = subTagDescribeService.findSubTagByQueryInfo(tenantId, tagId, tagQueryInfo);
            subTagDescribeList = translateSubTagDescribeList(subTagDescribeList, false);
            return subTagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("find sub tag by query error,tenantId:{},queryInfo:{}", tenantId, tagQueryInfo, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ISubTagDescribe> findSubTagByQueryInfo(String tenantId, String tagId, TagQueryInfo tagQueryInfo, boolean isOnTime) {
        try {
            List<ISubTagDescribe> subTagDescribeList = subTagDescribeService.findSubTagByQueryInfo(tenantId, tagId, tagQueryInfo);
            subTagDescribeList = translateSubTagDescribeList(subTagDescribeList, isOnTime);
            return subTagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("find sub tag by query error,tenantId:{},queryInfo:{}", tenantId, tagQueryInfo, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ITagDescribe> findTagGroupByQueryInfo(String tenantId, TagQueryInfo tagQueryInfo) {
        try {
            List<ITagDescribe> tagDescribeList = tagDescribeService.findTagByQueryInfo(tenantId, tagQueryInfo);
            tagDescribeList = translateTagDescribeList(tagDescribeList, false);
            return tagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("find tag group by query error ,tenantId:{},queryInfo:{}", tenantId, tagQueryInfo, e);
            throw new MetaDataBusinessException(e);
        }

    }

    @Override
    public List<ITagDescribe> findTagGroupByQueryInfo(String tenantId, TagQueryInfo tagQueryInfo, boolean isOnTime) {
        try {
            List<ITagDescribe> tagDescribeList = tagDescribeService.findTagByQueryInfo(tenantId, tagQueryInfo);
            tagDescribeList = translateTagDescribeList(tagDescribeList, isOnTime);
            return tagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("find tag group by query error ,tenantId:{},queryInfo:{}", tenantId, tagQueryInfo, e);
            throw new MetaDataBusinessException(e);
        }

    }


    // ---------------------------------------- 标签管理使用


    @Override
    public List<ITagDescribe> findTagGroups(String tenantId) {
        try {
            List<ITagDescribe> tagDescribeList = tagDescribeService.findAllTag(tenantId);
            tagDescribeList = translateTagDescribeList(tagDescribeList, false);
            return tagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("find all tag groups error, tenantId:{}", tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ITagDescribe> findTagGroups(String tenantId, boolean isOnTime) {
        try {
            List<ITagDescribe> tagDescribeList = tagDescribeService.findAllTag(tenantId);
            tagDescribeList = translateTagDescribeList(tagDescribeList, isOnTime);
            return tagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("find all tag groups error, tenantId:{}", tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ITagDescribe createTagGroup(String name,
                                       String apiName,
                                       List<String> describeApiNames,
                                       Boolean isAppliedToAll,
                                       User user) {
        return createTagGroup(name, apiName, describeApiNames, isAppliedToAll, user, "custom");
    }

    @Override
    @Transactional
    public ITagDescribe createTagGroup(String name,
                                       String apiName,
                                       List<String> describeApiNames,
                                       Boolean isAppliedToAll,
                                       List<ISubTagDescribe> subTagDescribes,
                                       String tagDefineType,
                                       Ranges ranges,
                                       Boolean isMutex,
                                       String groupDescription,
                                       User user) {

        try {
            ITagDescribe group = new TagDescribe();
            group.setApiName(apiName);
            group.setTagRange(describeApiNames);
            group.setIsAll(isAppliedToAll);  // 是否适用全部
            group.setTagDefineType(tagDefineType);
            group.setType(name);
            group.setGroupDescription(groupDescription);
            group.setTenantId(user.getTenantId());
            group.setRanges(ranges);
            group.setIsMutex(isMutex);
            ITagDescribe tagDescribe = tagDescribeService.create(group, ActionContextExt.of(user).getContext());
            //如果标签信息不为空，则批量创建标签
            if (Objects.nonNull(tagDescribe) && CollectionUtils.notEmpty(subTagDescribes)) {
                subTagDescribes.forEach(x -> x.setTagId(tagDescribe.getId()));
                subTagDescribeService.bulkCreateSubTag(subTagDescribes, buildContext(user));
            }
            return tagDescribe;
        } catch (MetadataServiceException e) {
            log.warn("create tag group error, user:{}", user, e);
            throw new MetaDataBusinessException(e);
        }

    }

    @Override
    public ITagDescribe createTagGroup(User user) {
        return createTagGroup(I18NExt.text(I18NKey.DEFAULT_GROUP), "default_group", Lists.newArrayList(), Boolean.TRUE, user, "package");
    }

    private ITagDescribe createTagGroup(String name,
                                        String apiName,
                                        List<String> describeApiNames,
                                        Boolean isAppliedToAll,
                                        User user,
                                        String defineType) {
        try {

            ITagDescribe group = new TagDescribe();
            group.setApiName(apiName);
            group.setTagRange(describeApiNames);
            group.setIsAll(isAppliedToAll);  // 是否适用全部
            group.setTagDefineType(defineType);
            group.setType(name);
            group.setTenantId(user.getTenantId());
            return tagDescribeService.create(group, ActionContextExt.of(user).getContext());
        } catch (MetadataServiceException e) {
            log.warn("create tag group error, user:{}", user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Map<String, Long> findTagCountByGroup(String tenantId) {
        try {
            return subTagDescribeService.batchAggFindSubTagCount(tenantId);
        } catch (MetadataServiceException e) {
            log.warn("find count error, tenantId:{}", tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ITagDescribe updateTagGroupName(ITagDescribe group, User user) {
        try {
            ITagDescribe updateGroup = tagDescribeService.update(group, buildContext(user));
            updateTagDescribe(group, user.getTenantId());
            return updateGroup;
        } catch (MetadataServiceException e) {
            log.warn("update tag group error, user:{}", user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean deleteTagGroup(String groupId, String tenantId) {
        try {
            return tagDescribeService.deleteTag(tenantId, groupId);
        } catch (MetadataServiceException e) {
            log.warn("delete tag group error,groupId:{}, tenantId:{}", groupId, tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ISubTagDescribe createTag(ISubTagDescribe tag, User user) {
        try {
            validateTagGroup(tag.getTagId(), user);
            return subTagDescribeService.createSubTag(tag, buildContext(user));
        } catch (MetadataServiceException e) {
            log.warn("create tag error,tag:{}, user:{}", tag, user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ISubTagDescribe updateTag(ISubTagDescribe tag, Collection<String> fields, User user) {
        try {
            validateTag(tag.getId(), user);
            validateTagGroup(tag.getTagId(), user);
            validateTagName(tag, user);
            updateSubTagDescribe(tag, user.getTenantId());
            return subTagDescribeService.updateSubTag(tag, fields, buildContext(user));
        } catch (MetadataServiceException e) {
            log.warn("update tag error,tag:{}, user:{}", tag, user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    private void validateTagName(ISubTagDescribe subTagDescribe, User user) {
        ISubTagDescribe subTagBySubTagName;
        try {
            subTagBySubTagName = subTagDescribeService.findSubTagBySubTagName(user.getTenantId(), subTagDescribe.getTagId(), subTagDescribe.getName(), subTagDescribe.getSupTag());
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e);
        }
        if (subTagBySubTagName != null && !subTagBySubTagName.getId().equals(subTagDescribe.getId())) {
            throw new ValidateException("Illegal arguments, the TagName already exists");
        }
    }

    private void validateTag(String tagId, User user) {
        ISubTagDescribe subTagDescribe = findTagById(tagId, user);
        if (Objects.isNull(subTagDescribe)) {
            throw new ValidateException("Illegal arguments, TagId should exist");
        }
    }

    private void validateTagGroup(String groupId, User user) {
        List<ITagDescribe> tagDescribes = findTagGroupsByIds(user.getTenantId(), Lists.newArrayList(groupId));
        if (CollectionUtils.empty(tagDescribes)) {
            throw new ValidateException("Illegal arguments, GroupId should exist");
        }
    }

    @Override
    public boolean deleteTag(String groupId, String tagId, String tenantId) {
        try {
            return subTagDescribeService.deleteSubTag(tenantId, groupId, tagId);
        } catch (MetadataServiceException e) {
            log.warn("delete tag error,tagId:{}, tenantId:{}", tagId, tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    private IActionContext buildContext(User user) {
        return ActionContextExt.of(user).getContext();
    }

    @Override
    public ISubTagDescribe createTag(String describeApiName, String tagName, String groupId, User user, int grade) {
        try {
            ISubTagDescribe tag = new SubTagDescribe();
            tag.setGrade(grade);
            tag.setDescribeApiName(describeApiName);
            tag.setName(tagName);
            tag.setTagId(groupId);
            return subTagDescribeService.createSubTag(tag, buildContext(user));
        } catch (MetadataServiceException e) {
            log.warn("create tag error, userInfo:{}", user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ISubTagDescribe createTag(String describeApiName, String tagName, String groupId, User user) {
        return createTag(describeApiName, tagName, groupId, user, 1);
    }

    @Override
    public ISubTagDescribe updateTag(String describeApiName, String tagId, String tagName, User user) {
        try {
            List<ISubTagDescribe> tags = subTagDescribeService.findBySubTagIds(user.getTenantId(), Lists.newArrayList(tagId));
            ISubTagDescribe tag = tags.get(0);
            updateSubTagDescribe(tag, user.getTenantId());
            tag.setName(tagName);
            return subTagDescribeService.partialUpdateSubTag(tag, buildContext(user));
        } catch (MetadataServiceException e) {
            log.warn("update tag error, userInfo:{}", user, e);
            throw new MetaDataBusinessException(e);
        }

    }

    @Override
    public boolean disableTag(String tagId, User user) {
        try {
            subTagDescribeService.disableSubTag(tagId, buildContext(user));
            return true;
        } catch (MetadataServiceException e) {
            log.warn("disable tag error,tagId:{}, user:{}", tagId, user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean enableTag(String tagId, User user) {
        try {
            subTagDescribeService.enableSubTag(tagId, buildContext(user));
            return true;
        } catch (MetadataServiceException e) {
            log.warn("enable tag error,tagId:{}, user:{}", tagId, user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ISubTagDescribe> findTagsByKeyword(String keyword, User user) {
        try {
            List<ISubTagDescribe> subTagDescribeList = subTagDescribeService.findSubTagListByName(user.getTenantId(), keyword);
            subTagDescribeList = translateSubTagDescribeList(subTagDescribeList, false);
            return subTagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("find tags by keyword error, user:{},keyword:{}", user, keyword, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public ITagDescribe findTagGroupByApiName(String apiName, User user) {
        try {
            return tagDescribeService.findTagByApiName(user.getTenantId(), apiName);
        } catch (MetadataServiceException e) {
            log.warn("find tag group by apiName error,apiName:{}, user:{}", apiName, user, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<DataAndSubTag> findAllTagByBulkDataId(String apiName, Set<String> dataIds, User user) {
        if (CollectionUtils.empty(dataIds)) {
            return Collections.emptyList();
        }
        try {
            List<DataAndSubTag> subTags = tagDataRelationService.findAllTagByBulkDataId(ActionContextExt.of(user).getContext(), user.getTenantId(), Lists.newArrayList(dataIds), apiName);
            I18N.I18NContext context = I18N.getContext();
            String tenantId = String.valueOf(context.getTenantId());
            if (!licenseService.isSupportMultiLanguage(tenantId)) {
                return subTags;
            }
            subTags.removeAll(Collections.singleton(null));
            if (CollectionUtils.empty(subTags)) {
                return subTags;
            }

            if (!licenseService.isSupportMultiLanguage(tenantId)) {
                return subTags;
            }
            String language = context.getLanguage();
            List<ISubTagDescribe> subTagDescribeList = Lists.newArrayList();

            List<ISubTagDescribe> finalSubTagDescribeList = subTagDescribeList;
            subTags.forEach(subList -> {
                if (!Objects.isNull(subList) && CollectionUtils.notEmpty(subList.getResultList())) {
                    subList.getResultList().forEach(x -> finalSubTagDescribeList.add((ISubTagDescribe) x));
                }
            });

            List<String> i18Key = finalSubTagDescribeList.stream()
                    .map(x -> TranslateI18nUtils.getTransTagManagerSubTag(x.getTagApiName()))
                    .collect(Collectors.toList());

            Map<String, Localization> i18Map = i18nSettingService.getLocalization(i18Key, tenantId, true, false);

            if (CollectionUtils.notEmpty(i18Map)) {
                subTags.forEach(subList -> {
                    if (!Objects.isNull(subList) && CollectionUtils.notEmpty(subList.getResultList())) {
                        subList.getResultList().forEach(x -> {
                            String nameKey = TranslateI18nUtils.getTransTagManagerSubTag(((ISubTagDescribe) x).getTagApiName());
                            if (i18Map.containsKey(nameKey)) {
                                ((ISubTagDescribe) x).setName(i18Map.get(nameKey).get(language, ((ISubTagDescribe) x).getName()));
                            }
                        });
                    }

                });
            }

            return subTags;

        } catch (MetadataServiceException e) {
            log.warn("findAllTagByBulkDataId error,apiName:{}, user:{}, dataIds:{}", apiName, user, dataIds, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean appendTags(String dataId, Collection<String> tagIds, String describeApiName, User user) {
        try {
            if (CollectionUtils.empty(tagIds)) {
                return true;
            }
            Map<String, List<String>> map = Maps.newHashMap();
            map.put(dataId, Lists.newArrayList(tagIds));
            return tagDataRelationService.bulkAppendTagForData(map, user.getTenantId(), describeApiName, ActionContextExt.of(user).getContext());
        } catch (MetadataServiceException e) {
            log.warn("batchAppendTags error, tenantId:{}, describeApiName:{}, dataId:{}, tagIds{}", user.getTenantId(),
                    describeApiName, dataId, tagIds, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean bulkAppendTags(Map<String, List<String>> dataIdTagMap, String describeApiName, User user) {
        try {
            if (CollectionUtils.empty(dataIdTagMap)) {
                return true;
            }
            return tagDataRelationService.bulkAppendTagForData(dataIdTagMap, user.getTenantId(), describeApiName, ActionContextExt.of(user).getContext());
        } catch (MetadataServiceException e) {
            log.warn("bulkAppendTags error, tenantId:{}, describeApiName:{}, tagMap:{}", user.getTenantId(),
                    describeApiName, dataIdTagMap, e);
            throw new MetaDataBusinessException(e);
        }
    }


    @Override
    public void enableOrDisableTagGroup(ServiceContext context, List<String> groupIds, boolean enable) {
        try {
            if (CollectionUtils.empty(groupIds)) {
                return;
            }
            if (enable) {
                tagDescribeService.enable(groupIds, ActionContextExt.of(context.getUser()).getContext());
            } else {
                tagDescribeService.disable(groupIds, ActionContextExt.of(context.getUser()).getContext());
            }
        } catch (MetadataServiceException e) {
            log.warn("enableOrDisableTagGroup error, tenantId:{}, groupIds:{}, enable{}", context.getTenantId(),
                    groupIds, enable, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, List<ISubTagDescribe>> bulkFindSubTagByTagIds(ServiceContext context, List<String> groupIdList) {
        if (CollectionUtils.empty(groupIdList)) {
            return Maps.newHashMap();
        }
        Map<String, List<ISubTagDescribe>> groupIdSubTagMap = Maps.newHashMap();
        Map<String, List<ISubTagDescribe>> returnGroupIdSubTagMap = Maps.newHashMap();
        try {
            groupIdSubTagMap = subTagDescribeService.bulkFindSubTagByTagIds(context.getTenantId(), groupIdList);
            groupIdSubTagMap.forEach((k, v) -> {
                v = translateSubTagDescribeList(v, false);
                returnGroupIdSubTagMap.put(k, v);
            });
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
        return returnGroupIdSubTagMap;
    }

    @Override
    public Map<String, List<ISubTagDescribe>> bulkFindSubTagByTagIds(ServiceContext context, List<String> groupIdList, boolean isOnTime) {
        if (CollectionUtils.empty(groupIdList)) {
            return Maps.newHashMap();
        }
        Map<String, List<ISubTagDescribe>> groupIdSubTagMap = Maps.newHashMap();
        Map<String, List<ISubTagDescribe>> returnGroupIdSubTagMap = Maps.newHashMap();
        try {
            groupIdSubTagMap = subTagDescribeService.bulkFindSubTagByTagIds(context.getTenantId(), groupIdList);
            groupIdSubTagMap.forEach((k, v) -> {
                v = translateSubTagDescribeList(v, isOnTime);
                returnGroupIdSubTagMap.put(k, v);
            });
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
        return returnGroupIdSubTagMap;
    }

    @Override
    public List<ITagDescribe> findAllTagGroupByObjectApiNameInObjRange(String objectApiName, String tenantId) {
        try {
            List<ITagDescribe> tagGroups = tagDescribeService.findAllTag(tenantId, objectApiName);
            if (CollectionUtils.empty(tagGroups)) {
                return Lists.newArrayList();
            }
            tagGroups = translateTagDescribeList(tagGroups, false);
            return tagGroups;
        } catch (MetadataServiceException e) {
            log.warn("Find all tags fail,tenantId:{}, apiName;{}", tenantId, objectApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<ISubTagDescribe> findSubTagBySubTagIds(User user, Set<String> subTagIds) {
        try {
            List<ISubTagDescribe> subTagDescribeList = subTagDescribeService.findBySubTagIds(user.getTenantId(), Lists.newArrayList(subTagIds));
            subTagDescribeList = translateSubTagDescribeList(subTagDescribeList, false);
            return subTagDescribeList;
        } catch (MetadataServiceException e) {
            log.warn("findSubTagBySubTagIds error, tenantId:{}, subTagIds:{}", user.getTenantId(), subTagIds, e);
            throw new MetaDataBusinessException(e);
        }
    }

    private List<ISubTagDescribe> translateSubTagDescribeList(List<ISubTagDescribe> subTagDescribeList, boolean isOnTime) {
        subTagDescribeList.removeAll(Collections.singleton(null));
        if (CollectionUtils.empty(subTagDescribeList)) {
            return subTagDescribeList;
        }
        I18N.I18NContext context = I18N.getContext();
        String tenantId = String.valueOf(context.getTenantId());
        if (!licenseService.isSupportMultiLanguage(tenantId)) {
            return subTagDescribeList;
        }
        String language = context.getLanguage();
        List<String> i18Key = subTagDescribeList.stream()
                .map(x -> TranslateI18nUtils.getTransTagManagerSubTag(x.getTagApiName()))
                .collect(Collectors.toList());

        Map<String, Localization> i18Map = i18nSettingService.getLocalization(i18Key, tenantId, true, isOnTime);
        if (CollectionUtils.notEmpty(i18Map)) {
            subTagDescribeList.forEach(x -> {
                String nameKey = TranslateI18nUtils.getTransTagManagerSubTag(x.getTagApiName());
                if (i18Map.containsKey(nameKey) && Objects.nonNull(i18Map.get(nameKey))) {
                    x.setName(i18Map.get(nameKey).get(language, x.getName()));
                }
            });
        }
        return subTagDescribeList;
    }


    private List<ITagDescribe> translateTagDescribeList(List<ITagDescribe> tagDescribeList, boolean isOnTime) {
        tagDescribeList.removeAll(Collections.singleton(null));
        if (CollectionUtils.empty(tagDescribeList)) {
            return tagDescribeList;
        }
        I18N.I18NContext context = I18N.getContext();
        String tenantId = String.valueOf(context.getTenantId());
        if (!licenseService.isSupportMultiLanguage(tenantId)) {
            return tagDescribeList;
        }
        String language = context.getLanguage();
        List<String> i18Key = tagDescribeList.stream()
                .map(x -> TranslateI18nUtils.getTransTagManagerTagGroup(x.getApiName()))
                .collect(Collectors.toList());

        Map<String, Localization> i18Map = i18nSettingService.getLocalization(i18Key, tenantId, true, isOnTime);
        if (CollectionUtils.notEmpty(i18Map)) {
            tagDescribeList.forEach(x -> {
                String nameKey = TranslateI18nUtils.getTransTagManagerTagGroup(x.getApiName());
                if (i18Map.containsKey(nameKey) && Objects.nonNull(i18Map.get(nameKey))) {
                    x.setType(i18Map.get(nameKey).get(language, x.getType()));
                }
            });
        }
        return tagDescribeList;
    }

    private void updateSubTagDescribe(ISubTagDescribe iSubTagDescribe, String tenantId) {
        Map<String, String> keyToNewName = Maps.newHashMap();
        keyToNewName.put(TranslateI18nUtils.getTransTagManagerSubTag(iSubTagDescribe.getTagApiName()), iSubTagDescribe.getName());
        i18nSettingService.syncTransValue(keyToNewName, I18N.getContext().getLanguage(), tenantId);
    }

    private void updateTagDescribe(ITagDescribe iTagDescribe, String tenantId) {
        Map<String, String> keyToNewName = Maps.newHashMap();
        keyToNewName.put(TranslateI18nUtils.getTransTagManagerTagGroup(iTagDescribe.getApiName()), iTagDescribe.getType());
        i18nSettingService.syncTransValue(keyToNewName, I18N.getContext().getLanguage(), tenantId);
    }

}

