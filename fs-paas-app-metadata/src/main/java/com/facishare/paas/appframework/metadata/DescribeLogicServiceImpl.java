package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.operate.report.OperateReportUtil;
import com.facishare.crm.operate.report.model.ProductLine;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.PlatServiceProxy;
import com.facishare.paas.appframework.common.service.dto.OrganizationStatus;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.ManageGroupType;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.config.ApplicationLayeredGrayService;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.exception.ObjectDefNotFoundError;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.ref.RefMessage;
import com.facishare.paas.appframework.core.model.ref.RefMessage.Ref;
import com.facishare.paas.appframework.core.model.ref.RefMessage.Refs;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowService;
import com.facishare.paas.appframework.flow.StageThrusterProxy;
import com.facishare.paas.appframework.flow.dto.HasStageInstance;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.quota.TenantLicenseInfo;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderLogicService;
import com.facishare.paas.appframework.metadata.config.ObjectListManageDefineConfig;
import com.facishare.paas.appframework.metadata.config.util.ProductCategoryConfigUtils;
import com.facishare.paas.appframework.metadata.dto.*;
import com.facishare.paas.appframework.metadata.exception.FieldNotExistException;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.appframework.metadata.fieldextra.FieldBackgroundExtraService;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.TableComponentRender;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.menu.MenuCommonService;
import com.facishare.paas.appframework.metadata.mtresource.IMtResourceService;
import com.facishare.paas.appframework.metadata.objects.DescribeChangeEvent;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.appframework.metadata.options.OptionSetLogicService;
import com.facishare.paas.appframework.metadata.options.SelectFieldDependenceLogicService;
import com.facishare.paas.appframework.metadata.plugin.FunctionPluginConfLogicService;
import com.facishare.paas.appframework.metadata.publicobject.module.FieldPublicFlagHandler;
import com.facishare.paas.appframework.metadata.publicobject.verify.PublicObjectEnableJobVerify;
import com.facishare.paas.appframework.metadata.publicobject.verify.VerifyResult;
import com.facishare.paas.appframework.metadata.reference.FieldRef;
import com.facishare.paas.appframework.metadata.reference.RefFieldService;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.metadata.relation.SourceTypes;
import com.facishare.paas.appframework.metadata.repository.model.MtOptionSet;
import com.facishare.paas.appframework.metadata.util.DefObjUtil;
import com.facishare.paas.appframework.metadata.util.OperateReport;
import com.facishare.paas.appframework.metadata.util.ProductUtil;
import com.facishare.paas.appframework.metadata.util.SaleContractUtil;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.privilege.EnterpriseRelationLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.ObjectDataPermissionInfo;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.ActionContextKey;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.action.IActionDescribe;
import com.facishare.paas.metadata.api.checker.CheckerResult;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.i18n.MetadataI18NKey;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.service.*;
import com.facishare.paas.metadata.common.MetadataContext;
import com.facishare.paas.metadata.exception.ErrorCode;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.CopyOnWriteMap;
import com.fxiaoke.bizconf.arg.ConfigArg;
import com.fxiaoke.bizconf.bean.ValueType;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.facishare.crm.common.exception.CRMErrorCode.FS_CRM_DEFOBJ_CHECKED_DUPLICATE_DISPLAY_NAME;
import static com.facishare.crm.openapi.Utils.*;
import static com.facishare.crm.openapi.Utils.DEPARTMENT_OBJ_API_NAME;
import static com.facishare.paas.appframework.common.util.AppFrameworkConfig.isDateTimeSupportNotUseMultitimeZoneTenant;
import static com.facishare.paas.appframework.common.util.AppFrameworkConfig.isEnableCheckEnterpriseResourcesQuote;
import static com.facishare.paas.appframework.core.i18n.I18NKey.CALC_JOB_FOR_MAX_COUNT_DATA_TOO_LARGE;
import static com.facishare.paas.appframework.metadata.ObjectDescribeExt.*;
import static com.facishare.paas.appframework.metadata.dto.tag.Factor.CALC_JOB;
import static com.facishare.paas.appframework.metadata.dto.tag.Factor.MAX_COUNT;
import static com.facishare.paas.appframework.metadata.expansion.DescribeExtra.validateFieldPromptLength;
import static com.facishare.paas.common.util.UdobjConstants.*;
import static com.facishare.paas.metadata.api.describe.IFieldType.RECORD_TYPE;
import static com.facishare.paas.metadata.api.describe.IObjectDescribe.VISIBLE_SCOPE_BI;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

/**
 * Created by zhouwr on 2017/10/31
 */
@Slf4j
@Service("describeLogicService")
public class DescribeLogicServiceImpl implements DescribeLogicService {
    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Autowired
    private DefObjLifeStatusService defObjLifeStatusService;
    @Autowired
    private DefObjUtil defObjUtil;
    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private ISearchTemplateService searchTemplateService;
    @Autowired
    private LogService logService;
    @Autowired
    private IFieldService fieldService;
    @Autowired
    private IObjectDataProxyService dataProxyService;
    @Autowired
    private RecordTypeLogicService recordTypeLogicService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private DataPrivilegeService dataPrivilegeService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private ButtonLogicService buttonLogicService;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private MenuCommonService menuCommonService;
    @Autowired
    private AutoNumberLogicService autoNumberLogicService;
    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;
    @Autowired
    private JobScheduleService jobScheduleService;
    @Autowired
    private ApprovalFlowService approvalFlowService;
    @Autowired
    private StageThrusterProxy proxy;
    @Autowired
    private AutoNumberService autoNumberService;
    @Autowired
    private CustomButtonService customButtonService;
    @Autowired
    private IMetadataMultiCurrencyService metadataMultiCurrencyService;
    @Autowired
    private MultiCurrencyLogicService multiCurrencyLogicService;

    @Autowired
    private IMtResourceService mtResourceService;
    @Autowired
    private MetaDataService metaDataService;
    @Autowired
    private FunctionLogicService functionLogicService;

    @Autowired
    private EnterpriseRelationLogicService enterpriseRelationLogicService;
    @Autowired
    private OptionSetLogicService optionSetLogicService;
    @Autowired
    private RedissonService redissonService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private IObjectFieldDescribeExtService fieldDescribeExtService;
    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private DescribeChangeEvent describeChangeEvent;
    @Autowired
    private SelectFieldDependenceLogicService selectFieldDependenceLogicService;
    @Autowired
    private OptionalFeaturesService optionalFeaturesService;
    @Autowired
    private MaskFieldLogicService maskFieldLogicService;
    @Autowired
    private PlatServiceProxy platServiceProxy;
    @Autowired
    private ManageGroupService manageGroupService;
    @Autowired
    private ObjectConvertRuleService objectConvertRuleService;
    @Autowired
    private ObjectArchiveService objectArchiveService;
    @Autowired
    private DuplicatedSearchService duplicatedSearchService;
    @Autowired
    private RefFieldService refFieldService;
    @Autowired
    private FieldBackgroundExtraService fieldBackgroundExtraService;
    @Autowired
    private PublicObjectEnableJobVerify publicObjectEnableJobVerify;
    @Autowired
    private ChangeOrderLogicService changeOrderLogicService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private ApplicationLayeredGrayService applicationLayeredGrayService;
    @Autowired
    private FieldPublicFlagHandler fieldPublicFlagHandler;
    @Resource(name = "functionPluginConfLogicService")
    private FunctionPluginConfLogicService functionPluginConfLogicService;


    private static ObjectIcon objectIcon = new ObjectIcon();
    private static String tmpLang = "zh-CN";
    private static String IS_ASC = "is_asc";
    private static List<String> optionType = Lists.newArrayList(IFieldType.SELECT_ONE, IFieldType.SELECT_MANY,
            IFieldType.TRUE_OR_FALSE, IFieldType.RECORD_TYPE);
    private static List<String> numericalType = Lists.newArrayList(IFieldType.CURRENCY, IFieldType.NUMBER, IFieldType.PERCENTILE);
    private static List<String> containsPreFieldType = Lists.newArrayList(IFieldType.RICH_TEXT, IFieldType.HTML_RICH_TEXT);

    static {
        ConfigFactory.getConfig("fs-crm-icon-path", (config) -> {
            String iconPath = config.get("icon_list");
            objectIcon = JSON.parseObject(iconPath, ObjectIcon.class);
        });
    }

    @Override
    public IObjectDescribe findObject(String tenantId, String apiName) {
        try {
            IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(
                    tenantId, apiName, getActionContext(new User(tenantId, null)));
            if (describe == null) {
                throw new ObjectDefNotFoundError(I18N.text(I18NKey.OBJECT_NOT_EXIST, apiName));
            }
            //            processSpecialFormulaFields(describe);
            return describe;
        } catch (MetadataServiceException e) {
            log.warn("findObject error,tenantId:{},apiName:{}", tenantId, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectDescribe findObjectWithDefaultLang(String tenantId, String apiName) {
        try {
            IActionContext actionContext = ActionContextExt.of(getActionContext(new User(tenantId, null)))
                    .setDefaultValueFlag()
                    .getContext();
            IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(
                    tenantId, apiName, actionContext);
            if (describe == null) {
                throw new ObjectDefNotFoundError(I18N.text(I18NKey.OBJECT_NOT_EXIST, apiName));
            }
            //            processSpecialFormulaFields(describe);
            return describe;
        } catch (MetadataServiceException e) {
            log.warn("findObject error,tenantId:{},apiName:{}", tenantId, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectDescribe findObjectWithoutCopy(String tenantId, String apiName) {
        try {
            IActionContext context = getActionContext(new User(tenantId, null));
            ActionContextExt.of(context).doNotDeepCopyDescribe();
            IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(
                    tenantId, apiName, context);
            if (describe == null) {
                throw new ObjectDefNotFoundError(I18N.text(I18NKey.OBJECT_NOT_EXIST, apiName));
            }
            return describe;
        } catch (MetadataServiceException e) {
            log.warn("findObjectWithoutCopy failed,tenantId:{},apiName:{}", tenantId, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectDescribe findObjectWithoutCopyIfGray(String tenantId, String apiName) {
        if (AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)) {
            return findObjectWithoutCopy(tenantId, apiName);
        }
        return findObject(tenantId, apiName);
    }

    @Override
    public IObjectDescribe findObjectUseThreadLocalCache(String tenantId, String apiName) {
        return ContextCacheUtil.getOrElse(tenantId, apiName, () -> findObject(tenantId, apiName));
    }

    @Override
    public IObjectDescribe findObjectWithoutCopyUseThreadLocalCache(String tenantId, String apiName) {
        return ContextCacheUtil.getOrElse(tenantId, apiName, () -> findObjectWithoutCopy(tenantId, apiName));
    }

    //需要实时计算的计算字段不支持筛选
//    private void processSpecialFormulaFields(IObjectDescribe describe) {
//        ObjectDescribeExt.of(describe).getFormulaFields().stream()
//                .filter(x -> ExpressionFactory.createExpression(describe, x, true).needRealTimeCalculate(describe.getTenantId()))
//                .forEach(x -> {
//                    //部分不落地的计算字段也支持筛选
//                    if (Strings.isNullOrEmpty(x.getFilterExpression())) {
//                        x.setIndex(false);
//                    }
//                });
//    }

    @Override
    public IObjectDescribe findObjectById(String tenantId, String id) {
        try {
            IObjectDescribe describe = objectDescribeService.findById(tenantId, id, getActionContext(new User(tenantId, null)));
            if (null == describe) {
                throw new ObjectDefNotFoundError(I18N.text(I18NKey.OBJECT_NOT_EXIST, id));
            }
//            processSpecialFormulaFields(describe);
            return describe;
        } catch (MetadataServiceException e) {
            log.warn("findObject error, tenantId:{},describeId:{}", tenantId, id, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectDescribe findObjectIncludeDeleted(String tenantId, String apiName) {
        try {
            IObjectDescribe describe = objectDescribeService.findByTenantIdAndObjectDescribeApiNameIncludedDeletedData(
                    tenantId, apiName, getActionContext(new User(tenantId, null)));
            if (describe == null) {
                throw new ObjectDefNotFoundError(I18N.text(I18NKey.OBJECT_NOT_EXIST, apiName));
            }
//            processSpecialFormulaFields(describe);
            return describe;
        } catch (MetadataServiceException e) {
            log.warn("findObjectIncludeDeleted error,tenantId:{},apiName:{}", tenantId, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectDescribe findObjectByRevision(String tenantId, String apiName) {
        return findObject(tenantId, apiName);
    }

    @Override
    public List<IObjectDescribe> findAllObjectsByTenantId(String tenantId, String describeDefineType, boolean isOnlyActivate,
                                                          boolean isExcludeDetailObj, boolean isExcludeDetailWithMasterCreated,
                                                          boolean isAsc, String sourceInfo) {
        StopWatch stopWatch = StopWatch.create(this.getClass().getSimpleName() + "#findObjectsByTenantId");
        try {
            Document example = new Document();
            example.put(IObjectDescribe.PACKAGE, "CRM");
            if (!Strings.isNullOrEmpty(describeDefineType)) {
                example.put(IObjectDescribe.DEFINE_TYPE, describeDefineType);
            }
            if (isOnlyActivate) {
                example.put(IObjectDescribe.IS_ACTIVE, Boolean.TRUE);
            }
            if (isExcludeDetailObj) {
                example.put("exclude_detail", Boolean.TRUE);
            }
            if (isExcludeDetailWithMasterCreated) {
                example.put("exclude_detail_with_master_created", Boolean.TRUE);
            }
            if (isAsc) {
                example.put(IS_ASC, Boolean.TRUE);
            }

            List<IObjectDescribe> describeList = findByExample(tenantId, example);
            stopWatch.lap("findByExample");
            List<IObjectDescribe> result = Lists.newArrayList();
            if (!Objects.equals(describeDefineType, DEFINE_TYPE_CUSTOM)) {
                Set<String> availableObject = queryAvailableObject(tenantId);
                List<IObjectDescribe> preDefineDescribeList = describeList.stream().filter(x -> availableObject.contains(x.getApiName())).collect(Collectors.toList());
                preDefineDescribeList = CollectionUtils.sortByGivenOrder(preDefineDescribeList, ObjectDescribeExt.getObjectOrderList(), IObjectDescribe::getApiName);
                result.addAll(preDefineDescribeList);
            }
            result.addAll(describeList.stream().filter(x -> DEFINE_TYPE_CUSTOM.equals(x.getDefineType())).collect(Collectors.toList()));

            //根据业务处理特殊对象
            handleObjectsWith(User.systemUser(tenantId), sourceInfo, result);
            // 产品分类灰度处理
            ProductCategoryConfigUtils.productCategoryGrayHandle(tenantId, result);
            return result;
        } finally {
            stopWatch.logSlow(150);
        }
    }

    @Override
    public List<IObjectDescribe> findObjectsByTenantId(User user, String describeDefineType, boolean isOnlyActivate,
                                                       boolean isExcludeDetailObj, boolean isExcludeDetailWithMasterCreated,
                                                       boolean isAsc, String sourceInfo) {
        ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder.builder()
                .user(user)
                .describeDefineType(describeDefineType)
                .isOnlyActivate(isOnlyActivate)
                .isExcludeDetailObj(isExcludeDetailObj)
                .isExcludeDetailWithMasterCreated(isExcludeDetailWithMasterCreated)
                .isAsc(isAsc)
                .sourceInfo(sourceInfo)
                .build();
        return findObjectsByTenantId(objectDescribeFinder);
    }

    @Deprecated
    @Override
    public List<IObjectDescribe> findObjectsByTenantId(User user, String describeDefineType, boolean isOnlyActivate, boolean isExcludeDetailObj, boolean isExcludeDetailWithMasterCreated, boolean isAsc, String sourceInfo, ObjectType objectType) {
        boolean includeBigObject = objectType == ObjectType.ALL || objectType == ObjectType.BIG;
        Set<String> visibleScopeSetSet = includeBigObject ? Sets.newHashSet(IObjectDescribe.BIG_OBJECT_VISIBLE_SCOPE) : null;
        ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder.builder()
                .user(user)
                .describeDefineType(describeDefineType)
                .isOnlyActivate(isOnlyActivate)
                .isExcludeDetailObj(isExcludeDetailObj)
                .isExcludeDetailWithMasterCreated(isExcludeDetailWithMasterCreated)
                .isAsc(isAsc)
                .sourceInfo(sourceInfo)
                .visibleScope(visibleScopeSetSet)
                .build();
        return findObjectsByTenantId(objectDescribeFinder);
    }

    @Override
    public List<IObjectDescribe> findObjectsByTenantId(ObjectDescribeFinder objectDescribeFinder) {
        StopWatch stopWatch = StopWatch.create(this.getClass().getSimpleName() + "#findObjectsByTenantId");
        try {
            Document example = new Document();
            example.put(IObjectDescribe.PACKAGE, "CRM");
            if (!Strings.isNullOrEmpty(objectDescribeFinder.getDescribeDefineType())) {
                example.put(IObjectDescribe.DEFINE_TYPE, objectDescribeFinder.getDescribeDefineType());
            }
            if (objectDescribeFinder.isOnlyActivate()) {
                example.put(IObjectDescribe.IS_ACTIVE, Boolean.TRUE);
            }
            if (objectDescribeFinder.isExcludeDetailObj()) {
                example.put("exclude_detail", Boolean.TRUE);
            }
            if (objectDescribeFinder.isExcludeDetailWithMasterCreated()) {
                example.put("exclude_detail_with_master_created", Boolean.TRUE);
            }
            if (objectDescribeFinder.isAsc()) {
                example.put(IS_ASC, Boolean.TRUE);
            }
            example.put("include_changed_describe", BooleanUtils.isNotFalse(objectDescribeFinder.getIncludeChangeOrderObject()));

            IActionContext context = ActionContextExt.of(objectDescribeFinder.getUser())
                    .getContext();
            Set<String> visibleScopeSet = CollectionUtils.nullToEmpty(objectDescribeFinder.getVisibleScope());
            context.setIncludeVisibleScopeSet(visibleScopeSet);
            context.setIsOnlyVisibleScope(objectDescribeFinder.isOnlyVisibleScope());

            List<IObjectDescribe> describeList = findByExample(objectDescribeFinder.getUser().getTenantId(), example, context);
            stopWatch.lap("findByExample");
            List<IObjectDescribe> result = Lists.newArrayList();
            if (!Objects.equals(objectDescribeFinder.getDescribeDefineType(), DEFINE_TYPE_CUSTOM)) {
                result = queryAvailableObject(objectDescribeFinder.getUser(), describeList);
                stopWatch.lap("queryAvailableObject");
                specialHandleLogAnalysis(objectDescribeFinder.getUser().getTenantId(), objectDescribeFinder.getSourceInfo(), result);
                stopWatch.lap("specialHandleLogAnalysis");
            }
            result.addAll(describeList.stream().filter(x -> DEFINE_TYPE_CUSTOM.equals(x.getDefineType())).collect(Collectors.toList()));
            //根据业务处理特殊对象
            handleObjectsWith(objectDescribeFinder.getUser(), objectDescribeFinder.getSourceInfo(), result);
            // 产品分类灰度处理
            ProductCategoryConfigUtils.productCategoryGrayHandle(objectDescribeFinder.getUser().getTenantId(), result);
            SaleContractUtil.grayHandle(objectDescribeFinder.getUser().getTenantId(), result);
//            handleManageGroup(user, sourceInfo, result);
            stopWatch.lap("queryAvailableObject");
            return result;
        } finally {
            stopWatch.logSlow(150);
        }
    }

    public List<IObjectDescribe> queryAvailableObject(User user, List<IObjectDescribe> describeList) {
        List<IObjectDescribe> result = Lists.newArrayList();
        Set<String> availableObject = queryAvailableObject(user.getTenantId());
        //根据配置增加再license没有的对象，依赖能否查到描述下发
        availableObject.addAll(AppFrameworkConfig.getPresetObjectsNoLicense());

        //去掉不支持lookup的预设自定义对象
        availableObject.removeIf(ObjectAPINameMapping.NOT_SUPPORT_OLD_OBJ::contains);
        availableObject.removeIf(ObjectDescribeExt.CANNOT_LOOKUP_PREDEFINE_CUSTOM_OBJECTS::contains);
        availableObject.removeIf(BLACK_LIST::contains);
        List<String> unusableProductApiNames = ProductUtil.findUnusableProductApiNames(user.getTenantId());
        if (CollectionUtils.notEmpty(unusableProductApiNames)) {
            availableObject.removeIf(unusableProductApiNames::contains);
        }
        //历史协同对象灰度
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SOCIAL_OBJ_GRAY_EI, user.getTenantId())) {
            availableObject.addAll(AppFrameworkConfig.getSocialGrayObj());
        }
        List<IObjectDescribe> preDefineDescribeList = describeList.stream().filter(x -> availableObject.contains(x.getApiName())).collect(Collectors.toList());
        preDefineDescribeList = CollectionUtils.sortByGivenOrder(preDefineDescribeList, ObjectDescribeExt.getObjectOrderList(), IObjectDescribe::getApiName);
        result.addAll(preDefineDescribeList);
        return result;
    }

    @Override
    public List<IObjectDescribe> findObjectsByTenantId(String tenantId, String describeDefineType, boolean isOnlyActivate,
                                                       boolean isExcludeDetailObj, boolean isExcludeDetailWithMasterCreated,
                                                       boolean isAsc, String sourceInfo) {
        return findObjectsByTenantId(User.systemUser(tenantId), describeDefineType, isOnlyActivate, isExcludeDetailObj,
                isExcludeDetailWithMasterCreated, isAsc, sourceInfo);
    }

    @Override
    public List<IObjectDescribe> findSystemObjectDescribes(String tenantId, String describeDefineType, boolean isOnlyActivate, boolean isExcludeDetailObj, boolean isExcludeDetailWithMasterCreated, boolean isAsc) {
        Document example = new Document();
        example.put(IObjectDescribe.PACKAGE, "CRM");
        if (!Strings.isNullOrEmpty(describeDefineType)) {
            example.put(IObjectDescribe.DEFINE_TYPE, describeDefineType);
        }
        if (isOnlyActivate) {
            example.put(IObjectDescribe.IS_ACTIVE, Boolean.TRUE);
        }
        if (isExcludeDetailObj) {
            example.put("exclude_detail", Boolean.TRUE);
        }
        if (isExcludeDetailWithMasterCreated) {
            example.put("exclude_detail_with_master_created", Boolean.TRUE);
        }
        if (isAsc) {
            example.put(IS_ASC, Boolean.TRUE);
        }
        return findByExample(tenantId, example);
    }

    private void specialHandleLogAnalysis(String tenantId, String sourceInfo, List<IObjectDescribe> result) {
        if (CollectionUtils.empty(result)) {
            return;
        }
        if (ObjectListConfig.OBJECT_MANAGEMENT.equals(sourceInfo)) {
            Map<String, IObjectDescribe> logAnalysisObjects = findObjectsWithoutCopyIfGray(tenantId, Lists.newArrayList(EMPLOYEE_LOGIN_USAGE_API_NAME, EMPLOYEE_OBJECT_USAGE_API_NAME));
            String loginKey = ObjectDescribeExt.LOG_ANALYSIS_IS_OPEN + EMPLOYEE_LOGIN_USAGE_API_NAME;
            String operationKey = ObjectDescribeExt.LOG_ANALYSIS_IS_OPEN + EMPLOYEE_OBJECT_USAGE_API_NAME;
            Map<String, String> logAnalysisObjectKeys = configService.queryTenantConfigs(User.systemUser(tenantId), Lists.newArrayList(loginKey, operationKey));
            if (CollectionUtils.notEmpty(logAnalysisObjectKeys) && CollectionUtils.notEmpty(logAnalysisObjects)) {
                if (Boolean.parseBoolean(logAnalysisObjectKeys.get(loginKey))
                        && Objects.nonNull(logAnalysisObjects.get(EMPLOYEE_LOGIN_USAGE_API_NAME))
                        && result.stream().noneMatch(x -> EMPLOYEE_LOGIN_USAGE_API_NAME.equals(x.getApiName()))
                ) {
                    result.add(logAnalysisObjects.get(EMPLOYEE_LOGIN_USAGE_API_NAME));
                }
                if (Boolean.parseBoolean(logAnalysisObjectKeys.get(operationKey))
                        && Objects.nonNull(logAnalysisObjects.get(EMPLOYEE_OBJECT_USAGE_API_NAME))
                        && result.stream().noneMatch(x -> EMPLOYEE_OBJECT_USAGE_API_NAME.equals(x.getApiName()))
                ) {
                    result.add(logAnalysisObjects.get(EMPLOYEE_OBJECT_USAGE_API_NAME));
                }
            }
        } else {
            result.removeIf(a -> Objects.equals(a.getApiName(), EMPLOYEE_LOGIN_USAGE_API_NAME) || Objects.equals(a.getApiName(), EMPLOYEE_OBJECT_USAGE_API_NAME));
        }
    }

    private void handleObjectsWith(User user, String sourceInfo, List<IObjectDescribe> describeList) {
        if (ObjectListManageDefineConfig.isAllow(user.getTenantId())) {
            ObjectListManageDefineConfig.SourceInfoItem sourceInfoItem = ObjectListManageDefineConfig.getItemWithSourceInfo(sourceInfo);
            if (Objects.isNull(sourceInfoItem)) {
                return;
            }
            describeList.removeIf(sourceInfoItem::needRemove);
            Set<String> addObjects = sourceInfoItem.needAddObjs(describeList);
            if (CollectionUtils.notEmpty(addObjects)) {
                List<IObjectDescribe> describes = findObjectList(user.getTenantId(), addObjects);
                describeList.addAll(describes);
            }
            return;
        }
        if (Objects.equals(sourceInfo, ObjectListConfig.BUSINESS_OBJECT)) {
            describeList.removeIf(a -> !(Objects.equals(DEFINE_TYPE_CUSTOM, a.getDefineType()) || Objects.equals(DEFINE_TYPE_PACKAGE, a.getDefineType())));
        }
        if (!Objects.equals(sourceInfo, ObjectListConfig.OBJECT_MANAGEMENT)
                && !Objects.equals(sourceInfo, ObjectListConfig.I18N)
                // 对象列表灰度放开部门对象
                && !UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OBJECT_LIST_SUPPORT_DEPARTMENT_GRAY_EI, user.getTenantId())) {
            describeList.removeIf(a -> Objects.equals(a.getApiName(), DEPARTMENT_OBJ_API_NAME));
        }
        // 对象管理列表屏蔽对象
        if (ObjectListConfig.OBJECT_MANAGEMENT.equals(sourceInfo)) {
            describeList.removeIf(it -> DEPENDENCIES_API_NAME.equals(it.getApiName()));
            describeList.removeIf(it -> AppFrameworkConfig.getUndisplayedPresetObjects().contains(it.getApiName()));
        }
        //对象API界面
        if (ObjectListConfig.OBJECT_FIELD_API.equals(sourceInfo)) {
            describeList.removeIf(it -> DEPENDENCIES_API_NAME.equals(it.getApiName()));
            List<IObjectDescribe> describes = findObjectList(user.getTenantId(), Lists.newArrayList(LEADS_TRANSFER_LOG_OBJ));
            describeList.addAll(describes);
        }
        if (Objects.equals(sourceInfo, ObjectListConfig.FUNCTION)) {
            IObjectDescribe describes = findObjectWithoutCopy(user.getTenantId(), DIMENSION_OBJ_API_NAME);
            describeList.add(describes);
        }
    }

    @Override
    public List<IObjectDescribe> findObjectsByTenantId(String tenantId, boolean isOnlyUdObj, boolean isOnlyActivate, boolean isExcludeDetailObj, boolean isAsc) {
        return findObjectsByTenantId(tenantId, isOnlyUdObj, isOnlyActivate, isExcludeDetailObj, isAsc, null);
    }

    @Override
    public List<IObjectDescribe> findObjectsByTenantId(String tenantId, boolean isOnlyUdObj, boolean isOnlyActivate,
                                                       boolean isExcludeDetailObj, boolean isAsc, String sourceInfo) {
        return findObjectsByTenantId(tenantId, isOnlyUdObj ? DEFINE_TYPE_CUSTOM : null, isOnlyActivate, isExcludeDetailObj, false, isAsc, sourceInfo);
    }


    @Override
    public List<IObjectDescribe> findDescribeList(String tenantId, boolean isOnlyUdObj, String packageName,
                                                  boolean includeUnActived, boolean isExcludeDetail,
                                                  boolean isExcludeDetailWithMasterCreated, String sourceInfo) {
        return findObjectsByTenantId(tenantId, isOnlyUdObj ? DEFINE_TYPE_CUSTOM : null, !includeUnActived, isExcludeDetail, isExcludeDetailWithMasterCreated, true, sourceInfo);
    }

    @Override
    public List<IObjectDescribe> findByExample(String tenantId, Map<String, Object> example) {
        return findByExample(tenantId, example, ActionContextExt.of(new User(tenantId, null)).getContext());
    }

    @Override
    public List<IObjectDescribe> findByExample(String tenantId, Map<String, Object> example, IActionContext context) {

        try {
            return objectDescribeService.findByExample(tenantId, example,
                    context);
        } catch (MetadataServiceException e) {
            log.warn("Error in findByExample, ei:{}, example:{}", tenantId, example, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Map<String, IObjectDescribe> findObjects(String tenantId, Collection<String> apiNames) {
        if (CollectionUtils.empty(apiNames)) {
            return Maps.newHashMap();
        }
        try {
            List<IObjectDescribe> list = getDescribeListByApiNames(tenantId, Lists.newArrayList(Sets.newHashSet(apiNames)));
            return Maps.newHashMap(CollectionUtils.nullToEmpty(list).stream().collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x)));
        } catch (MetadataServiceException e) {
            log.warn("error in find describeList by apiName list,tenantId:{},apiNames:{}", tenantId, apiNames, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Map<String, IObjectDescribe> findObjectsWithoutCopy(String tenantId, Collection<String> apiNames) {
        if (CollectionUtils.empty(apiNames)) {
            return Maps.newHashMap();
        }
        try {
            List<IObjectDescribe> list = getDescribeListByApiNamesWithoutCopy(tenantId, Lists.newArrayList(Sets.newHashSet(apiNames)));
            return Maps.newHashMap(CollectionUtils.nullToEmpty(list).stream().collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x)));
        } catch (MetadataServiceException e) {
            log.warn("error in find describeList by apiName list,tenantId:{},apiNames:{}", tenantId, apiNames, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Map<String, IObjectDescribe> findObjectsWithoutCopyIfGray(String tenantId, Collection<String> apiNames) {
        if (AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)) {
            return findObjectsWithoutCopy(tenantId, apiNames);
        }
        return findObjects(tenantId, apiNames);
    }

    @Override
    public List<IObjectDescribe> findObjectList(String tenantId, Collection<String> apiNames) {
        if (CollectionUtils.empty(apiNames)) {
            return Lists.newArrayList();
        }
        try {
            return getDescribeListByApiNames(tenantId, Lists.newArrayList(Sets.newHashSet(apiNames)));
        } catch (MetadataServiceException e) {
            log.warn("error in find describeList by apiName list,tenantId:{},apiNames:{}", tenantId, apiNames, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Map<String, String> findDisplayNameByApiNames(String tenantId, Collection<String> apiNames) {
        return queryDisplayNameByApiNames(tenantId, Lists.newArrayList(apiNames));
    }

    @Override
    public List<IObjectDescribe> findDescribeListWithoutFields(String tenantId, Collection<String> apiNames) {
        return findDescribeListWithoutFields(tenantId, apiNames, false);
    }

    @Override
    public List<IObjectDescribe> findDescribeListWithoutFields(String tenantId, Collection<String> apiNames, boolean checkDetailObjectButton) {
        if (CollectionUtils.empty(apiNames)) {
            return Lists.newArrayList();
        }
        try {
            IActionContext context = getActionContext(new User(tenantId, null));
            List<IObjectDescribe> list = objectDescribeService.findDescribeListByApiNamesWithoutFields(tenantId,
                    Lists.newArrayList(apiNames), context);

            if (checkDetailObjectButton) {
                List<String> hideButtonApiNames = findDetailApiNamesCreateWithMasterAndHiddenButton(tenantId);
                list.stream().filter(x -> hideButtonApiNames.contains(x.getApiName())).forEach(x -> ObjectDescribeExt.of(x).hideButton());
            }

            return list;
        } catch (MetadataServiceException e) {
            log.error("error in find describeList by apiname list, tenantId:{}, apinames:{},checkDetailObjectButton:{}",
                    tenantId, apiNames, checkDetailObjectButton);
            throw new MetaDataBusinessException(e);
        }
    }

    /**
     * 查询lookup和detail对象
     */
    @Override
    public List<IObjectDescribe> findRelatedDescribes(String tenantId, String apiName) {
        IActionContext context = getActionContext(new User(tenantId, null));
        return findRelatedDescribes(tenantId, apiName, context);
    }

    @Override
    public List<IObjectDescribe> findRelatedDescribesWithoutCopy(String tenantId, String apiName) {
        IActionContext context = getActionContext(new User(tenantId, null));
        ActionContextExt.of(context).doNotDeepCopyDescribe();
        return findRelatedDescribes(tenantId, apiName, context);
    }

    public List<IObjectDescribe> findRelatedDescribes(String tenantId, String apiName, IActionContext context) {
        try {
            ActionContextExt.of(context).doNotDeepCopyDescribe();
            List<IObjectDescribe> relateds = objectDescribeService.findReferenceList(tenantId, apiName,
                    DefObjConstants.PACKAGE_NAME_CRM, context);
            relateds = CollectionUtils.nullToEmpty(relateds).stream()
                    .filter(x -> !ObjectDescribeExt.of(x).isBiObject()).collect(Collectors.toList());

            filterByVersion(tenantId, relateds);
            sortByCreateTime(relateds);

            return relateds;
        } catch (MetadataServiceException e) {
            log.warn("Error in find related Object describe, tenantId:{}, apiName:{}", tenantId, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectDescribe> findRelatedDescribesWithoutCopyIfGray(String tenantId, String apiName) {
        if (AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)) {
            return findRelatedDescribesWithoutCopy(tenantId, apiName);
        }
        return findRelatedDescribes(tenantId, apiName);
    }

    private void filterByVersion(String tenantId, List<IObjectDescribe> describeList) {
        if (ObjectDescribeExt.allCustomObject(describeList) || CollectionUtils.empty(describeList)) {
            return;
        }
        //计算服务不根据license做过滤，防止企业在license续费前查不到相关对象导致无法正确计算
        if (!RequestUtil.isCalculateContext()) {
            Set<String> availableObject = queryAvailableObject(tenantId);
            //历史协同对象灰度
            Set<String> socialObjs = getSocialObjs(tenantId);
            describeList.removeIf(a -> !ObjectDescribeExt.of(a).isCustomObject() &&
                    !availableObject.contains(a.getApiName()) && !socialObjs.contains(a.getApiName()));
        }
        describeList.removeIf(x -> ObjectDescribeExt.INTERNAL_BLACK_OBJECTS.contains(x.getApiName()));
    }

    private Set<String> queryAvailableObject(String tenantId) {
        return licenseService.queryAvailableObject(tenantId);
    }

    private Set<String> getSocialObjs(String tenantId) {
        Set<String> socialObjs = Sets.newHashSet();
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SOCIAL_OBJ_GRAY_EI, tenantId)) {
            socialObjs = AppFrameworkConfig.getSocialGrayObj();
        }
        return socialObjs;
    }

    private void sortByCreateTime(List<IObjectDescribe> describeList) {
        if (CollectionUtils.empty(describeList)) {
            return;
        }
        Collections.sort(describeList, Comparator.comparingLong(o -> Optional.ofNullable(o.getCreateTime()).orElse(0l)));
    }

    @Override
    public List<IObjectDescribe> findRelatedDescribesByListPrivilege(User user, String apiName) {
        List<IObjectDescribe> relatedList = findRelatedDescribes(user.getTenantId(), apiName);
        return filterDescribesWithActionCode(user, relatedList, ObjectAction.VIEW_LIST.getActionCode());
    }

    @Override
    public List<IFieldDescribe> findRelatedFields(String tenantId, String targetApiName) {
        try {
            List<IFieldDescribe> relatedFields = objectDescribeService.findRelatedFields(tenantId, targetApiName);
            if (CollectionUtils.empty(relatedFields)) {
                return Lists.newArrayList();
            }

            //过滤license不支持的预设对象
            Set<String> availableObject = queryAvailableObject(tenantId);
            //历史协同对象灰度
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SOCIAL_OBJ_GRAY_EI, tenantId)) {
                availableObject.addAll(AppFrameworkConfig.getSocialGrayObj());
            }
            relatedFields.removeIf(x -> !ObjectDescribeExt.isCustomObject(x.getDescribeApiName()) && !availableObject.contains(x.getDescribeApiName()));
            relatedFields.removeIf(x -> INTERNAL_BLACK_OBJECTS.contains(x.getDescribeApiName()));
            return relatedFields;
        } catch (MetadataServiceException e) {
            log.warn("findRelatedFields failed,tenantId:{},targetApiName:{}", tenantId, targetApiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IFieldDescribe> findRelatedFieldsByListPrivilege(User user, String targetApiName) {
        List<IFieldDescribe> relatedFields = findRelatedFields(user.getTenantId(), targetApiName);
        if (CollectionUtils.empty(relatedFields)) {
            return relatedFields;
        }

        //过滤没有List功能权限的对象
        List<String> apiNameList = relatedFields.stream().map(IFieldDescribe::getDescribeApiName).distinct().collect(Collectors.toList());
        List<String> authorizedApiNames = functionPrivilegeService.funPrivilegeCheck(user, apiNameList, ObjectAction.VIEW_LIST.getActionCode());
        return relatedFields.stream().filter(x -> authorizedApiNames.contains(x.getDescribeApiName())).collect(Collectors.toList());
    }

    @Override
    public List<IObjectDescribe> filterDescribesWithActionCode(User user, List<IObjectDescribe> describeList, String actionCode) {
        if (CollectionUtils.empty(describeList) || Strings.isNullOrEmpty(actionCode)) {
            return describeList;
        }

        List<String> apiNameList = describeList.stream().map(IObjectDescribe::getApiName).distinct().collect(Collectors.toList());
        List<String> authorizedApiNames = functionPrivilegeService.funPrivilegeCheck(user, apiNameList, actionCode);
        return describeList.stream().filter(x -> authorizedApiNames.contains(x.getApiName())).collect(Collectors.toList());
    }

    @Override
    public List<IObjectDescribe> findDescribeByPrivilegeAndModule(User user,
                                                                  String actionCode,
                                                                  boolean isOnlyUdObj,
                                                                  boolean isOnlyActivate,
                                                                  boolean isExcludeDetailObj,
                                                                  boolean isAsc) {
        return findDescribeByPrivilegeAndModule(user, actionCode, isOnlyUdObj, isOnlyActivate, isExcludeDetailObj, isAsc, null);
    }

    @Override
    public List<IObjectDescribe> findDescribeByPrivilegeAndModule(User user,
                                                                  String actionCode,
                                                                  boolean isOnlyUdObj,
                                                                  boolean isOnlyActivate,
                                                                  boolean isExcludeDetailObj,
                                                                  boolean isAsc,
                                                                  String sourceInfo) {
        List<IObjectDescribe> describeList = findObjectsByTenantId(user.getTenantId(),
                isOnlyUdObj, isOnlyActivate, isExcludeDetailObj, isAsc, sourceInfo);

        if (CollectionUtils.empty(describeList)) {
            return Lists.newArrayList();
        }

        describeList = filterDescribesWithActionCode(user, describeList, actionCode);
        return describeList;
    }

    @Override
    public int useableDescribeCount(User user) {
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.UDOBJ.getBizCode()));
        int describeCount = getCustomDescribeCount(user.getTenantId());
        return tenantLicenseInfo.usableDescribeCount(describeCount);
    }

    @Override
    public int useableDescribeCount(User user, boolean includeBigObject, boolean includeSocialObject, boolean onlyVisibleScope) {
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .user(user)
                .licenseService(licenseService)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.UDOBJ.getBizCode()));
        int describeCount = getCustomDescribeCount(user.getTenantId(), includeBigObject, includeSocialObject, onlyVisibleScope);
        return tenantLicenseInfo.usableDescribeCount(describeCount, includeBigObject, includeSocialObject, onlyVisibleScope);
    }

    /**
     * 查detail对象
     */
    @Override
    public List<IObjectDescribe> findDetailDescribes(String tenantId, String apiName) {
        try {
            List<IObjectDescribe> details = objectDescribeService.findDetailDescribeList(tenantId, apiName,
                    DefObjConstants.PACKAGE_NAME_CRM, getActionContext(new User(tenantId, null)));
            filterByVersion(tenantId, details);
            return CollectionUtils.nullToEmpty(details).stream()
                    .filter(x -> !ObjectDescribeExt.of(x).isBiObject()).collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            log.warn("Error in find detail object describe, tenantId:{}, apiName:{}", tenantId, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectDescribe> findDetailDescribesWithoutCopy(String tenantId, String apiName) {
        try {
            IActionContext context = getActionContext(new User(tenantId, null));
            ActionContextExt.of(context).doNotDeepCopyDescribe();
            List<IObjectDescribe> details = objectDescribeService.findDetailDescribeList(tenantId, apiName,
                    DefObjConstants.PACKAGE_NAME_CRM, context);
            filterByVersion(tenantId, details);
            return CollectionUtils.nullToEmpty(details).stream()
                    .filter(x -> !ObjectDescribeExt.of(x).isBiObject()).collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            log.warn("Error in find detail object describe, tenantId:{}, apiName:{}", tenantId, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectDescribe> findDetailDescribesWithoutCopyIfGray(String tenantId, String apiName) {
        if (AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)) {
            return findDetailDescribesWithoutCopy(tenantId, apiName);
        }
        return findDetailDescribes(tenantId, apiName);
    }

    @Override
    public List<IObjectDescribe> findSimpleDetailDescribes(String tenantId, String apiName) {
        try {
            List<IObjectDescribe> details = objectDescribeService.findSimpleDetailDescribeList(tenantId, apiName,
                    DefObjConstants.PACKAGE_NAME_CRM, getActionContext(new User(tenantId, null)));
            filterByVersion(tenantId, details);
            return CollectionUtils.nullToEmpty(details).stream()
                    .filter(x -> !ObjectDescribeExt.of(x).isBiObject()).collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            log.warn("findSimpleDetailDescribeList failed, tenantId:{}, apiName:{}", tenantId, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectDescribe> findDetailDescribesCreateWithMaster(String tenantId, String apiName) {
        List<IObjectDescribe> details = findDetailDescribes(tenantId, apiName);
        return details.stream()
                .filter(x -> ObjectDescribeExt.of(x).getMasterDetailFieldDescribe().get().getIsCreateWhenMasterCreate())
                .collect(Collectors.toList());
    }

    @Override
    public List<IObjectDescribe> findDetailDescribesCreateWithMasterWithoutCopy(String tenantId, String apiName) {
        List<IObjectDescribe> details = findDetailDescribes(tenantId, apiName);
        return details.stream()
                .filter(x -> ObjectDescribeExt.of(x).getMasterDetailFieldDescribe().get().getIsCreateWhenMasterCreate())
                .collect(Collectors.toList());
    }

    @Override
    public List<IObjectDescribe> findDetailDescribesCreateWithMasterWithoutCopyIfGray(String tenantId, String apiName) {
        if (AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)) {
            return findDetailDescribesCreateWithMasterWithoutCopy(tenantId, apiName);
        }
        return findDetailDescribesCreateWithMaster(tenantId, apiName);
    }

    @Override
    public List<String> findDetailApiNamesCreateWithMasterAndHiddenButton(String tenantId) {
        try {
            return objectDescribeService.findDetailDescribeApiNamesWithMasterCreated(tenantId, false,
                    getActionContext(new User(tenantId, null)));
        } catch (MetadataServiceException e) {
            log.warn("findDetailDescribeWithMasterCreatedAndHiddenButton failed,tenantId:{}", tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    /**
     * 查lookup对象
     */
    @Override
    public List<IObjectDescribe> findLookupDescribes(String tenantId, String apiName, boolean excludeInvalid) {
        try {
            List<IObjectDescribe> lookups = objectDescribeService.findLookupDescribeList(tenantId, apiName,
                    DefObjConstants.PACKAGE_NAME_CRM, !excludeInvalid, getActionContext(new User(tenantId, null)));
            filterByVersion(tenantId, lookups);
            return CollectionUtils.nullToEmpty(lookups).stream()
                    .filter(x -> !ObjectDescribeExt.of(x).isBiObject()).collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            log.warn("Error in find related Object describe, tenantId:{}, apiName:{}", tenantId, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectDescribe> findLookupManyDescribes(String tenantId, String apiName, boolean excludeInvalid) {
        List<IObjectDescribe> objectDescribes = findLookupDescribeListByTypes(tenantId, apiName, Lists.newArrayList(IFieldType.OBJECT_REFERENCE_MANY));
        if (!excludeInvalid) {
            return objectDescribes;
        }
        List<IObjectDescribe> resultList = Lists.newArrayList();
        for (IObjectDescribe objectDescribe : objectDescribes) {
            if (BooleanUtils.isNotTrue(objectDescribe.isActive())) {
                continue;
            }
            List<IObjectReferenceMany> fields = of(objectDescribe).getActiveObjectRefManyFieldDescribesByTargetApiName(apiName);
            if (Objects.nonNull(fields)) {
                resultList.add(objectDescribe);
            }
        }
        return resultList;
    }

    private List<IObjectDescribe> findLookupDescribeListByTypes(String tenantId, String describeApiName, Collection<String> types) {
        try {
            List<IObjectDescribe> describeList = objectDescribeService.findLookupDescribeListByType(tenantId, describeApiName, DefObjConstants.PACKAGE_NAME_CRM,
                    Sets.newHashSet(types), getActionContext(new User(tenantId, null)));
            filterByVersion(tenantId, describeList);
            return CollectionUtils.nullToEmpty(describeList).stream()
                    .filter(x -> !ObjectDescribeExt.of(x).isBiObject()).collect(Collectors.toList());
        } catch (MetadataServiceException e) {
            log.warn("error in find lookup describes, tenantId:{}, apiName:{}, types:{}", tenantId, describeApiName, types, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectDescribe> findAssociationDescribes(String tenantId, IObjectDescribe describe) {
        return findAssociationDescribes(tenantId, Lists.newArrayList(describe));
    }

    @Override
    public List<IObjectDescribe> findAssociationDescribesWithoutCopyIfGray(String tenantId, IObjectDescribe describe) {
        return findAssociationDescribesWithoutCopyIfGray(tenantId, Lists.newArrayList(describe));
    }

    @Override
    public List<IObjectDescribe> findAssociationDescribes(String tenantId, List<IObjectDescribe> objectDescribes) {
        if (CollectionUtils.empty(objectDescribes)) {
            return Lists.newArrayList();
        }
        Set<String> apiNames = Sets.newHashSet();
        objectDescribes.forEach(describe -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
            Optional<MasterDetailFieldDescribe> masterDetailField = describeExt.getMasterDetailFieldDescribe();
            describeExt.getActiveReferenceFieldDescribes().forEach(field -> apiNames.add(field.getTargetApiName()));
            masterDetailField.ifPresent(field -> apiNames.add(field.getTargetApiName()));
        });
        try {
            return getDescribeListByApiNames(tenantId, Lists.newArrayList(apiNames));
        } catch (MetadataServiceException e) {
            log.warn("Error in find Relation Object describe, tenantId:{}, apiName:{}", tenantId, apiNames, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectDescribe> findAssociationDescribesWithoutCopy(String tenantId, List<IObjectDescribe> objectDescribes) {
        if (CollectionUtils.empty(objectDescribes)) {
            return Lists.newArrayList();
        }
        Set<String> apiNames = Sets.newHashSet();
        objectDescribes.forEach(describe -> {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
            Optional<MasterDetailFieldDescribe> masterDetailField = describeExt.getMasterDetailFieldDescribe();
            describeExt.getActiveReferenceFieldDescribes().forEach(field -> apiNames.add(field.getTargetApiName()));
            masterDetailField.ifPresent(field -> apiNames.add(field.getTargetApiName()));
        });
        try {
            return getDescribeListByApiNamesWithoutCopy(tenantId, Lists.newArrayList(apiNames));
        } catch (MetadataServiceException e) {
            log.warn("Error in find Relation Object describe, tenantId:{}, apiName:{}", tenantId, apiNames, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectDescribe> findAssociationDescribesWithoutCopyIfGray(String tenantId, List<IObjectDescribe> objectDescribes) {
        if (AppFrameworkConfig.notCopyDescribeInInnerMethod(tenantId)) {
            return findAssociationDescribesWithoutCopy(tenantId, objectDescribes);
        }
        return findAssociationDescribes(tenantId, objectDescribes);
    }

    private List<IObjectDescribe> getDescribeListByApiNames(String tenantId, List<String> apiNames) throws MetadataServiceException {
        if (CollectionUtils.empty(apiNames)) {
            return Lists.newArrayList();
        }
        return objectDescribeService.findDescribeListByApiNames(tenantId, apiNames, getActionContext(new User(tenantId, null)));
    }

    private List<IObjectDescribe> getDescribeListByApiNamesWithoutCopy(String tenantId, List<String> apiNames) throws MetadataServiceException {
        if (CollectionUtils.empty(apiNames)) {
            return Lists.newArrayList();
        }
        IActionContext context = getActionContext(new User(tenantId, null));
        ActionContextExt.of(context).doNotDeepCopyDescribe();
        return objectDescribeService.findDescribeListByApiNames(tenantId, apiNames, context);
    }

    @Override
    public boolean isMasterObject(String tenantId, String apiName) {
        try {
            return objectDescribeService.checkReferenceExist(tenantId, apiName, IFieldType.MASTER_DETAIL, DefObjConstants.PACKAGE_NAME_CRM);
        } catch (MetadataServiceException e) {
            log.warn("checkReferenceExist error ", e);
        }
        return false;
    }

    @Override
    public DescribeDetailResult findDescribeByApiName(RequestContext context,
                                                      String describeApiName,
                                                      Boolean isIncludeLayout,
                                                      String layoutType,
                                                      String recordType,
                                                      Boolean isIncludeRefDescribeList,
                                                      Boolean isIncludeDetailDescribeList,
                                                      Boolean isIncludeDeleted,
                                                      String dataId) {
        User user = context.getUser();
        String tenantId = user.getTenantId();
        String userId = user.getUserId();

        IObjectData data = null;
        if (!Strings.isNullOrEmpty(dataId)) {
            IActionContext actionContext = new ActionContext();
            actionContext.setEnterpriseId(tenantId);
            actionContext.setUserId(userId);
            try {
                data = dataProxyService.findById(dataId, tenantId, actionContext, describeApiName);
            } catch (MetadataServiceException e) {
                log.warn("findDescribeByApiName findObjectData error,dataId:{},tenantId:{},actionContext:{},describeApiName:{}",
                        dataId, tenantId, actionContext, describeApiName, e);
                throw new MetaDataBusinessException(e);
            }
        }

        return findDescribeByApiName(user, describeApiName, isIncludeLayout, layoutType, recordType,
                isIncludeRefDescribeList, isIncludeDetailDescribeList, isIncludeDeleted, data);
    }

    @Override
    public DescribeDetailResult findDescribeByApiName(User user,
                                                      String describeApiName,
                                                      Boolean isIncludeLayout,
                                                      String layoutType,
                                                      String recordType,
                                                      Boolean isIncludeRefDescribeList,
                                                      Boolean isIncludeDetailDescribeList,
                                                      Boolean isIncludeDeleted,
                                                      IObjectData data) {
        String findLayoutType = Strings.isNullOrEmpty(layoutType) ? ILayout.DETAIL_LAYOUT_TYPE : layoutType;
        String tenantId = user.getTenantId();

        DescribeDetailResult result = new DescribeDetailResult();
        //获取describe
        IObjectDescribe describe = findObjectIncludeDeleted(tenantId, describeApiName);
        ObjectDescribeExt.of(describe).fillParentLookup();
        ObjectDescribeExt.of(describe).fillCascadeDetailLookup();
        processWaterMarkField(describe, user);

        if (BooleanUtils.isTrue(describe.isDeleted())) {
            if (isIncludeDeleted) {
                result.setObjectDescribe(((DocumentBasedBean) describe).getContainerDocument());
                return result;
            }
            throw new ObjectDefNotFoundError(I18N.text(I18NKey.OBJECT_CANNOT_BE_FIND2, describeApiName));
        }

        //获取layout
        ILayout layout = getLayoutResult(describe, isIncludeLayout, findLayoutType, user, recordType, data);
        if (StringUtils.equals(layoutType, LayoutExt.Edit_LAYOUT_TYPE)) {
            processSelectOneByStageInstance(user, describe, layout, data);
        }

        if (isIncludeDetailDescribeList) {
            List<DetailObjectListResult> detailObjectListResultList = Lists.newArrayList();
            List<DetailObjectListResult> allObjectListResultList = Lists.newArrayList();
            List<IObjectDescribe> detailDescribes = findDetailDescribes(describe.getTenantId(), describe.getApiName());
            detailDescribes = detailDescribes.stream().filter(x -> x.isActive()).collect(Collectors.toList());
            detailDescribes = filterDescribesWithActionCode(user, detailDescribes, getObjectActionCode(layoutType));
            if (CollectionUtils.notEmpty(detailDescribes)) {
                //按照布局里配置的显示顺序来对从对象进行排序，先将从对象按照id排序，防止没有在布局管理配置过顺序的从对象出现乱序。
                Collections.sort(detailDescribes, Comparator.comparing(IObjectDescribe::getId));
                List<String> detailApiNames = detailDescribes.stream().map(x -> x.getApiName()).distinct().collect(Collectors.toList());
                Map<String, ILayout> listLayoutMap;
                Map<String, List<IRecordTypeOption>> recordTypeOptionMap;
                if (isIncludeLayout) {
                    Collections.sort(detailDescribes, Comparator.comparingInt(o -> LayoutExt.of(layout).getDetailComponentOrder(o.getApiName())));
                    listLayoutMap = layoutLogicService.findListLayoutByDescribeApiNames(user.getTenantId(), detailApiNames);
                    if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_673)) {
                        recordTypeOptionMap = recordTypeLogicService.findValidRecordTypeListMap(detailApiNames, user);
                    } else {
                        recordTypeOptionMap = recordTypeLogicService.findRecordTypes(user.getTenantId(), detailApiNames);
                    }
                } else {
                    listLayoutMap = Maps.newHashMap();
                    recordTypeOptionMap = Maps.newHashMap();
                }

                detailDescribes.forEach(detailDescribe -> {
                    //处理水印字段
                    processWaterMarkField(detailDescribe, user);
                    //获取到主从类型的字段
                    ObjectDescribeExt describeExt = ObjectDescribeExt.of(detailDescribe);
                    describeExt.fillParentLookup();
                    describeExt.getMasterDetailFieldDescribe().ifPresent(x -> {
                        List<RecordTypeLayoutStructure> layoutStructures = Lists.newArrayList();
                        if (isIncludeLayout) {
                            layoutStructures = getRecordTypeLayoutStructureList(user, findLayoutType, detailDescribe, x,
                                    listLayoutMap.get(detailDescribe.getApiName()), recordTypeOptionMap.get(detailDescribe.getApiName()));
                        }
                        DetailObjectListResult detailObjectListResult = DetailObjectListResult.builder()
                                .fieldApiName(x.getApiName())
                                .fieldLabel(x.getLabel())
                                .objectDescribe(describeExt.toMap())
                                .relatedListName(x.getTargetRelatedListName())
                                .relatedListLabel(x.getTargetRelatedListLabel())
                                .layoutList(layoutStructures)
                                .build();

                        if (StringUtils.equals(layoutType, LayoutExt.Edit_LAYOUT_TYPE)) {
                            modifyDetailSelectOneReadOnlyProperty(user, detailDescribe, detailObjectListResult);
                        }
                        allObjectListResultList.add(detailObjectListResult);

                        //判断是否勾选了"新建主对象时跟随着一起新建的属性",未勾选则不返回
                        if (x.getIsCreateWhenMasterCreate()) {
                            detailObjectListResultList.add(detailObjectListResult);
                        }

                    });
                });
            }
            //获取相关对象的describe
            result.setDetailObjectList(detailObjectListResultList);
            result.setAllObjectDetailObjectList(allObjectListResultList);
            // 添加带过滤条件的 lookup 字段的联级依赖关系
            ObjectDescribeExt.of(describe).fillCascadeDetailLookup(detailDescribes);
        }

        if (isIncludeRefDescribeList) {
            List<RefObjectDescribeListResult> refObjectResultList = Lists.newArrayList();
            List<ObjectReferenceWrapper> refFields = ObjectDescribeExt.of(describe).getActiveReferenceFieldDescribes();
            if (CollectionUtils.notEmpty(refFields)) {
                List<String> refApiNames = refFields.stream().map(x -> x.getTargetApiName()).collect(Collectors.toList());
                Map<String, IObjectDescribe> relatedDescribes = findObjects(describe.getTenantId(), refApiNames);
                refFields.stream().filter(x -> relatedDescribes.containsKey(x.getTargetApiName())).forEach(x -> {
                    refObjectResultList.add(RefObjectDescribeListResult.builder()
                            .fieldApiName(x.getApiName())
                            .fieldLabel(x.getLabel())
                            .objectDescribe(ObjectDescribeExt.of(relatedDescribes.get(x.getTargetApiName())).toMap())
                            .build());
                });
            }
            result.setRefObjectDescribeList(refObjectResultList);
        }

        //除了Detail，其他请求只保留formComponent
        if (isIncludeLayout && !ILayout.DETAIL_LAYOUT_TYPE.equals(layoutType)) {
            LayoutExt.of(layout).removeOthersExceptFormComponent();
        }

        result.setLayout(LayoutExt.of(layout).toMap());
        result.setObjectDescribe(ObjectDescribeExt.of(describe).toMap());

        return result;
    }

    private String getObjectActionCode(String layoutType) {
        ObjectAction action;
        if (UdobjConstants.LAYOUT_TYPE_ADD.equals(layoutType)) {
            action = ObjectAction.CREATE;
        } else if (UdobjConstants.LAYOUT_TYPE_EDIT.equals(layoutType)) {
            action = ObjectAction.UPDATE;
        } else {
            action = ObjectAction.VIEW_LIST;
        }
        return action.getActionCode();
    }

    @Override
    public void processWaterMarkField(IObjectDescribe objectDescribe, User user) {
        processWaterMarkFieldWithExt(objectDescribe, user, null);
    }

    @Override
    public void processWaterMarkFieldWithExt(IObjectDescribe objectDescribe, User user, Map<String, IObjectDescribe> describeExtMap) {
        List<Image> imageList = ObjectDescribeExt.of(objectDescribe).getImageFieldList().stream()
                .filter(Image::getIsWaterMark)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(imageList)) {
            return;
        }
        Set<String> unauthorizedFields = functionPrivilegeService.getUnauthorizedFields(user, objectDescribe.getApiName());
        imageList.stream()
                .map(x -> {
                    if (describeExtMap == null) {
                        return x;
                    } else {
                        return (Image) ObjectDescribeExt.of(objectDescribe).copyField4Ext(describeExtMap, x);
                    }
                })
                .forEach(x -> {
                    if (CollectionUtils.empty(x.getWatermark())) {
                        ImageExt.of(x).setDefaultWaterMark();
                        return;
                    }
                    x.getWatermark().removeIf(y -> {
                        if (!Objects.equals(y.get("type"), "field")) {
                            return false;
                        }
                        Optional<IFieldDescribe> fieldOpt = ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribeSilently((String) y.get("value"));
                        return !fieldOpt.isPresent() || FieldDescribeExt.of(fieldOpt.get()).isShowMask() || unauthorizedFields.contains(y.get("value"));
                    });
                });
    }

    @Override
    public FieldResult findCustomFieldDescribe(String tenantId, String describeAPIName, String fieldApiName) {
        return findCustomFieldDescribe(tenantId, describeAPIName, fieldApiName, false);
    }

    @Override
    public FieldResult findCustomFieldDescribe(String tenantId, String describeAPIName, String fieldApiName, boolean defaultLang) {
        IObjectDescribe describeDraft = defaultLang ? findObjectWithDefaultLang(tenantId, describeAPIName) : findObject(tenantId, describeAPIName);
        Optional<IFieldDescribe> fieldDescribeSilently = ObjectDescribeExt.of(describeDraft).getFieldDescribeSilently(fieldApiName);
        if (!fieldDescribeSilently.isPresent()) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.FIELD_ALREADY_DELETED));
        }

        IFieldDescribe fieldDescribe = fieldDescribeSilently.get();
        //补充默认值属性
        Set<String> fieldTypes = AppFrameworkConfig.getFieldEnableCloneFieldTypes(tenantId);
        if (!(!FieldDescribeExt.of(fieldDescribe).isCustomField()
                && !AppFrameworkConfig.isNeedEnableClone(describeAPIName, fieldApiName))//自定义字段全部支持，预设字段查黑名单
                && !CollectionUtils.empty(fieldTypes)
                && fieldTypes.contains(fieldDescribe.getType())
                && Objects.isNull(FieldDescribeExt.of(fieldDescribe).getEnableCloneActual())) {
            fieldDescribe.setEnableClone(true);
        }
        //给单选和多选字段补充默认的其他选项，不包含签到组件中的字段
        if (FieldDescribeExt.of(fieldDescribe).isCustomField() && !ObjectDescribeExt.of(describeDraft).isInGroupField(fieldApiName)) {
            FieldDescribeExt.of(fieldDescribe).addDefaultOtherOption();
        }

        List<ILayout> layoutList = layoutLogicService.findByTypesIncludeFlowLayout(tenantId, describeAPIName, Lists.newArrayList(LayoutTypes.DETAIL, LayoutTypes.EDIT));
        List<FieldLayoutPojo> fieldLayoutPojoList = Lists.newArrayList();
        for (ILayout layout : layoutList) {
            if (!LayoutExt.of(layout).isDetailLayout() && !LayoutExt.of(layout).isEditLayout()) {
                continue;
            }
            FieldLayoutPojo fieldLayoutPojo = new FieldLayoutPojo();
            fieldLayoutPojo.setApiName(layout.getName());
            fieldLayoutPojo.setLabel(layout.get("display_name", String.class));
            fieldLayoutPojo.setLayoutType(layout.getLayoutType());
            fieldLayoutPojo.setNamespace(layout.getNamespace());
            // 详情页布局补充reference_field_config配置
            if (LayoutExt.of(layout).isDetailLayout()) {
                Optional<FormComponentExt> formComponentOp = LayoutExt.of(layout).getFormComponent();
                formComponentOp.ifPresent(formComponent -> {
                    if (StringUtils.isNotEmpty(formComponent.getReferenceFieldConfig())) {
                        fieldLayoutPojo.setReferenceFieldConfig(formComponent.getReferenceFieldConfig());
                    }
                });
            }

            if (IFieldType.GROUP.equals(fieldDescribe.getType())) {
                // 0.what字段、what组件布局配置中互斥 使用what组件在字段描述中来实现，布局显示和隐藏配置，相当于what组件和what字段共享此配置
                // 1.what字段隐藏 或 what组件隐藏 布局都隐藏
                // 2.同理，what字段显示 或 what字段显示 布局都显示
                fieldLayoutPojo.setReadonly(false);
                fieldLayoutPojo.setRequired(false);
                IFieldSection section = getSectionInDetailLayout(layout, fieldApiName);
                if (fieldDescribe instanceof WhatFieldDescribe && Objects.isNull(section)) {//再看下有没有what组件
                    fieldLayoutPojo.setShow(haveWhatField(layout, (WhatFieldDescribe) fieldDescribe));
                } else if (fieldDescribe instanceof WhatListFieldDescribe && Objects.isNull(section)) {
                    fieldLayoutPojo.setShow(haveWhatListField(layout, ((WhatListFieldDescribe) fieldDescribe)));
                } else if (fieldDescribe instanceof DateTimeRangeFieldDescribe && Objects.isNull(section)) {
                    DateTimeRangeFieldDescribe dateTimeRangeFieldDescribe = (DateTimeRangeFieldDescribe) fieldDescribe;
                    fieldLayoutPojo.setShow(haveDateTimeRangeField(layout, dateTimeRangeFieldDescribe));
                    String startTimeFieldApiName = dateTimeRangeFieldDescribe.getStartTimeFieldApiName();
                    LayoutExt.of(layout).getField(startTimeFieldApiName).ifPresent(formField -> {
                        fieldLayoutPojo.setReadonly(Objects.equals(formField.isReadOnly(), Boolean.TRUE));
                        fieldLayoutPojo.setRequired(Objects.equals(formField.isRequired(), Boolean.TRUE));
                    });
                } else if (FieldDescribeExt.of(fieldDescribe).isGroupBizField() && Objects.isNull(section)) {
                    IFieldDescribe bizField = FieldDescribeExt.of(fieldDescribe).getBizField(describeDraft);
                    if (Objects.nonNull(bizField)) {
                        LayoutExt.of(layout).getField(bizField.getApiName()).ifPresent(formField -> {
                            fieldLayoutPojo.setShow(true);
                            fieldLayoutPojo.setReadonly(Objects.equals(formField.isReadOnly(), Boolean.TRUE));
                            fieldLayoutPojo.setRequired(Objects.equals(formField.isRequired(), Boolean.TRUE));
                        });
                    }
                } else {
                    fieldLayoutPojo.setShow(section != null);
                }
            } else {
                IFormField formField = LayoutExt.of(layout).getField(fieldApiName).orElse(null);
                if (formField != null) {
                    fieldLayoutPojo.setReadonly(Objects.equals(formField.isReadOnly(), Boolean.TRUE));
                    fieldLayoutPojo.setRequired(Objects.equals(formField.isRequired(), Boolean.TRUE));
                    fieldLayoutPojo.setShow(true);
                    fieldLayoutPojo.setRenderType(fieldLayoutPojo.getRenderType());
                } else {
                    fieldLayoutPojo.setReadonly(false);
                    fieldLayoutPojo.setRequired(false);
                    fieldLayoutPojo.setShow(false);
                    fieldLayoutPojo.setRenderType(fieldDescribe.getType());
                }
            }

            fieldLayoutPojoList.add(fieldLayoutPojo);
        }
        FieldResult result = new FieldResult();
        result.setField(fieldDescribe);
        result.setLayout_list(fieldLayoutPojoList);
        return result;
    }

    @Transactional
    @Override
    public DescribeResult createDescribe(User user, String jsonData, String jsonLayout, String jsonListLayout,
                                         boolean isActive, boolean isIncludeLayout) {
        log.debug("Entering create(Json_data={})", jsonData);

        IObjectDescribe objectDescribe = new ObjectDescribe(Document.parse(jsonData));
        // 修改layout
        ILayout layout = null;
        if (isIncludeLayout) {
            layout = new Layout(Document.parse(jsonLayout));
        }

        //创建默认的list类型layout
        ILayout listLayout = null;
        if (!Strings.isNullOrEmpty(jsonListLayout)) {
            listLayout = new Layout(Document.parse(jsonListLayout));
        }

        return createDescribe(user, objectDescribe, layout, listLayout, isIncludeLayout);
    }

    @Transactional
    @Override
    public DescribeResult createDescribe(User user, IObjectDescribe objectDescribe, ILayout createLayout, ILayout listLayout, boolean isIncludeLayout) {
        if (Strings.isNullOrEmpty(objectDescribe.getDefineType())) {
            objectDescribe.setDefineType(IObjectDescribe.DEFINE_TYPE_CUSTOM);
        }

        if (!Objects.equals(objectDescribe.getVisibleScope(), VISIBLE_SCOPE_BI)
                && IObjectDescribe.DEFINE_TYPE_CUSTOM.equals(objectDescribe.getDefineType())) {
            //检查自定义对象个数是否已经超过了分版允许范围。
            checkDescribeCountLimit(user, false, objectDescribe.isBigObject(), objectDescribe.isSocialObject());
            //检查MD相关是否超出了限制
            checkDescribeMasterDetailLimit(user.getTenantId(), objectDescribe);
            //校验当前对象为从对象时关联了开启变更单的主对象，当前对象是否可以开启变更单
            if (objectDescribe.isActive()) {
                checkMasterDetailDescribeByChangeOrder(user, objectDescribe, true);
            }
            //校验自定义对象是否开启了变更单，如果开启则拦截创建
            if (ObjectDescribeExt.of(objectDescribe).enabledChangeOrder()) {
                throw new ValidateException(I18N.text(I18NKey.CUSTOM_OBJECT_CHANGE_ORDER_ENABLED_CANNOT_CREATE));
            }
        }

        try {
            // 增加负责人,所属部门,锁定状态,锁定人,锁定规则字段。
            objectDescribe = generateUserDefObjDescribeDraftByDescribeJson(objectDescribe, user.getTenantId(), user.getUserId());
            //检验布局名字是否重复
            validateLayoutApiNameDuplicate(user, createLayout, isIncludeLayout);
            //校验自定义字段数量限制
            checkCustomFieldCountLimit(user, objectDescribe, null);
            //如果没有业务类型，则添加默认业务类型
            fillRecordTypeTo(objectDescribe);
            //补充多货币字段信息
            fillMultiCurrencyField(user, objectDescribe);
            // 补充归属组织字段
            fillDataOwnOrganization(user, objectDescribe);
            //添加签到额外信息
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
            describeExt.fillSignInInfoField();
            //给describe增加Actions。
            insertActionsIntoDescribe(objectDescribe);
            //给所有的filed都添加上createTime的值。
            defObjUtil.addFieldDescribeCreateTime(objectDescribe);
            ObjectDescribeExt.of(objectDescribe).handleNumberStepValue();
            //校验自增编号是否合法
            autoNumberLogicService.autoNumberValidateByObjectDescribe(objectDescribe, user);
            //定位字段校验
            ObjectDescribeExt.of(objectDescribe).validateGeoLocationField(null);
            //富文本字段校验
            ObjectDescribeExt.of(objectDescribe).validateRichTextFieldInDescribe(null);
            //字段唯一性校验
            ObjectDescribeExt.of(objectDescribe).checkFieldUnique(objectDescribe.getFieldDescribes());
            //label去除特殊字符
            ObjectDescribeExt.of(objectDescribe).formatLabel(objectDescribe.getFieldDescribes());
            //校验target_related_list_name重复
            ObjectDescribeExt.of(objectDescribe).validateObjectReferenceField(null);
            ObjectDescribeExt.of(objectDescribe).validateGroupField(null, user.getTenantId());
            // 负责人字段修改为非必输需要同步更新从对象的负责人字段为非必输
            syncMasterAndDetailOwnerField(user, objectDescribe);
            // 校验公共对象
            checkPublicObjectBeforeUpdateDescribe(user, objectDescribe, null);
            // 获取不需要自动添加到相关列表的查找关联字段
            List<ObjectReferenceWrapper> referenceFieldDescribes = ObjectDescribeExt.of(objectDescribe).getNotAddToLayoutReferenceFieldDescribes(null);
            //开启变更单的对象源单要新加字段
            addFieldForOriginalDescribe(user, objectDescribe);
            //调用service,生成describe。
            IObjectDescribe created = create(user, true, objectDescribe);
            //从对象开启变更单
            removeFieldOriginalDescribe(user, objectDescribe);
            openDetailObjChangeOrder(user, objectDescribe);
            //创建自增编号的重计规则
            autoNumberLogicService.createCondition(objectDescribe, objectDescribe.getFieldDescribes());

            // 修改layout
            ILayout layout = null;
            if (isIncludeLayout) {
                layout = layoutLogicService.createLayout(user, createLayout);
            }
            //创建默认的list类型layout
            if (listLayout != null) {
                layoutLogicService.createLayout(user, listLayout);
            }

            // 功能权限初始化
            functionPrivilegeService.initFunctionPrivilege(user, created);

            // 数据权限初始化
            dataPrivilegeService.addCommonPrivilegeListResult(user, Lists.newArrayList(
                    new ObjectDataPermissionInfo(created.getApiName(), created.getDisplayName(),
                            DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE.getValue())
            ));
            //recordType初始化(业务类型)
            recordTypeLogicService.recordTypeInit(user, null == layout ? null : layout.getName(), user.getTenantId(),
                    created.getApiName());

            logService.log(user, EventType.ADD, ActionType.CREATE_OBJ, created.getApiName(),
                    I18N.text(I18NKey.OBJECT_SPECIFY, created.getDisplayName()));

            //初始化crm菜单,包括任何自定义对象新建都需要新建菜单、后开启的自定义对象初始化如库存、报价单等
            //menuCommonService.createMenuItem(user, objectDescribe.getApiName());
            batchUpsertRelatedListAddToLayoutConfig(user, referenceFieldDescribes);

            return DescribeResult.builder()
                    .objectDescribe(created)
                    .layout(layout)
                    .build();
        } catch (MetadataServiceException e) {
            log.warn("createDescribe error,user:{},jsonData:{},jsonLayout:{},jsonListLayout:{},isIncludeLayout:{}",
                    user, objectDescribe, createLayout, listLayout, isIncludeLayout, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void batchUpsertRelatedListAddToLayoutConfig(User user, List<ObjectReferenceWrapper> referenceFieldDescribes) {
        if (CollectionUtils.empty(referenceFieldDescribes)) {
            return;
        }
        List<ConfigArg> configArgs = Lists.newArrayList();
        referenceFieldDescribes.forEach(fieldDescribe -> {
            String relatedObjectComponentKey = LayoutExt.getRelatedListIssueKey(fieldDescribe.getDescribeApiName(), fieldDescribe.getApiName());
            ConfigArg configTenantArg = ConfigArg.builder().key(relatedObjectComponentKey)
                    .value(Boolean.FALSE.toString())
                    .valueType(ValueType.STRING).build();
            configArgs.add(configTenantArg);
        });
        configService.batchUpsertTenantConfig(user, configArgs);
    }

    @Override
    public ManageGroup queryObjectManageGroup(User user, String sourceInfo) {
        return queryObjectManageGroup(user, sourceInfo, false);
    }

    @Override
    public ManageGroup queryObjectManageGroup(User user, String sourceInfo, boolean useOldMangeGroupGray) {
        if (!ObjectListConfig.OBJECT_MANAGEMENT.equals(sourceInfo)) {
            return null;
        }
        return manageGroupService.queryManageGroup(user, null, ManageGroupType.OBJECT, useOldMangeGroupGray);
    }

    @Override
    public void batchUpdateFieldDescribe(User user, List<DynamicDescribe> dynamicDescribeList, Map<String, Set<String>> failApiNameMap) {

        List<DynamicDescribe> failInfo = Lists.newCopyOnWriteArrayList();

        List<List<DynamicDescribe>> partition = Lists.partition(dynamicDescribeList, 10);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        partition.forEach(batchUpdateInfo -> parallelTask.submit(() -> {
            try {
                objectDescribeService.bulkUpdateFieldAttribute(user.getTenantId(), batchUpdateInfo);
            } catch (Exception e) {
                log.error("batchUpdateFieldDescribe error,tenantId:{},failInfo:{}", user.getTenantId(), JSON.toJSONString(batchUpdateInfo), e);
                failInfo.addAll(batchUpdateInfo);
            }
        }));
        try {
            parallelTask.await(10, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("batchUpdateFieldAttribute time out! ei:{}, userId:{}", user.getTenantId(), user.getUserId(), e);
        }
        //收集编辑失败的字段
        for (DynamicDescribe dynamicDescribe : failInfo) {
            Set<String> failFields = dynamicDescribe.getFields().stream().map(DynamicField::getApiName).collect(Collectors.toSet());
            String apiName = dynamicDescribe.getApiName();
            Set<String> failFieldList = failApiNameMap.get(apiName);
            if (CollectionUtils.empty(failFieldList)) {
                failApiNameMap.put(apiName, failFields);
            } else {
                failFieldList.addAll(failFields);
            }
        }
    }

    private void checkPublicObjectBeforeUpdateDescribe(User user, List<IFieldDescribe> fieldDescribes, IObjectDescribe describe) {
        IObjectDescribe oldDescribe = describe.copy();
        IObjectDescribe newDescribe = describe.copy();
        newDescribe.addFieldDescribeList(fieldDescribes);
        checkPublicObjectBeforeUpdateDescribe(user, newDescribe, oldDescribe);
    }

    @Override
    public void checkPublicObjectBeforeUpdateDescribe(User user, IObjectDescribe describe, IObjectDescribe describeInDB) {
        initNewFieldPublicFieldFlag(user, describe, describeInDB);
        VerifyResult verifyResult = publicObjectEnableJobVerify.verifyWithDescribe(user, describe, describeInDB);
        if (!verifyResult.success()) {
            throw new ValidateException(verifyResult.toMessage());
        }
    }

    @Override
    public Boolean isExistObjectByApiName(String tenantId, String apiName) {
        try {
            IObjectDescribe describe = objectDescribeService.findByTenantIdAndDescribeApiName(tenantId, apiName,
                    getActionContext(new User(tenantId, null)));
            return Objects.nonNull(describe);
        } catch (MetadataServiceException e) {
            log.warn("isExistObjectByApiName error,tenantId:{},apiName:{}", tenantId, apiName, e);
            throw new MetaDataBusinessException(e);
        }
    }

    /**
     * 初始化新建字段的公共字段标识
     *
     * @param user
     * @param describe
     * @param describeInDB
     */
    private void initNewFieldPublicFieldFlag(User user, IObjectDescribe describe, IObjectDescribe describeInDB) {
        fieldPublicFlagHandler.initNewFieldPublicFieldFlag(user, describe, describeInDB);
    }

    private void fillDataOwnOrganization(User user, IObjectDescribe objectDescribe) {
        //多组织只处理自定义对象
        if (!ObjectDescribeExt.of(objectDescribe).isCustomObject()) {
            return;
        }
        OrganizationStatus.Arg arg = new OrganizationStatus.Arg();
        arg.setTenantId(user.getTenantId());
        OrganizationStatus.Result result = platServiceProxy.openOrganization(arg);
        if (!result.isOpenOrganization()) {
            return;
        }
        Department dataOwnOrganization = createDataOwnOrganization(objectDescribe);
        objectDescribe.addFieldDescribe(dataOwnOrganization);
    }

    private void fillMultiCurrencyField(User user, IObjectDescribe objectDescribe) throws MetadataServiceException {
        //预设对象和大对象不处理多币种字段
        if (!ObjectDescribeExt.of(objectDescribe).isCustomObject() || objectDescribe.isBigObject()) {
            return;
        }
        Integer multiCurrencyStatus = multiCurrencyLogicService.findMultiCurrencyStatus(user);
        if (MultiCurrencyStatus.OPEN_SUCCESS.getStatus().equals(multiCurrencyStatus)) {
            List<MtCurrency> mtCurrencyList = multiCurrencyLogicService.findCurrencyList(user);
            Optional<MtCurrency> functionalCurrency = mtCurrencyList.stream().filter(MtCurrency::getIsFunctional).findFirst();
            List<Map<String, Object>> optionList = Lists.newArrayList();
            List<Map<String, Object>> functionalCurrencyList = Lists.newArrayList();
            if (functionalCurrency.isPresent()) {
                Map<String, Object> functionalCurrencyMap = Maps.newHashMap();
                functionalCurrencyMap.put(ISelectOption.OPTION_LABEL, MtCurrency.getI18NLabel(functionalCurrency.get().getCurrencyCode()));
                functionalCurrencyMap.put(ISelectOption.NOT_USABLE, false);
                functionalCurrencyMap.put(ISelectOption.OPTION_VALUE, functionalCurrency.get().getCurrencyCode());
                functionalCurrencyList.add(functionalCurrencyMap);
            } else {
                throw new MetaDataBusinessException(I18N.text(I18NKey.FUNCTIONAL_CURRENCY_NOT_EXIST));
            }
            mtCurrencyList.forEach(x -> {
                Map<String, Object> optionMap = Maps.newHashMap();
                optionMap.put(ISelectOption.OPTION_LABEL, MtCurrency.getI18NLabel(x.getCurrencyCode()));
                optionMap.put(ISelectOption.NOT_USABLE, !Objects.equals(DELETE_STATUS.NORMAL.getValue(), x.getStatus()));
                optionMap.put(ISelectOption.OPTION_VALUE, x.getCurrencyCode());
                optionList.add(optionMap);
            });
            String optionJson = JSON.toJSONString(optionList);
            String functionalJson = JSON.toJSONString(functionalCurrencyList);
            List<IFieldDescribe> multiCurrencyFields = metadataMultiCurrencyService.generateMultiCurrencyFields(objectDescribe.getApiName(), optionJson, functionalJson, RequestUtil.getCurrentLang().getValue());
            objectDescribe.addFieldDescribeList(multiCurrencyFields);
        }
    }

    @Override
    public IObjectDescribe create(boolean isActive, IObjectDescribe objectDescribe) {
        return create(buildUserByDescribe(objectDescribe), isActive, objectDescribe);
    }

    public IObjectDescribe create(User user, boolean isActive, IObjectDescribe objectDescribe) {
        try {
            optionSetLogicService.validateAndUpdateOptionReference(user, null, objectDescribe);
            validateEnterpriseRelation(objectDescribe);
            IObjectDescribe result = objectDescribeService.create(objectDescribe, isActive, getActionContext(user));
            manageGroupService.addToManageGroup(user, null, result.getApiName(), ManageGroupType.OBJECT);
            // 记录字段描述中的引用关系
            executeReferenceByField(RefMessage.ActionType.CREATE, objectDescribe.getFieldDescribes(), objectDescribe);
            return result;
        } catch (MetadataServiceException e) {
            log.warn("Error in create describe, describe:{}", objectDescribe, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public DescribeResult updateDescribe(User user, String jsonData, String jsonLayout, boolean isActive, boolean isIncludeLayout) {
        log.debug("Entering updateDescribe(Json_data={})", jsonData);
        try {
            IObjectDescribe objectDescribe = getObjectDescribe(user, jsonData);
            ILayout layout = null;
            if (isIncludeLayout) {
                layout = getLayout(user, jsonLayout);
            }
            return updateDescribe(user, objectDescribe, layout, isIncludeLayout);
        } catch (MetadataServiceException e) {
            log.warn("updateDescribe error,user:{},jsonData:{}, jsonLayout:{}", user, jsonData, jsonLayout, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public DescribeResult updateDescribe(User user, IObjectDescribe objectDescribe, ILayout layout, boolean isIncludeLayout) {
        try {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
            describeExt.setUpdateDescribeDefaultValue(user);
            describeExt.fillSignInInfoField();

            //给所有的filed都添加上createTime的值。
            defObjUtil.addFieldDescribeCreateTime(objectDescribe);

            //校验自定义字段数量限制
            checkCustomFieldCountLimit(user, objectDescribe, null);

            validateDraftApiNameAndDisplayName(user.getTenantId(), objectDescribe.getDisplayName(),
                    objectDescribe.getApiName(), false);

            //检查单选字段是否存在成环依赖
            describeExt.checkSelectCascadeCircle();

            //检查单选或多选字段的级联关系
            describeExt.checkFieldCascade();
            IObjectDescribe oldDescribe = findObject(user.getTenantId(), objectDescribe.getApiName());

            // 检查MD字段数量限制，更新时不用校验
            if (!ObjectDescribeExt.of(oldDescribe).isSlaveObject() && ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
                checkDescribeMasterDetailLimit(user.getTenantId(), objectDescribe);
                //如果存在审批流定义，则不允许创建主从字段
                if (ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMaster()) {
                    checkIfHasApprovalFlowDefinition(user, objectDescribe.getApiName());
                }
                if (objectDescribe.isActive()) {
                    checkMasterDetailDescribeByChangeOrder(user, objectDescribe, false);
                }
            }

            ObjectDescribeExt.of(objectDescribe).handleNumberStepValue();

            // 校验自增编码是否合法
            autoNumberLogicService.autoNumberValidateByObjectDescribe(objectDescribe, oldDescribe, user);
            // 新增或变更了自增编码，需要切换重新计算规则
            autoNumberLogicService.diffAutoNumberField(objectDescribe, oldDescribe);

            //字段校验
            List<IFieldDescribe> calculateFields = fieldRelationCalculateService.validateByObjectDescribe(objectDescribe, oldDescribe);
            List<IFieldDescribe> quoteFields = fieldRelationCalculateService.checkQuoteFieldsByObjectDescribe(objectDescribe, oldDescribe);
            fieldRelationCalculateService.checkObjectReferenceFieldsByObjectDescribe(user, objectDescribe, oldDescribe);

            ObjectDescribeExt.of(oldDescribe).checkFieldUnique(describeExt.getFieldDescribes());
            // 校验公共对象
            checkPublicObjectBeforeUpdateDescribe(user, describeExt, oldDescribe);
            // 主属性首次配置显示字段进行校验
            checkDisplayFields(user, objectDescribe, oldDescribe);

            //从对象开启变更单
            if (!ObjectDescribeExt.of(oldDescribe).isSlaveObject() && ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMaster()) {
                openDetailObjChangeOrder(user, objectDescribe);
            }
            IObjectDescribe updated = update(user, objectDescribe);
            describeChangeEvent.sendDescribeMessage(user, oldDescribe, objectDescribe);
            ILayout updatedLayout = null;
            if (isIncludeLayout) {
                updatedLayout = layoutLogicService.updateLayout(user, layout);
            }
            updateRenderTypeInLayouts(user, objectDescribe, oldDescribe);

            logService.log(user, EventType.MODIFY, ActionType.UPDATE_OBJ, updated.getApiName(),
                    I18N.text(I18NKey.OBJECT_SPECIFY, updated.getDisplayName()));

            //提交计算任务,新增或变更了统计字段重新计算历史数据
            List<String> fieldNameList = Lists.newArrayList();
            List<String> calculateFieldApiNames = calculateFields.stream().map(x -> x.getApiName()).collect(Collectors.toList());
            List<String> quoteFieldApiNames = quoteFields.stream().map(x -> x.getApiName()).collect(Collectors.toList());
            fieldNameList.addAll(calculateFieldApiNames);
            fieldNameList.addAll(quoteFieldApiNames);
            jobScheduleService.submitCalculateJob(user, fieldNameList, objectDescribe.getApiName());

            fieldRelationCalculateService.checkSelectOneChangeOfDescribe(objectDescribe, oldDescribe);

            //清理已删除的计算字段的引用关系
            fieldRelationCalculateService.deleteFormulaReferenceByDescribe(user.getTenantId(), oldDescribe, objectDescribe);

            return DescribeResult.builder()
                    .objectDescribe(updated)
                    .layout(updatedLayout)
                    .build();
        } catch (MetadataServiceException e) {
            log.warn("updateDescribe error,user:{},describe:{}, layout:{}", user, objectDescribe, layout, e);
            throw new MetaDataBusinessException(e);
        }
    }

    private void updateRenderTypeInLayouts(User user, IObjectDescribe objectDescribe, IObjectDescribe oldDescribe) {
        IFieldDescribe oldNameDescribe = oldDescribe.getFieldDescribe(IObjectData.NAME);
        IFieldDescribe newNameDescribe = objectDescribe.getFieldDescribe(IObjectData.NAME);
        if (Objects.isNull(oldNameDescribe) || Objects.isNull(newNameDescribe)
                || oldNameDescribe.getType().equals(newNameDescribe.getType())) {
            return;
        }
        List<ILayout> layoutList = findAllAppIdLayoutsByTypes(user, objectDescribe, Lists.newArrayList(LayoutTypes.DETAIL, LayoutTypes.EDIT));
        FieldLayoutPojo fieldLayout = new FieldLayoutPojo();
        fieldLayout.setRenderType(newNameDescribe.getType());
        fieldLayout.setRequired(newNameDescribe.isRequired());
        fieldLayout.setReadonly(false);
        fieldLayout.setShow(true);
        layoutList.forEach(x -> {
            LayoutExt.of(x).addOrUpdateField(newNameDescribe, fieldLayout);
            layoutLogicService.updateLayout(user, x);
        });
    }

    private void checkDisplayFields(IFieldDescribe fieldDescribe, IObjectDescribe objectDescribe) {
        if (ObjectDescribeExt.of(objectDescribe).isSupportDisplayName()
                && Objects.equals(FieldDescribeExt.of(fieldDescribe).getApiName(), FieldDescribeExt.DISPLAY_NAME)
                && !Objects.equals(objectDescribe.getFieldDescribe(FieldDescribeExt.DISPLAY_NAME).getType(), fieldDescribe.getType())) {
            throw new ValidateException(I18N.text(I18NKey.DISPLAY_FIELDS_ONLY_FORMULA_CAN_USED));
        }
    }

    @Override
    public void checkDisplayFields(User user, IObjectDescribe objectDescribe, IObjectDescribe objectDescribeInDB) {
        boolean supportDisplayNameInDB = ObjectDescribeExt.of(objectDescribeInDB).isSupportDisplayName();
        boolean supportDisplayName = ObjectDescribeExt.of(objectDescribe).isSupportDisplayName();
        if (supportDisplayNameInDB && supportDisplayName
                && !Objects.equals(objectDescribe.getFieldDescribe(FieldDescribeExt.DISPLAY_NAME).getType()
                , objectDescribeInDB.getFieldDescribe(FieldDescribeExt.DISPLAY_NAME).getType())) {
            throw new ValidateException(I18N.text(I18NKey.DISPLAY_FIELDS_ONLY_FORMULA_CAN_USED));
        }
        if (!supportDisplayNameInDB && supportDisplayName) {
            checkExistHistoryData(user, objectDescribe.getApiName(), objectDescribe.getFieldDescribe(FieldDescribeExt.DISPLAY_NAME));
            // 主属性配置了可见字段需要更新布局
            updateDisplayFieldLayout(user, objectDescribe);
        }
    }

    private void checkExistHistoryData(User user, String apiName, IFieldDescribe fieldDescribe) {
        if (!FieldDescribeExt.of(fieldDescribe).isFormula()
                && metaDataService.existData(user.getTenantId(), apiName)) {
            throw new ValidateException(I18N.text(I18NKey.DISPLAY_FIELDS_ONLY_FORMULA_CAN_USED));
        }
    }

    private void updateDisplayFieldLayout(User user, IObjectDescribe objectDescribe) {
        if (!ObjectDescribeExt.of(objectDescribe).isSupportDisplayName()) {
            return;
        }
        Set<String> appIds = applicationLayeredGrayService.getDefineAppIdByDescribeApiNames(user, objectDescribe.getApiName());
        if (CollectionUtils.empty(appIds)) {
            if (layoutLogicService.isEditLayoutEnable(user.getTenantId(), objectDescribe.getApiName(), false)) {
                List<ILayout> editLayoutList = layoutLogicService.findByTypesIncludeFlowLayout(user.getTenantId(), objectDescribe.getApiName(), Lists.newArrayList(LayoutTypes.EDIT, LayoutTypes.DETAIL));
                updateDisplayNameLayout(user, objectDescribe, editLayoutList);
            } else {
                List<ILayout> layoutList = layoutLogicService.findByTypesIncludeFlowLayout(user.getTenantId(), objectDescribe.getApiName(), Lists.newArrayList(LayoutTypes.DETAIL));
                updateDisplayNameLayout(user, objectDescribe, layoutList);
            }
            return;
        }
        for (String appId : getNewAppIds(appIds)) {
            LayoutLogicService.LayoutContext layoutContext = buildLayoutContext(user, appId);
            if (layoutLogicService.isEditLayoutEnable(layoutContext, objectDescribe.getApiName(), false)) {
                List<ILayout> editLayoutList = layoutLogicService.findByTypesIncludeFlowLayout(layoutContext, objectDescribe.getApiName(), Lists.newArrayList(LayoutTypes.EDIT, LayoutTypes.DETAIL));
                updateDisplayNameLayout(user, objectDescribe, editLayoutList);
            } else {
                List<ILayout> layoutList = layoutLogicService.findByTypesIncludeFlowLayout(layoutContext, objectDescribe.getApiName(), Lists.newArrayList(LayoutTypes.DETAIL));
                updateDisplayNameLayout(user, objectDescribe, layoutList);
            }
        }
    }

    private void updateDisplayNameLayout(User user, IObjectDescribe objectDescribe, List<ILayout> layoutList) {
        List<ILayout> needUpdateLayoutList = layoutList.stream()
                .filter(layout -> !LayoutExt.of(layout).getField(FieldDescribeExt.DISPLAY_NAME)
                        .isPresent())
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(needUpdateLayoutList)) {
            ObjectDescribeExt.of(objectDescribe).getFieldDescribeSilently(FieldDescribeExt.DISPLAY_NAME)
                    .ifPresent(fieldDescribe ->
                            needUpdateLayoutList.forEach(x -> {
                                FieldLayoutPojo fieldLayout = getDisplayNameFieldLayoutPojo(fieldDescribe);
                                LayoutExt layoutExt = LayoutExt.of(x);
                                layoutExt.updateField(fieldDescribe, Lists.newArrayList(), fieldLayout);
                                layoutLogicService.updateLayout(user, layoutExt.getLayout());
                            })
                    );
        }
    }

    @NotNull
    private FieldLayoutPojo getDisplayNameFieldLayoutPojo(IFieldDescribe fieldDescribe) {
        FieldLayoutPojo fieldLayout = new FieldLayoutPojo();
        fieldLayout.setRenderType("text");
        fieldLayout.setRequired(fieldDescribe.isRequired());
        fieldLayout.setReadonly(false);
        fieldLayout.setShow(true);
        return fieldLayout;
    }

    @Override
    @Transactional
    public IObjectDescribe deleteDescribe(User user, String apiName) {
        IObjectDescribe describe = findObjectWithoutCopy(user.getTenantId(), apiName);
        List<ILayout> layoutList = findLayoutByObjectApiName(user, apiName);
        log.warn("deleteDescribe,tenantId:{},userId:{},apiName:{}", user.getTenantId(), user.getUserId(), apiName);

        if (ObjectDescribeExt.of(describe).isChangeOrderObject() || changeOrderLogicService.isOpenChangeOrder(user, apiName)) {
            throw new ValidateException(I18NExt.text(I18NKey.OPEN_CHANGE_ORDER_OBJ_CANNOT_DISABLE_OR_DELETE));
        }

        deleteObjectMappingRules(user, apiName);

        CheckerResult result;
        try {
            result = objectDescribeService.delete(describe);
            executeReferenceByField(RefMessage.ActionType.DELETE, describe.getFieldDescribes(), describe);
            layoutLogicService.executeReferenceByLayout(RefMessage.ActionType.DELETE, describe, layoutList);
        } catch (MetadataServiceException e) {
            log.warn("deleteDescribe failed,user:{},apiName:{}", user, apiName, e);
            throw new MetaDataBusinessException(e);
        }

        if (result.isPass()) {
            logAndDeleteRelateThings(user, apiName, describe);
            return result.getDescribe();
        } else {
            log.warn("deleteDescribe failed,user:{},apiName:{},result:{}", user, apiName, JSON.toJSONString(result));
            throw new MetaDataBusinessException(result.getFailMessage());
        }
    }

    private void deleteObjectMappingRules(User user, String apiName) {
        objectMappingService.deleteRuleBySourceDescribe(user, apiName);
        objectMappingService.deleteRuleByTargetDescribe(user, apiName);
    }

    private void logAndDeleteRelateThings(User user, String apiName, IObjectDescribe describe) {
        logService.log(user, EventType.DELETE, ActionType.DELETE_OBJ, describe.getApiName(),
                I18N.text(I18NKey.OBJECT_SPECIFY, describe.getDisplayName()));
        //关闭新建编辑页布局
        deleteEditLayoutConfig(user, apiName);
        //删除数据权限
        dataPrivilegeService.delDataRights(user, apiName);
        //删除功能权限
        functionPrivilegeService.deleteFunctionPrivilege(user, apiName);
        //需要删除的字段函数引用关系
        functionLogicService.batchDeleteRelation(user, fieldFuncReferenceList(user, describe));
        // 移除分管小组
        deletedDescribeManageGroup(user, apiName);
        // 删除高级开关
        configService.deleteTenantConfig(user, apiName + "|" + OptionalFeaturesService.OPTIONAL_FEATURES);
        // 移除字段后台扩展信息
        fieldBackgroundExtraService.bulkDelete(user, apiName);
        // 删除 [扩展] 页的数据, 并一并删除函数引用
        functionPluginConfLogicService.deleteAll(user, apiName);
        //删除计算字段的引用关系
        fieldRelationCalculateService.deleteFormulaReferenceByDescribe(user.getTenantId(), describe, null);
    }

    private void deletedDescribeManageGroup(User user, String describeApiName) {
        manageGroupService.deleteManageGroup(user, null, Lists.newArrayList(describeApiName), ManageGroupType.OBJECT);
        layoutLogicService.deletedLayoutManageGroupByParentApiName(user, describeApiName);
        duplicatedSearchService.deletedDuplicateSearchManageGroup(user, describeApiName);
        deletedScoreRuleManageGroup(user, describeApiName);
    }

    private void deletedScoreRuleManageGroup(User user, String describeApiName) {
        ManageGroup manageGroup = manageGroupService.queryManageGroup(user, describeApiName, ManageGroupType.SCORE_RULE);
        Set<String> apiNames = manageGroup.getApiNames();
        if (CollectionUtils.empty(apiNames)) {
            return;
        }
        manageGroupService.deleteManageGroup(user, describeApiName, apiNames, ManageGroupType.SCORE_RULE);
    }


    private List<ReferenceData> fieldFuncReferenceList(User user, IObjectDescribe describe) {
        List<ReferenceData> referenceDataList = Lists.newArrayList();
        ObjectDescribeExt.of(describe).getAllNeedCleanFields().forEach(fieldDescribe -> {
            FieldDescribeExt ext = FieldDescribeExt.of(fieldDescribe);
            if (ext.isAutoNumber() && AutoNumberExt.of((AutoNumber) fieldDescribe).isFunctionAutoNumber()) {
                ReferenceData referenceData = ReferenceData.builder()
                        .sourceType(SourceTypes.AUTO_NUMBER)
                        .sourceValue(fieldDescribe.getApiName())
                        .targetValue(((AutoNumber) fieldDescribe).getFuncApiName())
                        .build();
                referenceDataList.add(referenceData);
            } else if (ext.isWhereRelationField()) {
                List<ReferenceData> referenceData = fieldRelationCalculateService.buildReferenceFieldRelation(user, fieldDescribe);
                referenceDataList.addAll(referenceData);
            } else if (ext.isGeneralOptions()) {
                ReferenceData referenceData = ReferenceData.builder()
                        .sourceType(OptionSetLogicService.OPTION_REFERENCE_SOURCE_TYPE)
                        .sourceValue(String.format("%s.%s", describe.getApiName(), fieldDescribe.getApiName()))
                        .build();
                referenceDataList.add(referenceData);
            } else if (ext.isWhatListField()) {
                String functionAPIName = FieldDescribeExt.of(fieldDescribe).getFuncApiNameFromWhatListField();
                ReferenceData referenceData = ReferenceData.builder()
                        .sourceType(SourceTypes.RELATED_SCOPE)
                        .sourceValue(fieldDescribe.getApiName())
                        .targetValue(functionAPIName)
                        .build();
                referenceDataList.add(referenceData);
            }
        });
        return referenceDataList;
    }

    @Override
    @Transactional
    public IObjectDescribe deleteDescribeDirect(User user, String apiName) {
        IObjectDescribe describe = findObject(user.getTenantId(), apiName);
        log.warn("deleteDescribeDirect,user:{},apiName:{}", user, apiName);

        deleteObjectMappingRules(user, apiName);

        CheckerResult result;
        try {
            result = objectDescribeService.deleteDirect(describe);
        } catch (MetadataServiceException e) {
            log.warn("deleteDescribeDirect failed,user:{},apiName:{}", user, apiName, e);
            throw new MetaDataBusinessException(e);
        }

        if (result.isPass()) {
            logAndDeleteRelateThings(user, apiName, describe);
            return result.getDescribe();
        } else {
            log.warn("deleteDescribeDirect failed,user:{},apiName:{},result:{}", user, apiName, JSON.toJSONString(result));
            throw new MetaDataBusinessException(result.getFailMessage());
        }
    }

    @Override
    public IObjectDescribe disableDescribe(User user, String apiName) {
        IObjectDescribe describeDraft = findObject(user.getTenantId(), apiName);
        if (Objects.isNull(describeDraft)) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.OBJECT_IN_UNEXIST_OR_DELETE));
        }

        if (ObjectDescribeExt.of(describeDraft).isChangeOrderObject()) {
            throw new ValidateException(I18NExt.text(I18NKey.OPEN_CHANGE_ORDER_OBJ_CANNOT_DISABLE_OR_DELETE));
        }
        CheckerResult checkerResult;
        try {
            checkerResult = objectDescribeService.disableDescribe(describeDraft, ActionContextExt.of(user).getContext());
        } catch (MetadataServiceException e) {
            log.warn("disableDescribe failed,user:{},apiName:{}", user, apiName, e);
            throw new MetaDataBusinessException(e);
        }

        if (checkerResult.isPass()) {
            //禁用映射规则
            objectMappingService.disableRuleByTargetDescribe(user, apiName);
            objectMappingService.disableRuleBySourceDescribe(user, apiName);
            //禁用转换规则
            objectConvertRuleService.disableRuleByTargetDescribe(user, apiName);
            objectConvertRuleService.disableRuleBySourceDescribe(user, apiName);
            // 禁用数据归档
            objectArchiveService.disableByObjectApiName(user, apiName);
            logService.log(user, EventType.DISABLE, ActionType.DISABLE_OBJ, describeDraft.getApiName(),
                    I18N.text(I18NKey.OBJECT_SPECIFY, describeDraft.getDisplayName()));
            return checkerResult.getDescribe();
        } else {
            log.warn("disableDescribe failed,user:{},apiName:{},result:{}", user, apiName, JSON.toJSONString(checkerResult));
            throw new MetaDataBusinessException(checkerResult.getFailMessage());
        }
    }

    @Override
    @Transactional
    public IObjectDescribe enableDescribe(User user, String apiName) {

        log.debug("Entering enableDescribe(draft_apiname={})", apiName);

        IObjectDescribe describeDraft = findObject(user.getTenantId(), apiName);
        boolean isOpenChange = false;
        if (ObjectDescribeExt.of(describeDraft).isSlaveObject()) {
            MasterDetail masterDetailField = of(describeDraft).getMasterDetailField()
                    .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.MASTER_DETAIL_FIELD_NOT_EXIST)));
            if (changeOrderLogicService.isOpenChangeOrder(user, masterDetailField.getTargetApiName())) {
                isOpenChange = true;
                checkMasterDetailDescribeByChangeOrder(user, describeDraft, masterDetailField, false);
            }
        }

        if (isEnableCheckEnterpriseResourcesQuote(user.getTenantId())
                && !Objects.equals(describeDraft.getVisibleScope(), VISIBLE_SCOPE_BI)
                && IObjectDescribe.DEFINE_TYPE_CUSTOM.equals(describeDraft.getDefineType())) {
            //检查自定义对象个数是否已经超过了分版允许范围。
            checkDescribeCountLimit(user, true, describeDraft.isBigObject(), describeDraft.isSocialObject(), isOpenChange ? 1 : 0);
            //检查MD相关是否超出了限制
            checkDescribeMasterDetailLimit(user.getTenantId(), describeDraft);
        }
        CheckerResult checkerResult;
        try {
            checkerResult = objectDescribeService.enableDescribe(describeDraft, ActionContextExt.of(user).getContext());
        } catch (MetadataServiceException e) {
            log.warn("enableDescribe failed,user:{},apiName:{}", user, apiName, e);
            throw new MetaDataBusinessException(e);
        }

        if (checkerResult.isPass()) {
            logService.log(user, EventType.ENABLE, ActionType.ENABLE_OBJ, describeDraft.getApiName(),
                    I18N.text(I18NKey.OBJECT_SPECIFY, describeDraft.getDisplayName()));

            if (isOpenChange) {
                //跳过缓存直接查库，解决version问题
                RequestContextManager.getContext().setAttribute(RequestContext.Attributes.DESCRIBE_SKIP_STATIC, true);
                describeDraft = findObject(user.getTenantId(), apiName);
                changeOrderLogicService.openDetailChangeOrder(user, describeDraft);
                describeLogicService.update(user, describeDraft);
            }

            //启用对象触发统计字段和计算字段的全量计算
            List<IFieldDescribe> calculateFields = ObjectDescribeExt.of(describeDraft).getCountAndFormulaFields();
            List<String> fieldNameList = calculateFields.stream().map(x -> x.getApiName()).collect(Collectors.toList());
            jobScheduleService.submitCalculateJob(user, fieldNameList, describeDraft.getApiName());

            return checkerResult.getDescribe();
        } else {
            log.warn("enableDescribe failed,user:{},apiName:{},result:{}", user, apiName, JSON.toJSONString(checkerResult));
            throw new MetaDataBusinessException(checkerResult.getFailMessage());
        }
    }

    @Override
    @Transactional
    public IObjectDescribe addDescribeCustomField(User user, String describeAPIName, String fieldDescribeJson,
                                                  List<FieldLayoutPojo> layoutPojoList, List<IFieldDescribe> fieldList) {
        return addDescribeCustomField(user, describeAPIName, fieldDescribeJson, null, layoutPojoList, fieldList);
    }

    public IObjectDescribe fixObjectDescribe(User user, IObjectDescribe objectDescribe) {
        if (Objects.isNull(objectDescribe) || Objects.isNull(user)) {
            return objectDescribe;
        }
        CollectionUtils.nullToEmpty(objectDescribe.getFieldDescribes())
                .forEach(fieldDescribe -> fixFieldDescribe(user, fieldDescribe));
        return objectDescribe;
    }

    public IFieldDescribe fixFieldDescribe(User user, IFieldDescribe fieldDescribe) {
        if (Objects.isNull(fieldDescribe) || Objects.isNull(user)) {
            return fieldDescribe;
        }
        return fixSelectFieldDefaultValue(user, fieldDescribe);
    }

    // 修复 自定义 单选/多选字段 的默认值: default_is_expression=false, 并且default_value不在字段option中, 清理default_value
    public IFieldDescribe fixSelectFieldDefaultValue(User user, IFieldDescribe fieldDescribe) {
        if (Objects.isNull(fieldDescribe) || Objects.isNull(user) || StringUtils.isBlank(user.getTenantId())) {
            return fieldDescribe;
        }
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        if (fieldDescribeExt.isCustomField() && fieldDescribeExt.isSelectField()
                && !fieldDescribeExt.defaultIsExpression()
                && fieldDescribe instanceof SelectOne) {
            SelectOne selectOne = (SelectOne) fieldDescribe;
            SelectOneExt selectOneExt = SelectOneExt.of(selectOne);
            Set<String> optionValues;
            if (fieldDescribeExt.isGeneralOptions()) {    // 使用通用选项集作为选项
                optionValues = optionSetLogicService.find(user, selectOneExt.getOptionApiName())
                        .map(optionSet -> CollectionUtils.nullToEmpty(optionSet.getOptions())
                                .stream()
                                .map(MtOptionSet.MtOption::getValue)
                                .collect(Collectors.toSet()))
                        .orElse(Sets.newHashSet());
            } else {
                optionValues = selectOne.getSelectOptions().stream()
                        .map(ISelectOption::getValue)
                        .filter(StringUtils::isNotBlank)
                        .collect(toSet());
            }
            Object defaultValueObj = fieldDescribe.getDefaultValue();
            if (CollectionUtils.empty(optionValues) || Objects.isNull(defaultValueObj)) {
                return fieldDescribe;
            }

            if (fieldDescribe instanceof SelectMany && defaultValueObj instanceof Collection) {    // 多选
                Collection<String> defaultValues = org.apache.commons.collections4.CollectionUtils.emptyIfNull
                        ((Collection<String>) defaultValueObj);
                selectOne.setDefaultValue(defaultValues.stream()
                        .filter(optionValues::contains).collect(toList()));
            } else {
                String defaultValue = (String) selectOne.getDefaultValue();
                selectOne.setDefaultValue(optionValues.contains(defaultValue) ? defaultValue : "");
            }
        }
        return fieldDescribe;
    }

    @Override
    @Transactional
    public IObjectDescribe addDescribeCustomField(User user, String describeAPIName, String fieldDescribeJson,
                                                  String persistentDataCalc, List<FieldLayoutPojo> layoutPojoList, List<IFieldDescribe> fieldList) {
        // 给describe增加customFieldDescribe
        StopWatch stopWatch = StopWatch.create("addDescribeCustomField-logicService");
        try {
            IFieldDescribe fieldDescribe = fixFieldDescribe(user, FieldDescribeFactory.newInstance(fieldDescribeJson));
            fieldDescribe.setCreateTime(System.currentTimeMillis());
            IObjectDescribe oldDescribe = findObject(user.getTenantId(), describeAPIName);
            stopWatch.lap("findObject");
            //预制对象不能增加必填字段
            noRequiredCheck(describeAPIName, fieldDescribe, oldDescribe);
            stopWatch.lap("noRequiredCheck");
            validFieldApiNameRepeat(fieldDescribe, oldDescribe);
            stopWatch.lap("validFieldApiNameRepeat");

            if (IFieldType.DATE_TIME.equals(fieldDescribe.getType())) {
                DateTimeFieldDescribe dateTimeFieldDescribe = (DateTimeFieldDescribe) fieldDescribe;
                if (!isDateTimeSupportNotUseMultitimeZoneTenant(user.getTenantId()) && dateTimeFieldDescribe.getNotUseMultiTimeZone()) {
                    throw new ValidateException(I18N.text(I18NKey.TENANT_NOT_GRAY_MULTIPLE_TIME_ZONES_CANNONT_USE_ATTRIBUTE));
                }
            }

            if (IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType())) {
                checkDescribeMasterDetailLimit(user.getTenantId(), oldDescribe, (MasterDetail) fieldDescribe);
                //不在白名单的企业，如果存在审批流定义，则不允许创建主从字段
                if (BooleanUtils.isNotFalse(((MasterDetail) fieldDescribe).getIsCreateWhenMasterCreate())) {
                    checkIfHasApprovalFlowDefinition(user, describeAPIName);
                }
                if (oldDescribe.isActive()) {
                    checkMasterDetailDescribeByChangeOrder(user, oldDescribe, (MasterDetail) fieldDescribe, false);
                }
                stopWatch.lap("checkMasterDetail");
            }

            validReferenceFieldByRelatedTeamSwitch(user, fieldDescribe, oldDescribe);
            stopWatch.lap("validReferenceFieldByRelatedTeamSwitch");
            validReferenceFieldCondition(user.getTenantId(), fieldDescribe);
            stopWatch.lap("validReferenceFieldCondition");

            //字段新建编辑层级校验（库里和新建的区别）
            List<IFieldDescribe> calculateFields = fieldRelationCalculateService.validateByField(oldDescribe, fieldDescribe);
            List<IFieldDescribe> quoteFields = fieldRelationCalculateService.checkQuoteField(oldDescribe, fieldDescribe);
            fieldRelationCalculateService.checkObjectReferenceField(user, oldDescribe, fieldDescribe);
            stopWatch.lap("fieldRelationCalculateService");

            oldDescribe.setLastModifiedBy(user.getUserId());
            oldDescribe.setLastModifiedTime(System.currentTimeMillis());

            List<IFieldDescribe> allList = Lists.newArrayList();
            allList.addAll(CollectionUtils.nullToEmpty(fieldList));
            allList.add(fieldDescribe);

            if (ObjectDescribeExt.isSignInField(fieldDescribe)) {
                ((SignInFieldDescribe) fieldDescribe).setSignInInfoListFieldApiName("sign_in_info__c");
                EmbeddedObjectList signInfoListField = ObjectDescribeExt.generateSignInfoListField();
                allList.add(signInfoListField);
            }

            //校验自定义字段数量限制
            checkCustomFieldCountLimit(user, oldDescribe, allList);
            stopWatch.lap("checkCustomFieldCountLimit");

            ObjectDescribeExt.of(oldDescribe).handleNumberStepValue(allList);

            if (FieldDescribeExt.of(fieldDescribe).isAutoNumber()) {
                autoNumberLogicService.autoNumberValidate(oldDescribe, Lists.newArrayList((AutoNumber) fieldDescribe), user);
                autoNumberLogicService.createCondition(oldDescribe, Lists.newArrayList(fieldDescribe));
                stopWatch.lap("autoNumberLogic");
            }
            //字段值唯一性校验
            ObjectDescribeExt.of(oldDescribe).checkFieldUnique(allList);
            //定位字段校验
            ObjectDescribeExt.of(oldDescribe).validateGeoLocationField(allList);
            //富文本字段校验
            ObjectDescribeExt.of(oldDescribe).validateRichTextFieldInDescribe(allList);
            //label去除特殊字符
            ObjectDescribeExt.of(oldDescribe).formatLabel(allList);
            //校验target_related_list_name重复
            ObjectDescribeExt.of(oldDescribe).validateObjectReferenceField(allList);
            ObjectDescribeExt.of(oldDescribe).validateGroupField(allList, user.getTenantId());
            //统计字段校验 新租户有效
            maxCountValidate(user, false, Lists.newArrayList(fieldDescribe));
            stopWatch.lap("maxCountValidate");
            // 负责人字段修改为非必输需要同步更新从对象的负责人字段为非必输
            syncMasterAndDetailOwnerField(user, fieldDescribe, oldDescribe);
            stopWatch.lap("syncMasterAndDetailOwnerField");
            // 校验公共对象
            checkPublicObjectBeforeUpdateDescribe(user, allList, oldDescribe);
            stopWatch.lap("checkPublicObjectBeforeUpdateDescribe");

            stopWatch.lap("checkLocalizationFileReferenceDupRule");

            boolean addToLayout = FieldDescribeExt.of(fieldDescribe).isRelatedListAddToLayout();

            //如果添加的是主从字段，并且主对象开启了变更单，则从对象也要开启变更单
            if (IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType()) && oldDescribe.isActive()) {
                IObjectDescribe copyDescribe = of(oldDescribe).copy();
                copyDescribe.addFieldDescribe(fieldDescribe);
                openDetailObjChangeOrder(user, copyDescribe);
                List<IFieldDescribe> fieldDescribes = of(copyDescribe).getFieldDescribes();
                allList.addAll(fieldDescribes.stream()
                        .filter(x -> !oldDescribe.containsField(x.getApiName()) && !fieldDescribe.getApiName().equals(x.getApiName()))
                        .collect(Collectors.toList()));
                stopWatch.lap("openDetailObjChangeOrder");
            }

            IObjectDescribe iObjectDescribe = addCustomFieldDescribe(user, oldDescribe, allList);
            stopWatch.lap("addCustomFieldDescribe");
            IFieldDescribe ownerField = addMasterDetailFieldAndSyncOwnerField(user, fieldDescribe, oldDescribe);
            stopWatch.lap("addMasterDetailFieldAndSyncOwnerField");
            if (Objects.nonNull(ownerField)) {
                updateFieldDescribe(user, iObjectDescribe, Lists.newArrayList(ownerField));
                stopWatch.lap("updateFieldDescribe");
            }
            // 记录引用关系
            executeReferenceByField(RefMessage.ActionType.CREATE, Lists.newArrayList(fieldDescribe), oldDescribe);
            stopWatch.lap("executeReferenceByField");
            if (CollectionUtils.notEmpty(layoutPojoList)) {
                // 把新增的字段放入到指定的layout中的FormComponent中的基本信息FieldSection的里面。
                List<String> layoutApiNames = layoutPojoList.stream().map(FieldLayoutPojo::getApiName).collect(Collectors.toList());
                Map<String, Layout> layoutMap = layoutLogicService.findLayoutByApiNames(user.getTenantId(), layoutApiNames, describeAPIName);
                stopWatch.lap("findLayoutByApiNames");
                layoutPojoList.stream().filter(x -> x.isShow() && layoutMap.containsKey(x.getApiName())).forEach(x -> {
                    ILayout layout = layoutMap.get(x.getApiName());
                    LayoutExt.of(layout).addField(fieldDescribe, fieldList, x);
                });
                User newUser = orgService.fillUserName(user);
                stopWatch.lap("orgService.fillUserName");
                user.setUserName(newUser.getUserName());

                // 使用并行或同步方式更新布局
                updateLayoutsInParallelOrSequential(layoutMap, newUser, oldDescribe, "addDescribeCustomField");
                stopWatch.lap("updateLayout");
            }

            Map<String, Object> allFields = getFieldsDocMap(allList, null);
            logService.log(user, EventType.ADD, ActionType.CREATE_FIELD, oldDescribe.getApiName(), allFields
                    , I18N.text(I18NKey.FIELD_OBJECT, fieldDescribe.getLabel(), iObjectDescribe.getDisplayName()));
            // 设置增加字段埋点
            OperateReportUtil.postMessage(user.getTenantId(), user.getUserId(),
                    ProductLine.CRM_SERVICE, OperateReport.FIELD, fieldDescribe.getType(), OperateReport.ADD);

            //重新计算历史数据
            List<String> fieldNameList = Lists.newArrayList();
            List<String> calculateFieldApiNames = calculateFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            List<String> quoteFieldApiNames = quoteFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            fieldNameList.addAll(calculateFieldApiNames);
            fieldNameList.addAll(quoteFieldApiNames);
            if (TenantUtil.isCalcCriteria(user.getTenantId())) {
                List<IFieldDescribe> calcFields = Lists.newArrayList(calculateFields);
                calcFields.addAll(quoteFields);
                jobScheduleService.submitCalculateJob(user, oldDescribe.getApiName(), calcFields, persistentDataCalc, false);
            } else {
                jobScheduleService.submitCalculateJob(user, fieldNameList, oldDescribe.getApiName());
            }
            stopWatch.lap("submitCalculateJob");
            if (!addToLayout) {
                batchUpsertRelatedListAddToLayoutConfig(user, Lists.newArrayList(ObjectReferenceWrapper.of(fieldDescribe)));
                stopWatch.lap("batchUpsertRelatedListAddToLayoutConfig");
            }
            return iObjectDescribe;
        } catch (MetadataServiceException e) {
            log.warn("addDescribeCustomField error,user:{},describeAPIName:{},fieldDescribeJson:{},layoutPojoList:{},fieldList:{}",
                    user, describeAPIName, fieldDescribeJson, layoutPojoList, fieldList, e);
            throw new MetaDataBusinessException(e);
        } finally {
            stopWatch.logSlow(1000);
        }

    }


    private static Map<String, Object> getFieldsDocMap(List<IFieldDescribe> allList, List<IFieldDescribe> fieldsInDB) {
        if (CollectionUtils.empty(allList) && CollectionUtils.empty(fieldsInDB)) {
            return Maps.newHashMap();
        }
        // 对比新旧字段列表，找出新增、删除和修改的字段
        Map<String, Object> fieldsDocMap = Maps.newHashMap();

        try {
            // 新建字段
            if (CollectionUtils.empty(fieldsInDB) && CollectionUtils.notEmpty(allList)) {
                List<Map> allFieldDocs = allList.stream().map(a -> FieldDescribeExt.of(a).getFieldDocument()).collect(toList());
                fieldsDocMap.put("added_fields", allFieldDocs);
                return fieldsDocMap;
            }

            // 删除字段
            if (CollectionUtils.empty(allList) && CollectionUtils.notEmpty(fieldsInDB)) {
                List<Map> allFieldDocs = fieldsInDB.stream().map(a -> FieldDescribeExt.of(a).getFieldDocument()).collect(toList());
                fieldsDocMap.put("deleted_fields", allFieldDocs);
                return fieldsDocMap;
            }

            //更新字段
            // 将旧字段列表转换为Map，方便查找
            Map<String, IFieldDescribe> oldFieldMap = fieldsInDB.stream()
                    .collect(Collectors.toMap(IFieldDescribe::getApiName, Function.identity()));

            // 将新字段列表转换为Map，方便查找
            Map<String, IFieldDescribe> newFieldMap = allList.stream()
                    .collect(Collectors.toMap(IFieldDescribe::getApiName, Function.identity()));

            // 找出新增的字段
            List<Map> addedFields = allList.stream()
                    .filter(field -> !oldFieldMap.containsKey(field.getApiName()))
                    .map(field -> FieldDescribeExt.of(field).getFieldDocument())
                    .collect(toList());

            // 找出删除的字段
            List<Map> deletedFields = fieldsInDB.stream()
                    .filter(field -> !newFieldMap.containsKey(field.getApiName()))
                    .map(field -> FieldDescribeExt.of(field).getFieldDocument())
                    .collect(toList());


            // 找出修改的字段
            List<Map> changedFields = new ArrayList<>();
            for (IFieldDescribe newField : allList) {
                String apiName = newField.getApiName();
                if (oldFieldMap.containsKey(apiName)) {
                    IFieldDescribe oldField = oldFieldMap.get(apiName);
                    // 比较新旧字段，检查是否有变化
                    Map<String, Object> newFieldDoc = FieldDescribeExt.of(newField).getFieldDocument();
                    Map<String, Object> oldFieldDoc = FieldDescribeExt.of(oldField).getFieldDocument();

                    // 创建一个Map来存储变化的属性
                    Map<String, Object> changedProperties = new HashMap<>();
                    boolean hasChanges = false;

                    // 遍历新字段的所有属性，与旧字段比较
                    for (Map.Entry<String, Object> entry : newFieldDoc.entrySet()) {
                        String key = entry.getKey();
                        Object newValue = entry.getValue();
                        Object oldValue = oldFieldDoc.get(key);

                        // 检查属性值是否发生变化
                        if (!Objects.equals(newValue, oldValue)) {
                            changedProperties.put(key, newValue);
                            changedProperties.put(key + "_old", oldValue);
                            hasChanges = true;
                        }
                    }

                    // 如果有变化，将变化的字段添加到changedFields列表中
                    if (hasChanges) {
                        Map<String, Object> changedFieldInfo = Maps.newHashMap();
                        changedFieldInfo.put(IFieldDescribe.API_NAME, apiName);
                        changedFieldInfo.put("changed_properties", changedProperties);
                        changedFields.add(changedFieldInfo);
                    }
                }
            }

            // 将所有字段文档添加到结果Map中
            fieldsDocMap.put("added_fields", addedFields);
            fieldsDocMap.put("deleted_fields", deletedFields);
            fieldsDocMap.put("changed_fields", changedFields);
        } catch (Exception e) {
            log.warn("getFieldsDocMap error, allList:{},fieldsInDB:{}", allList, fieldsInDB, e);
        }
        return fieldsDocMap;

    }

    private void validFieldApiNameRepeat(IFieldDescribe fieldDescribe, IObjectDescribe oldDescribe) {
        if (ObjectDescribeExt.of(oldDescribe).containsField(fieldDescribe.getApiName())) {
            throw new ValidateException(I18NExt.text(I18NKey.FIELD_API_NAME_DUPLICATE, fieldDescribe.getApiName()));
        }
    }


    @Override
    @Transactional
    public IObjectDescribe batchAddDescribeCustomField(User user, String describeAPIName, List<IFieldDescribe> fieldDescribeList) {
        // 给describe增加customFieldDescribe
        IObjectDescribe iObjectDescribe = findObject(user.getTenantId(), describeAPIName);
        List<IFieldDescribe> allFieldList = iObjectDescribe.getFieldDescribes();
        allFieldList.addAll(fieldDescribeList);
        iObjectDescribe.setFieldDescribes(allFieldList);
        iObjectDescribe.setLastModifiedBy(user.getUserId());
        iObjectDescribe.setLastModifiedTime(System.currentTimeMillis());
        //更新描述
        updateDescribe(user, iObjectDescribe, null, false);
        //查询布局
        LayoutLogicService.LayoutContext layoutContext = LayoutLogicService.LayoutContext.of(user, null);
        List<ILayout> layouts = layoutLogicService.findByTypes(layoutContext,
                describeAPIName, Lists.newArrayList(LAYOUT_TYPE_DETAIL, LAYOUT_TYPE_EDIT));
        if (CollectionUtils.notEmpty(layouts)) {
            // 把新增的字段放入到指定的layout中的FormComponent中的基本信息FieldSection的里面。
            Map<String, Layout> layoutMap = layouts.stream().collect(Collectors.toMap(ILayout::getName, x -> (Layout) x));
            User newUser = orgService.fillUserName(user);
            user.setUserName(newUser.getUserName());
            layouts.forEach(x -> {
                ILayout layout = layoutMap.get(x.getName());
                LayoutExt layoutExt = LayoutExt.of(layout);
                layoutExt.addFields(fieldDescribeList);
            });

            // 使用并行或同步方式更新布局
            updateLayoutsInParallelOrSequential(layoutMap, user, iObjectDescribe, "batchAddDescribeCustomField");
        }
        return iObjectDescribe;
    }


    @Override
    public void openDetailObjChangeOrder(User user, IObjectDescribe describe) {
        if (!checkOpenChangeOrder(user, describe)) {
            return;
        }
        changeOrderLogicService.openDetailChangeOrder(user, describe);
    }

    public void addFieldForOriginalDescribe(User user, IObjectDescribe describe) {
        if (!checkOpenChangeOrder(user, describe)) {
            return;
        }
        changeOrderLogicService.addFieldForOriginalDescribe(user, describe);
    }

    public void removeFieldOriginalDescribe(User user, IObjectDescribe describe) {
        if (!checkOpenChangeOrder(user, describe)) {
            return;
        }
        List<String> fieldList = ChangeOrderConfig.getOriginalDescribeFields(describe.getApiName()).stream()
                .map(IFieldDescribe::getApiName).collect(Collectors.toList());

        describe.removeFieldDescribeList(fieldList);
    }

    private boolean checkOpenChangeOrder(User user, IObjectDescribe describe) {
        if (Objects.isNull(describe) || !describe.isActive()) {
            return false;
        }
        Optional<MasterDetail> masterDetailField = of(describe).getMasterDetailField();
        return masterDetailField.filter(masterDetail -> changeOrderLogicService.isOpenChangeOrder(user, masterDetail.getTargetApiName())).isPresent();
    }

    private void validateImportAddAction(User user, String describeApiName) {
        boolean grayImportAddAction = UdobjGrayUtil.isObjectAndTenantGray("gray_import_add_action", user.getTenantId(), describeApiName);
        if (!grayImportAddAction) {
            return;
        }
        String importSettingKey = buildImportSettingKey(describeApiName);
        String tenantConfig = configService.findTenantConfig(user, importSettingKey);
        if (StringUtils.isBlank(tenantConfig)) {
            return;
        }
        ImportTenantSetting importTenantSetting = JSON.parseObject(tenantConfig, ImportTenantSetting.class);
        if (Objects.isNull(importTenantSetting)) {
            return;
        }
        ImportSetting insertImport = importTenantSetting.getInsertImport();
        if (Objects.isNull(insertImport)) {
            return;
        }
        if (Objects.equals(insertImport.getImportMethod(), ImportTenantSetting.ImportMethod.ADD_ACTION.getMethod())) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.IMPORT_ADD_NOT_ADD_MASTER_DETAIL, "该对象导入设置中导入模式为「同新建页数据逻辑」，此模式从对象不生效，请修改为独立配置，再添加主从字段"));// ignoreI18n
        }
    }

    private String buildImportSettingKey(String describeApiName) {
        return "importSetting_" + describeApiName;
    }

    private void validReferenceFieldByRelatedTeamSwitch(User user, IFieldDescribe fieldDescribe, IObjectDescribe describeDraft) {
        if (!Objects.equals(fieldDescribe.getType(), IFieldType.OBJECT_REFERENCE)) {
            return;
        }
        IObjectReferenceField objectReferenceField = (IObjectReferenceField) fieldDescribe;
        if (StringUtils.isNotEmpty(objectReferenceField.getRelationOuterDataPrivilege())
                && !StringUtils.equalsAny(objectReferenceField.getRelationOuterDataPrivilege(), FieldDescribeExt.RELATION_NOT_RELATED, FieldDescribeExt.RELATION_OUTER_OWNER)) {
            OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), describeDraft);
            if (!optionalFeaturesSwitch.getIsRelatedTeamEnabled()) {
                throw new ValidateException(I18NExt.text(I18NKey.RESET_RELATION_OUTER_DATA_PRIVILEGE_CONFIGURATION));
            }
        }
    }

    private IObjectDescribe addCustomFieldDescribe(User user, IObjectDescribe describeDraft, List<IFieldDescribe> fieldDescribes) throws MetadataServiceException {
        validateEnterpriseRelation(describeDraft, fieldDescribes);
        optionSetLogicService.validateAndUpdateOptionReference(user, describeDraft, fieldDescribes);
        return objectDescribeService.addCustomFieldDescribe(describeDraft, fieldDescribes, getActionContext(user));
    }

    @Override
    public void checkIfHasApprovalFlowDefinition(User user, String describeAPIName) {
        if (AppFrameworkConfig.isInMasterDetailApprovalWhiteList(user.getTenantId())) {
            return;
        }
        if (approvalFlowService.hasApprovalFlowDefinitions(user, describeAPIName)) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_ADD_MASTERDETAIL_FIELD_BECAUSE_HAS_FLOW_DEFINITION));
        }
    }

    private static final Set<String> NO_REQUIRED_FIELD_OBJECTS = Sets.newHashSet(
            "OutboundDeliveryNoteObj",
            "OutboundDeliveryNoteProductObj",
            "GoodsReceivedNoteObj",
            "GoodsReceivedNoteProductObj",
            "CustomerAccountObj",
            "PrepayDetailObj",
            "RebateIncomeDetailObj",
            "StockObj"
    );

    private void noRequiredCheck(String describeAPIName, IFieldDescribe fieldDescribe, IObjectDescribe describeDraft) {
        if (NO_REQUIRED_FIELD_OBJECTS.contains(describeAPIName)) {
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
            if (fieldDescribeExt.isRequired() && fieldDescribeExt.isCustomField()) {
                throw new MetaDataBusinessException(I18N.text(I18NKey.CANNOT_ADD_REQUIREDL_FIELD,
                        describeDraft.getDisplayName()));
            }
        }
    }

    @Override
    public boolean updateLookupRolesList(String tenantId, List<String> objectApiNames, String fieldApiName, Boolean isChecked, String roleType) {
        return false;
    }

    @Override
    public boolean updateLookupRoles(String tenantId, String objectApiName, String fieldApiName, Boolean isChecked, String roleType) {
        IObjectDescribe describe = findObject(tenantId, objectApiName);
        IFieldDescribe fieldDescribe = describe.getFieldDescribe(fieldApiName);
        if (fieldDescribe instanceof IObjectReferenceField) {
            List<String> lookupRoles = ((IObjectReferenceField) fieldDescribe).getLookupRoles();
            lookupRoles = Objects.isNull(lookupRoles) ? Lists.newArrayList() : lookupRoles;
            if (isChecked) {
                lookupRoles.add(roleType);
            } else {
                lookupRoles.remove(roleType);
            }
            ((IObjectReferenceField) fieldDescribe).setLookupRoles(lookupRoles);
            IObjectDescribe objectDescribe = updateFieldDescribe(describe, Lists.newArrayList(fieldDescribe));
            if (Objects.nonNull(objectDescribe)) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    @Override
    public IObjectDescribe updateCustomFieldDescribe(User user, String describeAPIName, String fieldDescribeJson, List<FieldLayoutPojo> layoutPojoList, List<IFieldDescribe> fieldList) {
        return describeLogicService.updateCustomFieldDescribe(user, describeAPIName, fieldDescribeJson, null, layoutPojoList, fieldList);
    }

    @Transactional
    @Override
    public IObjectDescribe updateCustomFieldDescribe(User user, String describeAPIName, String fieldDescribeJson, String persistDataCalc,
                                                     List<FieldLayoutPojo> layoutPojoList, List<IFieldDescribe> fieldList) {
        // 修改fieldDescribe
        StopWatch stopWatch = StopWatch.create("updateCustomFieldDescribe-logicService");
        try {
            IFieldDescribe fieldDescribe = fixFieldDescribe(user, FieldDescribeFactory.newInstance(fieldDescribeJson));

            IObjectDescribe describeDraft = findObject(user.getTenantId(), describeAPIName);
            stopWatch.lap("findObject");
            validReferenceFieldByRelatedTeamSwitch(user, fieldDescribe, describeDraft);
            stopWatch.lap("validReferenceFieldByRelatedTeamSwitch");
            validReferenceFieldCondition(user.getTenantId(), fieldDescribe);
            stopWatch.lap("validReferenceFieldCondition");

            //处理乡镇逻辑findCustomFieldDescribe
            describeDraft = dealTownAndVillageField(user, describeAPIName, fieldList, fieldDescribe, describeDraft);
            stopWatch.lap("dealTownAndVillageField");
            ObjectDescribeExt draftExt = ObjectDescribeExt.of(describeDraft);
            Optional<IFieldDescribe> field = draftExt.getFieldDescribeSilently(fieldDescribe.getApiName());
            if (!field.isPresent()) {
                throw new MetaDataBusinessException(I18N.text(I18NKey.FIELD_ALREADY_DELETED));
            }

            checkLocalizationFileReferenceDupRule(user.getTenantId(), describeAPIName, fieldDescribe, fieldList, describeDraft);
            stopWatch.lap("checkLocalizationFileReferenceDupRule");

            checkDateTimeChangeNotUseMultiTimeZone(field.get(), fieldDescribe, user.getTenantId());
            stopWatch.lap("checkDateTimeChangeNotUseMultiTimeZone");

            // 锁定下游对象的同步字段，不可以进行编辑和删除
//            validateFieldChangeByMtResource(user, Lists.newArrayList(fieldDescribe), draftExt);
            //为库存和发货单添加一个特殊逻辑，不允许添加必填字段
            noRequiredCheck(describeAPIName, fieldDescribe, describeDraft);
            stopWatch.lap("noRequiredCheck");

            //字段校验、提交计算任务计算统计和计算字段
            List<IFieldDescribe> calculateFields = fieldRelationCalculateService.validateByField(describeDraft, fieldDescribe);
            stopWatch.lap("fieldRelationCalculateService.validateByField");
            fieldRelationCalculateService.checkDecimalDigitChangeByFields(describeDraft, Lists.newArrayList(fieldDescribe), true);
            stopWatch.lap("fieldRelationCalculateService.checkDecimalDigitChangeByFields");
            List<IFieldDescribe> quoteFields = fieldRelationCalculateService.checkQuoteField(describeDraft, fieldDescribe);
            stopWatch.lap("fieldRelationCalculateService.checkQuoteField");
            fieldRelationCalculateService.checkObjectReferenceField(user, describeDraft, fieldDescribe);
            stopWatch.lap("fieldRelationCalculateService.checkObjectReferenceField");

            describeDraft.setLastModifiedBy(user.getUserId());
            describeDraft.setLastModifiedTime(System.currentTimeMillis());

            List<IFieldDescribe> allList = Lists.newArrayList();
            allList.addAll(CollectionUtils.nullToEmpty(fieldList));
            allList.add(fieldDescribe);

            allList.addAll(handleSelectFieldCascade(fieldDescribe, describeDraft, draftExt, field.get()));

            ObjectDescribeExt.of(describeDraft).handleNumberStepValue(allList);

            if (FieldDescribeExt.of(fieldDescribe).isAutoNumber()) {
                autoNumberLogicService.autoNumberValidate(describeDraft, Lists.newArrayList((AutoNumber) fieldDescribe), user);
                autoNumberLogicService.updateCondition(describeDraft, Lists.newArrayList(fieldDescribe));
                stopWatch.lap("autoNumberLogic");
            }
            primaryAttributeTypeChange(fieldDescribe, describeDraft);
            stopWatch.lap("primaryAttributeTypeChange");
            //字段唯一性校验
            ObjectDescribeExt.of(describeDraft).checkFieldUnique(allList);
            //校验支持地理位置过滤的定位字段的数量
            ObjectDescribeExt.of(describeDraft).validateGeoLocationField(allList);
            //富文本字段校验
            ObjectDescribeExt.of(describeDraft).validateRichTextFieldInDescribe(allList);
            //label特殊字符去除
            ObjectDescribeExt.of(describeDraft).formatLabel(allList);
            //校验target_related_list_name重复
            ObjectDescribeExt.of(describeDraft).validateObjectReferenceField(allList);
//            ObjectDescribeExt.of(describeDraft).validateGroupField(allList, user.getTenantId());
            checkDisplayFields(fieldDescribe, describeDraft);
            stopWatch.lap("checkDisplayFields");
            // 负责人字段修改为非必输需要同步更新从对象的负责人字段为非必输
            syncMasterAndDetailOwnerField(user, fieldDescribe, describeDraft);
            stopWatch.lap("syncMasterAndDetailOwnerField");
            processWhatListField(fieldDescribe, describeDraft);
            stopWatch.lap("processWhatListField");
            // 校验公共对象
            checkPublicObjectBeforeUpdateDescribe(user, allList, describeDraft);
            stopWatch.lap("checkPublicObjectBeforeUpdateDescribe");

            // 获取db中的字段原始内容
            List<String> apiNameList = allList.stream().map(IFieldDescribe::getApiName).collect(toList());
            List<IFieldDescribe> allFieldsInDB = of(describeDraft).getFieldDescribes(apiNameList);
            IObjectDescribe iObjectDescribe = updateFieldDescribe(user, describeDraft, allList);
            stopWatch.lap("updateFieldDescribe");
            // 记录引用关系
            executeReferenceByField(RefMessage.ActionType.DELETE_AND_CREATE, Lists.newArrayList(fieldDescribe), describeDraft);
            stopWatch.lap("executeReferenceByField");
            //重新计算历史数据
            List<String> fieldNameList = Lists.newArrayList();
            List<String> calculateFieldApiNames = calculateFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            List<String> quoteFieldApiNames = quoteFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            fieldNameList.addAll(calculateFieldApiNames);
            fieldNameList.addAll(quoteFieldApiNames);

            if (TenantUtil.isCalcCriteria(user.getTenantId())) {
                List<IFieldDescribe> calcFields = Lists.newArrayList(calculateFields);
                calcFields.addAll(quoteFields);
                jobScheduleService.submitCalculateJob(user, describeDraft.getApiName(), calcFields, persistDataCalc, false);
            } else {
                jobScheduleService.submitCalculateJob(user, fieldNameList, describeDraft.getApiName());
            }
            stopWatch.lap("submitCalculateJob");
            if (CollectionUtils.notEmpty(layoutPojoList)) {
                List<String> layoutApiNames = layoutPojoList.stream().map(FieldLayoutPojo::getApiName).collect(Collectors.toList());
                Map<String, Layout> layoutMap = layoutLogicService.findLayoutByApiNames(user.getTenantId(), layoutApiNames, describeAPIName);
                stopWatch.lap("findLayoutByApiNames");
                layoutPojoList.stream().filter(x -> layoutMap.containsKey(x.getApiName())).forEach(x -> {
                    ILayout layout = layoutMap.get(x.getApiName());
                    LayoutExt layoutExt = LayoutExt.of(layout);
                    //如果修改的是selectOne，并且是级联的父，且有从layout中移除的行为，且layout中有子存在，则不能被移出layout
                    checkSelectOneCascaded(x, fieldDescribe, layoutExt);
                    if (fieldDescribe instanceof WhatFieldDescribe) {
                        WhatFieldDescribe whatFieldDescribe = (WhatFieldDescribe) fieldDescribe;
                        layoutExt.updateWhatField(whatFieldDescribe, draftExt.getFieldDescribe(whatFieldDescribe.getIdFieldApiName()), fieldList, x);
                    } else if (fieldDescribe instanceof WhatListFieldDescribe) {
                        WhatListFieldDescribe whatListFieldDescribe = ((WhatListFieldDescribe) fieldDescribe);
                        layoutExt.updateWhatListField(whatListFieldDescribe, draftExt.getFieldDescribe(whatListFieldDescribe.getIdFieldApiName()), fieldList, x);
                    } else {
                        layoutExt.updateField(fieldDescribe, fieldList, x);
                    }
                });
                stopWatch.lap("layout.updateField");
                IObjectDescribe finalDescribeDraft = describeDraft;

                User newUser = orgService.fillUserName(user);
                stopWatch.lap("orgService.fillUserName");
                user.setUserName(newUser.getUserName());

                // 使用并行或同步方式更新布局
                updateLayoutsInParallelOrSequential(layoutMap, user, finalDescribeDraft, "updateCustomFieldDescribe");
            }

            Map<String, Object> fieldsDocMap = getFieldsDocMap(allList, allFieldsInDB);
            logService.log(user, EventType.MODIFY, ActionType.UPDATE_FIELD, describeDraft.getApiName(), fieldsDocMap,
                    I18N.text(I18NKey.FIELD_OBJECT, fieldDescribe.getLabel(), iObjectDescribe.getDisplayName()));
            return iObjectDescribe;
        } catch (MetadataServiceException e) {
            log.warn("updateCustomFieldDescribe error,user:{},describeAPIName:{},fieldDescribeJson:{},layoutPojoList:{},fieldList:{}", user, describeAPIName, layoutPojoList, fieldList, e);
            throw new MetaDataBusinessException(e);
        } finally {
            stopWatch.logSlow(1000);
        }
    }

    private void checkLocalizationFileReferenceDupRule(String tenantId, String describeAPIName, IFieldDescribe newFieldDescribe,
                                                       List<IFieldDescribe> newFieldList, IObjectDescribe describeDraft) {

        if (StringUtils.isAnyBlank(tenantId, describeAPIName) || Objects.isNull(describeDraft) ||
                (Objects.isNull(newFieldDescribe) && CollectionUtils.empty(newFieldList))) {
            return;
        }
        if (!AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(tenantId, describeAPIName)) {
            return;
        }

        List<IFieldDescribe> newlocalFieldList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(newFieldList)) {
            newlocalFieldList = newFieldList.stream().filter(x -> FieldDescribeExt.of(x).isLocation()).collect(toList());
        }
        if (FieldDescribeExt.of(newFieldDescribe).isLocation()) {
            newlocalFieldList.add(newFieldDescribe);
        }
        //开启筛选范围则无需校验
        if (CollectionUtils.empty(newlocalFieldList) ||
                newlocalFieldList.stream().map(x -> (Location) x).allMatch(Location::getIsGeoIndex)) {
            return;
        }

        Map<String, IFieldDescribe> oldFieldDescribeMap = describeDraft.getFieldDescribeMap();

        Set<String> geoFields = Sets.newHashSet();

        for (IFieldDescribe fieldDescribe : newlocalFieldList) {
            IFieldDescribe oldField = oldFieldDescribeMap.get(fieldDescribe.getApiName());
            if (Objects.isNull(oldField)) {
                continue;
            }
            Location oldLocationField = (Location) oldField;
            Location newLocationFiled = (Location) fieldDescribe;
            if (newLocationFiled.getIsGeoIndex() != oldLocationField.getIsGeoIndex()) {
                geoFields.add(fieldDescribe.getApiName());
            }
        }

        if (CollectionUtils.empty(geoFields)) {
            return;
        }

        Set<String> geoRuleFileds = Sets.newHashSet();
        List<IDuplicatedSearch> duplicateSearchRuleList = duplicatedSearchService.getDuplicateSearchRuleList(describeAPIName,
                null, tenantId, true, DuplicateSearchOrderByType.ORDER_BY_SORT);

        duplicateSearchRuleList.forEach(x -> geoRuleFileds.addAll(DuplicatedSearchExt.of(x).getGeoFields()));

        if (CollectionUtils.empty(geoRuleFileds) || !geoRuleFileds.containsAll(geoFields)) {
            return;
        }
        throw new ValidateException(I18N.text(I18NKey.GEO_FIELD_REFERENCE_DUP_RULE_ERROR));
    }


    @Override
    public void executeReferenceByField(RefMessage.ActionType actionType, List<IFieldDescribe> sourceFieldDescList, IObjectDescribe sourceObjDesc) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FIND_WHERE_A_FIELD_IS_USED_GRAY_KEY, sourceObjDesc.getTenantId())) {
            return;
        }
        Set<String> targetObjAndUsedRelatedObj = ObjectDescribeExt.of(sourceObjDesc).findTargetObjAndUsedRelatedObj();
        Map<String, IObjectDescribe> allObjDesc = findObjects(sourceObjDesc.getTenantId(), targetObjAndUsedRelatedObj);
        List<Ref> refList = FieldRef.buildRefByField(actionType, sourceFieldDescList, sourceObjDesc, allObjDesc);
        Refs refs = Refs.builder().refs(refList).build();
        refFieldService.sendRefs(refs);
    }

    private void checkDateTimeChangeNotUseMultiTimeZone(IFieldDescribe field, IFieldDescribe fieldDescribe, String tenantId) {
        if (!AppFrameworkConfig.isDateTimeSupportNotUseMultitimeZoneTenant(tenantId)) {
            return;
        }
        if (IFieldType.DATE_TIME.equals(field.getType()) && IFieldType.DATE_TIME.equals(fieldDescribe.getType())) {
            DateTimeFieldDescribe dateTimeFieldDescribeDB = (DateTimeFieldDescribe) field;
            DateTimeFieldDescribe dateTimeFieldDescribe = (DateTimeFieldDescribe) fieldDescribe;
            if (!dateTimeFieldDescribe.getNotUseMultiTimeZone().equals(dateTimeFieldDescribeDB.getNotUseMultiTimeZone())) {
                throw new ValidateException(I18N.text(I18NKey.NOT_USE_MULTI_TIME_ZONE_CANNOT_CHANGE));
            }
        }
    }

    private void primaryAttributeTypeChange(IFieldDescribe fieldDescribe, IObjectDescribe describeInDB) {
        if (StringUtils.equals(fieldDescribe.getApiName(), ObjectDataExt.NAME) && FieldDescribeExt.of(fieldDescribe).isText()) {
            IFieldDescribe primaryFieldDescribe = describeInDB.getFieldDescribe(ObjectDataExt.NAME);
            if (FieldDescribeExt.of(primaryFieldDescribe).isAutoNumber()) {
                autoNumberService.changeFunctionStatus(describeInDB, (AutoNumber) primaryFieldDescribe, IUdefFunction.NOT_USED);
            }
        }
    }

    private List<IFieldDescribe> handleSelectFieldCascade(IFieldDescribe newField, IObjectDescribe describeDraft, ObjectDescribeExt draftExt, IFieldDescribe oldField) {
        if (!FieldDescribeExt.of(newField).isCascadeChildField()
                || !FieldDescribeExt.of(oldField).isCascadeChildField()) {
            return Collections.emptyList();
        }
        if (FieldDescribeExt.of(newField).isGeneralOptions() || FieldDescribeExt.of(oldField).isGeneralOptions()) {
            return Collections.emptyList();
        }
        //判断是否删除了选项，如果是，则清理选项的关系
        //比较选项
        SelectOne newSelectOne = ((SelectOne) newField);
        List<ISelectOption> oldOptions = ((SelectOne) oldField).getSelectOptions();
        List<ISelectOption> deletedOption = oldOptions.stream().filter(a -> !newSelectOne.isOptionExist(a.getValue())).collect(Collectors.toList());
        List<IFieldDescribe> updatedFields = Lists.newArrayList();
        deletedOption.forEach(a -> updatedFields.addAll(draftExt.cleanOptionRelation(newSelectOne, a)));
        List<IFieldDescribe> fieldDescribeList = updatedFields.stream().distinct().collect(Collectors.toList());

        // 触发全量计算
        IObjectDescribe newDescribe = draftExt.mergeFields(Lists.newArrayList(newField));
        fieldRelationCalculateService.checkSelectOneChangeOfFields(newDescribe, describeDraft, Lists.newArrayList(newField), true);
        return fieldDescribeList;
    }

    @Override
    public void processWhatListField(IFieldDescribe fieldDescribe, IObjectDescribe objectDescribeInDB) {
        if (FieldDescribeExt.of(fieldDescribe).isWhatListField()) {
            List<WhatList> whatListFields = ObjectDescribeExt.of(objectDescribeInDB).getWhatListFields();
            if (CollectionUtils.notEmpty(whatListFields)) {
                String functionAPINameInDB = getFunctionAPIName(whatListFields.get(0));
                String newFunctionAPIName = getFunctionAPIName((WhatList) fieldDescribe);
                if (!StringUtils.equals(functionAPINameInDB, newFunctionAPIName)) {
                    if (StringUtils.isNotEmpty(functionAPINameInDB)) {
                        functionLogicService.processFunctionReference(fieldDescribe, SourceTypes.RELATED_SCOPE, functionAPINameInDB, fieldDescribe.getDescribeApiName(), true);
                    }
                    if (StringUtils.isNotEmpty(newFunctionAPIName)) {
                        functionLogicService.processFunctionReference(fieldDescribe, SourceTypes.RELATED_SCOPE, newFunctionAPIName, fieldDescribe.getDescribeApiName(), false);
                    }
                }
            }
        }
    }

    private String getFunctionAPIName(WhatList whatList) {
        return FieldDescribeExt.of(whatList).getFuncApiNameFromWhatListField();
    }

    @Override
    public Map<String, Map<String, Object>> fillRefObjFieldSupportDisplayName(User user, IObjectDescribe objectDescribe) {
        Map<String, Map<String, Object>> resultMap = Maps.newHashMap();
        if (AppFrameworkConfig.isSupportDisplayNameFieldEnterprise(user.getTenantId())) {
            List<IFieldDescribe> refFieldList = ObjectDescribeExt.of(objectDescribe).getAllActiveRefFieldDescribesExcludeWhatField();
            Set<String> refObjApiNameSet = refFieldList.stream().map(field -> FieldDescribeExt.of(field).getRefObjTargetApiName())
                    // 过滤预置对象，预置对象未支持显示字段
                    .filter(apiName -> AppFrameworkConfig.isSupportDisplayNameField(apiName)).collect(Collectors.toSet());
            if (CollectionUtils.notEmpty(refObjApiNameSet)) {
                List<IObjectDescribe> describes = findDescribeListWithoutFields(objectDescribe.getTenantId(), refObjApiNameSet);
                List<String> supportDisplayNameObjectApiNames = describes.stream().filter(describe -> ObjectDescribeExt.of(describe).isSupportDisplayName()).map(describe -> describe.getApiName()).collect(Collectors.toList());
                if (CollectionUtils.notEmpty(supportDisplayNameObjectApiNames)) {
                    refFieldList.stream().filter(field -> supportDisplayNameObjectApiNames.contains(FieldDescribeExt.of(field).getRefObjTargetApiName())).forEach(field -> {
                        Map<String, Object> fieldExtMap = Maps.newHashMap(FieldDescribeExt.of(field).toMap());
                        fieldExtMap.put(IObjectDescribe.IS_OPEN_DISPLAY_NAME, Boolean.TRUE);
                        resultMap.put(FieldDescribeExt.of(field).getRefObjApiName(), fieldExtMap);
                    });
                }
            }
        }
        return resultMap;
    }

//    private void validateFieldChangeByMtResource(User user, List<IFieldDescribe> fieldDescribes, ObjectDescribeExt describeInDb) {
//        List<IFieldDescribe> changedField = describeInDb.getChangedField(fieldDescribes);
//        if (CollectionUtils.empty(changedField)) {
//            return;
//        }
//        List<MtResource> mtResources = null;
//        try {
//            mtResources = mtResourceService.queryResource(user.getTenantId(), describeInDb.getApiName(), MtResource.RESOURCE_TYPE_FILED, null);
//        } catch (Exception e) {
//            log.warn("queryResource failed! ei:{}, describeApiName:{}", user.getTenantId(), describeInDb.getApiName(), e);
//        }
//        if (CollectionUtils.empty(mtResources)) {
//            return;
//        }
//        Set<String> fieldNames = mtResources.stream().filter(it -> MtResource.STATUS_TYPE_ENABLE.equals(it.getStatus())).filter(it -> MtResource.RESOURCE_TYPE_FILED.equals(it.getResourceType())).filter(it -> MtResource.CONTROL_LEVEL_UNCHANGEABLE.equals(it.getControlLevel())).map(MtResource::getResourceValue).collect(Collectors.toSet());
//        if (CollectionUtils.empty(fieldNames)) {
//            return;
//        }
//        String fieldLabel = fieldDescribes.stream().filter(it -> fieldNames.contains(it.getApiName())).map(IFieldDescribe::getLabel).collect(Collectors.joining(", "));
//        if (StringUtils.isNotBlank(fieldLabel)) {
//            throw new ValidateException(I18N.text(I18NKey.DOWNSTREAM_OBJECT_CANNOT_EDIT_DELETE, fieldLabel));
//        }
//    }

    private IObjectDescribe dealTownAndVillageField(User user, String describeAPIName, List<IFieldDescribe> fieldList, IFieldDescribe fieldDescribe, IObjectDescribe describeDraft) {
        if (FieldDescribeExt.of(fieldDescribe).isAreaField() && BooleanUtils.isTrue(((Area) fieldDescribe).getIsSupportTown())) {
            List<IFieldDescribe> appendFieldList = Lists.newArrayList();
            IFieldDescribe addFieldDescribe = null;
            if (BooleanUtils.isTrue(((Area) fieldDescribe).getIsSupportTown())) {
                Optional<IFieldDescribe> areaOptional = of(describeDraft).getFieldDescribeSilently(fieldDescribe.getApiName());
                List<IFieldDescribe> townFieldDescribe = fieldList.stream().filter(x -> IFieldType.TOWN.equals(x.getType())).collect(Collectors.toList());
                if (CollectionUtils.empty(townFieldDescribe)) {
                    log.warn("townFieldDescribe is null,ei={},user={}", user.getTenantId(), user);
                    throw new MetaDataBusinessException(I18NExt.text(I18NKey.TOWN_FIELD_DESCRIBE_NULL));
                }
                if (areaOptional.isPresent() && StringUtils.isBlank(((Area) areaOptional.get()).getAreaTownFieldApiName())) {
                    addFieldDescribe = townFieldDescribe.get(0);
                }
                List<IFieldDescribe> villageFieldDescribe = fieldList.stream().filter(x -> IFieldType.VILLAGE.equals(x.getType())).collect(Collectors.toList());
                if (BooleanUtils.isTrue(((Area) fieldDescribe).getIsSupportVillage())) {
                    if (CollectionUtils.empty(villageFieldDescribe)) {
                        log.warn("villageFieldDescribe is null,ei={},user={}", user.getTenantId(), user);
                        throw new MetaDataBusinessException(I18NExt.text(I18NKey.TOWN_FIELD_DESCRIBE_NULL));
                    }
                    if (areaOptional.isPresent() && StringUtils.isBlank(((Area) areaOptional.get()).getAreaVillageFieldApiName())) {
                        if (Objects.isNull(addFieldDescribe)) {
                            addFieldDescribe = villageFieldDescribe.get(0);
                        } else {
                            appendFieldList.addAll(villageFieldDescribe);
                        }
                    }
                }
                if (Objects.nonNull(addFieldDescribe)) {
                    describeDraft = addDescribeCustomField(user, describeAPIName, addFieldDescribe.toJsonString(), Lists.newArrayList(), appendFieldList);
                }
            }
        }
        describeDraft.setFieldDescribes(describeDraft.getFieldDescribes().stream().filter(x -> !"deleted".equals(x.getStatus())).collect(Collectors.toList()));
        return describeDraft;
    }

    /**
     * 如果修改的是selectOne，并且是级联的父，且有从layout中移除的行为，且layout中有子存在，则不能被移出layout
     */
    private void checkSelectOneCascaded(FieldLayoutPojo layoutPojo, IFieldDescribe fieldDescribe, LayoutExt layout) {
        if (!Objects.equals(fieldDescribe.getType(), IFieldType.SELECT_ONE) || layoutPojo.isShow()) {
            return;
        }
        Set<String> childSelectOne = getChildSelectOne((SelectOne) fieldDescribe, layout.getTenantId());
        if (CollectionUtils.empty(childSelectOne)) {
            return;
        }

        if (childSelectOne.stream().anyMatch(layout::isFieldInLayout)) {
            //该单选字段被当前布局隐藏，且其为父字段
            throw new MetaDataBusinessException(I18N.text(I18NKey.FIELD_CANNOT_HIDDEN_IN_LAYOUT, fieldDescribe.getLabel(), layout.getDisplayName()));

        }
    }

    private Set<String> getChildSelectOne(SelectOne fieldDescribe, String tenantId) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OPTION_DEPENDENCE_GRAY_EI, tenantId)) {
            List<SelectOne> childFields = selectFieldDependenceLogicService.findChildFields(User.systemUser(tenantId), fieldDescribe.getDescribeApiName(), fieldDescribe.getApiName());
            return childFields.stream().map(SelectOne::getApiName).collect(Collectors.toSet());
        }

        List<ISelectOption> selectOptions = fieldDescribe.getSelectOptions();
        Set<String> childSelectOne = Sets.newHashSet();
        for (ISelectOption option : selectOptions) {
            List<Map<String, List<String>>> childOptions = option.getChildOptions();
            if (CollectionUtils.empty(childOptions)) {
                continue;
            }
            childOptions.forEach(a -> childSelectOne.addAll(a.keySet()));
        }
        return childSelectOne;
    }

    @Override
    @Transactional
    public IObjectDescribe deleteCustomField(User user, String describeAPIName, String fieldApiName) {
        log.debug("Entering deleteField(describeAPIName={},fieldApiName={})", describeAPIName, fieldApiName);
        IObjectDescribe describe = findObject(user.getTenantId(), describeAPIName);
        describe.setLastModifiedBy(user.getUserId());
        describe.setLastModifiedTime(System.currentTimeMillis());

        CheckerResult result = null;
        IFieldDescribe fieldDescribe;
        try {
            fieldDescribe = describe.getFieldDescribe(fieldApiName);
            if (Objects.isNull(fieldDescribe)) {
                throw new MetaDataBusinessException(I18N.text(I18NKey.FIELD_ALREADY_DELETED));
            }

            List<IFieldDescribe> list = Lists.newArrayList();
            list.add(fieldDescribe);


            if (IFieldType.GROUP.equals(fieldDescribe.getType())) {
                List<String> groupInnerFieldApiNames = Lists.newArrayList();
                ObjectDescribeExt.of(describe).getFieldDescribes().stream()
                        .filter(x -> FieldDescribeExt.of(x).isGroupField())
                        .filter(x -> !Objects.equals(x.getApiName(), fieldDescribe.getApiName()))
                        .forEach(x -> {
                            groupInnerFieldApiNames.addAll(ObjectDescribeExt.of(describe).getGroupInnerFields(x));
                        });
                GroupField groupField = (GroupField) fieldDescribe;
                List<IFieldDescribe> groupFieldList = groupField.getFieldList(describe);
                groupFieldList.removeIf(x -> Objects.isNull(x) || groupInnerFieldApiNames.contains(x.getApiName()));
                list.addAll(groupFieldList);
            }


            if (Objects.equals(fieldDescribe.isActive(), Boolean.TRUE) && Objects.equals(IFieldType.MASTER_DETAIL, fieldDescribe.getType())) {
                if (ObjectDescribeExt.of(describe).enabledChangeOrder()) {
                    throw new ValidateException(I18N.text(I18NKey.CHANGE_ORDER_EXISTS_CANNOT_DELETE_MASTER_DETAIL_FIELD));
                }
                //某些字段不禁用直接删除，先调用禁用
                CheckerResult checkerResult = disableField(user, describeAPIName, fieldDescribe.getApiName());
                if (!checkerResult.isPass()) {
                    log.warn("disableField failed,user:{},apiName:{},fieldApiName:{},result:{}", user, describeAPIName, fieldApiName, JSON.toJSONString(checkerResult));
                    throw new MetaDataBusinessException(checkerResult.getFailMessage());
                }
            }

            result = fieldService.deleteField(describe, list);
        } catch (MetadataServiceException e) {
            log.warn("disableField failed,user:{},apiName:{},fieldApiName:{},result:{}", user, describeAPIName, fieldApiName, JSON.toJSONString(result), e);
            throw new MetaDataBusinessException(e);
        }

        if (result.isPass()) {
            logService.log(user, EventType.DELETE, ActionType.DELETE_FIELD, describe.getApiName(), I18N.text(I18NKey.FIELD_OBJECT, fieldDescribe.getLabel(), describe.getDisplayName()));
            // 设置删除字段埋点
            OperateReportUtil.postMessage(user.getTenantId(), user.getUserId(), ProductLine.CRM_SERVICE, OperateReport.FIELD, fieldDescribe.getType(), OperateReport.DELETE);

            //如果主从字段，则删掉对应映射关系
            if (Objects.equals(IFieldType.MASTER_DETAIL, fieldDescribe.getType())) {
                objectMappingService.deleteDetailDescribeRule(user, describeAPIName);
            }
            FieldDescribeExt ext = FieldDescribeExt.of(fieldDescribe);
            if (ext.isAutoNumber() && AutoNumberExt.of((AutoNumber) fieldDescribe).isFunctionAutoNumber()) {
                autoNumberService.changeFunctionStatus(describe, (AutoNumber) fieldDescribe, IUdefFunction.NOT_USED);
            } else if (ext.isLookupField() && ext.isObjectReferenceField()) {
                configService.deleteTenantConfig(user, LayoutExt.getRelatedListIssueKey(ext.getDescribeApiName(), ext.getApiName()));
            } else if (ext.isGeneralOptions()) {
                optionSetLogicService.deleteReferenceByField(user, describe, fieldDescribe);
            } else if (ext.isWhatListField()) {
                String functionAPIName = getFunctionAPIName((WhatList) fieldDescribe);
                if (StringUtils.isNotEmpty(functionAPIName)) {
                    functionLogicService.processFunctionReference(fieldDescribe, SourceTypes.RELATED_SCOPE, functionAPIName, fieldDescribe.getDescribeApiName(), false);
                }
            }
            // 删除引用关系
            executeReferenceByField(RefMessage.ActionType.DELETE, Lists.newArrayList(fieldDescribe), describe);
            // 移除字段后台扩展信息
            fieldBackgroundExtraService.delete(user, describeAPIName, Lists.newArrayList(fieldApiName));
            fieldRelationCalculateService.cleanReferenceFieldRelation(user, describe, fieldDescribe);
            return result.getDescribe();
        } else {
            log.warn("deleteCustomField failed,user:{},apiName:{},fieldApiName:{},result:{}", user, describeAPIName, fieldApiName, JSON.toJSONString(result));
            throw new MetaDataBusinessException(result.getFailMessage());
        }
    }

    @Override
    public void deleteFieldDirect(User user, IObjectDescribe objectDescribe, Collection<String> fieldNames) {
        if (CollectionUtils.empty(fieldNames)) {
            return;
        }
        List<IFieldDescribe> fields = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(Lists.newArrayList(fieldNames)).stream()
                .filter(field -> !FieldDescribeExt.of(field).isSystemField())
                .collect(Collectors.toList());
        if (CollectionUtils.empty(fields)) {
            return;
        }
        fields.forEach(field -> field.setActive(false));
        try {
            CheckerResult checkerResult = fieldService.deleteFieldDirect(objectDescribe, fields);
            if (!checkerResult.isPass()) {
                log.warn("deleteCustomField failed,user:{},apiName:{},fieldApiName:{},result:{}", user, objectDescribe.getApiName(), fieldNames, JSON.toJSONString(checkerResult));
                throw new MetaDataBusinessException(checkerResult.getFailMessage());
            }
            //清理计算字段的引用关系
            fieldRelationCalculateService.deleteFormulaReferenceByFields(user.getTenantId(), objectDescribe.getApiName(), fields);
        } catch (MetadataServiceException e) {
            log.warn("disableField failed,user:{},apiName:{},fieldApiName:{}", user, objectDescribe.getApiName(), fieldNames, e);
            throw new MetaDataBusinessException(e);
        }
        // TODO: 2023/4/17 修改记录等
    }

    @Override
    public CheckerResult disableField(User user, String describeApiName, String fieldApiName) {
        IObjectDescribe describeDraft = findObject(user.getTenantId(), describeApiName);
        describeDraft.setLastModifiedBy(user.getUserId());
        describeDraft.setLastModifiedTime(System.currentTimeMillis());

        CheckerResult result = null;
        IFieldDescribe fieldDescribe;
        try {
            fieldDescribe = describeDraft.getFieldDescribe(fieldApiName);
            if (Objects.isNull(fieldDescribe)) {
                throw new MetaDataBusinessException(I18N.text(I18NKey.FIELD_ALREADY_DELETED));
            }

            List<IFieldDescribe> list = null;
            if (IFieldType.GROUP.equals(fieldDescribe.getType())) {
                GroupField groupField = (GroupField) fieldDescribe;
                list = groupField.getFieldList(describeDraft);
            }

            if (null == list) {
                list = Lists.newArrayList();
            }
            list.add(fieldDescribe);

            result = fieldService.disableField(describeDraft, list, getActionContext(user));
        } catch (MetadataServiceException e) {
            log.warn("disableCustomField error,user:{},apiName:{},fieldApiName:{},result:{}", user, describeApiName, fieldApiName, JSON.toJSONString(result), e);
            throw new MetaDataBusinessException(e);
        }

        if (result.isPass()) {
            //从布局中删除该字段
            removeFieldInLayout(user, describeApiName, fieldApiName);

            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
            if (fieldDescribeExt.isCountField() || fieldDescribeExt.isFormula() || fieldDescribeExt.hasCalculateValue() || (fieldDescribeExt.isQuoteField() && fieldDescribeExt.isIndex())) {
                jobScheduleService.cancelCalculateJob(user, describeApiName, fieldApiName, fieldDescribeExt.getType());
            }
            logService.log(user, EventType.DISABLE, ActionType.DISABLE_FIELD, describeDraft.getApiName(), I18N.text(I18NKey.FIELD_OBJECT, fieldDescribe.getLabel(), describeDraft.getDisplayName()));
        } else {
            log.warn("disableCustomField failed,user:{},apiName:{},fieldApiName:{},result:{}", user, describeApiName, fieldApiName, JSON.toJSONString(result));
            throw new MetaDataBusinessException(result.getFailMessage());
        }

        return result;
    }

    @Override
    public IObjectDescribe enableField(User user, String describeApiName, String fieldApiName) {
        return enableField(user, describeApiName, fieldApiName, null);
    }

    @Override
    public IObjectDescribe enableField(User user, String describeApiName, String fieldApiName, String persistentDataCalc) {
        IObjectDescribe describe = findObject(user.getTenantId(), describeApiName);
        describe.setLastModifiedBy(user.getUserId());
        describe.setLastModifiedTime(System.currentTimeMillis());

        CheckerResult result = null;
        IFieldDescribe fieldDescribe;
        try {
            fieldDescribe = describe.getFieldDescribe(fieldApiName);
            if (Objects.isNull(fieldDescribe)) {
                throw new MetaDataBusinessException(I18N.text(I18NKey.FIELD_ALREADY_DELETED));
            }

            List<IFieldDescribe> list = null;
            if (IFieldType.GROUP.equals(fieldDescribe.getType())) {
                GroupField groupField = (GroupField) fieldDescribe;
                list = groupField.getFieldList(describe);
            }

            if (null == list) {
                list = Lists.newArrayList();
            }
            list.add(fieldDescribe);
            //校验自定义字段数量限制
            if (isEnableCheckEnterpriseResourcesQuote(user.getTenantId())) {
                checkCustomFieldCountLimit(user, describe, null);
            }
            //字段校验
            IFieldDescribe cpField = FieldDescribeExt.copy(fieldDescribe);
            cpField.setActive(true);
            List<IFieldDescribe> calculateFields = fieldRelationCalculateService.validateByField(describe, cpField);
            List<IFieldDescribe> quoteFields = fieldRelationCalculateService.checkQuoteField(describe, cpField);
            if (CollectionUtils.notEmpty(quoteFields)) {
                calculateFields.addAll(quoteFields);
            }

            result = enableField(user, describe, list);

            //重新计算历史数据
            List<String> fieldNameList = calculateFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            if (TenantUtil.isCalcCriteria(user.getTenantId())) {
                jobScheduleService.submitCalculateJob(user, describeApiName, calculateFields, persistentDataCalc, false);
            } else {
                jobScheduleService.submitCalculateJob(user, fieldNameList, describeApiName);
            }

        } catch (MetadataServiceException e) {
            log.warn("enableField failed,user:{},apiName:{},fieldApiName:{},result:{}", user, describeApiName, fieldApiName, JSON.toJSONString(result), e);
            throw new MetaDataBusinessException(e);
        }

        if (result.isPass()) {
            logService.log(user, EventType.ENABLE, ActionType.ENABLE_FIELD, describe.getApiName(), I18N.text(I18NKey.FIELD_OBJECT, fieldDescribe.getLabel(), describe.getDisplayName()));
            return result.getDescribe();
        } else {
            log.warn("enableField failed,user:{},apiName:{},fieldApiName:{},result:{}", user, describeApiName, fieldApiName, JSON.toJSONString(result));
            throw new MetaDataBusinessException(result.getFailMessage());
        }
    }

    private CheckerResult enableField(User user,IObjectDescribe describe, List<IFieldDescribe> fieldDescribes) throws MetadataServiceException {
        fieldDescribes.stream().filter(Objects::nonNull).forEach(x -> x.setActive(true));
        validateEnterpriseRelation(describe, fieldDescribes, false);
        return fieldService.enableField(describe, fieldDescribes, getActionContext(user));
    }

    @Override
    public List<IconExt> findIconList() {
        List<IconExt> result = Lists.newArrayList();
        if (CollectionUtils.empty(objectIcon.getIconPathList())) {
            return result;
        }
        int index = 0;
        for (String path : objectIcon.getIconPathList()) {
            result.add(IconExt.builder().iconIndex(index++).iconPath(path).build());
        }
        return result;
    }

    @Override
    public DescribeResult findDescribeAndLayout(User user, String describeApiName, boolean isIncludeLayout, String layoutApiName) {
        log.debug("Entering findDescribeAndLayout,tenantId:{},apiName:{}", user.getTenantId(), describeApiName);
        IObjectDescribe describe = findObject(user.getTenantId(), describeApiName);
        //给单选和多选字段补充默认的其他选项
        processSelectFields(describe);
        ILayout layout = null;
        if (isIncludeLayout && !Strings.isNullOrEmpty(layoutApiName)) {
            layout = layoutLogicService.findLayoutByApiName(user, layoutApiName, describe.getApiName());
        }
        if (isIncludeLayout && layout == null) {
            layout = layoutLogicService.findDefaultLayout(user, ILayout.DETAIL_LAYOUT_TYPE, describe.getApiName());
        }

        return DescribeResult.builder().objectDescribe(describe).layout(layout).build();
    }

    //给单选和多选字段补充默认的其他选项，不包含签到组件中的字段
    private void processSelectFields(IObjectDescribe describe) {
        ObjectDescribeExt.of(describe).fillOtherOptionInSelectOneFields();
    }

    @Override
    public void checkDescribeCountLimit(User user) {
        checkDescribeCountLimit(user, false);
    }

    @Override
    public void checkDescribeCountLimit(User user, boolean isEnable) {
        checkDescribeCountLimit(user, isEnable, false, false);
    }

    @Override
    public void checkDescribeCountLimit(User user, boolean isEnable, boolean isBigObject, boolean isSocialObject) {
        checkDescribeCountLimit(user, isEnable, isBigObject, isSocialObject, 0);
    }

    @Override
    public void checkDescribeCountLimit(User user, boolean isEnable, boolean isBigObject, boolean isSocialObject, int countOperateNum) {
        int describeCount = getCustomDescribeCount(user.getTenantId(), isBigObject, isSocialObject, isBigObject || isSocialObject);
        if (isEnable) {
            describeCount--;
        }
        describeCount = describeCount + countOperateNum;
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder()
                .licenseService(licenseService)
                .user(user)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.UDOBJ.getBizCode()));
        tenantLicenseInfo.checkDescribeCount(describeCount, isBigObject, isSocialObject);
    }

    @Override
    public void checkDescribeMasterDetailLimit(String tenantId, IObjectDescribe objectDescribe) {
        checkDescribeMasterDetailLimit(tenantId, objectDescribe, null);
    }

    @Override
    public void checkMasterDetailDescribeByChangeOrder(User user, IObjectDescribe describe, boolean isCreateDescribe) {
        if (!describe.isActive()) {
            return;
        }
        ObjectDescribeExt.of(describe)
                .getMasterDetailField()
                .ifPresent(masterDetail -> checkMasterDetailDescribeByChangeOrder(user, describe, masterDetail, isCreateDescribe));
    }

    @Override
    public void touchDescribe(IObjectDescribe describe) {
        try {
            objectDescribeService.touchDescribe(describe, getActionContext(null));
        } catch (MetadataServiceException e) {
            log.warn("touchDescribe fail ei:{}, apiName:{}", describe.getTenantId(), describe.getApiName(), e);
        }
    }

    private void checkDescribeMasterDetailLimit(String tenantId, IObjectDescribe objectDescribe, MasterDetail mdField) {
        String targetApiName = null;
        if (Objects.nonNull(mdField)) {
            targetApiName = mdField.getTargetApiName();
        } else {
            Optional<MasterDetailFieldDescribe> masterDetailFieldDescribe = ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe();
            if (masterDetailFieldDescribe.isPresent()) {
                targetApiName = masterDetailFieldDescribe.get().getTargetApiName();
            }
        }

        if (Strings.isNullOrEmpty(targetApiName)) {
            return;
        }

        validateImportAddAction(User.systemUser(tenantId), objectDescribe.getApiName());

        ObjectDescribeExt.of(objectDescribe).checkMDFieldForOcr();

        List<IObjectDescribe> detailDescribes = findSimpleDetailDescribes(tenantId, targetApiName);
        detailDescribes.removeIf(x -> StringUtils.equals(x.getApiName(), objectDescribe.getApiName()));
        //一个Master对象的Detail个数限制：5个,支持按企业和对象限制
        int maxDetailCountByTenant = AppFrameworkConfig.getMaxDetailObjectCount(tenantId);
        int maxDetailCountPreObject = AppFrameworkConfig.getMaxDetailObjectCountPreObject(targetApiName);
        int maxDetailCount = Math.max(maxDetailCountByTenant, maxDetailCountPreObject);
        if (detailDescribes.size() >= maxDetailCount) {
            Map<String, String> displayNameMap = findDisplayNameByApiNames(tenantId, Lists.newArrayList(targetApiName));
            throw new ValidateException(I18N.text(I18NKey.SLAVE_OBJECTS_BE_PEAK_VALUE_V2,
                    displayNameMap.getOrDefault(targetApiName, targetApiName), detailDescribes.size(), maxDetailCount));
        }
        //MD不支持多级
        if (isMasterObject(tenantId, objectDescribe.getApiName())) {
            throw new ValidateException(I18N.text(I18NKey.CANNOT_ADD_MASTERDETAIL_FIELD));
        }

    }

    private void checkMasterDetailDescribeByChangeOrder(User user, IObjectDescribe describeDraft, MasterDetail masterDetail, boolean isCreateDescribe) {
        if (!changeOrderLogicService.isOpenChangeOrder(user, masterDetail.getTargetApiName())) {
            return;
        }
        if (!masterDetail.getIsCreateWhenMasterCreate() || masterDetail.getShowDetailButton()) {
            throw new ValidateException(I18N.text(I18NKey.DETAIL_OBJ_SINGLE_CREATE_CANNOT_OPEN_CHANGE_ORDER));
        }
        if (ObjectDescribeExt.getChangeOrderApiName(describeDraft.getApiName()).length() > DESCRIBE_API_NAME_LENGTH) {
            throw new ValidateException(I18N.text(I18NKey.APINAME_LENGTH_BEYOND_MAX_LIMIT, ObjectDescribeExt.getChangeOrderApiName(describeDraft.getApiName())));
        }
        if (changeOrderLogicService.isOpenChangeOrder(user, describeDraft.getApiName())) {
            throw new ValidateException(I18N.text(I18NKey.CHANGE_ORDER_EXISTS_CANNOT_ADD_MD, describeDraft.getDisplayName()));
        }
        if (describeLogicService.isExistObjectByApiName(user.getTenantId(), ObjectDescribeExt.getChangeOrderApiName(describeDraft.getApiName()))) {
            throw new ValidateException(I18N.text(I18NKey.CHANGE_ORDER_OBJECT_API_NAME_EXIST, ObjectDescribeExt.getChangeOrderApiName(describeDraft.getApiName())));
        }
        checkDescribeCountLimit(user, false, false, false, isCreateDescribe ? 2 : 1);
    }

    @Override
    public QueryResult<ObjectDescribe> findDescribeListWithFields(User user, boolean isActive, int limit, int offset, String orderBy, boolean isAsc) {
        log.debug("Enter findDescribeListWithFields, tenantId:{}, isActive:{}, limit:{}, offset:{}, orderBy:{}, isAsc:{}", user.getTenantId(), isActive, limit, offset, orderBy, isAsc);
        Map<String, Object> queryInfo = Maps.newHashMap();
        queryInfo.put("is_active", isActive);
        queryInfo.put("limit", limit);
        queryInfo.put("offset", offset);
        queryInfo.put("orderby", orderBy);
        queryInfo.put(IS_ASC, isAsc);
        try {
            QueryResult<ObjectDescribe> queryResult = objectDescribeService.findByExampleWithFields(user.getTenantId(), queryInfo, getActionContext(user));
            return queryResult;
        } catch (MetadataServiceException e) {
            log.warn("Error in findByExampleWithFields", e);
        }
        return null;
    }

    @Override
    public List<IObjectDescribe> findDescribeListWithFields(User user) {
        log.debug("Enter findDescribeListWithFields, tenantId:{}", user.getTenantId());
        try {
            List<IObjectDescribe> describe = objectDescribeService.findDescribeWithNameAndRecordTypeFieldByTenantId(
                    user.getTenantId(), DefObjConstants.PACKAGE_NAME_CRM, getActionContext(user));
            return describe;
        } catch (MetadataServiceException e) {
            log.warn("Error in findByExampleWithFields", e);
        }
        return Lists.newArrayList();
    }

    @Override
    public void checkDisableFields(User user, String describeApiName, List<String> fieldList) {
        IObjectDescribe objectDescribe = findObject(user.getTenantId(), describeApiName);
        List<IFieldDescribe> fields = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(fieldList);
        try {
            fieldService.checkDisableFields(objectDescribe, fields);
        } catch (MetadataServiceException e) {
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public int getCustomDescribeCount(String tenantId) {
        return getCustomDescribeCount(tenantId, false, false, false);
    }

    @Override
    public int getCustomDescribeCount(String tenantId, boolean includeBigObject, boolean includeSocialObject, boolean onlyVisibleScope) {
        Map example = Maps.newHashMap();
        example.put(IObjectDescribe.PACKAGE, "CRM");
        example.put(IObjectDescribe.DEFINE_TYPE, IObjectDescribe.DEFINE_TYPE_CUSTOM);
        example.put(IObjectDescribe.IS_DELETED, false);
        example.put("exclude_bi", true);

        try {
            IActionContext context = ActionContextExt.of(new User(tenantId, null)).getContext();

            Set<String> visibleScopeSetSet = Sets.newHashSet();
            if (includeBigObject) {
                visibleScopeSetSet.addAll(BIG_OBJECT_VISIBLE_SCOPE);
            }
            if (includeSocialObject) {
                visibleScopeSetSet.add(IObjectDescribe.VISIBLE_SCOPE_SOCIAL);
            }
            context.setIncludeVisibleScopeSet(visibleScopeSetSet);
            context.setIsOnlyVisibleScope(onlyVisibleScope);
            return objectDescribeService.getCountByExample(tenantId, example, context);
        } catch (MetadataServiceException e) {
            log.warn("getCustomDescribeCount error,tenantId:{}", tenantId, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void syncMasterAndDetailOwnerField(User user, IObjectDescribe objectDescribe) {
        syncMasterAndDetailOwnerField(user, objectDescribe, null);
    }

    @Override
    public void syncMasterAndDetailOwnerField(User user, IObjectDescribe objectDescribe, IObjectDescribe objectDescribeInDB) {
        if (Utils.SALES_ORDER_PRODUCT_API_NAME.equals(objectDescribe.getApiName())
                || Utils.SALES_ORDER_API_NAME.equals(objectDescribe.getApiName())) {
            return;
        }
        Optional<IFieldDescribe> ownerField = ObjectDescribeExt.of(objectDescribe).getOwnerField();
        if (ownerField.isPresent()) {
            if (Objects.nonNull(objectDescribeInDB)) {
                Optional<IFieldDescribe> ownerFieldInDB = ObjectDescribeExt.of(objectDescribeInDB).getOwnerField();
                // 数据库对象描述与上送对象描述负责人是否非必填一致则跳过下面逻辑
                if (ownerFieldInDB.isPresent() && Objects.equals(ownerFieldInDB.get().isRequired(), ownerField.get().isRequired())) {
                    return;
                }
            }
            // 从对象和主对象负责人非必填保持一致
            syncDetailFromMasterOwnerField(user, objectDescribe);
            // 主对象负责人非必填同步从对象
            if (Objects.nonNull(objectDescribeInDB)) {
                syncMasterToDetailOwnerField(user, objectDescribe, ownerField.get().isRequired());
            }
        }
    }

    private void syncMasterAndDetailOwnerField(User user, IFieldDescribe fieldDescribe, IObjectDescribe describeInDb) {
        if (Utils.SALES_ORDER_PRODUCT_API_NAME.equals(describeInDb.getApiName())
                || Utils.SALES_ORDER_API_NAME.equals(describeInDb.getApiName())) {
            return;
        }
        Optional<IFieldDescribe> oldOwnerField = ObjectDescribeExt.of(describeInDb).getOwnerField();
        if (oldOwnerField.isPresent() && Objects.equals(ObjectDataExt.OWNER, FieldDescribeExt.of(fieldDescribe).getApiName())
                && !Objects.equals(FieldDescribeExt.of(fieldDescribe).isRequired(), oldOwnerField.get().isRequired())) {
            // 从对象负责人是否必填需要和主对象保持一致
            syncDetailFromMasterOwnerField(user, fieldDescribe, describeInDb);
            // 同步从对象负责人是否非必填
            syncMasterToDetailOwnerField(user, describeInDb, FieldDescribeExt.of(fieldDescribe).isRequired());

        }
    }

    private IFieldDescribe addMasterDetailFieldAndSyncOwnerField(User user, IFieldDescribe fieldDescribe, IObjectDescribe describeInDb) {
        Optional<IFieldDescribe> ownerFieldInDb = ObjectDescribeExt.of(describeInDb).getOwnerField();
        if (ownerFieldInDb.isPresent() && FieldDescribeExt.of(fieldDescribe).isMasterDetailField()) {
            String targetApiName = ((MasterDetail) fieldDescribe).getTargetApiName();
            IObjectDescribe masterObjectDescribe = findObjectWithoutCopyIfGray(user.getTenantId(), targetApiName);
            Optional<IFieldDescribe> masterOwner = ObjectDescribeExt.of(masterObjectDescribe).getOwnerField();
            if (masterOwner.isPresent()) {
                Boolean masterOwnerRequired = masterOwner.get().isRequired();
                if (!Objects.equals(masterOwnerRequired, ownerFieldInDb.get().isRequired())) {
                    IFieldDescribe ownerField = ownerFieldInDb.get();
                    ownerField.setRequired(masterOwnerRequired);
                    return ownerField;
                }
            }
        }
        return null;
    }

    private void syncMasterToDetailOwnerField(User user, IObjectDescribe objectDescribe, Boolean ownerFiledRequired) {
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            return;
        }
        List<IObjectDescribe> detailDescribes = findDetailDescribes(user.getTenantId(), objectDescribe.getApiName());
        if (CollectionUtils.notEmpty(detailDescribes)) {
            detailDescribes.stream().map(ObjectDescribeExt::of).filter(describe -> describe.getOwnerField().filter(it -> !Objects.equals(ownerFiledRequired, it.isRequired())).isPresent()).forEach(obj -> {
                obj.getOwnerField().ifPresent(ownerFieldDescribe -> {
                    ownerFieldDescribe.setRequired(ownerFiledRequired);
                    updateFieldDescribe(user, obj.getObjectDescribe(), Lists.newArrayList(ownerFieldDescribe));
                });
            });
        }
    }

    private void syncDetailFromMasterOwnerField(User user, IObjectDescribe objectDescribe) {
        syncDetailFromMasterOwnerField(user, null, objectDescribe);
    }

    private void syncDetailFromMasterOwnerField(User user, IFieldDescribe fieldDescribe, IObjectDescribe objectDescribe) {
        ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe().ifPresent(masterDetailFieldDescribe -> {
            IObjectDescribe masterObjectDescribe = findObjectWithoutCopyIfGray(user.getTenantId(), masterDetailFieldDescribe.getTargetApiName());
            ObjectDescribeExt.of(masterObjectDescribe).getOwnerField().ifPresent(field -> {
                ObjectDescribeExt.of(objectDescribe).getOwnerField().ifPresent(owner -> owner.setRequired(field.isRequired()));
                if (Objects.nonNull(fieldDescribe)) {
                    fieldDescribe.setRequired(field.isRequired());
                }
            });
        });
    }

    @Override
    public IObjectDescribe updateFieldDescribe(User user, IObjectDescribe describe, List<IFieldDescribe> updatedFields) {
        if (CollectionUtils.empty(updatedFields)) {
            return describe;
        }

        validateEnterpriseRelation(describe, updatedFields, false);

        // 检查单选字段是否存在成环依赖
        checkSelectCascadeCircle(describe, updatedFields);

        // 校验单选、多选的选项集
        optionSetLogicService.validateAndUpdateOptionReference(user, describe, updatedFields);

        correctDescribe(updatedFields);
        try {
            IObjectDescribe describeCopy = describe.copy();
            IObjectDescribe result = objectDescribeService.updateFieldDescribe(describe, updatedFields, getActionContext(user));
            describeChangeEvent.sendDescribeMessage(user, describeCopy, result);
            return result;
        } catch (MetadataServiceException e) {
            log.warn("error in update fields", e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectDescribe updateFieldDescribe(IObjectDescribe describe, List<IFieldDescribe> updatedFields) {
        User user = buildUserByDescribe(describe);
        return updateFieldDescribe(user, describe, updatedFields);
    }

    private void checkSelectCascadeCircle(IObjectDescribe describe, List<IFieldDescribe> updatedFields) {
        if (CollectionUtils.empty(updatedFields)) {
            return;
        }
        // 只校验单选字段
        if (updatedFields.stream().map(FieldDescribeExt::of).noneMatch(FieldDescribeExt::isSelectOne)) {
            return;
        }
        IObjectDescribe copy = of(describe).copy();
        copy.addFieldDescribeList(updatedFields);
        ObjectDescribeExt.of(copy).checkSelectCascadeCircle();
    }

    private void correctDescribe(List<IFieldDescribe> updatedFields) {
        IObjectDescribe objectDescribe = new ObjectDescribe();
        objectDescribe.addFieldDescribeList(updatedFields);
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        describeExt.correctDescribe();
    }

    private void validateEnterpriseRelation(IObjectDescribe describe, List<? extends IFieldDescribe> fields) {
        validateEnterpriseRelation(describe, fields, true);
    }

    /**
     * 校验 关联了客户、合作伙伴的字段上的『关联外部数据权限』属性 是否合法
     * <p>
     * 1、启用了互联业务的租户
     * 2、仅支持设置1个关联客户和1个关联合作伙伴的字段的属性
     *
     * @param describe
     * @param fields
     */
    private void validateEnterpriseRelation(IObjectDescribe describe, List<? extends IFieldDescribe> fields, boolean checkLicense) {
        // TODO: 2021/10/13 把所有的字段先合并后在校验
        Map<String, Set<IObjectReferenceField>> fieldMap = Maps.newHashMap();
        boolean newFieldHasMasterDetail = false;
        for (IFieldDescribe field : fields) {
            if (Objects.isNull(field)) {
                continue;
            }
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(field);
            if (fieldDescribeExt.isMasterDetailField()) {
                newFieldHasMasterDetail = true;
                continue;
            }
            if (!fieldDescribeExt.isActive()) {
                continue;
            }
            if (fieldDescribeExt.isSupportRelationOuterOwner()) {
                IObjectReferenceField fieldDescribe = fieldDescribeExt.getFieldDescribe();
                Set<IObjectReferenceField> value;
                if ((value = fieldMap.get(fieldDescribe.getTargetApiName())) == null) {
                    value = Sets.newHashSet();
                }
                value.add(fieldDescribe);
                fieldMap.put(fieldDescribe.getTargetApiName(), value);
            }
        }
        if (newFieldHasMasterDetail && ObjectDescribeExt.of(describe).getSupportRelationOuterOwnerField().isPresent()) {
            throw new ValidateException(I18N.text(I18NKey.MASTER_DETAIL_UNSUPPORT_RELATION_OUT_DATA));
        }
        if (CollectionUtils.empty(fieldMap)) {
            return;
        }

        if (ObjectDescribeExt.of(describe).isSlaveObject()) {
            throw new ValidateException(I18N.text(I18NKey.MASTER_DETAIL_UNSUPPORT_RELATION_OUT_DATA));
        }
        if (checkLicense && !hasLicense(describe.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKey.NOT_OPEN_ENTERPRISE_INTERCONNECTION));
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        Set<String> updateFieldNames = fields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
        fieldMap.forEach((targetApiName, objectReferenceFields) -> {
            if (objectReferenceFields.size() > 1) {
                String fieldName = objectReferenceFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.joining(","));
                log.warn("validateEnterpriseRelation targetApiName:{}, describeApiName:{}, fieldName:{}", targetApiName, describe.getApiName(), fieldName);
                throw new ValidateException(I18N.text(I18NKey.RELATION_OUT_DATA_FIELD_COUNT));
            }
            describeExt.stream().filter(IFieldDescribe::isActive).map(FieldDescribeExt::of).filter(FieldDescribeExt::isObjectReferenceField).filter(FieldDescribeExt::isSupportRelationOuterOwner).map(it -> ((IObjectReferenceField) it.getFieldDescribe())).forEach(objectReference -> {
                if (!updateFieldNames.contains(objectReference.getApiName()) && Objects.equals(targetApiName, objectReference.getTargetApiName())) {
                    String newFieldName = objectReferenceFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.joining(","));
                    log.warn("validateEnterpriseRelation targetApiName:{}, describeApiName:{}, oldFieldName:{}, newFieldName:{}", targetApiName, describe.getApiName(), objectReference.getApiName(), newFieldName);
                    throw new ValidateException(I18N.text(I18NKey.RELATION_OUT_DATA_FIELD_COUNT));
                }
            });
        });
    }

    private boolean hasLicense(String tenantId) {
        return enterpriseRelationLogicService.supportInterconnectBaseAppLicense(tenantId);
    }

    private void validateEnterpriseRelation(IObjectDescribe describe) {
        validateEnterpriseRelation(describe, true);
    }

    private void validateEnterpriseRelation(IObjectDescribe describe, boolean checkLicense) {
        List<ObjectReferenceFieldDescribe> fieldDescribes = ObjectDescribeExt.of(describe).getActiveSingleReferenceFieldDescribes();
        validateEnterpriseRelation(describe, fieldDescribes, checkLicense);
    }

    @Override
    public IObjectDescribe update(User user, IObjectDescribe objectDescribe) {
        StopWatch sw = StopWatch.create("updateDescribe");
        RLock lock = tryLockObject(objectDescribe.getTenantId(), objectDescribe.getApiName());
        sw.lap("tryLock");
        try {
            // 数据修复
            fixObjectDescribe(User.systemUser(objectDescribe.getTenantId()), objectDescribe);
            //清理单选字段已删除选项的级联选项
            cleanOptionRelation(objectDescribe);
            sw.lap("cleanOptionRelation");
            validateEnterpriseRelation(objectDescribe, false);
            sw.lap("validateEnterpriseRelation");
            ObjectDescribeExt.of(objectDescribe).correctDescribe();
            sw.lap("correctDescribe");
            IObjectDescribe saved = objectDescribeService.update(objectDescribe, getActionContext(user));
            sw.lap("update");
            // 更新字段描述中的关系
            executeReferenceByField(RefMessage.ActionType.DELETE_AND_CREATE, objectDescribe.getFieldDescribes(), objectDescribe);
            sw.lap("executeReferenceByField");
            return saved;
        } catch (MetadataServiceException e) {
            log.warn("Error in update describe", e);
            throw new MetaDataBusinessException(e);
        } finally {
            sw.lap("before-unlock");
            redissonService.unlock(lock);
            sw.lap("unlock");
            sw.logSlow(2000);
        }
    }


    @Override
    public IObjectDescribe update(IObjectDescribe objectDescribe) {
        return update(null, objectDescribe);
    }

    private void cleanOptionRelation(IObjectDescribe objectDescribe) {
        IObjectDescribe oldDescribe = findObjectWithoutCopyIfGray(objectDescribe.getTenantId(), objectDescribe.getApiName());
        ObjectDescribeExt.of(objectDescribe).getSelectOneFields().stream()
                .filter(field -> AppFrameworkConfig.shouldOptionRelationCleanup(objectDescribe.getApiName(), field.getApiName()))
                .filter(x -> oldDescribe.containsField(x.getApiName()))
                .forEach(x -> {
                    SelectOne oldSelectOne = (SelectOne) oldDescribe.getFieldDescribe(x.getApiName());
                    oldSelectOne.getSelectOptions().stream().filter(y -> !x.isOptionExist(y.getValue())).forEach(y -> ObjectDescribeExt.of(objectDescribe).cleanOptionRelation(x, y));
                });
        // 选项集的级联关系
        User user = buildUserByDescribe(objectDescribe);
        optionSetLogicService.validateAndUpdateOptionReference(user, oldDescribe, objectDescribe);
    }

    private User buildUserByDescribe(IObjectDescribe objectDescribe) {
        return Optional.ofNullable(RequestContextManager.getContext()).map(RequestContext::getUser).orElseGet(() -> User.systemUser(objectDescribe.getTenantId()));
    }

    @Override
    public Map<String, DynamicDescribe> findBySelectFields(String tenantId, Map<String, List<String>> describeAndFields, List<String> describeSelects, List<String> fieldSelects) {
        try {
            IActionContext actionContext = getActionContext(new User(tenantId, null));
            return objectDescribeService.batchGetDescribeApiFields(tenantId, describeAndFields, describeSelects, fieldSelects, actionContext);
        } catch (MetadataServiceException e) {
            log.warn("findBySelectFields failed,tenantId:{},describeAndFields:{},describeSelects:{},fieldSelects:{}", tenantId, describeAndFields, describeSelects, fieldSelects, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectDescribe findObjectIncludeMultiField(String tenantId, String apiName) {
        Map<String, IObjectDescribe> objectDescribeMap = findObjectsIncludeMultiField(tenantId, Lists.newArrayList(apiName));
        if (CollectionUtils.empty(objectDescribeMap)) {
            throw new ObjectDefNotFoundError(I18N.text(I18NKey.OBJECT_NOT_EXIST, apiName));
        }
        return objectDescribeMap.get(apiName);
    }

    @Override
    public Map<String, IObjectDescribe> findObjectsIncludeMultiField(String tenantId, Collection<String> apiNames) {
        try {
            List<IObjectDescribe> list = objectDescribeService.findDescribeListByApiNamesWithMultiField(tenantId, Lists.newArrayList(Sets.newHashSet(apiNames)), getActionContext(new User(tenantId, null)));
            return Maps.newHashMap(CollectionUtils.nullToEmpty(list).stream().collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x)));
        } catch (MetadataServiceException e) {
            log.warn("findObjectsIncludeMultiField error,tenantId:{},apiNames:{}", tenantId, apiNames, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void batchUpdateLookupRoles(String tenantId, Map<String, List<IFieldDescribe>> fieldDescribeMap) {
        try {
            objectDescribeService.batchUpdateLookupRoles(tenantId, fieldDescribeMap);
        } catch (MetadataServiceException e) {
            log.warn("Error in batch update lookup roles", e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Map<String, Map<String, Object>> fillQuoteFieldOption(IObjectDescribe objectDescribe) {
        Map<String, String> quoteRelationMap = Maps.newHashMap();
        Map<String, Map<String, Object>> resultMap = Maps.newHashMap();
        List<Quote> quoteFields = objectDescribe.getFieldDescribes().stream()
                .map(x -> FieldDescribeExt.of(x))
                .filter(x -> x.isQuoteField())
//                .filter(x -> x.isIndex())
                .map(x -> (Quote) x.getFieldDescribe())
                .filter(x -> optionType.contains(x.getQuoteFieldType()) || numericalType.contains(x.getQuoteFieldType()))
                .collect(Collectors.toList());
        quoteFields.forEach(x -> {
            String[] split = x.getQuoteField().split("__r");
            IFieldDescribe fieldDescribe = objectDescribe.getFieldDescribe(split[0]);
            if (fieldDescribe != null) {
                String targetApiName;
                if (IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType())) {
                    targetApiName = ((MasterDetail) fieldDescribe).getTargetApiName();
                } else {
                    targetApiName = ((IObjectReferenceField) fieldDescribe).getTargetApiName();
                }
                quoteRelationMap.put(x.getApiName(), targetApiName);
            }
        });
        if (CollectionUtils.notEmpty(quoteRelationMap)) {
            Map<String, IObjectDescribe> describeMap = findObjectsWithoutCopyIfGray(objectDescribe.getTenantId(), Sets.newHashSet(quoteRelationMap.values()));
            quoteFields.forEach(x -> {
                String quoteFieldName = StringUtils.substringAfterLast(x.getQuoteField(), ".");
                String describeApiName = quoteRelationMap.get(x.getApiName());
                IFieldDescribe fieldDescribe = Optional.ofNullable(describeMap.get(describeApiName)).map(describe -> describe.getFieldDescribe(quoteFieldName)).orElse(null);
                if (Objects.isNull(fieldDescribe)) {
                    log.warn("fillQuoteFieldOption fail, describeApiName:{},quoteFieldName:{}", describeApiName, quoteFieldName);
                    return;
                }
                Map<String, Object> fieldExtMap = CopyOnWriteMap.copy(FieldDescribeExt.of(x).toMap());
                if (optionType.contains(x.getQuoteFieldType())) {
                    Object options = fieldDescribe.get("options");
                    //先拷贝一下，防止原始的option被修改
                    if (options instanceof List) {
                        List<Map> newOptions = ((List<Map>) options).stream().map(CopyOnWriteMap::copy).collect(Collectors.toList());
                        fieldExtMap.put("options", newOptions);
                    } else {
                        fieldExtMap.put("options", options);
                    }
                } else if (IFieldType.PERCENTILE.equals(x.getQuoteFieldType())) {
                    fieldExtMap.put("decimal_places", 6);
                    fieldExtMap.put("length", 10);
                } else if (numericalType.contains(x.getQuoteFieldType())) {
                    fieldExtMap.put("decimal_places", fieldDescribe.get("decimal_places"));
                    fieldExtMap.put("length", fieldDescribe.get("length"));
                }
                resultMap.put(x.getApiName(), fieldExtMap);
            });
        }
        return resultMap;
    }

    private boolean haveWhatField(ILayout layout, WhatFieldDescribe fieldDescribe) {
        String idField = fieldDescribe.getIdFieldApiName();
        return LayoutExt.of(layout).isFieldInLayout(idField);
    }

    private boolean haveWhatListField(ILayout layout, WhatListFieldDescribe fieldDescribe) {
        String idField = fieldDescribe.getIdFieldApiName();
        return LayoutExt.of(layout).isFieldInLayout(idField);
    }

    private boolean haveDateTimeRangeField(ILayout layout, DateTimeRangeFieldDescribe fieldDescribe) {
        String startTime = fieldDescribe.getStartTimeFieldApiName();
        return LayoutExt.of(layout).isFieldInLayout(startTime);
    }

    private IFieldSection getSectionInDetailLayout(ILayout layout, String fieldApiName) {
        Optional<FormComponentExt> formComponent = LayoutExt.of(layout).getFormComponent();
        if (formComponent.isPresent()) {
            Optional<IFieldSection> fieldSection = formComponent.get().getFieldSection(fieldApiName);
            if (fieldSection.isPresent()) {
                return fieldSection.get();
            }
        }
        return null;
    }

    private ILayout getLayoutResult(IObjectDescribe describe, boolean includeLayout, String layoutType, User user, String recordType, IObjectData data) {
        ILayout layout = new Layout();
        if (includeLayout) {
            layout = layoutLogicService.findObjectLayoutWithTypeIncludeAllComponent(user, recordType, describe, layoutType, data);
            List<IButton> buttons = findButtons(describe, layoutType, user);
            layout.setButtons(buttons);
        }
        return layout;
    }

    private List<IButton> findButtons(IObjectDescribe describe, String layoutType, User user) {
        if (LAYOUT_TYPE_ADD.equals(layoutType)) {
            return customButtonService.findButtonsForCreate(describe, user);
        }
        if (LAYOUT_TYPE_EDIT.equals(layoutType)) {
            return customButtonService.findButtonsForEdit(describe, user);
        }
        return buttonLogicService.getButtonByComponentActions(user, ComponentActions.LIST_PAGE_HEADER, describe, null, false);
    }

    private void insertActionsIntoDescribe(IObjectDescribe objectDescribe) {
        for (String supportCode : SUPPORT_ACTION_CODE) {
            IActionDescribe changeOwnerAction = new ActionDescribe();
            changeOwnerAction.setActionCode(supportCode);
            changeOwnerAction.setSourceType(SUPPORT_ACTION_SOURCE_TYPE_JAVA_SPRING);
            changeOwnerAction.setActionClass(DefObjUtil.getActionClassNameByActionCode(supportCode));
            changeOwnerAction.setLabel(ObjectAction.of(supportCode).getActionLabel());
            objectDescribe.addActionDescribe(changeOwnerAction);
        }
    }

    private void fillRecordTypeTo(IObjectDescribe describeDraft) {
        RecordTypeFieldDescribe recordTypeField = createRecordTypeField(describeDraft);
        describeDraft.addFieldDescribe(recordTypeField);
    }

    private RecordTypeFieldDescribe createRecordTypeField(IObjectDescribe objectDescribe) {
        RecordTypeFieldDescribe typeFieldDescribe = generateRecordTypeField(RECORD_TYPE, objectDescribe);
        typeFieldDescribe.setRequired(false);

        //设置默认业务类型
        if (null == typeFieldDescribe.getRecordTypeOptions() || typeFieldDescribe.getRecordTypeOptions().size() == 0) {
            List<IRecordTypeOption> options = Lists.newArrayList();
            options.add(0, new RecordTypeOption(I18N.text(I18NKey.DEFAULT_BUSINESS_TYPE), MultiRecordType.RECORD_TYPE_DEFAULT, I18N.text(I18NKey.DEFAULT_BUSINESS_TYPE), true));
            typeFieldDescribe.setRecordTypeOptions(options);
        }
        return typeFieldDescribe;
    }

    private Department createDataOwnOrganization(IObjectDescribe objectDescribe) {
        Department department = new DepartmentFieldDescribe();
        mergeCustomFieldSettings(ObjectDataExt.DATA_OWN_ORGANIZATION, objectDescribe, department);
        department.setApiName(ObjectDataExt.DATA_OWN_ORGANIZATION);
        department.setLabel(I18NExt.getOrDefault(I18NKey.DATA_OWN_ORGANIZATION_LABEL, "归属组织"));// ignoreI18n
        department.setUnique(false);
        department.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        department.setStatus(IFieldDescribe.STATUS_RELEASED);
        department.setIndex(true);
        department.setIsSingle(true);
        return department;
    }

    private RecordTypeFieldDescribe generateRecordTypeField(String apiName, IObjectDescribe objectDescribe) {
        RecordTypeFieldDescribe fieldDescribe = new RecordTypeFieldDescribe();
        mergeCustomFieldSettings(apiName, objectDescribe, fieldDescribe);
        fieldDescribe.setApiName(apiName);
        fieldDescribe.setLabel(I18N.text(I18NKey.BUSINESS_TYPE));
        fieldDescribe.setUnique(false);
        fieldDescribe.setDescription(apiName);
        fieldDescribe.setDefineType(IFieldDescribe.DEFINE_TYPE_PACKAGE);
        fieldDescribe.setStatus(IFieldDescribe.STATUS_RELEASED);
        fieldDescribe.setIndex(true);

        return fieldDescribe;
    }

    private void mergeCustomFieldSettings(String apiName, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe) {
        IFieldDescribe customized = objectDescribe.getFieldDescribe(apiName);
        if (customized != null && fieldDescribe.getType().equals(customized.getType())) {
            fieldDescribe.fromJsonString(customized.toJsonString());
        }
    }

    @Override
    public void checkCustomFieldCountLimit(User user, IObjectDescribe describeDraft, List<IFieldDescribe> newFieldList) {
        //如果newFieldList 都不是自定义字段，则不用校验
        if (CollectionUtils.notEmpty(newFieldList)) {
            boolean existCustomField = newFieldList.stream().anyMatch(a -> Objects.equals(IFieldDescribe.DEFINE_TYPE_CUSTOM, a.getDefineType()));
            if (!existCustomField) {
                return;
            }
        }

        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describeDraft);
        List<IFieldDescribe> customFields = objectDescribeExt.filter(a -> Objects.equals(IFieldDescribe.DEFINE_TYPE_CUSTOM, a.getDefineType()));
        List<IFieldDescribe> list = Lists.newArrayList(customFields);
        if (CollectionUtils.notEmpty(newFieldList)) {
            List<IFieldDescribe> newCustomFields = CollectionUtils.nullToEmpty(newFieldList).stream().filter(a -> FieldDescribeExt.of(a).isCustomField()).collect(Collectors.toList());
            list.addAll(newCustomFields);
        }
        Map<String, Integer> fieldCountMap = getFieldCountByType(list);
        int totalCount = customFields.size();
        fieldCountMap.put(ModulePara.ALL_FIELD_LIMIT.getParaKey(), totalCount);
        TenantLicenseInfo.builder().licenseService(licenseService).user(user)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.UDOBJ.getBizCode()))
                .checkFieldCount(fieldCountMap, describeDraft);
    }

    @Override
    public List<ResourcesRecord> getCustomFieldCountLimit(User user, String apiName) {
        List<ResourcesRecord> resourcesRecordList = Lists.newArrayList();
        IObjectDescribe describe = findObjectWithoutCopyIfGray(user.getTenantId(), apiName);
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);
        List<IFieldDescribe> customFields = objectDescribeExt.filter(a -> containsPreFieldType.contains(a.getType())
                || Objects.equals(IFieldDescribe.DEFINE_TYPE_CUSTOM, a.getDefineType()));
        Map<String, Integer> fieldCountMap = getFieldCountByType(customFields);
        TenantLicenseInfo tenantLicenseInfo = TenantLicenseInfo.builder().licenseService(licenseService)
                .user(user)
                .build()
                .init(Sets.newHashSet(ModulePara.ModuleBiz.UDOBJ.getBizCode()));
        tenantLicenseInfo.getNeedToCheckQuotaFieldType().forEach(fieldType -> fieldCountMap.putIfAbsent(fieldType, 0));
        Map<String, Integer> fieldLimit = tenantLicenseInfo.getFieldCount(fieldCountMap, describe, false);
        List<String> withoutQuotaFieldType = tenantLicenseInfo.getWithoutQuotaFieldType();
        withoutQuotaFieldType.forEach(type -> fieldLimit.putIfAbsent(type, 1));
        fieldLimit.forEach((type, limit) -> {
            Integer fieldCount = fieldCountMap.getOrDefault(type, 0);
            boolean canBuy = !withoutQuotaFieldType.contains(type);
            boolean isGroup = type.contains(IFieldType.GROUP);
            String fieldType = type.contains(IFieldType.GROUP) ? IFieldType.GROUP : type;
            String groupType = isGroup ? StringUtils.substringAfter(type, "|") : null;
            ResourcesRecord resourcesRecord = ResourcesRecord.builder()
                    .fieldType(fieldType)
                    .groupType(groupType)
                    .limit(limit)
                    .usage(fieldCount)
                    .available(limit - fieldCount)
                    .canBuy(canBuy)
                    .build();
            resourcesRecordList.add(resourcesRecord);
        });
        resourcesRecordList.sort(Comparator.comparingInt(ResourcesRecord::getLimit).reversed());
        return resourcesRecordList;
    }

//    private Map<String, Integer> getFieldCountByTypeIncludeGroup(List<IFieldDescribe> fieldList) {
//        Map<String, Integer> map = Maps.newHashMap();
//        for (IFieldDescribe fieldDescribe : fieldList) {
//            String type = fieldDescribe.getType();
//            if (Objects.equals(type, IFieldType.GROUP)) {
//                type = type + "|" + fieldDescribe.get(GroupField.GROUP_TYPE, String.class);
//            }
//            Integer count = map.getOrDefault(type, 0);
//            count++;
//            map.put(type, count);
//        }
//        return map;
//    }

    private Map<String, Integer> getFieldCountByType(List<IFieldDescribe> fieldList) {
        Map<String, Integer> map = Maps.newHashMap();

        for (IFieldDescribe fieldDescribe : fieldList) {
            Integer count = map.getOrDefault(fieldDescribe.getType(), 0);
            count++;
            map.put(fieldDescribe.getType(), count);
        }
        return map;
    }

//    private void validateLayoutApiNameDuplicate(User user, String jsonLayout, boolean isIncludeLayout) throws MetadataServiceException {
//        if (!isIncludeLayout) {
//            return;
//        }
//        ILayout layout = new Layout(Document.parse(jsonLayout));
//        ILayout byNameAndObjectDescribeApiNameAndTenantId = layoutLogicService.findLayoutByApiName(user, layout.getName(), layout.getRefObjectApiName());
//        if (byNameAndObjectDescribeApiNameAndTenantId != null) {
//            log.warn(FS_CRM_DEFOBJ_CHECKED_DUPLICATE_DISPLAY_NAME + "duplicate layout name:" + layout.getName());
//            throw new ValidateException(I18N.text(I18NKey.LAYOUT_ALREADY_EXIST, layout.getName()));
//        }
//    }

    private void validateLayoutApiNameDuplicate(User user, ILayout layout, boolean isIncludeLayout) throws MetadataServiceException {
        if (!isIncludeLayout) {
            return;
        }
        ILayout byNameAndObjectDescribeApiNameAndTenantId = layoutLogicService.findLayoutByApiName(user, layout.getName(), layout.getRefObjectApiName());
        if (byNameAndObjectDescribeApiNameAndTenantId != null) {
            log.warn(FS_CRM_DEFOBJ_CHECKED_DUPLICATE_DISPLAY_NAME + "duplicate layout name:" + layout.getName());
            throw new ValidateException(I18N.text(I18NKey.LAYOUT_ALREADY_EXIST, layout.getName()));
        }
    }

    private IObjectDescribe generateUserDefObjDescribeDraftByDescribeJson(IObjectDescribe objectDescribe, String tenantId, String userId) {
        //检测displayName是否有重复,因国际多语化，目前允许displayname重复
        validateDraftApiNameAndDisplayName(tenantId, objectDescribe.getDisplayName(), objectDescribe.getApiName(), true);
        return generateDescribeWithSystemDefinedFields(objectDescribe, tenantId, userId);
    }

    private IObjectDescribe generateDescribeWithSystemDefinedFields(IObjectDescribe objectDescribe, String tenantId, String userId) {
        objectDescribe.setLastModifiedBy(userId);
        objectDescribe.setCreatedBy(userId);
        objectDescribe.setLastModifiedTime(System.currentTimeMillis());
        objectDescribe.setTenantId(tenantId);

        //创建人、最后修改人、创建时间、最后修改时间四个字段
        EmployeeFieldDescribe createdByFieldDescribe = SystemDefinedFields.CREATED_BY(objectDescribe);
        createdByFieldDescribe.setIndex(true);
        createdByFieldDescribe.setDescription(I18N.text(I18NKey.CREATE_USER));
        createdByFieldDescribe.setIsNeedConvert(true);
        createdByFieldDescribe.setCreateTime(System.currentTimeMillis());

        EmployeeFieldDescribe lastModifiedByFieldDescribe = SystemDefinedFields.LAST_MODIFIED_BY(objectDescribe);
        lastModifiedByFieldDescribe.setDescription(I18N.text(I18NKey.LAST_MODIFY_USER));
        lastModifiedByFieldDescribe.setIndex(true);
        lastModifiedByFieldDescribe.setIsNeedConvert(true);
        lastModifiedByFieldDescribe.setCreateTime(System.currentTimeMillis());

        DateTimeFieldDescribe createTimeFieldDescribe = SystemDefinedFields.CREATE_TIME(objectDescribe);
        createTimeFieldDescribe.setIndex(true);
        createTimeFieldDescribe.setDescription(I18N.text(I18NKey.CREATE_TIME));
        createTimeFieldDescribe.setCreateTime(System.currentTimeMillis());

        DateTimeFieldDescribe lastModifiedDateTimeFieldDescribe = SystemDefinedFields.LAST_MODIFIED_TIME(objectDescribe);
        lastModifiedDateTimeFieldDescribe.setIndex(true);
        lastModifiedDateTimeFieldDescribe.setDescription(I18N.text(I18NKey.LAST_MODIFY_TIME));
        lastModifiedByFieldDescribe.setCreateTime(System.currentTimeMillis());

        BooleanFieldDescribe booleanFieldDescribe = SystemDefinedFields.IS_DELETED(objectDescribe);
        booleanFieldDescribe.setIndex(true);
        booleanFieldDescribe.setLabel(I18N.text(I18NKey.WHETHER_INVALID));
        booleanFieldDescribe.setDescription(I18N.text(I18NKey.WHETHER_INVALID));
        booleanFieldDescribe.setCreateTime(System.currentTimeMillis());


        //如果不存在，则放入draft中
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        describeExt.addFieldIfAbsent(createTimeFieldDescribe);
        describeExt.addFieldIfAbsent(lastModifiedDateTimeFieldDescribe);
        describeExt.addFieldIfAbsent(createdByFieldDescribe);
        describeExt.addFieldIfAbsent(lastModifiedByFieldDescribe);
        if (!objectDescribe.isBigObject()) {
            //生成相关团队字段
            EmbeddedObjectListFieldDescribe teamMemberField = ObjectDescribeExt.generateDefaultTeamMemberFieldDescribe();
            IFieldDescribe lockRuleFieldDescribe = generateDefaultFieldDescribe(UdobjConstants.LOCK_RULE_API_NAME);
            IFieldDescribe lockStatusFieldDescribe = generateDefaultFieldDescribe(UdobjConstants.LOCK_STATUS_API_NAME);
            IFieldDescribe lockUserFieldDescribe = generateDefaultFieldDescribe(UdobjConstants.LOCK_USER_API_NAME);
            IFieldDescribe lifeStatusFieldDescribe = defObjLifeStatusService.generateDefaultFieldDescribe(LIFE_STATUS_API_NAME);
            IFieldDescribe lifeStatusBeforeVFieldDescribe = defObjLifeStatusService.generateDefaultFieldDescribe(LIFE_STATUS_BEFORE_INVALID_API_NAME);
            describeExt.addFieldIfAbsent(teamMemberField);
            describeExt.addFieldIfAbsent(lockRuleFieldDescribe);
            describeExt.addFieldIfAbsent(lockStatusFieldDescribe);
            describeExt.addFieldIfAbsent(lockUserFieldDescribe);
            describeExt.addFieldIfAbsent(lifeStatusFieldDescribe);
            describeExt.addFieldIfAbsent(lifeStatusBeforeVFieldDescribe);
        }

        return objectDescribe;
    }

    private String getDescribeId(String tenantid, String id, String apiname) throws MetadataServiceException {
        if (id != null) {
            return id;
        }
        //如果没有describeID,则根据apiname查找describeid
        if (Strings.isNullOrEmpty(apiname)) {
            return null;
        }
        IObjectDescribe objectDescribeDraft;
        if ((objectDescribeDraft = findObjectWithoutCopyIfGray(tenantid, apiname)) != null) {
            return objectDescribeDraft.getId();
        }
        return null;
    }

    private void validateDraftApiNameAndDisplayName(String tenantId, String displayName, String apiName, boolean isCreate) {
        if (apiName.length() > 38) {
            log.warn(FS_CRM_DEFOBJ_CHECKED_DUPLICATE_DISPLAY_NAME + " api name length > 38:" + displayName);
            throw new ValidateException(I18N.text(I18NKey.APINAME_LENGTH_BEYOND_MAX_LIMIT, displayName));
        }
    }

    private void validReferenceFieldCondition(String tenantId, IFieldDescribe fieldDescribe) throws MetadataServiceException {
        if (Objects.isNull(fieldDescribe) || !(Objects.equals(fieldDescribe.getType(), IFieldType.OBJECT_REFERENCE) || Objects.equals(fieldDescribe.getType(), IFieldType.OBJECT_REFERENCE_MANY))) {
            return;
        }
        ObjectReferenceWrapper referenceFieldDescribe = ObjectReferenceWrapper.of(fieldDescribe);
        List<LinkedHashMap> wheres = referenceFieldDescribe.getWheres();
        if (CollectionUtils.empty(wheres)) {
            return;
        }

        IObjectDescribe describeDraft = findObjectWithoutCopyIfGray(tenantId, referenceFieldDescribe.getTargetApiName());
        if (null == describeDraft) {
            throw new ValidateException(I18N.text(I18NKey.ASSOCIATED_OBJECTS_UNEXIST_OR_DELETED));
        }

        for (Map doc : wheres) {
            Object filtersObj = doc.get("filters");
            if (null == filtersObj || !(filtersObj instanceof List)) {
                continue;
            }

            List filters = (List) filtersObj;
            if (CollectionUtils.empty(filters)) {
                continue;
            }

            for (Object filter : filters) {
                IFilter ifilter = new Filter();
                ifilter.fromJsonString(((JSONObject) filter).toJSONString());
                String fieldName = StringUtils.substringBefore(ifilter.getFieldName(), ".");
                IFieldDescribe targetField = ObjectDescribeExt.of(describeDraft).getFieldDescribeSilently(fieldName).orElse(null);
                if (StringUtils.equals(fieldName, "id")) {
                    targetField = ObjectDescribeExt.of(describeDraft).getFieldDescribeSilently(IFieldDescribe.ID).orElse(null);
                }
                if (Objects.isNull(targetField) && !StringUtils.equals(FilterExt.FUNCTION_WHERE_TYPE, referenceFieldDescribe.getWhereType())) {
                    throw new ValidateException(I18N.text(I18NKey.FILTER_UNEXIST_OR_DELETED));
                }

                //value_type不是常量的不用校验field_values
                if (!FilterExt.of(ifilter).hasConstantValueType()) {
                    continue;
                }

                List fieldValue = ifilter.getFieldValues();
                if (CollectionUtils.empty(fieldValue)) {
                    continue;
                }

                if (Objects.nonNull(targetField) && Objects.equals(IFieldType.SELECT_ONE, targetField.getType())) {
                    List<ISelectOption> selectOptions = ((SelectOne) targetField).getSelectOptions();
                    if (CollectionUtils.empty(selectOptions)) {
                        continue;
                    }

                    if (Objects.isNull(fieldValue.get(0))) {
                        continue;
                    }

                    String fv = String.valueOf(fieldValue.get(0));
                    if (Strings.isNullOrEmpty(fv)) {
                        continue;
                    }

                    boolean flag = false;
                    for (ISelectOption selectOption : selectOptions) {
                        if (Objects.equals(selectOption.getValue(), fv)) {
                            flag = true;
                            break;
                        }
                    }

                    if (!flag) {
                        throw new ValidateException(I18N.text(I18NKey.FILTER_UNEXIST_OR_DELETED));
                    }
                } else if (Objects.nonNull(targetField) && Objects.equals(IFieldType.SELECT_MANY, targetField.getType())) {
                    List<ISelectOption> selectOptions = ((SelectMany) targetField).getSelectOptions();
                    if (CollectionUtils.empty(selectOptions)) {
                        continue;
                    }

                    for (Object value : fieldValue) {
                        if (Objects.isNull(value)) {
                            continue;
                        }
                        String fv = String.valueOf(value);
                        if (Strings.isNullOrEmpty(fv)) {
                            continue;
                        }

                        boolean flag = false;
                        for (ISelectOption selectOption : selectOptions) {
                            if (Objects.equals(selectOption.getValue(), fv)) {
                                flag = true;
                                break;
                            }
                        }

                        if (!flag) {
                            throw new ValidateException(I18N.text(I18NKey.FILTER_UNEXIST_OR_DELETED));
                        }
                    }
                }
            }
        }
    }

    private IObjectDescribe getObjectDescribe(User user, String jsonData) throws MetadataServiceException {
        IObjectDescribe describeDraft = new ObjectDescribe();
        describeDraft.fromJsonString(jsonData);
        describeDraft.setLastModifiedBy(user.getUserId());
        describeDraft.setLastModifiedTime(System.currentTimeMillis());
        describeDraft.setTenantId(user.getTenantId());
        describeDraft.setId(getDescribeId(user.getTenantId(), describeDraft.getId(), describeDraft.getApiName()));
        return describeDraft;
    }

    private ILayout getLayout(User user, String layoutJson) {
        ILayout layout = new Layout();
        layout.fromJsonString(layoutJson);
        layout.setTenantId(user.getTenantId());
        layout.setCreatedBy(user.getUserId());
        layout.setLastModifiedBy(user.getUserId());
        return layout;
    }

    //描述层：生成默认的团队成员的EmbeddedObjectListFieldDescribe
    private IFieldDescribe generateDefaultFieldDescribe(String apiName) {
        switch (apiName) {
            case LOCK_USER_API_NAME: {
                return ObjectDescribeExt.generateLockUserField();
            }
            case LOCK_STATUS_API_NAME: {
                return ObjectDescribeExt.generateLockStatusField();
            }
            case LOCK_RULE_API_NAME: {
                return ObjectDescribeExt.generateLockRuleField();
            }
            default:
                break;
        }
        return null;
    }

    private List<RecordTypeLayoutStructure> getRecordTypeLayoutStructureList(User user, String layoutType, IObjectDescribe detailObject, MasterDetail refField, ILayout listLayout, List<IRecordTypeOption> recordTypeOptionList) {
        RelatedObjectDescribeStructure relatedStructure = RelatedObjectDescribeStructure.builder().relatedObjectDescribe(detailObject).fieldApiName(refField.getApiName()).fieldLabel(refField.getLabel()).relatedListName(refField.getTargetRelatedListName()).relatedListLabel(refField.getTargetRelatedListLabel()).build();

        if (listLayout != null) {
            TableComponent tableComponent = LayoutExt.of(listLayout).getTableComponent()
                    .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
            TableComponentRender.builder()
                    .functionPrivilegeService(functionPrivilegeService)
                    .user(user)
                    .describeExt(ObjectDescribeExt.of(detailObject))
                    .tableComponentExt(TableComponentExt.of(tableComponent))
                    .build()
                    .render();
        }

        List<RecordTypeLayoutStructure> result = Lists.newArrayList();
        for (IRecordTypeOption recordTypeOption : recordTypeOptionList) {
            ILayout detailLayout = layoutLogicService.findObjectLayoutWithType(user, recordTypeOption.getApiName(), detailObject, layoutType, null);
            LayoutExt detailLayoutExt = LayoutExt.of(detailLayout);
            detailLayoutExt.removeFieldByTypes(ObjectDescribeExt.of(detailObject), IFieldType.MASTER_DETAIL);

            LayoutExt cpLayoutExt = LayoutExt.of(new Layout(Maps.newLinkedHashMap(detailLayoutExt.toMap())));
            TableComponent tableComponentByRecordType = LayoutExt.of(listLayout).getTableComponentByRecordType(relatedStructure, recordTypeOption);
            cpLayoutExt.setComponents(Lists.newArrayList(tableComponentByRecordType));

            RecordTypeLayoutStructure recordTypeLayoutStructure = RecordTypeLayoutStructure.builder().record_type(recordTypeOption.getApiName()).detail_layout(detailLayoutExt.toMap()).list_layout(cpLayoutExt.toMap()).build();
            result.add(recordTypeLayoutStructure);
        }
        return result;
    }

    @Override
    @Transactional
    public IObjectDescribe initializeDescribe(User user, String jsonData, String jsonDetailLayout, String jsonListLayout) {
        try {
            IObjectDescribe describe = new ObjectDescribe(Document.parse(jsonData));
            initializeDescribeSystemDefinedFields(user, describe);
            initializeDescribeActions(describe);
            IObjectDescribe initialized = createOrUpdateDescribe(user, describe);
            ILayout detailLayout = new Layout(Document.parse(jsonDetailLayout));
            createOrUpdateLayout(user, initialized, detailLayout);
            ILayout listLayout = new Layout(Document.parse(jsonListLayout));
            createOrUpdateLayout(user, initialized, listLayout);
            //createOrUpdateSearchTemplate(user, initialized);
            initObjectFunctionPrivilege(user, initialized.getApiName());
            createOrUpdateDataPrivilege(user, initialized);
            createOrUpdateRecordType(user, initialized, detailLayout.getName());
            logService.log(user, EventType.ADD, ActionType.CREATE_OBJ, initialized.getApiName(), I18N.text(I18NKey.OBJECT_SPECIFY, initialized.getDisplayName()));
            return initialized;
        } catch (MetadataServiceException ex) {
            log.error(ex.getMessage(), ex);
            return null;
        }
    }

    @Override
    @Transactional
    public IObjectDescribe updateSfaDescribe(User user, String jsonData, String jsonDetailLayout, String jsonListLayout, String detailApiName, Map<String, String> functionsMapping) {
        try {
            IObjectDescribe describe = new ObjectDescribe(Document.parse(jsonData));
            initializeDescribeSystemDefinedFields(user, describe);
            initializeDescribeActions(describe);
            IObjectDescribe initialized = createOrUpdateDescribe(user, describe);
            objectDescribeService.updateStoreTableNameByApiName(user.getTenantId(), initialized.getApiName(), describe.getStoreTableName());
            deleteDefaultSfaLayout(user, initialized);
            ILayout detailLayout = new Layout(Document.parse(jsonDetailLayout));
            List<IFieldDescribe> requiredFields = getRequiredFields(describe);
            for (IFieldDescribe requiredField : requiredFields) {
                FieldLayoutPojo pojo = new FieldLayoutPojo();
                pojo.setReadonly(Boolean.TRUE.equals(requiredField.get("is_readonly", Boolean.class)));
                pojo.setRequired(Boolean.TRUE.equals(requiredField.get("is_required", Boolean.class)));
                pojo.setRenderType(requiredField.getType());
                LayoutExt.of(detailLayout).addField(requiredField, pojo);
            }
            createOrUpdateLayout(user, initialized, detailLayout);
            ILayout listLayout = new Layout(Document.parse(jsonListLayout));
            createOrUpdateLayout(user, initialized, listLayout);
            createOrUpdateSearchTemplate(user, initialized);
            //updateSfaFunctionPrivilege(user, initialized.getApiName(), detailApiName, functionsMapping);
            createOrUpdateRecordType(user, initialized, detailLayout.getName());
            return initialized;
        } catch (MetadataServiceException ex) {
            log.error(ex.getMessage(), ex);
            return null;
        }
    }

    private void initializeDescribeSystemDefinedFields(User user, IObjectDescribe describe) {
        IFieldDescribe lifeStatus = null;
        if (null != describe.getFieldDescribe("life_status")) {
            lifeStatus = describe.getFieldDescribe("life_status");
        }
        generateDescribeWithSystemDefinedFields(describe, user.getTenantId(), user.getUserId());
        if (null != lifeStatus) {
            describe.updateFieldDescribe(lifeStatus);
        }
        RecordTypeFieldDescribe recordTypeField = createRecordTypeField(describe);
        describe.addFieldDescribe(recordTypeField);
        // 添加归属组织字段
        fillDataOwnOrganization(user, describe);
        defObjUtil.addFieldDescribeCreateTime(describe);
    }

    private void initializeDescribeActions(IObjectDescribe describe) {
        insertActionsIntoDescribe(describe);
    }

    private List<IFieldDescribe> getRequiredFields(IObjectDescribe describe) {
        return ObjectDescribeExt.of(describe).getFieldDescribesSilently().stream().filter(f -> "custom".equals(f.getDefineType()) && Boolean.TRUE.equals(f.isActive())).collect(Collectors.toList());
    }

    private IObjectDescribe createOrUpdateDescribe(User user, IObjectDescribe describe) throws MetadataServiceException {
        IObjectDescribe existed = findObject(user.getTenantId(), describe.getApiName());
        if (null == existed) {
            return create(true, describe);
        } else {
            describe.setId(existed.getId());
            describe.setVersion(existed.getVersion() == null ? Integer.valueOf(1) : existed.getVersion());
            describe.addFieldDescribeList(existed.getFieldDescribes().stream().filter(f -> f.getDefineType().equals("custom")).collect(Collectors.toList()));
            return objectDescribeService.replace(describe, true, getActionContext(user));
        }
    }

    private void deleteDefaultSfaLayout(User user, IObjectDescribe describe) {
        String name = describe.getApiName() + "_layout_generate_by_UDObjectServer__c";
        ILayout layout = layoutLogicService.findLayoutByApiName(user, name, describe.getApiName());
        if (null == layout) {
            return;
        }
        layout.setIsDefault(false);
        layoutLogicService.updateLayout(user, layout);
        layoutLogicService.deleteLayout(user, layout.getId());
    }

    private void createOrUpdateLayout(User user, IObjectDescribe describe, ILayout layout) throws MetadataServiceException {
        layout.setTenantId(user.getTenantId());
        List<ILayout> layouts = findLayoutByObjectApiName(user, describe.getApiName());
        if (CollectionUtils.notEmpty(layouts)) {
            for (ILayout l : layouts) {
                if (Objects.equals(l.getName(), layout.getName()) || Objects.equals(l.getDisplayName(), layout.getDisplayName())) {
                    layout.setId(l.getId());
                    layoutLogicService.updateLayout(user, layout);
                    return;
                }
            }
        }
        layoutLogicService.createLayout(user, layout);
    }

    private void createOrUpdateSearchTemplate(User user, IObjectDescribe describe) throws MetadataServiceException {
        List<ISearchTemplate> templates = searchTemplateService.findByObjectDescribeAPIName(user.getTenantId(), describe.getApiName());
        MetadataContext context = MetadataContextExt.of(user).getMetadataContext();
        if (CollectionUtils.notEmpty(templates)) {
            for (ISearchTemplate t : templates) {
                searchTemplateService.delete(t, context);
            }
        }
        //insertDefaultTemplate(describe);
    }

    @Override
    public void initObjectFunctionPrivilege(User user, String apiName) {
        try {
            functionPrivilegeService.initFunctionPrivilege(user, findObjectWithoutCopy(user.getTenantId(), apiName));
        } catch (Exception ex) {
            log.warn(ex.getMessage(), ex);
        }
    }

    private void createOrUpdateDataPrivilege(User user, IObjectDescribe describe) {
        ObjectDataPermissionInfo result = dataPrivilegeService.getCommonPrivilege4DefObjResult(user, describe);
        if (result == null || DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.NO_PERMISSION.getValue().equals(result.getPermissionType())) {
            dataPrivilegeService.addCommonPrivilegeListResult(user, Lists.newArrayList(new ObjectDataPermissionInfo(describe.getApiName(), describe.getDisplayName(), DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE.getValue())));
        } else {
            dataPrivilegeService.updateCommonPrivilegeList(user, Lists.newArrayList(new ObjectDataPermissionInfo(describe.getApiName(), describe.getDisplayName(), DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE.getValue())));
        }
    }

    private void createOrUpdateRecordType(User user, IObjectDescribe describe, String detailLayoutName) {
        if (!recordTypeLogicService.checkRecordType(describe.getApiName(), "default__c")) {
            recordTypeLogicService.recordTypeInit(user, detailLayoutName, user.getTenantId(), describe.getApiName());
        }
    }

    /**
     * 支持多语，语言信息保存在actionContext中
     *
     * @return ActionContext
     */
    private IActionContext getActionContext(User user) {
        RequestContext requestContext = RequestContextManager.getContext();
        IActionContext actionContext = Objects.isNull(user) ? ActionContextExt.of(new User(null, null)).getContext()
                : ActionContextExt.of(user).getContext();
        boolean direct = false;
        boolean notNeedDeepCopy = false;
        Boolean upstreamCopyDescribe = null;
        if (Objects.nonNull(requestContext)) {
            if (Objects.nonNull(requestContext.getAttribute(RequestContext.DIRECT_KEY))) {
                direct = requestContext.getAttribute(RequestContext.DIRECT_KEY);
            }
            if (Objects.nonNull(requestContext.getAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY))) {
                notNeedDeepCopy = requestContext.getAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY);
            }
            if (Objects.nonNull(requestContext.getAttribute(RequestContext.UPSTREAM_COPY_DESCRIBE))) {
                upstreamCopyDescribe = requestContext.getAttribute(RequestContext.UPSTREAM_COPY_DESCRIBE);
            }
            // 查询描述的时候是否走缓存
            Object skipStatic = requestContext.getAttribute(RequestContext.Attributes.DESCRIBE_SKIP_STATIC);
            if (Objects.nonNull(skipStatic)) {
                actionContext.put(ActionContextKey.SKIP_STATIC, skipStatic);
            }
        }
        actionContext.put(RequestContext.DIRECT_KEY, direct);
        actionContext.put(RequestContext.IS_NOT_NEED_DEEP_COPY, notNeedDeepCopy);
        actionContext.put(ActionContextKey.UPSTREAM_COPY_DESCRIBE, upstreamCopyDescribe);
        return actionContext;
    }

    @Override
    public void processSelectOneByStageInstance(User user, IObjectDescribe describe, ILayout layout, IObjectData data) {
        List<String> selectOneApiNames = getSelectOneFieldsUsedByStageInstance(user, describe, layout, data);
        LayoutExt.of(layout).setReadOnly(Sets.newHashSet(selectOneApiNames), true);
    }

    @Override
    public List<String> getSelectOneFieldsUsedByStageInstance(User user, IObjectDescribe describe, ILayout layout, IObjectData data) {
        if (Objects.isNull(data)) {
            return Lists.newArrayList();
        }
        List<String> selectOneApiNames = describe.getFieldDescribes().stream().map(x -> FieldDescribeExt.of(x)).filter(x -> x.isSelectOne()).map(x -> (SelectOne) x.getFieldDescribe()).filter(x -> Boolean.TRUE.equals(x.getIsUsedByStage())).filter(x -> LayoutExt.of(layout).getField(x.getApiName()).isPresent()).map(x -> x.getApiName()).collect(Collectors.toList());
        if (CollectionUtils.empty(selectOneApiNames)) {
            return selectOneApiNames;
        }

        boolean hasStageInstance = true;
        if (data != null) {
            hasStageInstance = isHasStageInstance(user, describe.getApiName(), data.getId());
        }
        if (hasStageInstance) {
            return selectOneApiNames;
        }
        return Lists.newArrayList();
    }

    @Override
    public IObjectDescribe updateDescribeWithSubmitCalculateJob(IObjectDescribe objectDescribe, User user) {
        IObjectDescribe oldDescribe = findObject(user.getTenantId(), objectDescribe.getApiName());
        //字段校验
        List<IFieldDescribe> calculateFields = fieldRelationCalculateService.validateByObjectDescribe(objectDescribe, oldDescribe);
        List<IFieldDescribe> quoteFields = fieldRelationCalculateService.checkQuoteFieldsByObjectDescribe(objectDescribe, oldDescribe);
        fieldRelationCalculateService.checkObjectReferenceFieldsByObjectDescribe(user, objectDescribe, oldDescribe);
        // 校验公共对象
        checkPublicObjectBeforeUpdateDescribe(user, objectDescribe, oldDescribe);
        IObjectDescribe update = update(user, objectDescribe);
        //提交计算任务,新增或变更了统计字段重新计算历史数据
        List<String> fieldNameList = Lists.newArrayList();
        List<String> calculateFieldApiNames = calculateFields.stream().map(x -> x.getApiName()).collect(Collectors.toList());
        List<String> quoteFieldApiNames = quoteFields.stream().map(x -> x.getApiName()).collect(Collectors.toList());
        fieldNameList.addAll(calculateFieldApiNames);
        fieldNameList.addAll(quoteFieldApiNames);
        jobScheduleService.submitCalculateJob(user, fieldNameList, objectDescribe.getApiName());

        fieldRelationCalculateService.checkSelectOneChangeOfDescribe(objectDescribe, oldDescribe);
        return update;
    }

    private void modifyDetailSelectOneReadOnlyProperty(User user, IObjectDescribe describe, DetailObjectListResult detailObjectListResult) {
        List<RecordTypeLayoutStructure> layoutList = detailObjectListResult.getLayoutList();

        layoutList.forEach(x -> {
            LayoutExt detailLayout = LayoutExt.of(x.getDetail_layout());
            processSelectOneByStageInstance(user, describe, detailLayout, null);
        });
    }

    private boolean isHasStageInstance(User user, String describeApiName, String dataId) {
        Map<String, String> header = RestUtils.buildFlowHeaders(user);

        HasStageInstance.Arg arg = HasStageInstance.Arg.builder().entityId(describeApiName).objectId(dataId).build();
        HasStageInstance.Result result = proxy.hasStageInstance(arg, header);
        if (result.getData() != null) {
            if (result.getData().get("hasInstance")) {
                return true;
            }
        }
        return false;
    }

    // TODO: 2021/2/22 需要元数据提供根据apiName获取displayName的简单接口
    @Override
    public Map<String, String> getLookupObjectLabels(String tenantId, List<IObjectDescribe> describeList) {
        if (CollectionUtils.empty(describeList)) {
            return Maps.newHashMap();
        }
        Set<String> lookupApiNames = Sets.newHashSet();
        describeList.forEach(x -> {
            Set<String> tmpApiNames = ObjectDescribeExt.of(x).getActiveReferenceFieldDescribes().stream()
                    .map(ObjectReferenceWrapper::getTargetApiName).collect(Collectors.toSet());
            lookupApiNames.addAll(tmpApiNames);
        });
        return findDisplayNameByApiNames(tenantId, lookupApiNames);
    }

    @Override
    public RLock tryLockObject(String tenantId, String objectApiName) {
        String lockKey = "describeChangeLock|" + tenantId + "-" + objectApiName;
        RLock lock = redissonService.tryLock(0, 60, TimeUnit.SECONDS, lockKey);
        //没有抢到锁，提示对象已被他人修改
        if (Objects.isNull(lock)) {
            throw new ValidateException(I18N.text(MetadataI18NKey.OBJECT_HAS_BEEN_MODIFIED), ErrorCode.FS_PAAS_MDS_DESC_EXPIRED_EXCEPTION.getCode());
        }
        return lock;
    }

    @Override
    public List<IObjectDescribe> getWhatCountDescribes(String tenantId, List<String> describeApiNames) {
        if (CollectionUtils.empty(describeApiNames)) {
            return Lists.newArrayList();
        }
        List<String> apiNames = objectDescribeService.getWhatCountDescribe(tenantId, describeApiNames, FieldDescribeExt.WHAT_GROUP_TYPES);
        Map<String, IObjectDescribe> describeMap = findObjects(tenantId, apiNames);
        return Lists.newArrayList(describeMap.values());
    }

    @Override
    public List<IObjectDescribe> findDescribeByFieldTypes(String tenantId, List<String> fieldTypes, List<String> groupTypes) {
        if (CollectionUtils.empty(fieldTypes)) {
            return Lists.newArrayList();
        }
        try {
            IActionContext context = getActionContext(new User(tenantId, null));
            List<IObjectDescribe> describeByFieldTypes = objectDescribeService.findDescribeByFieldTypes(tenantId, fieldTypes, groupTypes, context);
            //过滤内部对象
            describeByFieldTypes.removeIf(x -> ObjectDescribeExt.of(x).isInternalObject());
            filterByVersion(tenantId, describeByFieldTypes);
            return describeByFieldTypes;
        } catch (MetadataServiceException e) {
            log.warn("Error in findDescribeByFieldTypes,tenantId:{},fieldTypes:{},groupTypes:{}", tenantId, fieldTypes, groupTypes);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public DescribeExtra updateDescribeExtra(User user, DescribeExtra describeExtra) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_EXTRA_GRAY_EI, user.getTenantId())) {
            return null;
        }
        Map<String, List<IObjectFieldExtra>> describeExtras = describeExtra.toObjectFieldExtras();
        String describeApiName = describeExtra.getApiName();
        List<IObjectFieldExtra> objectFieldExtras = describeExtras.get(describeApiName);
        if (CollectionUtils.empty(objectFieldExtras)) {
            return null;
        }
        IObjectDescribe describe = findObject(user.getTenantId(), describeApiName);
        List<IObjectFieldExtra> upsert = fieldDescribeExtService.upsert(ActionContextExt.of(user).getContext(), describeApiName, objectFieldExtras);
        return DescribeExtra.of(describe, upsert);
    }

    @Override
    public DescribeExtra updateDescribeExtra(User user, String describeApiName, Map<String, Object> describeExtraMap) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_EXTRA_GRAY_EI, user.getTenantId())) {
            return null;
        }
        List<IObjectFieldExtra> objectFieldExtras = DescribeExtra.getFieldExtraList(describeExtraMap);
        if (CollectionUtils.empty(objectFieldExtras)) {
            return null;
        }
        validateFieldPromptLength(objectFieldExtras);
        IObjectDescribe describe = findObject(user.getTenantId(), describeApiName);
        List<IObjectFieldExtra> upsert = fieldDescribeExtService.upsert(ActionContextExt.of(user).getContext(), describeApiName, objectFieldExtras);
        return DescribeExtra.of(describe, upsert);
    }

    @Override
    public void upsertObjectFieldExtra(User user, String describeApiName, List<IObjectFieldExtra> upsert) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_EXTRA_GRAY_EI, user.getTenantId())) {
            return;
        }
        if (CollectionUtils.empty(upsert)) {
            return;
        }
        fieldDescribeExtService.upsert(ActionContextExt.of(user).getContext(), describeApiName, upsert);
    }

    @Override
    public Map<String, List<IObjectFieldExtra>> findDescribeExtra(User user, Collection<String> describeApiNames) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_EXTRA_GRAY_EI, user.getTenantId()) || CollectionUtils.empty(describeApiNames)) {
            return Maps.newHashMap();
        }
        Map<String, List<IObjectFieldExtra>> fieldExtra = fieldDescribeExtService.findDescribeExtByDescribeApiName(
                Lists.newArrayList(describeApiNames), ActionContextExt.of(user).getContext());
        if (CollectionUtils.empty(fieldExtra)) {
            return fieldExtra;
        }
        Map<String, List<IObjectFieldExtra>> result = Maps.newHashMap();
        fieldExtra.forEach((key, values) -> {
            List<IObjectFieldExtra> fieldExtras = values.stream()
                    .filter(it -> !onlyShowTagIsFalse(it))
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(fieldExtras)) {
                result.put(key, fieldExtras);
            }
        });
        return result;
    }

    private boolean onlyShowTagIsFalse(IObjectFieldExtra fieldExtra) {
        if (BooleanUtils.isFalse(fieldExtra.getShowTag())) {
            Map map = ((ObjectFieldExtra) fieldExtra).getContainerDocument();
            return map.containsKey(IObjectFieldExtra.FIELD_API_NAME) && map.containsKey(IObjectFieldExtra.SHOW_TAG) && map.size() == 2;
        }
        return false;
    }

    @Override
    public DescribeExtra findDescribeExtraByRenderType(User user, IObjectDescribe describe, List<IObjectDescribe> detailDescribeList, DescribeExpansionRender.RenderType renderType, boolean describeCacheable) {
        return findDescribeExtraByRenderType(user, describe, detailDescribeList, renderType, describeCacheable, null, null);
    }

    @Override
    public DescribeExtra findDescribeExtraByRenderType(User user, IObjectDescribe describe, List<IObjectDescribe> detailDescribes,
                                                       DescribeExpansionRender.RenderType renderType, boolean describeCacheable,
                                                       Boolean computeCalculateRelation, IObjectData objectData) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_EXTRA_GRAY_EI, user.getTenantId())) {
            return null;
        }
        return DescribeExpansionRender.builder()
                .renderType(renderType)
                .user(user)
                .describeLogicService(describeLogicService)
                .fieldRelationCalculateService(fieldRelationCalculateService)
                .selectFieldDependenceLogicService(selectFieldDependenceLogicService)
                .optionalFeaturesService(optionalFeaturesService)
                .describeCacheable(describeCacheable)
                .computeCalculateRelation(computeCalculateRelation)
                .objectData(objectData)
                .maskEncryptFields(Objects.nonNull(objectData))
                .maskFieldLogicService(maskFieldLogicService)
                .multiCurrencyLogicService(multiCurrencyLogicService)
                .build()
                .render(describe, detailDescribes);
    }

    @Override
    public DescribeExtra findDescribeExtra(User user, IObjectDescribe describe) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DESCRIBE_EXTRA_GRAY_EI, user.getTenantId())) {
            return null;
        }
        return DescribeExpansionRender.builder()
                .user(user)
                .fieldDependence(true)
                .describeLogicService(describeLogicService)
                .fieldRelationCalculateService(fieldRelationCalculateService)
                .selectFieldDependenceLogicService(selectFieldDependenceLogicService)
                .optionalFeaturesService(optionalFeaturesService)
                .describeCacheable(true)
                .build()
                .render(describe, Collections.emptyList());
    }

    @Override
    public Map<String, String> queryDisplayNameByApiNames(String tenantId, List<String> apiNames) {
        return queryDisplayNameByApiNames(tenantId, apiNames, null);
    }

    @Override
    public Map<String, String> queryDisplayNameByApiNames(String tenantId, List<String> apiNames, Boolean includeInvalid) {
        if (CollectionUtils.empty(apiNames)) {
            return Maps.newHashMap();
        }
        IActionContext actionContext = ActionContextExt.of(User.systemUser(tenantId)).getContext();
        if (Objects.nonNull(includeInvalid)) {
            actionContext.put(ActionContextKey.QUERY_DISPLAY_NAME_IGNORE_STATUS, includeInvalid);
        }
        return objectDescribeService.queryDisplayNameByApiNames(tenantId, apiNames, actionContext);
    }

    private boolean isRelatedListFormSupportObject(User user, IObjectDescribe describe, String relatedFieldName) {
        return Optional.ofNullable(describe)
                .filter(it -> AppFrameworkConfig.relatedListFormSupportObjectWithField(user.getTenantId(), it.getApiName(), Lists.newArrayList(relatedFieldName)))
                .filter(IObjectDescribe::isActive)
                .map(ObjectDescribeExt::of)
                .filter(it -> !it.isSlaveObject())
                .map(it -> !isMasterObject(user.getTenantId(), it.getApiName()))
                .orElse(false);
    }

    @Override
    public boolean isRelatedListFormSupportObject(User user, String targetApiName, IObjectDescribe describe, String relatedFieldName) {
        boolean validateTarget = ObjectDescribeExt.of(describe).getActiveFieldDescribeSilently(relatedFieldName)
                .map(FieldDescribeExt::of).map(FieldDescribeExt::getRefObjTargetApiNameDirectly)
                // targetApiName 为空不校验
                .map(it -> Strings.isNullOrEmpty(targetApiName) || Objects.equals(it, targetApiName))
                .orElse(false);
        if (!validateTarget) {
            return false;
        }
        return isRelatedListFormSupportObject(user, describe, relatedFieldName);
    }

    @Override
    public CheckFieldsForCalc.Result checkFieldsForCalc(User user, String describeAPIName, List<String> factor, boolean fieldInfo, List<IFieldDescribe> fieldDescribes) {

        fieldDescribes = CollectionUtils.nullToEmpty(fieldDescribes).stream()
                .filter(IFieldDescribe::isActive)
                .filter(x -> FieldDescribeExt.of(x).isCountField() || FieldDescribeExt.of(x).isFormula())
                .collect(Collectors.toList());

        if (CollectionUtils.empty(factor) || CollectionUtils.empty(fieldDescribes)) {
            return CheckFieldsForCalc.Result.builder().openModal(false).build();
        }

        List<IFieldDescribe> newFields = Lists.newArrayList();
        List<IFieldDescribe> changedFields = Lists.newArrayList();
        IObjectDescribe objDesc = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeAPIName);

        fieldDescribes.forEach(fieldDescribe -> {
            if (objDesc.containsField(fieldDescribe.getApiName())) {
                IFieldDescribe oldField = objDesc.getFieldDescribe(fieldDescribe.getApiName());
                if (!oldField.isActive()) {
                    changedFields.add(fieldDescribe);
                    return;
                }
                boolean changed = false;
                if (StringUtils.equals(oldField.getType(), IFieldType.COUNT)) {
                    changed = CountExt.of(((Count) oldField)).isChanged(((Count) fieldDescribe));
                } else if (StringUtils.equals(oldField.getType(), IFieldType.FORMULA)) {
                    changed = FormulaExt.of(((Formula) oldField)).isChanged(((Formula) fieldDescribe));
                }
                if (changed) {
                    changedFields.add(fieldDescribe);
                }
            } else {
                newFields.add(fieldDescribe);
            }
        });

        if ((factor.contains(MAX_COUNT.getCode()) || factor.contains(MAX_COUNT.name())) && TenantUtil.isMaxCount(user.getTenantId())
                && CollectionUtils.notEmpty(newFields)) {
            maxCountValidate(user, fieldInfo, newFields);
        }

        if ((factor.contains(CALC_JOB.getCode()) || factor.contains(CALC_JOB.name()))
                && TenantUtil.isCalcCriteria(user.getTenantId())
                && (CollectionUtils.notEmpty(changedFields) || CollectionUtils.notEmpty(newFields))) {
            List<IFieldDescribe> related = Lists.newArrayList(newFields);
            related.addAll(changedFields);
            List<String> relatedApiNames = related.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
            //已有字段存在历史计算任务
            if (CollectionUtils.notEmpty(changedFields)) {
                List<IFieldDescribe> jobFields = fieldRelationCalculateService.notEndJobForEach(user, describeAPIName, changedFields);
                if (CollectionUtils.notEmpty(jobFields)) {
                    List<String> warringApiNames = jobFields.stream()
                            .map(IFieldDescribe::getApiName).collect(Collectors.toList());
                    return CheckFieldsForCalc.Result.builder()
                            .openModal(true)
                            .relatedApiNames(relatedApiNames)
                            .warningApiNames(warringApiNames)
                            .build();
                }
            }

            if (TenantUtil.needCalcCriteria(user.getTenantId())) {
                //特殊企业，不关心历史已存数据直接弹窗写条件
                return CheckFieldsForCalc.Result.builder().openModal(true).relatedApiNames(relatedApiNames).build();
            }

            //condition1 : 历史已存数据量 > 1w
            //condition2 : 新建字段、已有字段得值公式变更(统计字段统计对象数据筛选条件改变、计算字段计算公式改变)、禁用字段启用
            Integer countData = metaDataService.countObjectDataFromDB(user.getTenantId(), describeAPIName, new SearchTemplateQuery());

            if (countData > AppFrameworkConfig.getMaxCountHistoryObjectData()) {
                return CheckFieldsForCalc.Result.builder().openModal(true).relatedApiNames(relatedApiNames).build();
            }

        }
        return CheckFieldsForCalc.Result.builder().openModal(false).build();
    }

    @Override
    public void maxCountValidate(User user, boolean fieldInfo, List<IFieldDescribe> newFields) {
        if (!TenantUtil.isMaxCount(user.getTenantId())) {
            return;
        }
        StringJoiner fieldLabels = new StringJoiner(",");
        List<String> objApiNames = Lists.newArrayList();
        long maxCount = AppFrameworkConfig.getMaxCountObjectData();
        List<String> subObjectApiNames = newFields.stream()
                .filter(it -> FieldDescribeExt.of(it).isCountField())
                .map(it -> ((Count) it).getSubObjectDescribeApiName())
                .filter(it -> !Strings.isNullOrEmpty(it))
                .distinct()
                .collect(Collectors.toList());
        Map<String, IObjectDescribe> subObjectDescribes = findObjectsWithoutCopy(user.getTenantId(), subObjectApiNames);
        Map<String, Integer> subObjectDataCountMap = Maps.newHashMap();
        for (IFieldDescribe fieldDesc : newFields) {
            if (FieldDescribeExt.of(fieldDesc).isCountField()) {
                CountFieldDescribe countFieldDesc = ((CountFieldDescribe) fieldDesc);
                String subObjectDescribeApiName = countFieldDesc.getSubObjectDescribeApiName();
                IObjectDescribe subObjectDescribe = subObjectDescribes.get(subObjectDescribeApiName);
                // 子对象不存在，返回错误提示
                if (Objects.isNull(subObjectDescribe)) {
                    throw new ObjectDefNotFoundError(I18N.text(I18NKey.OBJECT_NOT_EXIST, subObjectDescribeApiName));
                }
                String lookupFieldApiName = countFieldDesc.getFieldApiName();
                if (StringUtils.isEmpty(lookupFieldApiName)) {
                    lookupFieldApiName = ObjectDescribeExt.of(subObjectDescribe).getMasterDetailFieldName(fieldDesc.getDescribeApiName())
                            .orElse(null);
                }
                //缺少关联字段属性，返回提示信息
                if (StringUtils.isEmpty(lookupFieldApiName)) {
                    throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR) + ":field_api_name is empty");
                }
                IFieldDescribe fieldDescribe = subObjectDescribe.getFieldDescribe(lookupFieldApiName);
                // 子对象字段不存在，返回错误提示
                if (Objects.isNull(fieldDescribe)) {
                    throw new FieldNotExistException(subObjectDescribeApiName + "." + lookupFieldApiName);
                }
                // 子对象字段不是lookup字段和主从字段，不校验
                if (!FieldDescribeExt.of(fieldDescribe).isLookupField() && !FieldDescribeExt.of(fieldDescribe).isMasterDetailField()) {
                    continue;
                }
                // 子对象字段是通表索引字段，不校验
                if (fieldDescribe.isIndexField()) {
                    continue;
                }
                //lookup字段白名单不做校验
                if (FormulaGrayConfig.isLookupFieldWhiteListForCount(user.getTenantId(), subObjectDescribeApiName, lookupFieldApiName)) {
                    continue;
                }
                Integer countValue = subObjectDataCountMap.get(subObjectDescribeApiName);
                if (Objects.isNull(countValue)) {
                    countValue = metaDataService.countObjectDataFromDB(user.getTenantId(), subObjectDescribeApiName, new SearchTemplateQuery());
                    subObjectDataCountMap.put(subObjectDescribeApiName, countValue);
                }
                if (countValue > maxCount) {
                    boolean hasIndex = metaDataService.hasIndex(user.getTenantId(), subObjectDescribeApiName, lookupFieldApiName);
                    //有索引，不需要返回提示信息
                    if (hasIndex) {
                        continue;
                    }
                    objApiNames.add(subObjectDescribeApiName);
                    String fieldLabel = countFieldDesc.getLabel();
                    fieldLabels.add(fieldLabel);
                }
            }
        }
        if (CollectionUtils.notEmpty(objApiNames)) {
            String objLabels = String.join(",", findDisplayNameByApiNames(user.getTenantId(), objApiNames).values());
            throw new ValidateException(I18NExt.text(CALC_JOB_FOR_MAX_COUNT_DATA_TOO_LARGE, objLabels, fieldLabels));
        }
    }

    public Map<String, String> queryAllSlave2Master(User user) {
        IActionContext context = ActionContextExt.of(user).getContext();
        try {
            return objectDescribeService.queryAllSlave2Master(user.getTenantId(), context);
        } catch (MetadataServiceException e) {
            log.error("queryAllSlave2Master error,user:{}", user);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public DescribeAndLayoutList.Result findDescribeAndLayoutList(String tenantId, String describeApiName) {
        IObjectDescribe describe = findObjectWithoutCopy(tenantId, describeApiName);
        List<ILayout> layoutList = layoutLogicService.findLayoutByObjectApiName(tenantId, describeApiName);
        return DescribeAndLayoutList.Result.builder().describe(describe).layoutList(layoutList).build();
    }


    private List<ILayout> findAllAppIdLayoutsByTypes(User user, IObjectDescribe objectDescribe, List<String> types) {
        return findLayoutsWithAllAppId(user, objectDescribe.getApiName(),
                () -> layoutLogicService.findByTypesIncludeFlowLayout(user.getTenantId(), objectDescribe.getApiName(), types),
                layoutContext -> layoutLogicService.findByTypesIncludeFlowLayout(layoutContext, objectDescribe.getApiName(), types));
    }

    /**
     * crm 应用的appId为空，优先查询 crm下的布局
     *
     * @param appIds
     * @return
     */
    private List<String> getNewAppIds(Set<String> appIds) {
        List<String> newAppIds = Lists.newArrayList(appIds);
        newAppIds.add(0, null);
        return newAppIds;
    }

    private LayoutLogicService.LayoutContext buildLayoutContext(User user, String appId) {
        return LayoutLogicService.LayoutContext.of(user, appId);
    }

    private List<ILayout> findLayoutByObjectApiName(User user, String apiName) {
        return findLayoutsWithAllAppId(user, apiName,
                () -> layoutLogicService.findLayoutByObjectApiName(user.getTenantId(), apiName),
                layoutContext -> layoutLogicService.findLayoutByObjectApiName(layoutContext, apiName));
    }

    private void removeFieldInLayout(User user, String describeApiName, String fieldApiName) {
        findLayoutsWithAllAppId(user, describeApiName,
                () -> {
                    layoutLogicService.removeFieldInLayout(user.getTenantId(), describeApiName, fieldApiName);
                    return Collections.emptyList();
                },
                layoutContext -> {
                    layoutLogicService.removeFieldInLayout(layoutContext, describeApiName, fieldApiName);
                    return Collections.emptyList();
                });
    }

    private void deleteEditLayoutConfig(User user, String apiName) {
        findLayoutsWithAllAppId(user, apiName,
                () -> {
                    layoutLogicService.deleteEditLayoutConfig(user, apiName);
                    return Collections.emptyList();
                },
                layoutContext -> {
                    layoutLogicService.deleteEditLayoutConfig(layoutContext, apiName);
                    return Collections.emptyList();
                });
    }

    private List<ILayout> findLayoutsWithAllAppId(User user, String describeApiName, Supplier<List<ILayout>> oldFinder,
                                                  Function<LayoutLogicService.LayoutContext, List<ILayout>> newFinder) {
        Set<String> appIds = applicationLayeredGrayService.getDefineAppIdByDescribeApiNames(user, describeApiName);
        if (CollectionUtils.empty(appIds)) {
            return oldFinder.get();
        }
        Map<String, ILayout> resultMap = Maps.newHashMap();
        for (String appId : getNewAppIds(appIds)) {
            List<ILayout> layouts = newFinder.apply(buildLayoutContext(user, appId));
            if (CollectionUtils.notEmpty(layouts)) {
                layouts.forEach(layout -> resultMap.putIfAbsent(layout.getName(), layout));
            }
        }
        return Lists.newArrayList(resultMap.values());
    }

    /**
     * 使用并行或同步方式更新布局
     *
     * @param layoutMap      布局映射
     * @param user           用户信息
     * @param objectDescribe 对象描述
     * @param methodName     调用方法名称，用于日志记录
     */
    private void updateLayoutsInParallelOrSequential(Map<String, Layout> layoutMap, User user, IObjectDescribe objectDescribe, String methodName) {
        // 使用灰度控制布局更新的并行处理
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.PARALLEL_LAYOUT_UPDATE_GRAY, user.getTenantId()) && layoutMap.size() > 3) {
            // 异步并行更新布局，每3个布局启动一个线程
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            List<Map.Entry<String, Layout>> layoutEntries = Lists.newArrayList(layoutMap.entrySet());
            List<List<Map.Entry<String, Layout>>> partitionedLayouts = Lists.partition(layoutEntries, 3);

            for (List<Map.Entry<String, Layout>> layoutBatch : partitionedLayouts) {
                parallelTask.submit(() -> {
                    for (Map.Entry<String, Layout> entry : layoutBatch) {
                        layoutLogicService.updateLayout(user, entry.getValue(), objectDescribe);
                    }
                });
            }

            try {
                parallelTask.await(10, TimeUnit.SECONDS);
                log.info("Parallel layout update completed for {} layouts in {}", layoutMap.size(), methodName);
            } catch (TimeoutException e) {
                log.warn("Parallel layout update timeout, falling back to sequential update. layoutCount: {}, user: {}, method: {}",
                        layoutMap.size(), user.getTenantId(), methodName, e);
            }
        } else {
            // 原有的同步更新逻辑
            layoutMap.forEach((x, y) -> layoutLogicService.updateLayout(user, y, objectDescribe));
        }
    }
}
