package com.facishare.paas.appframework.metadata.metadatahandle;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ProductAllCategoriesModel;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * create by zhaoju on 2020/06/09
 */
@Data
@Builder
public class LookupDataValidator {
    private User user;
    private IObjectData objectData;
    private IObjectReferenceField referenceField;

    private ProductCategoryService productCategoryService;
    private MetaDataFindService metaDataFindService;
    private DescribeLogicService describeLogicService;

    private boolean isIgnorePolygonal;
    private boolean throwException;

    public String validate() {
        if (AppFrameworkConfig.isSkipValidateLookupGrayTenant(user.getTenantId())) {
            return null;
        }

        Object o = objectData.get(referenceField.getApiName());
        String value = String.valueOf(o);

        if (CollectionUtils.empty(referenceField.getWheres())
                || Objects.isNull(o)
                || Strings.isNullOrEmpty(value)) {
            return null;
        }

        List<Wheres> wheres = ObjectDescribeExt.getWheresBy(referenceField.getWheres());
        handleProductCategory(wheres);
        SearchTemplateQuery query = buildSearchTemplateQuery(value, wheres);
        List<IObjectData> dataList = metaDataFindService.findBySearchQuery(user, referenceField.getTargetApiName(), query).getData();
        return buildResult(dataList);
    }

    private String buildResult(List<IObjectData> dataList) {
        if (CollectionUtils.notEmpty(dataList)) {
            return null;
        }
        if (!throwException) {
            return referenceField.getApiName();
        }
        throw new ValidateException(getValidateMessage(referenceField));
    }

    private void handleProductCategory(List<Wheres> wheres) {
        if (!Utils.PRODUCT_API_NAME.equals(referenceField.getTargetApiName())) {
            return;
        }
        //特殊处理产品对象的分类（把父类和子类一起作为查询条件）
        AtomicBoolean init = new AtomicBoolean(false);
        List<ProductAllCategoriesModel.CategoryPojo> productAllCategories = Lists.newArrayList();
        wheres.forEach(x -> {
            x.getFilters().stream().filter(y -> FieldDescribeExt.PRODUCT_CATEGORY.equals(y.getFieldName()))
                    .filter(y -> Operator.EQ.equals(y.getOperator()) || Operator.N.equals(y.getOperator()))
                    .forEach(y -> {
                        if (init.compareAndSet(false, true)) {
                            List<ProductAllCategoriesModel.CategoryPojo> dbProductAllCategories = getProductAllCategories();
                            productAllCategories.addAll(dbProductAllCategories);
                        }
                        Set<String> categorySet = productCategoryService.getCategoryChildrenCategoryCodesContainSelf(y.getFieldValues().get(0), productAllCategories);
                        if (Operator.EQ.equals(y.getOperator())) {
                            y.setOperator(Operator.IN);
                        } else {
                            y.setOperator(Operator.NIN);
                        }
                        y.setFieldValues(Lists.newArrayList(categorySet));
                    });
        });
    }

    private SearchTemplateQuery buildSearchTemplateQuery(String value, List<Wheres> wheres) {
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.ID);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Collections.singletonList(value));

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(1);
        query.addFilters(Lists.newArrayList(filter));

        query.setWheres(wheres);


        isIgnorePolygonal(wheres, query);
        SearchTemplateQueryExt.of(query).handleWheresFilterWithLookupFunction();
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.GRAY_REMOVE_MASTER_AND_NATIVE_FILTER, user.getTenantId())) {
            SearchTemplateQueryExt.removeMasterAndNativeObjVariableFilter(wheres);
        }
        return query;
    }

    private void isIgnorePolygonal(List<Wheres> wheres, SearchTemplateQuery query) {
        if (isIgnorePolygonal) {
            // 移除所有多角关系的lookup过滤条件
            SearchTemplateQueryExt.of(query).removeRelatedObjectFilter(wheres);
            return;
        }
        SearchTemplateQueryExt.of(query).handleWheresFilterWithLookupRelation(objectData);
    }

    private List<ProductAllCategoriesModel.CategoryPojo> getProductAllCategories() {
        if (Utils.PRODUCT_API_NAME.equals(referenceField.getTargetApiName())) {
            return productCategoryService.getProductAllCategories(user.getTenantId(), user.getUserId());
        }
        return Collections.emptyList();
    }

    private String getValidateMessage(IObjectReferenceField referenceField) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), referenceField.getDescribeApiName());
        if (StringUtils.isBlank(referenceField.getHelpText())) {
            return I18N.text(I18NKey.LOOKUP_SINGLE_UNSATISFIED_CONDITION, describe.getDisplayName(), referenceField.getLabel());
        }
        return String.format("%s,%s", I18N.text(I18NKey.LOOKUP_SINGLE_UNSATISFIED_CONDITION, describe.getDisplayName(), referenceField.getLabel()), referenceField.getHelpText());
    }
}
