package com.facishare.paas.appframework.metadata.changeorder;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.ApprovalFlowService;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.appframework.metadata.util.I18KeyBuildUtils;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by zhaooju on 2023/3/29
 */
@Slf4j
@Service("changeOrderRuleLogicService")
public class ChangeOrderRuleLogicServiceImpl implements ChangeOrderRuleLogicService {
    public static final int MAX_COUNT = 1;
    @Autowired
    private IRepository<MtChangeOrderRule> repository;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private ChangeOrderLogicService changeOrderLogicService;
    @Autowired
    private FunctionLogicService functionLogicService;
    @Autowired
    private LogService logService;
    @Autowired
    private ApprovalFlowService approvalFlowService;


    @Autowired
    private I18nSettingService i18nSettingService;
    @Autowired
    private LicenseService licenseService;

    @Override
    public List<MtChangeOrderRule> findAll(User user) { // 列表翻译信息不外露，不翻译
        SearchQuery searchQuery = SearchQueryImpl.filter(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());

        Query query = builderBySearchQuery(searchQuery);
        return repository.findBy(user, query, MtChangeOrderRule.class);
    }

    @Override
    public List<MtChangeOrderRule> findByDescribeApiName(User user, String describeApiName) {   // 都是前台调用
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, MtChangeOrderRule.ORIGINAL_DESCRIBE_API_NAME, describeApiName).getFilter(),
                FilterExt.of(Operator.EQ, MtChangeOrderRule.IS_ACTIVE, Boolean.TRUE.toString()).getFilter());
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        Query query = builderBySearchQuery(searchQuery);
        return transChangeRule(repository.findBy(user, query, MtChangeOrderRule.class), false);
    }


    @Override
    public Optional<MtChangeOrderRule> findByRuleName(User user, String ruleName, boolean onTime) { // 后台查询 实时
        if (Strings.isNullOrEmpty(ruleName)) {
            return Optional.empty();
        }
        Optional<MtChangeOrderRule> result = findOne(user, () -> SearchQueryImpl.filter(FilterExt.of(Operator.EQ, MtChangeOrderRule.API_NAME, ruleName).getFilter()));
        return result.isPresent() ?
                transChangeRule(Collections.singletonList(result.get()), onTime).stream().findFirst()
                : Optional.empty();
    }

    @Override
    public Optional<MtChangeOrderRule> findByRuleName(User user, String ruleName) {
        return findByRuleName(user, ruleName, false);
    }

    private void synChangeRuleTranslation(List<MtChangeOrderRule> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }
        Map<String, String> keyValue = rules.stream()
                .collect(Collectors.toMap(x -> I18KeyBuildUtils.getChangeRuleChangePromptKey(x.getApiName()), x -> x.getChangeCondition().getMessage()));
        keyValue.putAll(rules.stream()
                .collect(Collectors.toMap(x -> I18KeyBuildUtils.getChangeRuleVerificationPromptKey(x.getApiName()), x -> x.getVerifyCondition().getMessage())));
        i18nSettingService.saveTransValue(keyValue, true);
    }

    private List<MtChangeOrderRule> transChangeRule(List<MtChangeOrderRule> rules, boolean onTime) {
        if (CollectionUtils.isEmpty(rules)) {
            return new ArrayList<>();
        }
        List<String> i18Key = rules.stream()
                .flatMap(x -> Stream.of((I18KeyBuildUtils.getChangeRuleChangePromptKey(x.getApiName())),
                        I18KeyBuildUtils.getChangeRuleVerificationPromptKey(x.getApiName())))
                .collect(Collectors.toList());
        Map<String, String> i18Map = i18nSettingService.getTransValue(i18Key, false, onTime);
        if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(i18Map)) {
            rules.forEach(x -> {
                String changePromptKey = I18KeyBuildUtils.getChangeRuleChangePromptKey(x.getApiName());
                String verificationPromptKey = I18KeyBuildUtils.getChangeRuleVerificationPromptKey(x.getApiName());
                x.getChangeCondition().setMessage(i18Map.getOrDefault(changePromptKey, x.getChangeCondition().getMessage()));
                x.getVerifyCondition().setMessage(i18Map.getOrDefault(verificationPromptKey, x.getVerifyCondition().getMessage()));
            });
        }
        return rules;
    }


    @Override
    public void checkCount(User user, String describeApiName) {
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, MtChangeOrderRule.ORIGINAL_DESCRIBE_API_NAME, describeApiName).getFilter());
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .build();

        final Integer count = repository.findCountOnly(user, query, MtChangeOrderRule.class);
        if (count >= MAX_COUNT) {
            throw new ValidateException(I18N.text(I18NKey.CHANGE_RULE_MAX_LIMIT, MAX_COUNT));
        }
    }

    private Optional<MtChangeOrderRule> findOne(User user, Supplier<SearchQuery> supplier) {
        List<IFilter> filters = Lists.newArrayList(
                FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        SearchQuery searchQuery = supplier.get().and(filters);
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(1)
                .offset(0)
                .build();
        List<MtChangeOrderRule> result = repository.findBy(user, query, MtChangeOrderRule.class);
        return result.stream().findFirst();
    }

    @Override
    @Transactional
    public void create(User user, MtChangeOrderRule changeOrderRule) {
        // 校验个数
        String describeApiName = changeOrderRule.getOriginalDescribeApiName();
        checkCount(user, describeApiName);
        checkEffectiveType(user, changeOrderRule);
        checkFieldCount(user, changeOrderRule, describeApiName);
        checkObjectDeleted(user, changeOrderRule);

        changeOrderRule.setTenantId(user.getTenantId());
        changeOrderRule.setCreateBy(user.getUserId());
        changeOrderRule.setCreateTime(System.currentTimeMillis());
        changeOrderRule.setActive(true);
        changeOrderRule.setObjectDescribeApiName(MtChangeOrderRule.MT_CHANGE_RULE_OBJ_API_NAME);
        repository.create(user, changeOrderRule);
        updateChangeOrderDescribe(user, changeOrderRule);   // 创建不需要翻译
        changeFunctionStatus(user, changeOrderRule, null);
    }

    /**
     * 移除掉已经被删除对象的映射关系
     *
     * @param user
     * @param changeOrderRule
     */
    private void checkObjectDeleted(User user, MtChangeOrderRule changeOrderRule) {
        Set<String> objectApiNames = new HashSet<>();
        changeOrderRule.getFieldMapping().forEach(x -> objectApiNames.add(x.getSourceApiName()));
        Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjects(user.getTenantId(), objectApiNames);
        changeOrderRule.getFieldMapping().removeIf(x -> !objectDescribeMap.containsKey(x.getSourceApiName()));
    }

    private void checkFieldCount(User user, MtChangeOrderRule changeOrderRule, String describeApiName) {
        List<MtChangeOrderRule.ObjectMapping> fieldMapping = changeOrderRule.getFieldMapping();
        ChangeOrderConfig.ChangeOrderConfigItem changeOrderConfigItem = ChangeOrderConfig.getChangeOrderConfigItem(describeApiName);
        for (MtChangeOrderRule.ObjectMapping objectMapping : fieldMapping) {
            List<MtChangeOrderRule.FieldMapping> fieldMappings = objectMapping.getFieldMappings();
            if (Objects.isNull(fieldMappings)) {
                continue;
            }
            long count = fieldMappings.stream().filter(MtChangeOrderRule.FieldMapping::getRecordOriginalValue).count();
            long originalCountLimit = changeOrderConfigItem.getOriginalCount();
            if (count > originalCountLimit) {
                log.info("checkFieldCount fail! ei:{}, objectApiName:{}, count:{}, originalCountLimit:{}", user.getTenantId(), objectMapping.getSourceApiName(), count, originalCountLimit);
                throw new ValidateException(I18N.text(I18NKey.CHANGE_RULE_ORIGINAL_COUNT_OUT_OF_LIMIT, originalCountLimit, describeApiName));
            }
        }
    }

    private void checkEffectiveType(User user, MtChangeOrderRule changeOrderRule) {
        MtChangeOrderRule.EffectiveType effectiveType = changeOrderRule.toEffectiveType();
        if (MtChangeOrderRule.EffectiveType.AUTO_EFFECT_AFTER_APPROVAL != effectiveType) {
            return;
        }
        boolean flowDefinitions = approvalFlowService.hasActiveApprovalFlowDefinitions(user, changeOrderRule.getChangeDescribeApiName(),
                ApprovalFlowTriggerType.CREATE.getTriggerTypeCode());
        if (!flowDefinitions) {
            log.warn("checkEffectiveType fail! ei:{}, objectApiName:{}", user.getTenantId(), changeOrderRule.getChangeDescribeApiName());
            throw new ValidateException(I18NExt.text(I18NKey.NOT_DEFINITION_APPROVAL_WITH_CHANGE_ORDER_RULE));
        }
    }

    private void changeFunctionStatus(User user, MtChangeOrderRule changeOrderRule, MtChangeOrderRule.ChangeCondition oldChangeCondition) {
        if (Objects.isNull(oldChangeCondition)) {
            saveFunctionStatus(user, changeOrderRule, IUdefFunction.USED);
            return;
        }
        if (Objects.equals(changeOrderRule.getChangeCondition(), oldChangeCondition)) {
            return;
        }
        saveFunctionStatus(user, changeOrderRule, IUdefFunction.USED);
        changeOrderRule.setChangeCondition(oldChangeCondition);
        saveFunctionStatus(user, changeOrderRule, IUdefFunction.NOT_USED);
    }

    private void saveFunctionStatus(User user, MtChangeOrderRule changeOrderRule, String status) {
        String functionApiName = Optional.ofNullable(changeOrderRule.getChangeCondition())
                .map(MtChangeOrderRule.ChangeCondition::getFunctionApiName)
                .orElse("");
        if (Strings.isNullOrEmpty(functionApiName)) {
            return;
        }
        String describeApiName = changeOrderRule.getOriginalDescribeApiName();
        IUdefFunction function = functionLogicService.findUDefFunction(user, functionApiName, describeApiName);
        if (Objects.isNull(function)) {
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, functionApiName));
        }
        if (IUdefFunction.USED.equals(status)) {
            functionLogicService.saveRelation(user, Lists.newArrayList(buildReferenceData(changeOrderRule, function)));
        } else {
            functionLogicService.deleteRelation(user, function.getNameSpace(), changeOrderRule.getApiName(), function.getApiName());
        }
        logService.logUdefFunction(user, EventType.MODIFY, ActionType.UPDATE_FUNCTION_REFERENCE, describeApiName, functionApiName, function);
    }

    private ReferenceData buildReferenceData(MtChangeOrderRule changeOrderRule, IUdefFunction function) {
        return ReferenceData.builder()
                .sourceType(function.getNameSpace())
                .sourceLabel(changeOrderRule.getLabel())
                .sourceValue(changeOrderRule.getApiName())
                .targetType(TargetTypes.FUNCTION)
                .targetValue(function.getApiName()).build();
    }

    @Override
    @Transactional
    public void update(User user, MtChangeOrderRule changeOrderRule) {  // 唯一使用，不需要重载
        if (Objects.isNull(changeOrderRule)) {
            return;
        }
        // 规则不存在不能修改
        MtChangeOrderRule orderRule = findByRuleName(user, changeOrderRule.getApiName())
                .orElseThrow(() -> new ValidateException(I18N.text(I18NKey.CHANGE_RULE_NOT_EXIST_OR_DELETED)));
        checkEffectiveType(user, changeOrderRule);
        checkFieldCount(user, changeOrderRule, changeOrderRule.getOriginalDescribeApiName());
        // 合并
        MtChangeOrderRule.ChangeCondition oldChangeCondition = orderRule.getChangeCondition();
        orderRule.merge(changeOrderRule);
        orderRule.setLastModifiedBy(user.getUserId());
        orderRule.setLastModifiedTime(System.currentTimeMillis());
        orderRule.setObjectDescribeApiName(MtChangeOrderRule.MT_CHANGE_RULE_OBJ_API_NAME);

        checkObjectDeleted(user, changeOrderRule);

        repository.update(user, orderRule);
        updateChangeOrderDescribe(user, orderRule);
        changeFunctionStatus(user, changeOrderRule, oldChangeCondition);
        synChangeRuleTranslation(Collections.singletonList(orderRule));
    }

    private void updateChangeOrderDescribe(User user, MtChangeOrderRule orderRule) {
        List<MtChangeOrderRule.ObjectMapping> fieldMapping = orderRule.getFieldMapping();

        for (MtChangeOrderRule.ObjectMapping objectMapping : fieldMapping) {
            Map<String, String> fieldMappings = Maps.newHashMap();
            for (MtChangeOrderRule.FieldMapping mapping : objectMapping.getFieldMappings()) {
                if (mapping.getRecordOriginalValue()) {
                    String sourceFieldApiName = mapping.getSourceFieldApiName();
                    String targetFieldApiName = mapping.getTargetFieldApiName();
                    fieldMappings.put(FieldDescribeExt.getOriginalFieldName(sourceFieldApiName), targetFieldApiName);
                }
            }
            IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), objectMapping.getTargetApiName());
            List<String> originalFields = ObjectDescribeExt.of(describe).stream()
                    .map(IFieldDescribe::getApiName)
                    .filter(FieldDescribeExt::isOriginalFieldName)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(originalFields) && MapUtils.isEmpty(fieldMappings)) {
                continue;
            }
            Collection<String> toAddFields = CollectionUtils.subtract(fieldMappings.keySet(), originalFields);
            Collection<String> toDeleted = CollectionUtils.subtract(originalFields, fieldMappings.keySet());
            for (String originalFieldName : toAddFields) {
                String changeFieldName = fieldMappings.get(originalFieldName);
                IFieldDescribe fieldDescribe = describe.getFieldDescribe(changeFieldName);
                if (Objects.nonNull(fieldDescribe)) {
                    IFieldDescribe copyToSync = FieldDescribeExt.of(fieldDescribe).generatedChangeOrderOriginalField(originalFieldName);
                    describe.addFieldDescribe(copyToSync);
                }
            }
            describeLogicService.update(user, describe);
            describeLogicService.deleteFieldDirect(user, describe, toDeleted);

            List<String> changeFieldNames = originalFields.stream()
                    .map(fieldMappings::get)
                    .filter(fieldName -> !Strings.isNullOrEmpty(fieldName))
                    .collect(Collectors.toList());
            changeOrderLogicService.syncFieldI18n(user, describe, changeFieldNames);
        }
    }

    @Override
    public void enable(User user, String ruleName) {
        enableOrDisable(user, ruleName, true);
    }

    @Override
    public void disable(User user, String ruleName) {
        enableOrDisable(user, ruleName, false);
    }

    private MtChangeOrderRule enableOrDisable(User user, String ruleName, boolean enable) {
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, MtChangeOrderRule.API_NAME, ruleName).getFilter(),
                FilterExt.of(Operator.N, IFieldDescribe.IS_ACTIVE, String.valueOf(enable)).getFilter());
        return findOne(user, () -> SearchQueryImpl.filters(filters))
                .map(changeOrderRule -> {
                    changeOrderRule.setActive(enable);
                    return repository.update(user, changeOrderRule);
                }).orElse(null);
    }

    @Override
    public void delete(User user, String ruleName) {
        MtChangeOrderRule changeOrderRule = findByRuleName(user, ruleName)
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.CHANGE_RULE_NOT_EXIST_OR_DELETED)));
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, ObjectDataExt.CHANGE_ORDER_RULE, changeOrderRule.getApiName()).getFilter(),
                FilterExt.of(Operator.EQ, ObjectDataExt.EFFECTIVE_STATUS, ChangeOrderEffectiveStatus.INEFFECTIVE.getCode()).getFilter());
        String describeApiName = changeOrderRule.getChangeDescribeApiName();
        List<IObjectData> dataList = metaDataFindService.findDataWithWhere(user, describeApiName, filters, Lists.newArrayList(), 0, 1);
        if (CollectionUtils.isNotEmpty(dataList)) {
            log.warn("delete fail! data is in change ei:{}, ruleName:{}, describeApiName:{}", user.getTenantId(), ruleName, describeApiName);
            throw new ValidateException(I18NExt.text(I18NKey.DATA_IN_CHANGE_WITH_CHANGE_RULES));
        }
        repository.bulkDelete(user, Lists.newArrayList(changeOrderRule));
    }

    private Query builderBySearchQuery(SearchQuery searchQuery) {
        return Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, false)))
                .limit(1000)
                .offset(0)
                .build();
    }
}
