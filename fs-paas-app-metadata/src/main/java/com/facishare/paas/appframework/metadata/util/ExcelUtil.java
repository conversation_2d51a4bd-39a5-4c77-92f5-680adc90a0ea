package com.facishare.paas.appframework.metadata.util;

import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NUploadFileDirect;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Pair;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.dto.HeaderMergeInfo;
import com.facishare.paas.appframework.metadata.dto.ImageInfo;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.TrueOrFalse;
import com.facishare.paas.metadata.impl.describe.CurrencyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.NumberFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.support.GDSHandler;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.*;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.LongStream;

/**
 * Created by linqiuying on 17/4/20.
 */

@Service
@Slf4j
public class ExcelUtil {
    @Autowired
    private WebFileUtil webFileUtil;
    @Autowired
    private GDSHandler gdsHandler;

    /**
     * 用于过滤文件名中的非法字符
     * 匹配冒号（中英文）、方括号、星号、问号（中英文）、正斜杠、反斜杠
     */
    private final String INVALIDCHARREGEX = "[:：\\[\\]\\*\\?？/\\\\]+";

    public static final int FIELD_LENGTH = 32767;

    public static final List<String> IMAGE_AND_ATTACHMENT_TYPES = Lists.newArrayList(IFieldType.IMAGE, IFieldType.FILE_ATTACHMENT, IFieldType.SIGNATURE);

    public static final List<String> TEXT_FIELDS = Lists.newArrayList(IFieldType.TEXT, IFieldType.BIG_TEXT, IFieldType.LONG_TEXT, IFieldType.HTML_RICH_TEXT, IFieldType.RICH_TEXT);

    /**
     * `     * Excel函数名列表
     */
    private static final String EXCEL_FUNCTIONS = "SUM|AVERAGE|IF|HYPERLINK|CONCATENATE|INDEX|MATCH|VLOOKUP|" +
            "HLOOKUP|COUNT|MAX|MIN|AND|OR|NOT|NOW|TODAY|RAND|RANDBETWEEN|ROUND|FLOOR|CEILING|ABS|SQRT|LEFT|" +
            "RIGHT|MID|LEN|SEARCH|FIND|SUBSTITUTE|REPLACE|TRIM|LOWER|UPPER|PROPER|TEXT|VALUE|DOLLAR|FIXED|INT|" +
            "MOD|POWER|LOG|EXP|PI|SIN|COS|TAN|ASIN|ACOS|ATAN|FACT|SUBTOTAL|SUMIF|COUNTIF|SUMPRODUCT|INDIRECT|" +
            "OFFSET|ADDRESS|ROW|COLUMN|CELL|SHEET|SHEETS|LOOKUP|CHOOSE|IFERROR|IFNA|IFS|SWITCH|SORT|SORTBY|FILTER|" +
            "UNIQUE|SEQUENCE|RANDARRAY|XLOOKUP|XMATCH|LET|LAMBDA|MAP|REDUCE|SCAN|BYROW|BYCOL|MAKEARRAY|TAKE|" +
            "DROP|VSTACK|HSTACK|TOROW|TOCOL|WRAPROWS|WRAPCOLS";

    /**
     * 危险的系统命令和关键字
     */
    private static final String DANGEROUS_COMMANDS =
            "cmd|exec|shell|powershell|system|eval|execute|" +
                    "process|spawn|fork|" +
                    "script|run|invoke|" +
                    "execScript|Runtime|ProcessBuilder|" +
                    "cscript|wscript|mshta|psexec|at|schtasks|" +
                    "bash|sh|ksh|csh|awk|sed|" +
                    "ping|telnet|netcat|nc|" +
                    "ssh|ftp|sftp|" +
                    "mkdir|rmdir|copy|xcopy|robocopy|del|rm|mv|cp|" +
                    "createObject|getObject|newObject|activex|" +
                    "system32|cmd.exe|powershell.exe|" +
                    "select|insert|update|delete|drop|alter|create|" +
                    "dde|ddeinit|ddeauto|" +
                    "macro|module|vba|" +
                    "workbook|sheet|range|cell|application|" +
                    "call|apply|bind|function|open|write";

    /**
     * 高风险模式 - 这些模式在任何上下文中都表示潜在风险
     */
    private static final Pattern HIGH_RISK_PATTERN = Pattern.compile(
            "(?i)(" +
                    // 1. 以等号开头的内容
                    "^\\s*=|" +

                    // 2. CSV分隔符后跟等号（移除竖线）
                    "(?:^|[,;，；\\t])\\s*=|" +

                    // 3. 以加号开头但不是纯数字
                    "^\\s*\\+(?!\\d+(\\.\\d+)?$)(?!.+\\+$)|" +

                    // 4. 以减号开头但不是纯数字
                    "^\\s*-(?!\\d+(\\.\\d+)?$)(?!.+-$)|" +

                    // 5. 以@开头后跟Excel函数名
                    "^\\s*@(?:" + EXCEL_FUNCTIONS + ")\\s*\\(|" +

                    // 6. 危险命令函数调用
                    "\\b(?:" + DANGEROUS_COMMANDS + ")\\s*\\(|" +

                    // 7. Excel函数调用
                    "(?:^|[,;，；\\t])=\\s*(?:" + EXCEL_FUNCTIONS + ")\\s*\\(" +
                    ")"
    );

    /**
     * 安全业务文本模式 - 这些通常是安全的业务文本
     */
    private static final Pattern SAFE_BUSINESS_PATTERN = Pattern.compile(
            "(?i)(" +
                    // 1. 表格数据格式 - 支持多种分隔符（逗号、制表符、中文逗号等）
                    "^.+[,\\t，;；].+[,\\t，;；]=[^,\\t，;；]*$|" +  // 通用表格数据格式

                    // 2. 带标记的文本
                    "^-[^-]+-$|^\\+[^+]+\\+$|^\\*[^*]+\\*$|" +  // 如 -待审核- 或 +重要+ 或 *强调*

                    // 3. 电话号码和数字
                    "^\\+\\d{1,3}-\\d+-\\d+$|" +            // 国际电话，如 +86-10-12345678
                    "^\\+?\\d+(\\.\\d+)?$|" +               // 正数，如 +123 或 123.45
                    "^-\\d+(\\.\\d+)?$|" +                  // 负数，如 -456 或 -789.01

                    // 4. 竖线分隔的选项
                    "[^|]+\\|[^|]+\\|=.+|" +                // 如 选项A|选项B|=C

                    // 5. 数学和科学表达式
                    ".*\\b[a-zA-Z]\\([a-zA-Z0-9,\\s]+\\).*|" + // 函数表示，如 f(x) 或 add(x,y)
                    ".*[a-zA-Z]\\s*=\\s*[^=]+|" +            // 方程式，如 y=2x+1

                    // 6. 日期和时间
                    "\\d{1,4}-\\d{1,2}-\\d{1,2}|" +         // 如 2023-10-15
                    "\\d{1,2}:\\d{1,2}(-\\d{1,2}:\\d{1,2})?" + // 如 10:30 或 10:30-11:45
                    ")"
    );

    public Workbook genarateTemplate(String ea,
                                     String userId,
                                     String describeLabel,
                                     List<Pair<String, IFieldDescribe>> rowList,
                                     List<List<String[]>> sampleList) throws IOException {
        return createWorkbook(describeLabel, rowList, sampleList, null);
    }

    /**
     * 生成联合导入模板
     */
    public Workbook generateUnionTemplate(String ea,
                                          String userId,
                                          String describeLabel,
                                          List<Pair<String, IFieldDescribe>> rowList,
                                          List<List<String[]>> sampleList,
                                          Workbook workbook) {
        return createWorkbook(describeLabel, rowList, sampleList, workbook);
    }

    /**
     * 生成带表头合并的导入模板
     */
    public Workbook generateTemplateWithMerge(String ea,
                                             String userId,
                                             String describeLabel,
                                             List<Pair<String, IFieldDescribe>> rowList,
                                             List<List<String[]>> sampleList,
                                             List<com.facishare.paas.appframework.metadata.dto.HeaderMergeInfo> mergeInfoList) throws IOException {
        return createWorkbookWithMerge(describeLabel, rowList, sampleList, null, mergeInfoList);
    }

    /**
     * 生成带表头合并的联合导入模板
     */
    public Workbook generateUnionTemplateWithMerge(String ea,
                                                  String userId,
                                                  String describeLabel,
                                                  List<Pair<String, IFieldDescribe>> rowList,
                                                  List<List<String[]>> sampleList,
                                                  Workbook workbook,
                                                  List<HeaderMergeInfo> mergeInfoList) {
        return createWorkbookWithMerge(describeLabel, rowList, sampleList, workbook, mergeInfoList);
    }

    /**
     * 生成workbook
     */
    private Workbook createWorkbook(String describeLabel,
                                    List<Pair<String, IFieldDescribe>> rowList,
                                    List<List<String[]>> sampleList,
                                    Workbook workbookResult) {
        return createWorkbookWithMerge(describeLabel, rowList, sampleList, workbookResult, null);
    }

    /**
     * 生成带表头合并的workbook
     */
    private Workbook createWorkbookWithMerge(String describeLabel,
                                            List<Pair<String, IFieldDescribe>> rowList,
                                            List<List<String[]>> sampleList,
                                            Workbook workbookResult,
                                            List<HeaderMergeInfo> mergeInfoList) {
        workbookResult = Objects.isNull(workbookResult) ? new SXSSFWorkbook(500) : workbookResult;
        //初始化单元格样式
        Map<String, CellStyle> styles = createStyle(workbookResult);

        describeLabel = removeInvalidCharacters(describeLabel);
        Sheet newSheet = workbookResult.createSheet(describeLabel + I18N.text(I18NKey.IMPORT_TEMPLATE));
        //设置列宽度
        for (int col = 0; col < rowList.size(); col++) {
            newSheet.setColumnWidth(col, 15 * 256);
        }

        // 创建单行表头
        Row headerRow = newSheet.createRow(0);

        // 设置所有字段标题
        for (int i = 0; i < rowList.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(rowList.get(i).getKey());
        }

        // 处理图片字段的表头合并（在同一行中合并）
        if (CollectionUtils.notEmpty(mergeInfoList)) {
            for (HeaderMergeInfo mergeInfo : mergeInfoList) {
                // 在合并区域的第一个单元格设置父级标题（已包含必填标记）
                Cell firstCell = headerRow.getCell(mergeInfo.getStartColumn());
                firstCell.setCellValue(mergeInfo.getParentTitle());

                // 合并单元格（同一行内合并）
                CellRangeAddress mergeRegion = new CellRangeAddress(0, 0,
                    mergeInfo.getStartColumn(), mergeInfo.getEndColumn());
                newSheet.addMergedRegion(mergeRegion);
            }
        }

        // 如果示例为空，不再进行下面操作
        if (CollectionUtils.empty(sampleList)) {
            return workbookResult;
        }

        // 数据行从第1行开始（表头占用第0行）
        int dataRowStartIndex = 1;

        //构造示例数据
        for (int row = 0; row < sampleList.size(); row++) {
            Row sampleRow = newSheet.createRow(row + dataRowStartIndex);
            List<String[]> line = sampleList.get(row);
            for (int col = 0; col < line.size(); col++) {
                Cell cell = getFormattedCell(sampleRow, col, rowList.get(col).getValue(), styles);
                fillCellWithValue(cell, line.get(col), rowList.get(col).getValue());
            }
        }

        //构造下拉列表
        String hiddenSheetName = "hidden" + (describeLabel.hashCode() & 0x7fffffff);
        Sheet hidden = workbookResult.createSheet(hiddenSheetName);

        DataValidationHelper helper = newSheet.getDataValidationHelper();
        //如果第n列是单选字段，则把第n列单元格全部设置为单选
        int point = 0;
        for (int col = 0; col < rowList.size(); col++) {
            if (IFieldType.SELECT_ONE.equals(rowList.get(col).getValue().getType())
                    || IFieldType.TRUE_OR_FALSE.equals(rowList.get(col).getValue().getType())
                    || IFieldType.RECORD_TYPE.equals(rowList.get(col).getValue().getType())) {
                //在hidden中写入下拉框选项
                String[] options = sampleList.get(0).get(col);
                for (int i = 0; i < options.length; i++) {
                    Row row = hidden.createRow(point++);
                    Cell cell = row.createCell(0);
                    cell.setCellValue(options[i].trim());
                }
                //准备下拉列表数据
                CellRangeAddressList addressList = new CellRangeAddressList(dataRowStartIndex, 65535, col, col);
                DataValidationConstraint constraint = helper.createFormulaListConstraint(
                        hiddenSheetName + "!$A$" + (point - options.length + 1) + ":$A$" + point);
                DataValidation validation = helper.createValidation(constraint, addressList);
                //处理兼容性问题
                if (validation instanceof XSSFDataValidation) {
                    validation.setSuppressDropDownArrow(true);
                    validation.setShowErrorBox(true);
                } else {
                    validation.setSuppressDropDownArrow(false);
                }
                newSheet.addValidationData(validation);
            }
        }

        if (workbookResult.getNumberOfSheets() > 2) {
            //设置本次hiddeSheet为隐藏
            workbookResult.setSheetHidden(workbookResult.getSheetIndex(hidden), true);
        } else {
            workbookResult.setSheetHidden(1, true);
        }
        return workbookResult;
    }

    /**
     * 联合导入模板，多个sheet排序，主从按照从左到右排开, 隐藏的sheet排在后面
     */
    public void orderSheets(Workbook workbookResult) {
        int number = workbookResult.getNumberOfSheets();
        if (number <= 2) {
            return;
        }
        //第一个sheet是主对象sheet
        int i = 0;
        int j = 1;
        while (j < number) {
            if (i < number && workbookResult.isSheetHidden(i)) {
                //如果i位置为隐藏sheet，则从j位置遍历，直到找到第一个非隐藏sheet
                while (j < number && workbookResult.isSheetHidden(j)) {
                    j++;
                }
                if (j >= number) {
                    break;
                }
                //交换i和j位置的sheet
                swap(workbookResult, i, j);
                //下一次循环
            } else {
                j++;
            }
            i++;
        }
    }

    /**
     * 交换sheet
     */
    private void swap(Workbook workbookResult, int i, int j) {
        String iName = workbookResult.getSheetName(i);
        String jName = workbookResult.getSheetName(j);
        workbookResult.setSheetOrder(iName, j);
        workbookResult.setSheetOrder(jName, i);
    }

    /*
     * 根据字段类型填充单元格
     */
    private void fillCellWithValue(Cell cell, String[] values, IFieldDescribe field) {
        if (IFieldType.RECORD_TYPE.equals(field.getType())) {
            cell.setCellValue("");
        } else {
            cell.setCellValue(values[0]);
        }
    }

    /*
     * 生成格式化的单元格
     */
    private Cell getFormattedCell(Row sampleRow, int col, IFieldDescribe field, Map<String, CellStyle> styles) {
        Cell cell = sampleRow.createCell(col);
        //根据字段类型获取cell类型
        CellStyle style = getCellStyleByField(field, styles);
        cell.setCellStyle(style);
        return cell;
    }

    /**
     * 导出时生成格式化数据
     */
    private Cell getExportFormattedCell(Row row, int col, IFieldDescribe field, Map<String, CellStyle> styles) {
        Cell cell = row.createCell(col);
        //根据字段类型获取cell类型
        CellStyle style = getExportCellStyleByField(field, styles);
        cell.setCellStyle(style);
        return cell;
    }

    /**
     * 根据字段类型选取单元格格式
     */
    private CellStyle getExportCellStyleByField(IFieldDescribe field, Map<String, CellStyle> styles) {
        switch (field.getType()) {
            case IFieldType.NUMBER:
                return getNumberCell(field, styles);
            case IFieldType.CURRENCY:
                return getCurrencyCell(field, styles);
            case IFieldType.IMAGE:
            case IFieldType.SIGNATURE:
            case IFieldType.FILE_ATTACHMENT:
            case IFieldType.QUOTE:
                return getAttachmentCell(field, styles);
            default:
                return styles.get("text");
        }
    }

    private static CellStyle getAttachmentCell(IFieldDescribe field, Map<String, CellStyle> styles) {
        String type = FieldDescribeExt.of(field).getTypeOrReturnType();
        return styles.getOrDefault(type, styles.get("text"));
    }

    /**
     * 获取货币单元格
     */
    private CellStyle getCurrencyCell(IFieldDescribe field, Map<String, CellStyle> styles) {
        CurrencyFieldDescribe currencyFieldDescribe = (CurrencyFieldDescribe) field;
        return styles.getOrDefault("currency" + currencyFieldDescribe.getDecimalPlaces(), styles.get("text"));
    }

    /**
     * 获取数字类型格式样式
     */
    private CellStyle getNumberCell(IFieldDescribe field, Map<String, CellStyle> styles) {
        NumberFieldDescribe numberFieldDescribe = (NumberFieldDescribe) field;
        return styles.getOrDefault("decimal" + numberFieldDescribe.getDecimalPlaces(), styles.get("text"));
    }

    /*
     * 根据字段类型获取单元格类型
     */
    private CellStyle getCellStyleByField(IFieldDescribe field, Map<String, CellStyle> styles) {
        CellStyle style = styles.get("text"); //默认为常规
        return style;
    }

    /*
     * 生成单元格样式
     */
    public Map<String, CellStyle> createStyle(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();
        CreationHelper ch = wb.getCreationHelper();

        CellStyle style;
        //标题行
        Font titleFont = wb.createFont();
        titleFont.setFontHeightInPoints((short) 14);
        style = wb.createCellStyle();
        style.setFont(titleFont);
        style.setBorderBottom(BorderStyle.DOTTED);
        style.setBottomBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        styles.put("title", style);

        Font itemFont = wb.createFont();
        itemFont.setFontHeightInPoints((short) 12);
        //整数类型
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat(BuiltinFormats.getBuiltinFormat(1)));
        styles.put("decimal0", style);

        //小数保留1位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.0"));
        styles.put("decimal1", style);

        //小数保留2位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat(BuiltinFormats.getBuiltinFormat(2)));
        styles.put("decimal2", style);

        //小数保留3位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.000"));
        styles.put("decimal3", style);

        //小数保留4位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.0000"));
        styles.put("decimal4", style);

        //小数保留5位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.00000"));
        styles.put("decimal5", style);

        //小数保留6位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.000000"));
        styles.put("decimal6", style);

        //小数保留7位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.0000000"));
        styles.put("decimal7", style);

        //小数保留8位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.00000000"));
        styles.put("decimal8", style);

        //小数保留9位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.000000000"));
        styles.put("decimal9", style);

        //小数保留10位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.0000000000"));
        styles.put("decimal10", style);

        //小数保留11位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.00000000000"));
        styles.put("decimal11", style);

        //小数保留12位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.000000000000"));
        styles.put("decimal12", style);

        //货币保留0位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat(BuiltinFormats.getBuiltinFormat(3)));
        styles.put("currency0", style);

        //货币保留1位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("#,##0.0"));
        styles.put("currency1", style);

        //货币保留2位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat(BuiltinFormats.getBuiltinFormat(4)));
        styles.put("currency2", style);

        //货币保留3位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("#,##0.000"));
        styles.put("currency3", style);

        //货币保留4位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("#,##0.0000"));
        styles.put("currency4", style);

        //货币保留5位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("#,##0.00000"));
        styles.put("currency5", style);

        //货币保留6位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("#,##0.000000"));
        styles.put("currency6", style);

        //货币保留7位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("#,##0.0000000"));
        styles.put("currency7", style);

        //货币保留8位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("#,##0.00000000"));
        styles.put("currency8", style);

        //货币保留9位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("#,##0.000000000"));
        styles.put("currency9", style);

        //货币保留10位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("#,##0.0000000000"));
        styles.put("currency10", style);

        //货币保留11位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("#,##0.00000000000"));
        styles.put("currency11", style);

        //货币保留12位
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("#,##0.000000000000"));
        styles.put("currency12", style);

        //百分数
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat("0.000000%"));
        styles.put("percentile", style);

        //文本/常规
        style = wb.createCellStyle();
        style.setFont(itemFont);
        style.setDataFormat(ch.createDataFormat().getFormat(BuiltinFormats.getBuiltinFormat(0)));
        styles.put("text", style);

        //图片附件导出的文件显示蓝色
        Font imageAndFileFont = wb.createFont();
        imageAndFileFont.setFontHeightInPoints((short) 12);
        imageAndFileFont.setColor(IndexedColors.BLUE.index);

        //图片附件
        style = wb.createCellStyle();
        style.setFont(imageAndFileFont);
        styles.put("image", style);

        style = wb.createCellStyle();
        style.setFont(imageAndFileFont);
        styles.put("file_attachment", style);

        style = wb.createCellStyle();
        style.setFont(imageAndFileFont);
        styles.put("signature", style);
        return styles;
    }

    private String removeInvalidCharacters(String describeLabel) {
        return describeLabel.replaceAll(INVALIDCHARREGEX, "");
    }

    public Sheet createSheetWithTitle(Workbook workbook, List<IFieldDescribe> exportFieldDescribe, String title) {
        title = removeInvalidCharacters(title);
        Sheet exportDataNewSheet = workbook.createSheet(title);
        Row newTitleRow = exportDataNewSheet.createRow(0);
        int headerIndex = 0;
        for (IFieldDescribe fieldDescribe : exportFieldDescribe) {
            if (ObjectDataExt.RELEVANT_TEAM.equals(fieldDescribe.getApiName())) {
                continue;
            }
            String label = fieldDescribe.getLabel();
            if (IFieldType.PERCENTILE.equals(FieldDescribeExt.of(fieldDescribe).getTypeOrReturnType())) {
                label = label + "（%）";
            }
            if (fieldDescribe.isRequired()) {
                label = label + I18N.text(I18NKey.REQUIRED);
            }
            newTitleRow.createCell(headerIndex++).setCellValue(label);
        }

        return exportDataNewSheet;
    }

    public void generateSheetWithCellStyle(Workbook workbook,
                                           Sheet exportDataNewSheet,
                                           int index,
                                           IObjectDescribe objectDescribe,
                                           List<IFieldDescribe> fieldDescribes,
                                           List<IObjectData> objectDataList,
                                           Map<String, CellStyle> styleMap,
                                           List<IFieldDescribe> exportFileAttachment,
                                           Set<String> notExportFields) {
        fillSheet(workbook, exportDataNewSheet, index, objectDescribe, fieldDescribes, objectDataList, styleMap, exportFileAttachment, notExportFields);
    }

    private void fillSheet(Workbook workbook,
                           Sheet exportDataNewSheet,
                           int index,
                           IObjectDescribe objectDescribe,
                           List<IFieldDescribe> fieldDescribes,
                           List<IObjectData> objectDataList,
                           Map<String, CellStyle> styleMap,
                           List<IFieldDescribe> exportFileAttachment,
                           Set<String> noExportFields) {
        //设置列宽度
        for (int col = 0; col < fieldDescribes.size() * 2; col++) {
            exportDataNewSheet.setColumnWidth(col, 15 * 256);
        }
        boolean isSupportFileAttachment = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_SUPPORT_FILE_GRAY, objectDescribe.getTenantId());
        for (int i = 1; i <= objectDataList.size(); i++) {
            Row row = exportDataNewSheet.createRow(index + i);
            int columnIndex = 0;
            IObjectData objectData = objectDataList.get(i - 1);
            for (IFieldDescribe fieldDescribe : fieldDescribes) {
                if (CollectionUtils.notEmpty(noExportFields) && noExportFields.contains(fieldDescribe.getApiName())) {
                    row.createCell(columnIndex++).setCellValue(I18NExt.getOrDefault(I18NKey.EXPORT_FIELD_NO_PERMISSION, "无字段的导出权限"));// ignoreI18n
                    continue;
                }
                // 相关团队
                if (ObjectDataExt.RELEVANT_TEAM.equals(fieldDescribe.getApiName())) {
                    continue;
                }
                String value = objectData.get(fieldDescribe.getApiName(), String.class);
                if (TEXT_FIELDS.contains(fieldDescribe.getType())) {
                    value = getFormatValue(value, objectDescribe.getTenantId());
                }
                if (Strings.isNullOrEmpty(value) || "[]".equals(value)) {
                    row.createCell(columnIndex++).setCellValue("");
                    continue;
                }
                if (ObjectDataExt.isMaskValue(value)) {
                    //掩码使用文本单元格
                    row.createCell(columnIndex++).setCellValue(value);
                    continue;
                }
                //图片和附件字段导出屏蔽内容
                if (IMAGE_AND_ATTACHMENT_TYPES.contains(fieldDescribe.getType())
                        || (IFieldType.QUOTE.equals(fieldDescribe.getType())
                        && IMAGE_AND_ATTACHMENT_TYPES.contains(FieldDescribeExt.of(fieldDescribe).getTypeOrReturnType()))) {
                    if (isSupportFileAttachment) {
                        Cell cell = getExportFormattedCell(row, columnIndex++, fieldDescribe, styleMap);
                        // 检查是否是嵌入式图片字段
                        if (FileExtUtil.isEmbeddedImageField(fieldDescribe)) {
                            handleEmbeddedImageCell(workbook, exportDataNewSheet, cell, objectData, fieldDescribe);
                        } else if (CollectionUtils.notEmpty(exportFileAttachment) &&
                                exportFileAttachment.stream().anyMatch(x -> StringUtils.equals(x.getApiName(), fieldDescribe.getApiName()))) {
                            CreationHelper createHelper = workbook.getCreationHelper();
                            Hyperlink link = createHelper.createHyperlink(HyperlinkType.URL);
                            link.setAddress(buildFileAddress(objectDescribe.getDisplayName(), objectData.getName(), fieldDescribe.getLabel()));
                            cell.setHyperlink(link);
                            cell.setCellValue(I18NExt.getOrDefault(I18NKey.EXPORT_LOOK_LOCATION_FILE, "查看本地文件"));// ignoreI18n
                        } else {
                            cell.setCellValue("");
                        }
                    } else {
                        row.createCell(columnIndex++).setCellValue("");
                    }
                    continue;
                }
                Cell cell = getExportFormattedCell(row, columnIndex, fieldDescribe, styleMap);
                if (value.contains("*****")) {
                    cell.setCellValue(value);
                } else if (IFieldType.NUMBER.equals(fieldDescribe.getType())) {
                    cell.setCellValue(Double.parseDouble(value));
                } else if (IFieldType.CURRENCY.equals(fieldDescribe.getType())) {
                    cell.setCellValue(Double.parseDouble(value));
                } else {
                    cell.setCellValue(value);
                }
                columnIndex++;
            }
        }
    }


    public static String getFormatValue(String value) {
        return getFormatValue(value, null);
    }

    public static String getFormatValue(String value, String tenantId) {
        try {
            if (StringUtils.isBlank(value)) {
                return null;
            }

            // 1. 处理前的准备
            String processedValue = truncateIfNeeded(value);
            if (!UdobjGrayConfig.isAllow("csv_injection_protection", tenantId)) {
                return processedValue;
            }

            // 2. 检查处理优先级
            boolean isRisky = isFormulaInjectionRisk(processedValue);
            boolean needsEscaping = needsCsvEscaping(processedValue);

            // 3. 应用保护策略
            if (isRisky) {
                // 风险内容添加单引号前缀
                return "'" + processedValue;
            } else if (needsEscaping) {
                // 需要CSV转义的内容添加双引号包围
                return escapeAsQuotedString(processedValue);
            } else {
                // 普通内容保持不变
                return processedValue;
            }
        } catch (Exception e) {
            log.warn("getFormatValue error: {}", value, e);
            return escapeAsQuotedString(value);
        }
    }

    /**
     * 检查是否需要CSV转义（有特殊字符）
     */
    private static boolean needsCsvEscaping(String value) {
        return value.contains(",") ||
                value.contains("\t") ||
                value.contains("\"") ||
                value.contains("\n") ||
                value.contains("\r") ||
                value.startsWith(" ") ||
                value.endsWith(" ");
    }

    /**
     * 截断超过最大长度的文本
     */
    private static String truncateIfNeeded(String value) {
        return value.length() > FIELD_LENGTH ? value.substring(0, FIELD_LENGTH) : value;
    }

    /**
     * 将文本转义为带引号的字符串（用于异常情况）
     */
    private static String escapeAsQuotedString(String value) {
        return "\"" + value.replace("\"", "\"\"") + "\"";
    }

    private String buildFileAddress(String displayName, String dataName, String fieldLabel) {
        String displayName_new = XmlUtil.filterName(displayName);
        String dataName_new = XmlUtil.filterName(dataName);
        String fieldLabel_new = XmlUtil.filterName(fieldLabel);
        String path = "file:///" + displayName_new + "/" + dataName_new + "/" + fieldLabel_new;
        try {
            URL url = new URL(path);
            URI uri = new URI(url.getProtocol(), url.getHost(), url.getPath(), url.getQuery());
            return StringUtils.substringAfter(uri.getRawPath(), "/");
        } catch (MalformedURLException | URISyntaxException e) {
            log.warn("buildFileAddress failed,displayName:{},dataName:{},fieldLabel:{}", displayName, dataName, fieldLabel, e);
            throw new RuntimeException(e);
        }
    }

    public void makeDropDownMenu(Workbook workbook, Sheet sheet, List<IFieldDescribe> fields) {
        //构造下拉列表
        Sheet hiddenSheet = workbook.createSheet("hidden" + workbook.getSheetIndex(sheet));

        DataValidationHelper helper = sheet.getDataValidationHelper();
        String[] values = null;
        List<ISelectOption> options = null;
        int point = 0;
        for (int col = 0; col < fields.size(); col++) {
            if (IFieldType.SELECT_ONE.equals(fields.get(col).getType())) {
                options = ((SelectOneFieldDescribe) fields.get(col)).getSelectOptions().stream().filter(
                        o -> !o.isNotUsable()).collect(Collectors.toList());
            } else if (IFieldType.TRUE_OR_FALSE.equals(fields.get(col).getType())) {
                options = ((TrueOrFalse) fields.get(col)).getSelectOptions();
            }

            if (CollectionUtils.notEmpty(options)) {
                values = new String[options.size()];
                int index = 0;
                for (ISelectOption o : options) {
                    values[index++] = o.getLabel();
                }

                for (int i = 0; i < values.length; i++) {
                    Row row = hiddenSheet.createRow(point++);
                    Cell cell = row.createCell(0);
                    String value = StringUtils.substring(values[i], 0, FIELD_LENGTH);
                    cell.setCellValue(value);
                }

                //准备下拉列表数据
                CellRangeAddressList addressList = new CellRangeAddressList(1, 100000, col, col);
                DataValidationConstraint constraint = helper.createFormulaListConstraint(
                        hiddenSheet.getSheetName() + "!$A$" + (point - values.length + 1) + ":$A$" + point);
                DataValidation validation = helper.createValidation(constraint, addressList);
                //处理兼容性问题
                if (validation instanceof XSSFDataValidation) {
                    validation.setSuppressDropDownArrow(true);
                    validation.setShowErrorBox(true);
                } else {
                    validation.setSuppressDropDownArrow(false);
                }
                sheet.addValidationData(validation);
                options = null;
            }
        }
        workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), Workbook.SHEET_STATE_VERY_HIDDEN);
    }


    public String exportData(Workbook exportDataWorkbookResult, String tenantId, String userId) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        writeToStream(outputStream, exportDataWorkbookResult);

        //上传文件到服务器并返回path
        String ea = gdsHandler.getEAByEI(tenantId);
        return webFileUtil.uploadFile(ea, userId, outputStream);
    }

    public NUploadFileDirect.Result exportDataDirect(Workbook exportDataWorkbookResult, ActionContext context, int exportFileExpireDay) throws IOException {
        String ea = gdsHandler.getEAByEI(context.getTenantId());

        // 灰度判断：是否启用 Stone SDK 流式上传
        boolean useStoneStreamUpload = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.STONE_STREAM_UPLOAD_GRAY, context.getTenantId());

        if (useStoneStreamUpload) {
            // 使用临时文件 + Stone SDK 流式上传方式，避免内存双重占用
            File tempFile = null;
            try {
                // 创建临时文件
                tempFile = createCustomTempFile("export_", ".xlsx");

                // 将 Workbook 写入临时文件
                try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                    exportDataWorkbookResult.write(fos);
                    fos.flush();
                }

                // 使用 Stone SDK 流式上传
                return webFileUtil.updateFileDirectFromFile(ea, context.getUser(), tempFile, exportFileExpireDay, "xlsx");

            } finally {
                // 清理临时文件
                if (tempFile != null && tempFile.exists()) {
                    boolean deleted = tempFile.delete();
                    if (!deleted) {
                        log.warn("Failed to delete temp file: {}", tempFile.getAbsolutePath());
                    }
                }
            }
        } else {
            // 使用原有的 ByteArrayOutputStream 方式（兼容性保证）
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            try {
                exportDataWorkbookResult.write(outputStream);
                outputStream.flush();
                return webFileUtil.updateFileDirect(ea, context.getUser(), outputStream, exportFileExpireDay, "xlsx");
            } finally {
                outputStream.close();
            }
        }
    }

    public NDownloadFile.Result downloadFile(String path, String ea, String userId) {
        return webFileUtil.downloadFile(path, ea, userId);
    }

    public NUploadFileDirect.Result uploadFile(User user, byte[] data, int exportFileExpireDay) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        outputStream.write(data);
        String ea = gdsHandler.getEAByEI(user.getTenantId());
        return webFileUtil.updateFileDirect(ea, user, outputStream, exportFileExpireDay, "xlsx");
    }

    public SXSSFWorkbook mergeSheet(List<NDownloadFile.Result> files, List<INameCache> nameCacheList, String describeApiName, String tenantId) throws IOException {
        if (CollectionUtils.empty(files)) {
            return null;
        }
        SXSSFWorkbook newExcelCreat = null;
        try {
            newExcelCreat = new SXSSFWorkbook();
            SXSSFSheet toSheet = newExcelCreat.createSheet();

            int currentRow = 0;
            SXSSFWorkbook finalNewExcelCreat = newExcelCreat;
            LoadingCache<XSSFCellStyle, CellStyle> map = Caffeine.newBuilder()
                    .initialCapacity(10000)
                    .maximumSize(10000)
                    .build(key -> {
                        CellStyle newStyle = finalNewExcelCreat.createCellStyle();
                        // 复制单元格样式
                        newStyle.cloneStyleFrom(key);
                        return newStyle;
                    });
            if (!CollectionUtils.empty(nameCacheList)) {
                SXSSFSheet sxssfSheet = newExcelCreat.getSheetAt(0);
                SXSSFRow row = sxssfSheet.createRow(0);
                CellStyle cellStyle = newExcelCreat.createCellStyle();
                if (cellStyle instanceof XSSFWorkbook) {
                    cellStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
                } else {
                    cellStyle.setFillForegroundColor(HSSFColor.RED.index);
                }
                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                StringBuilder sb = new StringBuilder();
                sb.append(I18NExt.text(I18NKey.AFFECTED_BY_NETWORK_ANOMALIES) + ",");
                for (INameCache nameCache : nameCacheList) {
                    sb.append("【").append(nameCache.getName()).append("】");
                }
                sb.append(I18NExt.text(I18NKey.EXPORT_FAILED_PLEASE_RETRY_LATER));
                String failedMessage = sb.toString();
                SXSSFCell cell = row.createCell(0);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(failedMessage);
                currentRow = 1;
            }
            for (NDownloadFile.Result item : files) {
                try (InputStream in = new ByteArrayInputStream(item.getData())) {
                    XSSFWorkbook fromExcel = new XSSFWorkbook(in);
                    XSSFSheet fromSheet = fromExcel.getSheetAt(0);
                    //dosomething
                    currentRow += mergeSheet(map, fromSheet, toSheet, currentRow) + 3;
                    copyPageBreak(toSheet, currentRow, describeApiName, tenantId);
                }
            }
            return newExcelCreat;
        } catch (IOException e) {
            log.error("mergeSheet error : ", e);
            throw e;
        }
    }

    public SXSSFWorkbook mergeSheet(List<NDownloadFile.Result> files, List<INameCache> nameCacheList) throws IOException {
        return mergeSheet(files, nameCacheList, null, null);
    }

    public SXSSFWorkbook mergeSheet(List<NDownloadFile.Result> files) throws IOException {
        return mergeSheet(files, null, null, null);
    }


    private Integer mergeSheet(LoadingCache<XSSFCellStyle, CellStyle> cellStyleCache, XSSFSheet fromSheet, SXSSFSheet toSheet, int currentRow) {
        if (currentRow <= 1) {
            initSheet(fromSheet, toSheet);
        }
        return copySheet(cellStyleCache, fromSheet, toSheet, currentRow);
    }


    public void copyPageBreak(SXSSFSheet toSheet, int currentRow, String describeApiName, String tenantId) {
        boolean supportExportExcelPage = AppFrameworkConfig.isSupportExportExcelPage(tenantId, describeApiName);
        if (supportExportExcelPage) {
            PrintSetup printSetupTarget = toSheet.getPrintSetup();
            printSetupTarget.setFitHeight((short) 0);
            toSheet.setFitToPage(true);
            toSheet.setRowBreak(currentRow - 1);
        }
    }


    /**
     * 合并单元格
     */
    private void mergeSheetAllRegion(XSSFSheet fromSheet, SXSSFSheet toSheet, int beginRow) {
        int num = fromSheet.getNumMergedRegions();
        CellRangeAddress cellR;
        for (int i = 0; i < num; i++) {
            cellR = fromSheet.getMergedRegion(i);
            cellR.setFirstRow(cellR.getFirstRow() + beginRow);
            cellR.setLastRow(cellR.getLastRow() + beginRow);
            toSheet.addMergedRegion(cellR);
        }
    }

    /**
     * 复制单元格
     */
    private void copyCell(LoadingCache<XSSFCellStyle, CellStyle> cellStyleCache, XSSFCell fromCell, SXSSFCell toCell) {
        // 样式
        toCell.setCellStyle(cellStyleCache.get(fromCell.getCellStyle()));
        if (fromCell.getCellComment() != null) {
            toCell.setCellComment(fromCell.getCellComment());
        }
        // 不同数据类型处理
        CellType fromCellType = fromCell.getCellTypeEnum();
        toCell.setCellType(fromCellType);
        if (fromCellType == CellType.NUMERIC) {
            if (DateUtil.isCellDateFormatted(fromCell)) {
                toCell.setCellValue(fromCell.getDateCellValue());
            } else {
                toCell.setCellValue(fromCell.getNumericCellValue());
            }
        } else if (fromCellType == CellType.STRING) {
            toCell.setCellValue(fromCell.getRichStringCellValue());
        } else if (fromCellType == CellType.BOOLEAN) {
            toCell.setCellValue(fromCell.getBooleanCellValue());
        } else if (fromCellType == CellType.ERROR) {
            toCell.setCellErrorValue(fromCell.getErrorCellValue());
        } else if (fromCellType == CellType.FORMULA) {
            toCell.setCellFormula(fromCell.getCellFormula());
        }
    }

    /**
     * 行复制功能
     */
    private void copyRow(LoadingCache<XSSFCellStyle, CellStyle> cellStyleCache, XSSFRow fromRow, SXSSFRow toRow) {
        toRow.setHeight(fromRow.getHeight());
        for (Iterator<Cell> cellIterator = fromRow.cellIterator(); cellIterator.hasNext(); ) {
            XSSFCell fromCell = (XSSFCell) cellIterator.next();
            SXSSFCell toCell = toRow.createCell(fromCell.getColumnIndex());
            copyCell(cellStyleCache, fromCell, toCell);
        }
    }

    /**
     * Sheet复制
     */
    private Integer copySheet(LoadingCache<XSSFCellStyle, CellStyle> cellStyleCache, XSSFSheet fromSheet, SXSSFSheet toSheet, Integer currentRow) {
        mergeSheetAllRegion(fromSheet, toSheet, currentRow);
        // 复制行
        int realExcelRowNum = fromSheet.getLastRowNum();
        for (int i = 0; i <= realExcelRowNum; i++) {
            final XSSFRow fromRow = fromSheet.getRow(i);
            if (Objects.isNull(fromRow)) {
                continue;
            }
            final SXSSFRow toRow = toSheet.createRow(currentRow + i);
            copyRow(cellStyleCache, fromRow, toRow);
        }
        return realExcelRowNum + 1;
    }


    /**
     * Sheet初始化
     */
    private void initSheet(XSSFSheet fromSheet, SXSSFSheet toSheet) {
        // 设置列宽
        final int maxCellNum = getMaxCellNum(fromSheet);
        for (int i = 0; i <= maxCellNum; i++) {
            toSheet.setColumnWidth(i, fromSheet.getColumnWidth(i));
        }
    }

    /**
     * 获取最大的列数
     */
    private int getMaxCellNum(XSSFSheet fromSheet) {
        return LongStream.range(0, fromSheet.getLastRowNum())
                .mapToObj(rowNum -> fromSheet.getRow((int) rowNum))
                .filter(Objects::nonNull)
                .mapToInt(XSSFRow::getLastCellNum)
                .max().orElse(-1);
    }

    private void writeToStream(OutputStream outputStream, Workbook workbook) throws IOException {
        try {
            workbook.write(outputStream);
            outputStream.flush();
        } catch (IOException e) {
            log.error("Error in export data excel to inputStream,", e);
            throw e;
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("Error in closing stream when uploading excel", e);
            }
        }
    }

    /**
     * 检查文本是否有公式注入风险
     */
    private static boolean isFormulaInjectionRisk(String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }

        // 1. 先检查是否匹配安全业务文本模式
        if (SAFE_BUSINESS_PATTERN.matcher(value).matches()) {
            return false;  // 安全业务文本不视为风险
        }

        // 2. 再检查是否匹配高风险模式
        return HIGH_RISK_PATTERN.matcher(value).find();
    }


    /**
     * 处理嵌入式图片单元格
     */
    private void handleEmbeddedImageCell(Workbook workbook, Sheet exportDataNewSheet, Cell cell, IObjectData objectData, IFieldDescribe fieldDescribe) {
        try {
            // 获取图片数据，格式为：fileName#path
            if (!FileExtUtil.isEmbeddedImageField(fieldDescribe)) {
                return;
            }
            Object imageValue = objectData.get(fieldDescribe.getApiName());
            if (Objects.isNull(imageValue) || StringUtils.isBlank(imageValue.toString())) {
                return;
            }

            List<ImageInfo> imageInfos = ImageInfo.convert(imageValue);
            if (CollectionUtils.empty(imageInfos)) {
                return;
            }
            // 解析指定索引的图片路径和扩展名
            ImageInfo imageInfo = imageInfos.get(0);
            if (Objects.isNull(imageInfo) || StringUtils.isBlank(imageInfo.getPath()) && !(FileAttachmentUtils.isN(imageInfo.getPath())
                    || FileAttachmentUtils.isTN(imageInfo.getPath()))) {
                return;
            }

            // 下载图片数据
            byte[] imageData = downloadImageData(imageInfo);
            if (Objects.nonNull(imageData) && imageData.length > 0) {
                // 将图片嵌入到单元格作为背景
                embedImageToCell(workbook, exportDataNewSheet, cell, imageData, imageInfo.getExt());
            }
        } catch (Exception e) {
            log.warn("Failed to handle embedded image: fieldName={}, error={}", fieldDescribe.getApiName(), e.getMessage());
            // 失败时不抛异常，继续处理其他数据
        }
    }

    /**
     * 下载图片数据
     */
    private byte[] downloadImageData(ImageInfo imageInfo) {
        String imagePath = imageInfo.getPath();
        try {
            // 这里需要调用文件下载服务
            // 由于ExcelUtil中没有直接的用户信息，我们需要从上下文获取
            RequestContext requestContext = RequestContextManager.getContext();
            String signature = imageInfo.getSignature();
            String tenantId;
            if (StringUtils.isNotBlank(signature)) {
                tenantId = StringUtils.substringBefore(signature, "$");
            } else {
                tenantId = requestContext.getTenantId();
            }
            String ea = gdsHandler.getEAByEI(tenantId);
            NDownloadFile.Result result = downloadFile(imagePath, ea, requestContext.getUser().getUserId());
            return result.getData();
        } catch (Exception e) {
            log.warn("Failed to download image: path={}, error={}", imagePath, e.getMessage());
            return null;
        }
    }

    /**
     * 将图片嵌入到单元格中
     */
    private void embedImageToCell(Workbook workbook, Sheet sheet, Cell cell, byte[] imageData, String fileExtension) {
        try {
            // 根据文件扩展名确定图片类型，如果扩展名无效则通过文件头检测
            int pictureType = getPictureTypeByExtension(fileExtension);
            FastImageInfoHelper.ImageInfo imageInfo = FastImageInfoHelper.getImageInfo(imageData);
            if (pictureType == -1) {
                // 如果扩展名无法确定类型，回退到文件头检测
                pictureType = getPictureTypeByExtension(imageInfo.getExtensionName());
            }

            int pictureIdx = workbook.addPicture(imageData, pictureType);

            // 创建绘图对象
            Drawing<?> drawing = sheet.createDrawingPatriarch();

            // 创建锚点
            CreationHelper helper = workbook.getCreationHelper();
            ClientAnchor anchor = helper.createClientAnchor();

            // 方案四核心改进1：使用正确的锚点类型，让图片跟随单元格移动和调整大小
            anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);

            // 方案四核心改进2：精确设置锚点边界，确保图片完全在单元格内
            int cellCol = cell.getColumnIndex();
            int cellRow = cell.getRowIndex();

            anchor.setCol1(cellCol);
            anchor.setRow1(cellRow);
            anchor.setCol2(cellCol + 1);
            anchor.setRow2(cellRow + 1);

            // 方案四核心改进3：设置精确的偏移量，让图片在单元格内居中显示
            // 获取单元格尺寸信息
            int cellWidth = sheet.getColumnWidth(cellCol); // Excel列宽单位
            float cellHeight = cell.getRow().getHeightInPoints(); // 点单位

            // 转换为EMU单位（Excel Measurement Units）
            // 1 inch = 914400 EMU, 1 point = 12700 EMU, 1 Excel列宽单位 = 1/256 字符宽度
            int cellHeightEMU = (int) (cellHeight * 12700); // 点转EMU
            int cellWidthEMU = cellWidth * 36; // Excel列宽单位转EMU的近似值

            // 计算边距，让图片在单元格内居中显示（留出10%的边距）
            int horizontalMargin = cellWidthEMU / 10;
            int verticalMargin = cellHeightEMU / 10;

            // 设置偏移量实现居中效果
            anchor.setDx1(horizontalMargin);  // 左边距
            anchor.setDy1(verticalMargin);    // 上边距
            anchor.setDx2(-horizontalMargin); // 右边距（负值表示从右边界向内）
            anchor.setDy2(-verticalMargin);   // 下边距（负值表示从下边界向内）

            // 创建图片
            drawing.createPicture(anchor, pictureIdx);

            // 方案四核心改进4：调整单元格大小以适应图片（在设置图片之前）
            adjustCellSizeForImage(sheet, cell, imageInfo);

            // 设置单元格为空值，避免文本覆盖图片
            cell.setCellValue("");

            log.debug("Successfully embedded image to cell at row={}, col={}, imageSize={} bytes",
                    cellRow, cellCol, imageData.length);

        } catch (Exception e) {
            log.warn("Failed to embed image to cell at row={}, col={}: {}",
                    cell.getRowIndex(), cell.getColumnIndex(), e.getMessage(), e);
            // 如果设置图片失败，设置文本提示
            cell.setCellValue(I18NExt.text(I18NKey.IMAGE_LOAD_FAILED));
        }
    }

    /**
     * 调整单元格大小以适应图片
     */
    private void adjustCellSizeForImage(Sheet sheet, Cell cell, FastImageInfoHelper.ImageInfo imageInfo) {
        try {
            Dimension imageDimension = new Dimension(imageInfo.getWidth(), imageInfo.getHeight());
            if (imageDimension != null) {
                // 方案四改进：更合理的尺寸限制，考虑图片居中显示的需求
                int maxWidth = 200;  // 增加最大宽度（像素）
                int maxHeight = 150; // 增加最大高度（像素）
                int minWidth = 80;   // 最小宽度，确保图片可见
                int minHeight = 60;  // 最小高度，确保图片可见

                // 计算缩放比例，保持图片宽高比
                double scaleX = Math.min(1.0, (double) maxWidth / imageDimension.width);
                double scaleY = Math.min(1.0, (double) maxHeight / imageDimension.height);
                double scale = Math.min(scaleX, scaleY);

                int targetWidth = Math.max(minWidth, (int) (imageDimension.width * scale));
                int targetHeight = Math.max(minHeight, (int) (imageDimension.height * scale));

                // 方案四改进：考虑居中显示，需要额外的边距空间
                // 由于图片会居中显示并留出10%边距，单元格需要比图片稍大
                int cellWidth = (int) (targetWidth * 1.2); // 增加20%空间用于边距
                int cellHeight = (int) (targetHeight * 1.2); // 增加20%空间用于边距

                // 设置行高（点为单位，1点约等于1.33像素）
                float rowHeight = Math.max(25, cellHeight * 0.75f); // 最小行高25点

                // 边界检查：避免行高过大影响整体布局
                rowHeight = Math.min(rowHeight, 200); // 最大行高200点
                cell.getRow().setHeightInPoints(rowHeight);

                // 设置列宽（Excel列宽单位，256为一个字符宽度）
                int columnWidth = Math.max(15 * 256, cellWidth * 256 / 7); // 最小列宽15字符

                // 边界检查：避免列宽过大影响整体布局
                columnWidth = Math.min(columnWidth, 50 * 256); // 最大列宽50字符
                sheet.setColumnWidth(cell.getColumnIndex(), columnWidth);

                log.debug("Adjusted cell size for image: originalSize={}x{}, targetSize={}x{}, cellSize={}x{}",
                        imageDimension.width, imageDimension.height,
                        targetWidth, targetHeight,
                        columnWidth / 256, (int) rowHeight);

            } else {
                // 如果无法获取图片尺寸，使用优化后的默认大小
                cell.getRow().setHeightInPoints(80);  // 增加默认行高
                sheet.setColumnWidth(cell.getColumnIndex(), 25 * 256); // 增加默认列宽

                log.debug("Used default cell size for image (unable to determine image dimensions)");
            }
        } catch (Exception e) {
            log.debug("Failed to adjust cell size for image: {}", e.getMessage());
            // 使用安全的默认尺寸
            try {
                cell.getRow().setHeightInPoints(80);
                sheet.setColumnWidth(cell.getColumnIndex(), 25 * 256);
            } catch (Exception ex) {
                log.warn("Failed to set default cell size: {}", ex.getMessage());
            }
        }
    }

    /**
     * 根据文件扩展名确定图片类型
     */
    private int getPictureTypeByExtension(String extension) {
        if (StringUtils.isBlank(extension)) {
            return -1; // 无法确定
        }

        String ext = extension.toLowerCase();
        switch (ext) {
            case "png":
                return Workbook.PICTURE_TYPE_PNG;
            case "jpg":
            case "jpeg":
                return Workbook.PICTURE_TYPE_JPEG;
            case "gif":
                return Workbook.PICTURE_TYPE_PNG; // Excel不直接支持GIF，转为PNG处理
            case "bmp":
                return Workbook.PICTURE_TYPE_PNG; // Excel不直接支持BMP，转为PNG处理
            case "webp":
                return Workbook.PICTURE_TYPE_PNG; // Excel不直接支持WebP，转为PNG处理
            default:
                return -1; // 未知格式
        }
    }

    /**
     * 获取临时文件基础路径
     *
     * @return 基础路径，如果获取失败返回null
     */
    private String getTempFileBasePath() {
        URL url = this.getClass().getResource("/");
        if (url == null) {
            log.error("Can not find SourceTemp folder");
            return null;
        }

        String basePath;
        try {
            basePath = url.toURI().getPath();
        } catch (URISyntaxException e) {
            log.error("Error in get temp file path", e);
            return null;
        }
        return basePath;
    }

    /**
     * 创建自定义临时文件
     *
     * @param prefix 文件名前缀
     * @param suffix 文件名后缀
     * @return 临时文件对象
     * @throws IOException 如果创建文件失败
     */
    private File createCustomTempFile(String prefix, String suffix) throws IOException {
        String basePath = getTempFileBasePath();
        if (basePath == null) {
            throw new IOException("Failed to get temp file base path");
        }

        // 构建 sourceTemp 目录路径
        String sourceTempDir = basePath + "sourceTemp/";
        File tempDir = new File(sourceTempDir);

        // 确保目录存在
        if (!tempDir.exists()) {
            boolean created = tempDir.mkdirs();
            if (!created) {
                log.error("Failed to create sourceTemp directory: {}", sourceTempDir);
                throw new IOException("Failed to create sourceTemp directory: " + sourceTempDir);
            }
            log.info("Created sourceTemp directory: {}", sourceTempDir);
        }

        // 生成唯一文件名
        String fileName = prefix + System.currentTimeMillis() + "_" + Thread.currentThread().getId() + suffix;
        String filePath = sourceTempDir + fileName;

        File tempFile = new File(filePath);

        // 创建文件
        boolean created = tempFile.createNewFile();
        if (!created) {
            log.error("Failed to create temp file: {}", filePath);
            throw new IOException("Failed to create temp file: " + filePath);
        }

        log.debug("Created custom temp file: {}", filePath);
        return tempFile;
    }
}
