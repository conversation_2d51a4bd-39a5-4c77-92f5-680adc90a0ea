package com.facishare.paas.appframework.metadata.util;

import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NDownloadFile;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NGetFileMetaData;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NTempFileUpload;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.NUploadFileDirect;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.core.util.ObjectUtils;
import com.facishare.paas.appframework.metadata.exception.ExportException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Created by linqiuying on 17/4/18.
 * 上传／下载文件
 */
@Service
@Slf4j
public class WebFileUtil {
    public static final int _100_MB_LENGTH = 100 * 1024 * 1024;

    @Resource
    private NFileStorageService nFileStorageService;

    @Resource
    private StoneProxyApi stoneProxyApi;

    /**
     * 上传文件到文件服务器，并返回path
     *
     * @param ea
     * @param user
     * @return
     * @throws IOException
     */
    public String uploadFile(String ea, String user, ByteArrayOutputStream outputStream) {
        byte[] data = outputStream.toByteArray();
        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setEa(ea);
        arg.setSourceUser(user);
        arg.setData(data);

        NTempFileUpload.Result result = nFileStorageService.nTempFileUpload(arg, ea);
        if (null == result || Strings.isNullOrEmpty(result.getTempFileName())) {
            log.error("Can not upload excel file by calling nTempUploadByChunkStart");
            throw new MetaDataException(SystemErrorCode.METADATA_UPLOAD_ERROR);
        }

        return result.getTempFileName();
    }

    public NUploadFileDirect.Result updateFileDirect(String ea, User user, ByteArrayOutputStream outputStream, int expiredDay, String ext) {
        byte[] data = outputStream.toByteArray();
        NUploadFileDirect.Arg arg = new NUploadFileDirect.Arg();
        arg.setExpireDay(expiredDay);
        arg.setEa(ea);
        arg.setSourceUser("E." + user.getUserId());
        arg.setData(data);
        arg.setFileExt(ext);
        String byteCount = ObjectUtils.humanReadableByteCount(data.length, false);
        try {
            return nFileStorageService.nUploadFileDirect(arg, ea);
        } catch (Exception e) {
            log.warn("nUploadFileDirect fail!  ei:{}, size:{}", user.getTenantId(), byteCount, e);
            if (data.length > _100_MB_LENGTH) {
                throw new ExportException(I18NExt.text(I18NKey.EXPORT_FILE_TOO_LARGE), e);
            }
            throw new ExportException(I18NExt.text(I18NKey.UPLOAD_EXCEL_FAIL), e);
        }
    }

    public NDownloadFile.Result downloadFile(String path, String ea, String userId) {
        NDownloadFile.Arg arg = new NDownloadFile.Arg();
        arg.setDownloadUser(userId);
        arg.setEa(ea);
        arg.setnPath(path);
        return nFileStorageService.nDownloadFile(arg, ea);
    }

    public NGetFileMetaData.Result getFileMetaData(String ea, String path) {
        NGetFileMetaData.Arg arg = new NGetFileMetaData.Arg(ea, path);
        return nFileStorageService.nGetFileMetaData(arg, ea);
    }

    /**
     * 使用临时文件和流式上传，避免内存双重占用
     *
     * @param ea 企业账号
     * @param user 用户信息
     * @param tempFile 临时文件
     * @param expiredDay 过期天数
     * @param ext 文件扩展名
     * @return 上传结果，包含文件路径等信息
     */
    public NUploadFileDirect.Result updateFileDirectFromFile(String ea, User user, File tempFile,
                                                           int expiredDay, String ext) {
        String byteCount = ObjectUtils.humanReadableByteCount(tempFile.length(), false);
        try (FileInputStream fis = new FileInputStream(tempFile)) {
            // 构建 StoneFileUploadRequest
            StoneFileUploadRequest request = new StoneFileUploadRequest();
            request.setEa(ea);
            request.setEmployeeId(Integer.parseInt(user.getUserId()));
            request.setFileSize((int) tempFile.length());
            request.setExtensionName(ext);
            request.setNeedThumbnail(false);
            request.setBusiness("CRM");
            request.setExpireDay(expiredDay);

            // 使用 Stone SDK 的流式上传接口
            StoneFileUploadResponse response = stoneProxyApi.uploadByStream("n", request, fis);

            // 转换为 NUploadFileDirect.Result 格式
            NUploadFileDirect.Result result = new NUploadFileDirect.Result();
            result.setFinalNPath(response.getPath());
            result.setFileSize(response.getSize());

            return result;
        } catch (Exception e) {
            log.warn("updateFileDirectFromFile fail! ei:{}, size:{}", user.getTenantId(), byteCount, e);
            if (tempFile.length() > _100_MB_LENGTH) {
                throw new ExportException(I18NExt.text(I18NKey.EXPORT_FILE_TOO_LARGE), e);
            }
            throw new ExportException(I18NExt.text(I18NKey.UPLOAD_EXCEL_FAIL), e);
        }
    }

    /**
     * 使用流式上传，避免内存双重占用
     *
     * @param ea 企业账号
     * @param user 用户信息
     * @param inputStream 文件输入流
     * @param fileSize 文件大小（字节）
     * @param expiredDay 过期天数
     * @param ext 文件扩展名
     * @return 上传结果，包含文件路径等信息
     */
    public NUploadFileDirect.Result updateFileDirectByStream(String ea, User user, InputStream inputStream,
                                                           long fileSize, int expiredDay, String ext) {
        String byteCount = ObjectUtils.humanReadableByteCount(fileSize, false);
        try {
            // 构建 StoneFileUploadRequest
            StoneFileUploadRequest request = new StoneFileUploadRequest();
            request.setEa(ea);
            request.setEmployeeId(Integer.parseInt(user.getUserId()));
            request.setFileSize((int) fileSize);
            request.setOriginName("export." + ext);
            request.setExtensionName(ext);
            request.setNeedThumbnail(false);
            request.setBusiness("CRM");

            // 使用 Stone SDK 的流式上传接口
            StoneFileUploadResponse response = stoneProxyApi.uploadByStream("n", request, inputStream);

            // 转换为 NUploadFileDirect.Result 格式
            NUploadFileDirect.Result result = new NUploadFileDirect.Result();
            result.setFinalNPath(response.getPath());
            result.setFileSize(response.getSize());

            return result;
        } catch (Exception e) {
            log.warn("updateFileDirectByStream fail! ei:{}, size:{}", user.getTenantId(), byteCount, e);
            if (fileSize > _100_MB_LENGTH) {
                throw new ExportException(I18NExt.text(I18NKey.EXPORT_FILE_TOO_LARGE), e);
            }
            throw new ExportException(I18NExt.text(I18NKey.UPLOAD_EXCEL_FAIL), e);
        }
    }
}
