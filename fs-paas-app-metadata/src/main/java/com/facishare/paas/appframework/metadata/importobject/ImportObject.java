package com.facishare.paas.appframework.metadata.importobject;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.UniqueRuleExt;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.paas.metadata.api.IObjectData.NAME;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/03/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportObject {
    private String objectCode;
    private String objectApiName;
    private String objectName;
    private String tip;
    private int duplicateJudgmentType;
    private boolean isOpenWorkFlow;
    private boolean isApprovalFlow;
    private boolean isTextureImport;
    private boolean isNoBatch;
    private boolean isEnableUnionDuplicateChecking;
    /**
     * 是否验证企业名称
     */
    private boolean isVerifyEnterprise;
    /**
     * 是否回填工商信息
     */
    private boolean isBackFillIndustrialAndCommercialInfo;
    private List<RuleField> primaryAttributeFieldList;
    private List<String> unionImportApiNameList;
    // 支持导入销售记录
    private boolean isNotSupportSaleEvent;

    private ImportType importType;
    private Map<String, List<MatchingType>> matchingTypes;
    private String describeType;

    /**
     * 校验外部负责人
     */
    private boolean checkOutOwner;
    /**
     * 移除外部相关团队
     */
    private boolean removeOutTeamMember;
    /**
     * 是否跟新负责人
     */
    private boolean updateOwner;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RuleField {
        @SerializedName("FieldName")
        @JSONField(name = "FieldName")
        @JsonProperty(value = "FieldName")
        private String fieldName;
        @SerializedName("FieldCaption")
        @JSONField(name = "FieldCaption")
        @JsonProperty(value = "FieldCaption")
        private String fieldCaption;

        public static Optional<RuleField> of(IObjectDescribe describe) {
            RuleFieldBuilder builder = builder();
            Optional<IFieldDescribe> fieldOptional = ObjectDescribeExt.of(describe).getFieldDescribeSilently(NAME);
            fieldOptional.filter(IFieldDescribe::isUnique)
                    .ifPresent(name -> {
                        builder.fieldCaption(name.getDescribeApiName());
                        builder.fieldName(name.getApiName());
                    });

            return fieldOptional.isPresent() ? Optional.of(builder.build()) : Optional.empty();
        }

        public static List<RuleField> of(IUniqueRule uniqueRule, IObjectDescribe describe) {
            return UniqueRuleExt.of(uniqueRule).getRuleFieldName()
                    .stream()
                    .map(x -> builder().fieldName(x).fieldCaption(ObjectDescribeExt.of(describe).getFieldLabelByName(x)).build())
                    .collect(Collectors.toList());
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImportUniquenessRule {
        @SerializedName("IsEnabled")
        @JSONField(name = "IsEnabled")
        @JsonProperty(value = "IsEnabled")
        private Boolean isEnable;
        @SerializedName("RuleSettingRight")
        @JSONField(name = "RuleSettingRight")
        @JsonProperty(value = "RuleSettingRight")
        private Boolean ruleSettingRight;
        @SerializedName("RuleFieldList")
        @JSONField(name = "RuleFieldList")
        @JsonProperty(value = "RuleFieldList")
        List<RuleField> ruleFieldList;

        public static ImportUniquenessRule of(IUniqueRule uniqueRule, IObjectDescribe describe) {
            return ImportUniquenessRule.builder()
                    .isEnable(uniqueRule.isUseWhenImportExcel())
                    .ruleFieldList(RuleField.of(uniqueRule, describe))
                    .build();
        }

        public static ImportUniquenessRule empty() {
            return ImportUniquenessRule.builder()
                    .isEnable(false)
                    .build();
        }

    }
}
