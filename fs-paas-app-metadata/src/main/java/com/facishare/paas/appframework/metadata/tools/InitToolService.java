package com.facishare.paas.appframework.metadata.tools;


import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.tools.*;

import java.util.List;

public interface InitToolService {

    String SUCCESS = "success";
    String FAIL = "fail";
    String TOTAL = "total";

    public static String BI_EXECUTE_STATUS_START = "start";

    static String getKey(String recordType, String dataId) {
        return String.format("initTools_%s_%s:", recordType, dataId);
    }

    static String getKey(String recordType, String dataId, String keyword) {
        return String.format("initTools_%s_%s_%s:", recordType, dataId, keyword);
    }


    static String getBiCardKey(String env, String keyword) {
        return String.format("biCardI18n_%s_%s:", env, keyword);
    }

    static String getBiCardStatusKey() {
        return "bi_component_refresh_status";
    }


    List<Integer> getEnterpriseList(Integer currentEnv, List<Integer> runStatusList);

    BrushProgress.Result progressControl(BrushProgress.Arg arg);

    void executeBrushDescribe(BrushDescribe.Arg arg, String enterpriseId);

    void executeBrushLayout(BrushLayouts.Arg arg, String enterpriseId);

    void transferExecute(TransferBrush.Arg arg);

    String transferExecute(BrushHandler.Arg arg);

    List<HandlerDescribe> findHandlerDescribes(User user, String objectApiName, String interfaceCode);

    ValidateHandler.Result validateHandler(User user, List<HandlerDescribe> handlerDescribes, String objectApiName, String interfaceCode);

    String upsertHandlerDefinitionAndRuntimeConfig(User user, List<HandlerDescribe> handlerDescribes, String objectApiName, String interfaceCode);

    void transferBrushBiComponent(TransferBrushBiComponent.Arg arg);

    BiBrushProgress.Result biProgressControl(BiBrushProgress.Arg arg);
}
