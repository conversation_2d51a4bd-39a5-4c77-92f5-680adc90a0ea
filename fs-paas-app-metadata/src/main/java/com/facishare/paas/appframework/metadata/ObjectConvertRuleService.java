package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.MtConvertRule;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;

import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
public interface ObjectConvertRuleService {
    void create(User user, List<IObjectMappingRuleInfo> ruleList);

    void update(User user, List<IObjectMappingRuleInfo> ruleList);

    void updateStatus(User user, String ruleApiName, int status);

    void disableRuleByTargetDescribe(User user, String describeApiName);

    void disableRuleBySourceDescribe(User user, String describeApiName);

    void delete(User user, String ruleApiName);

    List<IObjectMappingRuleInfo> findRuleList(User user, String ruleName, String searchQueryInfo);

    List<IObjectMappingRuleInfo> findRuleListByOriginalApiName(User user, String originalApiName);

    List<IObjectMappingRuleInfo> findConvertRuleByApiName(User user, String ruleApiName);

    List<IObjectMappingRuleInfo> findConvertRuleByDescribeApiName(User user, String sourceDescribeApiName, String targetDescribeApiName, String sourceId);

    List<IObjectMappingRuleInfo> findConvertRuleByDescribeApiName(User user, String sourceDescribeApiName, String targetDescribeApiName, List<String> sourceIds);

    List<IObjectMappingRuleInfo> findConvertRuleByDescribeApiName(User user, String sourceDescribeApiName, String targetDescribeApiName, List<String> sourceIds, String recordType, boolean isDoublePull);

    boolean count(User user, String sourceDescribeApiName, String targetDescribeApiName);

    boolean supportPullOrder(User user, String describeApiName);

    List<IObjectMappingRuleInfo> findObjectMappingRuleByApiName(User user, String ruleApiName);

    List<IObjectMappingRuleInfo> findObjectMappingRuleBySourceTarget(User user, String sourceDescribeApiName, String targetDescribeApiName);

    List<MtConvertRule> findConvertRuleInInternalObjByApiName(User user, String ruleApiName);

    List<MtConvertRule> findConvertRuleInInternalObjByApiName(User user, String ruleApiName, Boolean isActive);

    List<MtConvertRule> findConvertRuleInInternalObjByApiName(User user, String ruleApiName, Boolean isActive, boolean isOnTime);

    List<MtConvertRule> find(User user, Supplier<SearchQuery> supplier);

    List<MtConvertRule> find(User user, Supplier<SearchQuery> supplier, boolean isOnTime);

    boolean supportConvertRule(String tenantId);

}
