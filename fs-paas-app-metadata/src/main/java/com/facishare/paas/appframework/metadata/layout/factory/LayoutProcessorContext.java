package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.PageType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.ui.layout.IComponent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LayoutProcessorContext {
    PageType pageType;
    LayoutExt webLayout;
    ObjectDescribeExt describeExt;
    IObjectData objectData;
    List<IComponent> componentConfig;
}
