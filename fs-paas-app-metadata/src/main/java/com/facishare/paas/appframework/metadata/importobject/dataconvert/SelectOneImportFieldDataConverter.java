package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/07/26
 */
@Component
public class SelectOneImportFieldDataConverter extends BaseImportFieldDataConverter {
    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.SELECT_ONE);
    }

    /**
     * 此方法会将单选其他的值 以fieldApiName__o的形式放在objectData
     *
     * @param objectData    数据集合
     * @param fieldDescribe 字段描述
     * @param user          用户信息
     * @return
     */
    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user) {
        String valueStr = getStringValue(objectData, fieldDescribe.getApiName());
        if (Strings.isNullOrEmpty(valueStr)) {
            return ConvertResult.buildSuccess(null);
        }

        SelectOne selectOne = (SelectOne) fieldDescribe;
        List<ISelectOption> options = selectOne.getSelectOptions();
        if (CollectionUtils.empty(options)) {
            return ConvertResult.buildSuccess(null);
        }
        for (ISelectOption option : options) {
            if (option.getLabel().equals(valueStr)) {
                //隐藏选项不能导入 970去掉检验
                /*if (option.isNotUsable()) {
                    return ConvertResult.buildError(I18N.text(I18NKey.OPTION_NOT_USED_CAN_NOT_IMPORT, option.getLabel()));
                }*/
                if (!option.getIsRequired()) {
                    return ConvertResult.buildSuccess(option.getValue());
                }
            }
        }

        //如果是支持其他选项，且没有匹配的选项，则把值放到其他中
        boolean hasOther = false;
        String otherLabel = I18N.text(I18NKey.OTHER);
        for (ISelectOption option : options) {
            if (Objects.equals(option.getValue(), SelectOne.OPTION_OTHER_VALUE) /*&& Objects.equals(option.isNotUsable(), false)*/) {
                hasOther = true;
                otherLabel = option.getLabel();
                break;
            }
        }
        if (hasOther) {
            //如果输入的是 "其他:xxx", 取xxx
            Matcher matcher = Pattern.compile(String.format("(%s[:：])+?", otherLabel)).matcher(valueStr);
            int end = 0;
            while (matcher.find()) {
                end = matcher.end();
            }
            //复选框必填但是没有填写(非 其他：xxx 形式)
            if (selectOne.getOptionOtherIsRequired() && end == 0 && otherLabel.equals(valueStr)) {
                return ConvertResult.buildError(I18N.text(I18NKey.OTHER_OPTION_CAN_NOT_IS_NULL, fieldDescribe.getLabel(), otherLabel));
            }
            String otherName = valueStr.substring(end);
            // 处理单选其他
            objectData.set(FieldDescribeExt.of(fieldDescribe).getFieldExtendName(), otherName);
            return ConvertResult.buildSuccess(SelectOne.OPTION_OTHER_VALUE);
        }

        StringBuilder stringBuilder = new StringBuilder();
        for (ISelectOption option : options) {
            /*if (Objects.equals(option.isNotUsable(), true)) {
                continue;
            }*/
            stringBuilder.append("[").append(option.getLabel()).append("]");
        }

        return ConvertResult.buildError(I18N.text(I18NKey.MUST_IS_ONE_OF, fieldDescribe.getLabel(), stringBuilder.toString()));
    }
}
