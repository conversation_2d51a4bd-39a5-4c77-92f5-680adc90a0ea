package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.facishare.crm.openapi.Utils;
import com.facishare.fsc.api.model.CreateFileShareIds;
import com.facishare.fsc.api.service.SharedFileService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.model.OuterDepartmentInfo;
import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.OuterOrganizationService;
import com.facishare.paas.appframework.common.service.PhoneNumberService;
import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds.DeptStatusEnum;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds.MainDeptInfo;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.coordination.CrmService;
import com.facishare.paas.appframework.core.exception.DataPermissionError;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowService;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.log.dto.TeamMemberInfo;
import com.facishare.paas.appframework.metadata.data.validator.ObjectDataValidator;
import com.facishare.paas.appframework.metadata.dimension.DimensionLogicService;
import com.facishare.paas.appframework.metadata.dto.CurrencyInfo;
import com.facishare.paas.appframework.metadata.dto.HeaderMergeInfo;
import com.facishare.paas.appframework.metadata.dto.ImageInfo;
import com.facishare.paas.appframework.metadata.dto.ProductAllCategoriesModel;
import com.facishare.paas.appframework.metadata.er.ErOrgManagementControlService;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.appframework.metadata.publicobject.PublicObjectEnterpriseRelationService;
import com.facishare.paas.appframework.metadata.publicobject.dto.ConnectedEnterpriseDTO;
import com.facishare.paas.appframework.metadata.treeview.TreeViewService;
import com.facishare.paas.appframework.metadata.util.ExcelUtil;
import com.facishare.paas.appframework.metadata.util.WebFileUtil;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.privilege.EnterpriseRelationServiceProxy;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.appframework.privilege.dto.GetUserRoleInfo;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderManager;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.INameCache;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.dispatcher.ObjectDataProxy;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.NameCache;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.WhatFieldDescribe;
import com.facishare.paas.metadata.impl.describe.WhatListFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.paas.metadata.util.IdUtil;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.enterpriserelation2.data.ErDepartmentSimpleData;
import com.fxiaoke.enterpriserelation2.data.PublicEmployeeObjOwnDepOrOrgData;
import com.fxiaoke.enterpriserelation2.result.OuterAccountVo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 对元数据服务的封装
 * <p>
 * Created by yusb on 2017/12/14.
 */
@Slf4j
@Service("metaDataMiscService")
public class MetaDataMiscServiceImpl implements MetaDataMiscService {

    @Autowired
    private ObjectDataProxy dataProxy;
    @Autowired
    private DataPrivilegeService dataPrivilegeService;
    @Autowired
    private UserRoleInfoService userRoleInfoService;
    @Autowired
    private ApprovalFlowService approvalFlowService;
    @Autowired
    private CrmService crmService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private GDSHandler gdsHandler;
    @Autowired
    private ExcelUtil excelUtil;
    @Autowired
    private OrgService orgService;
    @Autowired
    private CountryAreaService countryAreaService;
    @Autowired
    private PhoneNumberService phoneNumberService;
    @Autowired
    private SharedFileService sharedFileService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private DataPrivilegeProviderManager providerManager;
    @Autowired
    private ProductCategoryService productCategoryService;
    @Autowired
    private WebFileUtil webFileUtil;
    @Autowired
    private MetaDataGlobalService metaDataGlobalService;
    @Autowired
    private QuoteValueService quoteValueService;
    @Autowired
    private DimensionLogicService dimensionLogicService;
    @Autowired
    private ObjectDataValidator objectDataValidator;
    @Autowired
    private EnterpriseRelationServiceProxy enterpriseRelationService;
    @Autowired
    private TreeViewService treeViewService;
    @Autowired
    private MultiCurrencyLogicService multiCurrencyLogicService;
    @Autowired
    private OuterOrganizationService outerOrganizationService;
    @Autowired
    private PublicObjectEnterpriseRelationService publicObjectEnterpriseRelationService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private MaskFieldLogicService maskFieldLogicService;
    @Autowired
    private OptionalFeaturesService optionalFeaturesService;
    @Autowired
    private ErOrgManagementControlService erOrgManagementControlService;

    @Override
    public void fillObjectDataWithRefObject(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user) {
        fillObjectDataWithRefObject(sourceObject, objectDataList, user, null);
    }

    @Override
    public void fillObjectDataWithRefObject(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user,
                                            Map<String, List<IObjectData>> refObjectDataMap) {
        fillObjectDataWithRefObject(sourceObject, objectDataList, user, refObjectDataMap, true);
    }

    @Override
    public void fillObjectDataWithRefObject(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user,
                                            Map<String, List<IObjectData>> refObjectDataMap, boolean handleSFAObject) {
        List<IFieldDescribe> refFieldList = ObjectDescribeExt.of(sourceObject).getAllRefFieldDescribes();
        fillObjectDataWithRefObject(sourceObject, objectDataList, user, refObjectDataMap, handleSFAObject, refFieldList);
    }

    @Override
    public void fillObjectDataObjectManyWithRefObject(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user) {
        List<IFieldDescribe> refManyFieldList = ObjectDescribeExt.of(sourceObject).getAllRefFieldDescribes();
        if (CollectionUtils.empty(refManyFieldList)) {
            return;
        }
        fillObjectDataWithRefObject(sourceObject, objectDataList, user, null, true, refManyFieldList);
    }

    @Override
    public void fillObjectDataWithRefObject(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user,
                                            Map<String, List<IObjectData>> refObjectDataMap, boolean handleSFAObject, List<IFieldDescribe> refFieldList) {
        if (CollectionUtils.empty(objectDataList) || CollectionUtils.empty(refFieldList)) {
            return;
        }

        try {
            //2 得到要查询的idListMap<targetApiName,idList>。
            Map<String, List<String>> idListMap = ObjectDataExt.getRefObjectDataIds(refFieldList, objectDataList);

            //3 RPC调用,得到nameCache,然后生成id和Name的大map:idNameMap。因为id是全局唯一的。
            Map<String, String> idNameMap = Maps.newConcurrentMap();
            fillNameFromNameCache(sourceObject.getApiName(), user, refObjectDataMap, idListMap, idNameMap);
            // 3.1 判断数据权限，没有权限的显示星号，无需处理
            //handleNameWithDataPermission(sourceObject, user, refFieldList, objectDataList, idNameMap);
            // 对象配置可显示字段需要set成display_name的值, 将idNameMap中value替换为display_name的值
            fillRefObjDisplayName(user, idListMap, idNameMap, refObjectDataMap);

            // 处理查找关联树形对象展示全路径，将idNameMap中value替换为全路径的值。即：全部渠道/特殊渠道/专卖店渠道
            fillTreeViewObjectFullPath(user, idListMap, idNameMap);

            //4 循环ObjectReferenceFiledDescribeList,再循环ObjectDataList,将每条中的该字段替换成__r,根据Map,set成本来的id中的对应的Name
            fillRefObjNameToData(objectDataList, refFieldList, idNameMap);
        } catch (Exception e) {
            log.error("error in fillObjectDataWithRefObject, ei:{}, sourceObject {}", user.getTenantId(), sourceObject.getApiName(), e);
        }
    }

    private void fillTreeViewObjectFullPath(User user, Map<String, List<String>> idListMap, Map<String, String> idNameMap) {
        Map<String, List<String>> treeViewObjectIds = Maps.newHashMap();
        // 已兼容自定义对象支持树形视图
        List<String> preFilterTreeViewObjs = idListMap.keySet().stream()
                .filter(objectApiName -> AppFrameworkConfig.supportTreeView(user.getTenantId(), objectApiName))
                .filter(objectApiName -> !AppFrameworkConfig.lookupTreeViewObjectNotDisplayWholePath(user.getTenantId(), objectApiName))
                .collect(Collectors.toList());
        Map<String, IObjectDescribe> objectDescribeMap = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), preFilterTreeViewObjs);
        List<String> supportTreeViewObjects = preFilterTreeViewObjs.stream()
                .filter(x -> Objects.nonNull(objectDescribeMap.get(x)) && objectDescribeMap.get(x).isSupportTreeView())
                .collect(Collectors.toList());
        idListMap.keySet().forEach(objectApiName -> {
            if (supportTreeViewObjects.contains(objectApiName)) {
                treeViewObjectIds.put(objectApiName, idListMap.get(objectApiName));
            }
        });
        if (CollectionUtils.empty(treeViewObjectIds)) {
            return;
        }
        //  todo 跟元数据确认是否能通过一个sql查询出指定数据的全路径，预计845/850版本优化
        treeViewObjectIds.keySet().forEach(objectApiName -> {
            Map<String, List<String>> treeViewObjectIdRelationMap = Maps.newHashMap();
            ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribeMap.get(objectApiName));
            List<IObjectData> dataList = treeViewService.findTreeViewObjDataListByIds(user, treeViewObjectIds.get(objectApiName), objectApiName, objectDescribeExt.getTreeViewObjectFieldsToReturn());
            Set<String> treePathIds = Sets.newHashSet();
            Optional<IFieldDescribe> treeViewField = objectDescribeExt.getActiveLookupFieldDescribes().stream().filter(field -> FieldDescribeExt.of(field).isTreeViewSelfAssociatedField()).findFirst();
            if (!treeViewField.isPresent()) {
                return;
            }
            String treeViewFieldApiName = ((ObjectReferenceFieldDescribe) treeViewField.get()).getTreeViewField();
            for (IObjectData data : dataList) {
                String treeViewPath = data.get(treeViewFieldApiName, String.class);
                if (StringUtils.isNotEmpty(treeViewPath)) {
                    List<String> parentIds = Lists.newArrayList(treeViewPath.split("\\."));
                    treePathIds.addAll(parentIds);
                    treeViewObjectIdRelationMap.put(data.getId(), parentIds);
                }
            }
            List<IObjectData> parentDataList = treeViewService.findTreeViewObjDataListByIds(user, Lists.newArrayList(treePathIds), objectApiName, objectDescribeExt.getTreeViewObjectFieldsToReturn());
            Map<String, IObjectData> parentDataMap = parentDataList.stream().collect(Collectors.toMap(IObjectData::getId, x -> x, (x, y) -> x));
            treeViewObjectIdRelationMap.keySet().forEach(id -> {
                List<String> parentIdList = treeViewObjectIdRelationMap.get(id);
                if (CollectionUtils.empty(parentIdList)) {
                    return;
                }
                String labelKey = ObjectDescribeExt.of(objectDescribeMap.get(objectApiName)).isSupportDisplayName() ? IObjectData.DISPLAY_NAME : IObjectData.NAME;
                StringBuilder fullPathBuilder = new StringBuilder();
                parentIdList.forEach(parentId -> {
                    IObjectData objectData = parentDataMap.get(parentId);
                    String label = objectData.get(labelKey, String.class);
                    fullPathBuilder.append(Objects.isNull(label) ? "" : label).append("/");
                });
                String cacheKey = createNameCacheKey(objectApiName, id);
                fullPathBuilder.append(idNameMap.get(cacheKey));
                String fullPath = fullPathBuilder.toString();
                idNameMap.put(cacheKey, fullPath);
            });
        });
    }

    private void fillRefObjDisplayName(User user, Map<String, List<String>> idListMap, Map<String, String> idNameMap,
                                       Map<String, List<IObjectData>> refObjectDataMap) {
        if (AppFrameworkConfig.isSupportDisplayNameFieldEnterprise(user.getTenantId())) {
            Set<String> apiNames = idListMap.keySet().stream()
                    // 预置对象未支持显示字段
                    .filter(AppFrameworkConfig::isSupportDisplayNameField)
                    .collect(Collectors.toSet());
            List<IObjectDescribe> describeList = describeLogicService.findDescribeListWithoutFields(user.getTenantId(), apiNames);
            List<IObjectDescribe> objectDescribeList = describeList.stream()
                    .filter(objectDescribe -> ObjectDescribeExt.of(objectDescribe).isSupportDisplayName())
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(objectDescribeList)) {
                objectDescribeList.forEach(o -> {
                    String apiName = ObjectDescribeExt.of(o).getApiName();
                    List<String> ids = idListMap.get(apiName);
                    List<String> queryIdList = ids;
                    if (Objects.nonNull(refObjectDataMap)) {
                        List<IObjectData> refObjectDataList = refObjectDataMap.getOrDefault(apiName, Lists.newArrayList());
                        Map<String, String> idDisplayNameMap = refObjectDataList.stream()
                                .filter(x -> Objects.nonNull(x.getId()) && ids.contains(x.getId()))
                                .collect(Collectors.toMap(it -> createNameCacheKey(apiName, it.getId()),
                                        this::getDisplayNameLanguage, (x, y) -> x));
                        idNameMap.putAll(idDisplayNameMap);
                        // refObjectDataMap中不包含物理删除的数据，需要去数据库中查询
                        queryIdList = ids.stream()
                                .filter(id -> !idDisplayNameMap.containsKey(createNameCacheKey(apiName, id)))
                                .collect(Collectors.toList());
                    }
                    if (CollectionUtils.notEmpty(queryIdList)) {
                        List<IObjectData> dataList = metaDataFindService.findObjectDataByIdsIgnoreAll(user.getTenantId(), queryIdList, apiName);
                        dataList.forEach(obj -> idNameMap.put(createNameCacheKey(apiName, obj.getId()), getDisplayNameLanguage(obj)));
                        //将查到的关联对象数据合并到dependentDataMap中
                        if (Objects.nonNull(refObjectDataMap)) {
                            Set<String> refIds = refObjectDataMap.getOrDefault(apiName, Collections.emptyList()).stream()
                                    .map(IObjectData::getId)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toSet());
                            refObjectDataMap.computeIfAbsent(apiName, x -> Lists.newArrayList()).addAll(dataList.stream()
                                    .filter(x -> !refIds.contains(x.getId()))
                                    .collect(Collectors.toList()));
                        }
                    }
                });
            }
        }
    }

    private String getDisplayNameLanguage(IObjectData objectData) {
        String displayName = ObjectDataExt.of(objectData).getFieldLangValue(FieldDescribeExt.DISPLAY_NAME);
        return StringUtils.isBlank(displayName) ? "" : displayName;
    }

    private void fillRefObjNameToData(List<IObjectData> objectDataList, List<IFieldDescribe> refFieldList, Map<String, String> idNameMap) {
        try {
            for (IObjectData objectData : objectDataList) {
                for (IFieldDescribe fieldDescribe : refFieldList) {
                    // 有whatList字段时填充显示字段到name属性中  790 whatList字段支持显示字段
                    if (FieldDescribeExt.of(fieldDescribe).isWhatListField()) {
                        WhatListFieldDescribe whatListFieldDescribe = (WhatListFieldDescribe) fieldDescribe;
                        String objectIdFieldApiName = whatListFieldDescribe.getIdFieldApiName();
                        CollectionUtils.nullToEmpty((List<Map>) objectData.get(objectIdFieldApiName)).forEach(relatedData -> {
                            String id = (String) relatedData.get("id");
                            String describeApiName = (String) relatedData.get("describe_api_name");
                            String cacheKey = createNameCacheKey(describeApiName, id);
                            if (idNameMap.containsKey(cacheKey)) {
                                relatedData.put("name", idNameMap.get(cacheKey));
                            }
                        });
                    } else {
                        String fieldApiName = FieldDescribeExt.of(fieldDescribe).getRefObjApiName();
                        String targetApiName = getRefObjTargetApiName(fieldDescribe, objectData);
                        if (fieldApiName != null && objectData.get(fieldApiName) != null) {
                            if (IFieldType.OBJECT_REFERENCE_MANY.equals(fieldDescribe.getType())) {
                                List<String> lookupIds = objectData.get(fieldApiName, List.class);
                                if (CollectionUtils.notEmpty(lookupIds)) {
                                    List<Map> names = lookupIds.stream().filter(id -> Objects.nonNull(id) || !Strings.isNullOrEmpty(id)).map(id -> {
                                        String name = idNameMap.get(createNameCacheKey(targetApiName, id));
                                        Map ref = new HashMap();
                                        ref.put("_id", id);
                                        ref.put("name", name);
                                        return ref;
                                    }).collect(Collectors.toList());
                                    objectData.set(FieldDescribeExt.getLookupNameByFieldName(fieldApiName), names);
                                } else {
                                    objectData.set(FieldDescribeExt.getLookupNameByFieldName(fieldApiName), Lists.newArrayList());
                                }

                            } else {
                                String id = objectData.get(fieldApiName).toString();
                                String cacheKey = createNameCacheKey(targetApiName, id);
                                if (idNameMap.containsKey(cacheKey)) {
                                    objectData.set(FieldDescribeExt.getLookupNameByFieldName(fieldApiName), idNameMap.get(cacheKey));
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("error in fillRefObjNameToData, ei:", e);
        }
    }

    private String getRefObjTargetApiName(IFieldDescribe fieldDescribe, IObjectData objectData) {
        FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
        if (!fieldDescribeExt.isWhatField()) {
            return fieldDescribeExt.getRefObjTargetApiName();
        }
        WhatFieldDescribe whatFieldDescribe = (WhatFieldDescribe) fieldDescribe;
        String apiNameFieldApiName = whatFieldDescribe.getApiNameFieldApiName();
        return (String) objectData.get(apiNameFieldApiName);
    }


    public void fillNameFromNameCache(String source, User user, Map<String, List<IObjectData>> refObjectDataMap, Map<String, List<String>> idListMap, Map<String, String> idNameMap) {
        try {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            for (Map.Entry<String, List<String>> entry : idListMap.entrySet()) {
                String targetApiName = entry.getKey();
                List<String> ids = entry.getValue();
                if (CollectionUtils.empty(ids)) {
                    continue;
                }
                parallelTask.submit(() -> {
                    List<INameCache> nameCaches;
                    try {
                        if (Objects.nonNull(refObjectDataMap)) {
                            List<IObjectData> refObjectDataList = refObjectDataMap.getOrDefault(targetApiName, Lists.newArrayList());
                            nameCaches = refObjectDataList.stream().map(x -> new NameCache(ObjectDataExt.of(x).toMap())).collect(Collectors.toList());
                            // refObjectDataMap中不包含物理删除的数据，需要去name_cache表中查询
                            List<String> idList = ids.stream()
                                    .filter(id -> nameCaches.stream().noneMatch(x -> Objects.equals(x.getId(), id)))
                                    .collect(Collectors.toList());
                            if (CollectionUtils.notEmpty(idList)) {
                                nameCaches.addAll(dataProxy.findRecordName(buildContext(user), targetApiName, idList));
                            }
                        } else {
                            nameCaches = dataProxy.findRecordName(buildContext(user), targetApiName, ids);
                        }
                        // name_cache 优先使用多语言配置信息 languageName
                        nameCaches.stream().filter(x -> Objects.nonNull(x.getName())).forEach(it -> {
                            String languageName = it.getLanguageName();
                            if (Strings.isNullOrEmpty(languageName)) {
                                languageName = it.getName();
                            }
                            idNameMap.put(createNameCacheKey(targetApiName, it.getId()), languageName);
                        });
                    } catch (MetadataServiceException e) {
                        log.warn("Error in findRecordName,user:{},targetApiName:{},ids:{}", user, targetApiName, ids, e);
                    }
                });
            }

            parallelTask.await(5000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("fillObjectDataWithRefObject error,user:{},apiName:{},targetApiNames:{}",
                    user, source, idListMap.keySet(), e);
            //throw new MetaDataException(SystemErrorCode.METADATA_ERROR, e);
        }
    }

    private String createNameCacheKey(String describeApiName, String id) {
        return describeApiName + "_" + id;
    }

    @Override
    public Map<String, ApprovalFlowStartResult> batchStartApproval(ApprovalFlowTriggerType type, User user, List<IObjectData> objectDataList, Map<String, Map<String, Object>> dataMap) {
        return batchStartApproval(type, user, objectDataList, dataMap, dataMap, null);
    }

    private IActionContext buildContext(User user) {
        return this.buildContext(user, false);
    }

    private IActionContext buildContext(User user, boolean allowUpdateInvalid) {
        return ActionContextExt.of(user).allowUpdateInvalid(allowUpdateInvalid).getContext();
    }

    @Override
    public Map<String, ApprovalFlowStartResult> batchStartApproval(ApprovalFlowTriggerType type, User user, List<IObjectData> objectDataList,
                                                                   Map<String, Map<String, Object>> dataMap, Map<String, Map<String, Object>> callbackDataMap,
                                                                   Map<String, Object> freeApprovalDef) {
        List<Tuple<String, String>> objects = objectDataList.stream()
                .map(x -> Tuple.of(x.getId(), x.getDescribeApiName()))
                .collect(Collectors.toList());
        Map<String, ApprovalFlowStartResult> resultMap = approvalFlowService.batchStartApproval(user, objects, type, dataMap, callbackDataMap, freeApprovalDef);

        return resultMap;
    }

    @Override
    public Map<String, ApprovalFlowStartResult> batchStartApproval(String type, User user, List<IObjectData> objectDataList,
                                                                   Map<String, Map<String, Object>> dataMap, Map<String, Map<String, Object>> callbackDataMap) {
        List<Tuple<String, String>> objects = objectDataList.stream()
                .map(x -> Tuple.of(x.getId(), x.getDescribeApiName()))
                .collect(Collectors.toList());
        Map<String, ApprovalFlowStartResult> resultMap = approvalFlowService.batchStartApproval(user, objects, type, dataMap, callbackDataMap, null);

        return resultMap;
    }

    @Override
    public void batchStartApprovalAsynchronous(ApprovalFlowTriggerType type, User user, List<IObjectData> objectDataList,
                                               Map<String, Map<String, Object>> dataMap) {
        batchStartApprovalAsynchronous(type, user, objectDataList, dataMap, dataMap);
    }

    @Override
    public void batchStartApprovalAsynchronous(ApprovalFlowTriggerType type, User user, List<IObjectData> objectDataList,
                                               Map<String, Map<String, Object>> dataMap, Map<String, Map<String, Object>> callbackDataMap) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<Tuple<String, String>> objects = objectDataList.stream()
                .map(x -> Tuple.of(x.getId(), x.getDescribeApiName()))
                .collect(Collectors.toList());
        approvalFlowService.batchStartApprovalAsynchronous(user, objects, type, dataMap, callbackDataMap);
    }

    @Override
    public void doDataPrivilegeCheck(User user, List<IObjectData> dataList, IObjectDescribe describe, String actionCode) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }

        //系统用户不校验数据权限
        if (user.isSupperAdmin()) {
            return;
        }

        Permissions permission = Permissions.READ_ONLY;
        if (ObjectAction.of(actionCode).needWriteDataPrivilege()) {
            permission = Permissions.READ_WRITE;
        }

        List<String> idList = dataList.stream().map(x -> x.getId()).collect(Collectors.toList());
        Map<String, Permissions> permissionsMap = checkDataPrivilege(user, dataList, describe, actionCode);

        idList.forEach(x -> {
            if (!permissionsMap.containsKey(x)) {
                throw new DataPermissionError(I18N.text(I18NKey.DATA_PERMISSION_WITHOUT_THIS_OPERATION));
            }
        });

        Set<Permissions> permissions = Sets.newHashSet(permissionsMap.values());
        if (permission.equals(Permissions.READ_ONLY)) {
            if (permissions.contains(Permissions.NO_PERMISSION)) {
                throw new DataPermissionError(I18N.text(I18NKey.DATA_PERMISSION_WITHOUT_THIS_OPERATION));
            }
        } else if (permission.equals(Permissions.READ_WRITE)) {
            if (permissions.contains(Permissions.NO_PERMISSION) || permissions.contains(Permissions.READ_ONLY)) {
                throw new DataPermissionError(I18N.text(I18NKey.DATA_PERMISSION_WITHOUT_THIS_OPERATION));
            }
        }
    }

//    @Override
//    public void checkLookupRolesConfig(User user, IObjectDescribe describe, IObjectData objectData) {
//        IObjectDescribe masterObjectDescribe = describe;
//        IObjectData masterData = objectData;
//        //如果是从对象取主对象的描述，用主对象的lookup字段配置判断
//        boolean slaveObject = ObjectDescribeExt.of(describe).isSlaveObject();
//        if (slaveObject) {
//            String masterObjectAPIName = ObjectDescribeExt.of(describe).getMasterAPIName().get();
//            masterObjectDescribe = describeLogicService.findObjectIncludeMultiField(user.getTenantId(), masterObjectAPIName);
//            MasterDetailFieldDescribe masterDetailFieldDescribe = ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().get();
//            String masterDetailFieldName = masterDetailFieldDescribe.getApiName();
//            String masterDataId = (String) objectData.get(masterDetailFieldName);
//            masterData = metaDataFindService.findObjectData(user, masterDataId, masterObjectDescribe);
//        }
//        boolean anyMatch = isAnyMatchLookupRoles(user, masterObjectDescribe, masterData, Maps.newHashMap());
//        if (!anyMatch) {
//            throw new ValidateException(I18N.text(I18NKey.DATA_PERMISSION_WITHOUT_THIS_OPERATION));
//        }
//    }

//    @Override
//    public boolean isAnyMatchLookupRoles(User user, IObjectDescribe objectDescribe, IObjectData objectData, @NonNull Map<String, List<String>> apiNameAndIdsMap) {
//        List<ObjectReferenceWrapper> referenceHaveConfigFields = ObjectDescribeExt.of(objectDescribe).getRefFieldAndHaveLookupRoles();
//        if (CollectionUtils.empty(referenceHaveConfigFields)) {
//            return false;
//        }
//        // 批量查询相关团队成员
//        if (apiNameAndIdsMap.isEmpty() && Objects.nonNull(objectData)) {
//            referenceHaveConfigFields.forEach(x -> {
//                String targetApiName = x.getTargetApiName();
//                if (!Strings.isNullOrEmpty(targetApiName) && Objects.nonNull(objectData.get(x.getApiName()))) {
//                    if (CollectionUtils.empty(apiNameAndIdsMap.get(targetApiName))) {
//                        apiNameAndIdsMap.put(targetApiName, Lists.newArrayList((String) objectData.get(x.getApiName())));
//                    } else {
//                        List<String> list = apiNameAndIdsMap.get(targetApiName);
//                        list.add((String) objectData.get(x.getApiName()));
//                        apiNameAndIdsMap.put(targetApiName, list);
//                    }
//                }
//            });
//        }
//
//        if (CollectionUtils.empty(apiNameAndIdsMap)) {
//            return false;
//        }
//
//        Map<String, Map<String, List<RelevantTeam>>> teamMemberMaps = metaDataFindService.batchFindTeamMember(user.getTenantId(), apiNameAndIdsMap);
//
//        if (CollectionUtils.empty(teamMemberMaps)) {
//            return false;
//        }
//
//        if (Objects.isNull(objectData)) {
//            return apiNameAndIdsMap.keySet().stream().anyMatch(x -> {
//                Map<String, List<RelevantTeam>> teamMemberMap = teamMemberMaps.get(x);
//                if (CollectionUtils.empty(teamMemberMap)) {
//                    return false;
//                }
//                List<RelevantTeam> relevantTeams = teamMemberMap.get(apiNameAndIdsMap.get(x).get(0));
//                return ObjectDescribeExt.of(objectDescribe).isRelatedAndLookupRolesObject(relevantTeams, user, x);
//            });
//        }
//
//        return referenceHaveConfigFields.stream().anyMatch(x -> {
//            String targetApiName = x.getTargetApiName();
//            Map<String, List<RelevantTeam>> teamMemberMap = teamMemberMaps.get(targetApiName);
//            if (CollectionUtils.empty(teamMemberMap)) {
//                return false;
//            }
//            List<RelevantTeam> relevantTeams = teamMemberMap.get(objectData.get(x.getApiName()));
//            return ObjectDescribeExt.of(objectDescribe).isRelatedAndLookupRolesObject(relevantTeams, user, targetApiName);
//        });
//    }

    @Override
    public Map<String, Permissions> checkPrivilege(User user, List<IObjectData> dataList, IObjectDescribe describe, String actionCode) {
        if (CollectionUtils.empty(dataList)) {
            return Maps.newHashMap();
        }

        boolean hasFunction = functionPrivilegeService.funPrivilegeCheck(user, describe.getApiName(), actionCode);
        if (!hasFunction) {
            return Maps.newHashMap();
        }

        return checkDataPrivilege(user, dataList, describe, actionCode);
    }

    private Map<String, Permissions> checkDataPrivilegeWithoutAdminRole(User user, List<String> idList, IObjectDescribe describe) {
        if (CollectionUtils.empty(idList)) {
            return Maps.newHashMap();
        }

        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describe);

        //如果是主对象,直接校验权限
        if (!objectDescribeExt.isSlaveObject()) {
            return dataPrivilegeService.checkDataPrivilege(user, idList, describe.getApiName());
        }

        //如果是从对象,要校验其主对象的数据权限
        //如果是从对象,要校验其主对象的数据权限
        String masterName = objectDescribeExt.getMasterAPINameOrThrowException();
        IObjectDescribe masterObjectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), masterName);

        Map<String, Permissions> resultMap = Maps.newHashMap();
        //初始化从对象id到主对象id的映射关系。
        Map<String, String> detailIdToMasterIdMap = Maps.newHashMap();
        //为了满足三层MD级联,特意增加的临时处理。
        if (ObjectDescribeExt.of(masterObjectDescribe).isSlaveObject()) {
            List<IObjectData> detailDataList = metaDataFindService.findObjectDataByIds(
                    user.getTenantId(), idList, describe.getApiName());
            //初始化从对象id到主对象id的映射关系。
            //获取所有要校验的主数据
            List<String> masterIdToCheckList = detailDataList.stream().map(it -> {
                String masterId = it.get(ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe()
                        .get().getApiName(), String.class);
                detailIdToMasterIdMap.put(it.getId(), masterId);
                return masterId;
            }).distinct().collect(Collectors.toList());
            log.debug("doDataPrivilegeCheck isSlaveSlaveObject,{},{}", masterIdToCheckList, masterObjectDescribe.getApiName());
            Map<String, Permissions> permissionsMap = getPermissionMapResultByDetailDataList(user, masterIdToCheckList, masterObjectDescribe,
                    ObjectDescribeExt.of(masterObjectDescribe).getMasterAPINameOrThrowException(), detailIdToMasterIdMap);
            for (String id : idList) {
                //没查到主对象认为没有权限
                if (!detailIdToMasterIdMap.containsKey(id)) {
                    resultMap.put(id, Permissions.NO_PERMISSION);
                } else {
                    resultMap.put(id, permissionsMap.getOrDefault(detailIdToMasterIdMap.get(id), Permissions.NO_PERMISSION));
                }
            }
            return resultMap;
        } else {
            //如果仅仅是二层MD级联,则查询出主对象Id,再批量查权限
            resultMap = getPermissionMapResultByDetailDataList(user, idList, describe, masterName, detailIdToMasterIdMap);
        }

        return resultMap;
    }

    @Override
    public Map<String, Permissions> checkDataPrivilege(User user, List<IObjectData> dataList
            , IObjectDescribe describe, String actionCode) {

        Map<String, Map<String, Permissions>> actionDataPrivilegeMap =
                checkDataPrivilege(user, dataList, describe, Lists.newArrayList(actionCode));

        return actionDataPrivilegeMap.get(actionCode);

    }

    @Override
    public Map<String, Map<String, Permissions>> checkDataPrivilege(User user, List<IObjectData> dataList
            , IObjectDescribe describe, List<String> actionCodes) {
        if (CollectionUtils.empty(dataList)) {
            return Maps.newHashMap();
        }

        List<String> idList = dataList.stream().map(objectData -> objectData.getId()).collect(Collectors.toList());


        Map<String, Permissions> dataPrivilegeMap = user.isOutUser() ? checkOutUserDataPrivilege(user, dataList, describe)
                : checkDataPrivilege(user, idList, describe);

        DataPrivilegeProvider provider = providerManager.getProvider(describe.getApiName());
        if (provider != null) {
            if (AppFrameworkConfig.isGrayDataPrivilegeProviderSimple(user.getTenantId())) {
                //1 过滤掉自定义字段
                //2 过滤掉null和空字符串的字段
                List<IObjectData> simpleList = ObjectDataExt.copyListNonNullAndNonCustomField(dataList);
                return provider.checkBusinessPrivilege(user, dataPrivilegeMap, simpleList, actionCodes);
            } else {
                return provider.checkBusinessPrivilege(user, dataPrivilegeMap, dataList, actionCodes);
            }
        } else {
            Map<String, Map<String, Permissions>> actionDataPrivilegeMap = Maps.newHashMap();
            actionCodes.forEach(actionCode -> actionDataPrivilegeMap.put(actionCode, dataPrivilegeMap));
            return actionDataPrivilegeMap;
        }
    }


    private Map<String, Permissions> dealWLJTempData(User user, List<IObjectData> dataList) {
        return dataList.stream().collect(Collectors.toMap(DBRecord::getId, a -> {
            if (ObjectDataExt.of(a).isDataOutOwner(user)) {
                //当前人是外部负责人，读写权限
                return Permissions.READ_WRITE;
            }

            Object value = a.get("out_tenant_ids");
            boolean exist = (Objects.nonNull(value) && value instanceof List)
                    ? CollectionUtils.nullToEmpty((List) value).contains(user.getOutTenantId())
                    : String.valueOf(value).contains(user.getOutTenantId());
            return exist ? Permissions.READ_ONLY : Permissions.NO_PERMISSION;
        }, (a, b) -> a));
    }


    private Map<String, Permissions> checkOutUserDataPrivilege(User user, List<IObjectData> dataList,
                                                               IObjectDescribe describe) {
        if (CollectionUtils.empty(dataList) || !user.isOutUser()) {
            return Maps.newHashMap();
        }

        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (!describeExt.isSlaveObject()) {
            return checkOutUserDataPrivilegeWithMaster(user, dataList, describe);
        }
        //如果是从对象,要校验其主对象的外部联系人权限
        MasterDetailFieldDescribe masterName = describeExt.getMasterDetailFieldDescribe().orElseThrow(() -> new ValidateException(I18N.text(I18NKey.PARAM_ERROR)));
        IObjectDescribe masterObjectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), masterName.getTargetApiName());

        Map<String, List<String>> masterDetailIdMap = dataList.stream()
                .collect(Collectors.groupingBy(data -> data.get(masterName.getApiName(), String.class),
                        Collectors.mapping(IObjectData::getId, Collectors.toList())));
        List<IObjectData> masterDataList = metaDataFindService.findObjectDataByIdsIgnoreFormula(user.getTenantId(),
                Lists.newArrayList(masterDetailIdMap.keySet()), masterObjectDescribe.getApiName());
        Map<String, Permissions> permissionsMap = checkOutUserDataPrivilegeWithMaster(user, masterDataList, masterObjectDescribe);
        // 将permissionsMap中主对象的id转换为从id
        return masterDetailIdMap.entrySet().stream()
                .flatMap(entry -> entry.getValue().stream().map(detailId -> Tuple.of(detailId, permissionsMap.get(entry.getKey()))))
                .collect(Collectors.toMap(Tuple::getKey, Tuple::getValue));
    }

    private Map<String, Permissions> checkOutUserDataPrivilegeWithMaster(User user, List<IObjectData> dataList,
                                                                         IObjectDescribe describe) {
        if (CollectionUtils.empty(dataList) || !user.isOutUser()) {
            return Maps.newHashMap();
        }
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CHECK_OUT_USER_DATA_PRIVILEGE_GRAY, user.getTenantId())) {
            List<String> idList = dataList.stream().map(DBRecord::getId).collect(Collectors.toList());
            return checkDataPrivilege(user, idList, describe);
        }
        Map<String, Permissions> map;

        OutDataPrivilege dataPrivilege = dataPrivilegeService.getOutDataPrivilege(user,
                RequestContextManager.getContext().getAppId(), describe.getApiName());

        if (Objects.equals(OutDataPrivilege.PUBLIC_WRITE, dataPrivilege)) {
            map = dataList.stream().collect(Collectors.toMap(DBRecord::getId, a -> Permissions.READ_WRITE));
        } else if (Objects.equals(OutDataPrivilege.PUBLIC_READONLY, dataPrivilege)) {
            map = checkPublicReadonly(user, dataList);
        } else if (Objects.equals(OutDataPrivilege.SAME_DOWNSTREAM_READONLY, dataPrivilege)) {
            map = checkSameDownstreamReadOnly(user, dataList, describe, dataPrivilege);
        } else if (Objects.equals(OutDataPrivilege.SAME_DOWNSTREAM_WRITE, dataPrivilege)) {
            map = checkSameDownstreamWrite(user, dataList, describe);
        } else {
            List<String> idList = dataList.stream().map(DBRecord::getId).collect(Collectors.toList());
            map = checkDataPrivilege(user, idList, describe);
        }

        return map;
    }

    private Map<String, Permissions> checkSameDownstreamWrite(User user, List<IObjectData> dataList, IObjectDescribe describe) {
        Map<String, Permissions> map = Maps.newHashMap();
        List<String> ids = Lists.newArrayList();
        for (IObjectData data : dataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            if (dataExt.isSameDownStreamCorp(user)) {
                map.put(dataExt.getId(), Permissions.READ_WRITE);
            } else {
                ids.add(dataExt.getId());
            }
        }
        //对于不是同一公司，但是在相关团队中的人，或者其他情况，请求数据权限接口判断
        if (CollectionUtils.notEmpty(ids)) {
            Map<String, Permissions> permissionsMap = checkDataPrivilege(user, ids, describe);
            map.putAll(permissionsMap);
        }
        return map;
    }

    private Map<String, Permissions> checkPublicReadonly(User user, List<IObjectData> dataList) {
        Set<String> subordinate = getOutUserDirectSubordinate(user);
        return dataList.stream().collect(Collectors.toMap(DBRecord::getId,
                data -> {
                    ObjectDataExt dataExt = ObjectDataExt.of(data);
                    return dataExt.isOutOwner(user) ||
                            isOutOwnerDirectLeader(subordinate, dataExt.getOutOwnerId().orElse(null)) ?
                            Permissions.READ_WRITE : Permissions.READ_ONLY;
                }));
    }

    private Map<String, Permissions> checkSameDownstreamReadOnly(User user, List<IObjectData> dataList,
                                                                 IObjectDescribe describe, OutDataPrivilege dataPrivilege) {
        Map<String, Permissions> map = Maps.newHashMap();
        Set<String> subordinate = getOutUserDirectSubordinate(user);
        List<String> ids = Lists.newArrayList();
        for (IObjectData data : dataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            if (dataExt.isOutOwner(user) || isOutOwnerDirectLeader(subordinate, dataExt.getOutOwnerId().orElse(null))) {
                map.put(dataExt.getId(), Permissions.READ_WRITE);
            } else if (dataExt.isSameDownStreamCorp(user)) {
                map.put(dataExt.getId(), Permissions.OUT_DATA_PRIVILEGE_PERMISSIONS_MAP.get(dataPrivilege));
            } else {
                ids.add(dataExt.getId());
            }
        }

        //对于不是同一公司，但是在相关团队中的人，或者其他情况，请求数据权限接口判断
        if (CollectionUtils.notEmpty(ids)) {
            Map<String, Permissions> permissionsMap = checkDataPrivilege(user, ids, describe);
            map.putAll(permissionsMap);
        }
        return map;
    }

    private boolean isOutOwnerDirectLeader(Set<String> subordinate, String outOwnerId) {
        return subordinate.contains(outOwnerId);
    }

    private Set<String> getOutUserDirectSubordinate(User user) {
        if (Strings.isNullOrEmpty(user.getOutUserId())) {
            return Sets.newHashSet();
        }
        List<UserInfo> userInfos = orgService.batchGetResponsibleEmployeeByUserId(user.getTenantId(), user.getUserId(),
                Sets.newHashSet(user.getOutUserId()));
        return userInfos.stream().map(UserInfo::getId).collect(Collectors.toSet());
    }


    @Override
    public Map<String, Permissions> checkDataPrivilege(User user, List<String> idList, IObjectDescribe describe) {
        if (CollectionUtils.empty(idList)) {
            return Maps.newHashMap();
        }

        //如果用户是管理员，直接返回有读写权限
        if (user.isSupperAdmin() || userRoleInfoService.isAdmin(user)) {
            return idList.stream().distinct().collect(Collectors.toMap(x -> x, x -> Permissions.READ_WRITE));
        }

        return checkDataPrivilegeWithoutAdminRole(user, idList, describe);
    }

    @NotNull
    private Map<String, Permissions> getPermissionMapResultByDetailDataList(User user, List<String> idList,
                                                                            IObjectDescribe detailDescribe,
                                                                            String masterName,
                                                                            Map<String, String> detailIdToMasterIdMap) {

        //获取所有的从对象数据
        List<IObjectData> masterDataList = metaDataFindService.findObjectDataByIdsIgnoreFormula(user.getTenantId(), idList, detailDescribe.getApiName());

        //获取所有要校验的主数据
        List<String> masterIdToCheckList = masterDataList.stream().map(it -> {
            String masterId = it.get(ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldDescribe()
                    .get().getApiName(), String.class);
            detailIdToMasterIdMap.put(it.getId(), masterId);
            return masterId;
        }).distinct().collect(Collectors.toList());

        Map<String, Permissions> permissionsMap = dataPrivilegeService.checkDataPrivilege(user, masterIdToCheckList, masterName);
        Map<String, Permissions> resultMap = Maps.newHashMap();
        for (String id : idList) {
            //没查到主对象认为没有权限
            if (!detailIdToMasterIdMap.containsKey(id)) {
                resultMap.put(id, Permissions.NO_PERMISSION);
            } else {
                resultMap.put(id, permissionsMap.getOrDefault(detailIdToMasterIdMap.get(id), Permissions.NO_PERMISSION));
            }
        }
        return resultMap;
    }

    @Override
    public boolean canUpdateAccountName(String tenantId) {
        //http://wiki.firstshare.cn/pages/viewpage.action?pageId=********
        String configValue = crmService.getConfigValue(tenantId, 11);
        return "1".equals(configValue);
    }

    @Override
    public void validateLookupData(User user, IObjectData objectData, ObjectReferenceWrapper referenceField) {
        Object o = objectData.get(referenceField.getApiName());
        String value = String.valueOf(o);

        if (CollectionUtils.empty(referenceField.getWheres())
                || Objects.isNull(o)
                || Strings.isNullOrEmpty(value)) {
            return;
        }
        validateLookupData(user, objectData, referenceField, null, false);
    }

    @Override
    public void validateLookupData(User user, IObjectData objectData, ObjectReferenceFieldDescribe referenceField) {
        validateLookupData(user, objectData, ObjectReferenceWrapper.of(referenceField));
    }

    @Override
    public void validateLookupDataIgnorePolygonal(User user, IObjectData objectData, ObjectReferenceWrapper referenceField) {
        Object o = objectData.get(referenceField.getApiName());
        String value = String.valueOf(o);

        if (CollectionUtils.empty(referenceField.getWheres())
                || Objects.isNull(o)
                || Strings.isNullOrEmpty(value)) {
            return;
        }
        validateLookupData(user, objectData, referenceField, null, true);
    }

    @Override
    public void validateLookupDataIgnorePolygonal(User user, IObjectData objectData, IObjectReferenceField referenceField) {
        validateLookupDataIgnorePolygonal(user, objectData, ObjectReferenceWrapper.of(referenceField));
    }

    private void validateLookupData(User user, IObjectData objectData, ObjectReferenceWrapper referenceField,
                                    ProductCategoryPackage categoryPackage, boolean isIgnorePolygonal) {
        if (AppFrameworkConfig.isSkipValidateLookupGrayTenant(user.getTenantId())) {
            return;
        }

        List<String> value;
        if (IFieldType.OBJECT_REFERENCE.equals(referenceField.getType())) {
            Object o = objectData.get(referenceField.getApiName());
            value = Lists.newArrayList(String.valueOf(o));
        } else {
            value = objectData.get(referenceField.getApiName(), List.class);
        }

        if (CollectionUtils.empty(referenceField.getWheres())
                || CollectionUtils.empty(value)) {
            return;
        }

        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.ID);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(value);

        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setOffset(0);
        query.setLimit(value.size());
        query.addFilters(Lists.newArrayList(filter));
        if (AppFrameworkConfig.validateLookupSearchDataInDb()) {
            query.setSearchSource("db");
        }
        List<Wheres> wheres = ObjectDescribeExt.getWheresBy(referenceField.getWheres());

        //特殊处理产品对象的分类（把父类和子类一起作为查询条件）
        if (Utils.PRODUCT_API_NAME.equals(referenceField.getTargetApiName())) {
            wheres.forEach(x -> {
                x.getFilters().stream().filter(y -> FieldDescribeExt.PRODUCT_CATEGORY.equals(y.getFieldName()))
                        .filter(y -> Operator.EQ.equals(y.getOperator()) || Operator.N.equals(y.getOperator())).forEach(y -> {
                            List<ProductAllCategoriesModel.CategoryPojo> productAllCategories;
                            if (Objects.nonNull(categoryPackage) && categoryPackage.isInit()) {
                                productAllCategories = categoryPackage.getProductAllCategories();
                            } else {
                                //特殊处理产品对象的分类（把父类和子类一起作为查询条件）
                                productAllCategories = productCategoryService.getProductAllCategories(user.getTenantId(), user.getUserId());
                                //防止调用方传一个不可变的List进来
                                if (Objects.nonNull(categoryPackage)) {
                                    categoryPackage.setInit(true);
                                    categoryPackage.setProductAllCategories(productAllCategories);
                                }
                            }
                            Set<String> categorySet = productCategoryService.getCategoryChildrenCategoryCodesContainSelf(y.getFieldValues().get(0), productAllCategories);
                            if (Operator.EQ.equals(y.getOperator())) {
                                y.setOperator(Operator.IN);
                            } else {
                                y.setOperator(Operator.NIN);
                            }
                            y.setFieldValues(Lists.newArrayList(categorySet));
                        });
            });
        }

        query.setWheres(wheres);
        if (isIgnorePolygonal) {
            // 移除所有多角关系的lookup过滤条件
            SearchTemplateQueryExt.of(query).removeRelatedObjectFilter(wheres);
        }
        SearchTemplateQueryExt.of(query).handleWheresFilterWithLookupFunction();
        SearchTemplateQueryExt.of(query).handleWheresFilterWithLookupRelation(objectData);
        SearchTemplateQueryExt.removeMasterAndNativeObjVariableFilter(wheres);

        List<IObjectData> dataList = metaDataFindService.findBySearchQueryIgnoreAll(user, referenceField.getTargetApiName(), query).getData();

        Set<String> ids = dataList.stream().map(IObjectData::getId).collect(Collectors.toSet());
        List<String> noneMatchValue = value.stream().filter(it -> !ids.contains(it)).distinct().collect(Collectors.toList());

        String message = getValidateMessage(user, referenceField, noneMatchValue);
        if (!Strings.isNullOrEmpty(message)) {
            log.warn("validate lookup data failed, data: {}, field:{}", objectData.toJsonString(), referenceField.toJsonString());
            throw new ValidateException(message);
        }
    }

    private String getValidateMessage(User user, ObjectReferenceWrapper referenceField, List<String> noneMatchIds) {
        if (CollectionUtils.empty(noneMatchIds)) {
            return null;
        }
        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsWithoutCopyIfGray(user.getTenantId(), Lists.newArrayList(referenceField.getDescribeApiName(), referenceField.getTargetApiName()));
        IObjectDescribe referenceObjectDescribe = describeMap.get(referenceField.getTargetApiName());
        IObjectDescribe describe = describeMap.get(referenceField.getDescribeApiName());
        if (IFieldType.OBJECT_REFERENCE_MANY.equals(referenceField.getType())) {
            if (CollectionUtils.empty(noneMatchIds)) {
                return null;
            }
            List<String> names;
            if (ObjectDescribeExt.of(referenceObjectDescribe).isSupportDisplayName()) {
                List<IObjectData> dataList = metaDataFindService.findObjectDataByIdsIgnoreAll(user.getTenantId(), noneMatchIds, referenceObjectDescribe.getApiName());
                names = dataList.stream().map(ObjectDataExt::of)
                        .map(ObjectDataExt::getDisplayName)
                        .collect(Collectors.toList());
            } else {
                List<INameCache> nameCaches = findRecordName(ActionContextExt.of(user).getContext(), referenceField.getTargetApiName(), noneMatchIds);
                names = nameCaches.stream()
                        .map(INameCache::getName)
                        .collect(Collectors.toList());
            }

            return I18N.text(I18NKey.LOOKUP_MULTIPLE_UNSATISFIED_CONDITION, describe.getDisplayName(), referenceField.getLabel(), StringUtils.join(names, ","));
        }
        if (StringUtils.isBlank(referenceField.getHelpText())) {
            return I18N.text(I18NKey.LOOKUP_SINGLE_UNSATISFIED_CONDITION, describe.getDisplayName(), referenceField.getLabel());
        }
        return String.format("%s,%s", I18N.text(I18NKey.LOOKUP_SINGLE_UNSATISFIED_CONDITION, describe.getDisplayName(), referenceField.getLabel()), referenceField.getHelpText());
    }

    @Override
    public void validateLookupData(User user, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        List<ObjectReferenceWrapper> referenceFieldDescribes = ObjectDescribeExt.of(objectDescribe).getReferenceFieldDescribes();
        if (CollectionUtils.empty(referenceFieldDescribes)) {
            return;
        }
        ProductCategoryPackage categoryPackage = new ProductCategoryPackage();
        referenceFieldDescribes.forEach(referenceField -> {
            objectDataList.forEach(data -> {
                Object o = data.get(referenceField.getApiName());
                String value = String.valueOf(o);

                if (CollectionUtils.empty(referenceField.getWheres())
                        || Objects.isNull(o)
                        || Strings.isNullOrEmpty(value)) {
                    return;
                }
                validateLookupData(user, data, referenceField, categoryPackage, false);
            });
        });
    }

    @Override
    public String generateId() {
        return IdUtil.generateId();
    }

    @Override
    public String generateTemplate(User user, String describeLabel, List<Pair<String, IFieldDescribe>> rowList,
                                   List<List<String>> sampleList) {
        List<List<String[]>> formattedSampleList = getFormattedSampleList(rowList, sampleList);
        try {
            String ea = gdsHandler.getEAByEI(user.getTenantId());
            return generatePath(excelUtil.genarateTemplate(ea, user.getUserId(),
                            describeLabel,
                            rowList,
                            formattedSampleList),
                    ea,
                    user.getUserId());
        } catch (IOException e) {
            log.error("Error in generate template", e);
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR);
        }
    }

    @Override
    public Workbook generateUnionTemplate(User user,
                                          String describeLabel,
                                          List<Pair<String, IFieldDescribe>> rowList,
                                          List<List<String>> sampleList,
                                          Workbook workbook) {
        List<List<String[]>> formattedSampleList = getFormattedSampleList(rowList, sampleList);
        try {
            return excelUtil.generateUnionTemplate(gdsHandler.getEAByEI(user.getTenantId()),
                    user.getUserId(),
                    describeLabel,
                    rowList,
                    formattedSampleList,
                    workbook);
        } catch (Exception e) {
            log.error("Error in generate template", e);
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR);
        }
    }

    @Override
    public String generateTemplateWithMerge(User user, String describeLabel, List<Pair<String, IFieldDescribe>> rowList,
                                            List<List<String>> sampleList, List<HeaderMergeInfo> mergeInfoList) {
        List<List<String[]>> formattedSampleList = getFormattedSampleList(rowList, sampleList);
        try {
            String ea = gdsHandler.getEAByEI(user.getTenantId());
            return generatePath(excelUtil.generateTemplateWithMerge(ea, user.getUserId(),
                            describeLabel, rowList, formattedSampleList, mergeInfoList),
                    ea, user.getUserId());
        } catch (IOException e) {
            log.error("Error in generate template with merge", e);
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR);
        }
    }

    @Override
    public Workbook generateUnionTemplateWithMerge(User user, String describeLabel, List<Pair<String, IFieldDescribe>> rowList,
                                                   List<List<String>> sampleList, Workbook workbook, List<HeaderMergeInfo> mergeInfoList) {
        List<List<String[]>> formattedSampleList = getFormattedSampleList(rowList, sampleList);
        try {
            return excelUtil.generateUnionTemplateWithMerge(gdsHandler.getEAByEI(user.getTenantId()),
                    user.getUserId(), describeLabel, rowList, formattedSampleList, workbook, mergeInfoList);
        } catch (Exception e) {
            log.error("Error in generate union template with merge", e);
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR);
        }
    }

    /**
     * 生成模板路径
     */
    @Override
    public String generatePath(Workbook workbookResult, String ea, String userId) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbookResult.write(outputStream);
            outputStream.flush();
        } catch (IOException e) {
            log.error("Error in turn result excel to inputStream,", e);
            throw e;
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("Error in closing stream when uploading excel", e);
            }
        }

        String path = webFileUtil.uploadFile(ea, userId, outputStream);

        return path;
    }

    @Override
    public void orderSheets(Workbook workbook) {
        excelUtil.orderSheets(workbook);
    }

    /**
     * 获取格式化示例
     */
    private List<List<String[]>> getFormattedSampleList(List<Pair<String, IFieldDescribe>> rowList,
                                                        List<List<String>> sampleList) {
        List<List<String[]>> formattedSampleList = Lists.newArrayList();
        for (List<String> line : sampleList) {
            List<String[]> arrayLine = Lists.newArrayList();
            for (int col = 0; col < line.size(); col++) {
                if (IFieldType.TRUE_OR_FALSE.equals(rowList.get(col).getValue().getType())
                        || IFieldType.SELECT_ONE.equals(rowList.get(col).getValue().getType())
                        || IFieldType.RECORD_TYPE.equals(rowList.get(col).getValue().getType())) {
                    String str = line.get(col);
                    if (!Strings.isNullOrEmpty(str)) {
                        String[] array = str.split("\\|\\|");
                        arrayLine.add(array);
                    } else {
                        arrayLine.add(new String[]{""});
                    }
                } else {
                    arrayLine.add(new String[]{line.get(col)});
                }
            }
            formattedSampleList.add(arrayLine);
        }
        return formattedSampleList;
    }

//    @Override
//    @Deprecated
//    public Map<String, Object> getUpdatedFieldValueMapFromUpdateObjectData(User user, IObjectData objectData, IObjectDescribe objectDescribe) {
//        if (objectData.getId() == null) {
//            return Maps.newHashMap();
//        }
//        IObjectData objectDataInDb = metaDataFindService.findObjectData(user.getTenantId(), objectData.getId(), objectDescribe);
//
//        return ObjectDataExt.of(objectDataInDb).diff(objectData, objectDescribe);
//    }

    @Override
    public Map<String, Map<String, Object>> getUpdatedFieldValueMapFromUpdateObjectDataList(User user, List<IObjectData> objectDataList, IObjectDescribe objectDescribe) {
        Map<String, Map<String, Object>> idToDifferenceMap = Maps.newHashMap();
        List<String> ids = objectDataList.stream().filter(it -> it.getId() != null).map(it -> it.getId()).collect(Collectors.toList());
        List<IObjectData> objectDataListInDb = metaDataFindService.findObjectDataByIds(user.getTenantId(), ids, objectDescribe.getApiName());
        Map<String, IObjectData> objectDataListInDbMap = objectDataListInDb.stream()
                .collect(Collectors.toMap(key -> key.getId(), value -> value));
        Map<String, IObjectData> objectDataListMap = objectDataList.stream().filter(it -> it.getId() != null)
                .collect(Collectors.toMap(key -> key.getId(), value -> value));

        for (String id : ids) {
            IObjectData objectDataInDb = objectDataListInDbMap.get(id);
            if (objectDataInDb == null) {
                continue;
            }
            IObjectData objectData = objectDataListMap.get(id);
            Map<String, Object> differenceMap = ObjectDataExt.of(objectDataInDb).diff(objectData, objectDescribe);
            idToDifferenceMap.put(id, differenceMap);
        }

        return idToDifferenceMap;
    }

    @Override
    public List<INameCache> findRecordName(IActionContext context, String describeApiName, List<String> ids) {
        if (CollectionUtils.empty(ids)) {
            return Lists.newArrayList();
        }
        List<INameCache> result;
        try {
            result = dataProxy.findRecordName(context, describeApiName, ids);
            return result;
        } catch (MetadataServiceException e) {
            log.warn("Error in findRecordName,context:{},describeApiName:{},ids:{}", context, describeApiName, ids, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Map<String, String> findNameByIds(User user, String describeApiName, List<String> ids) {
        Map<String, String> result = Maps.newHashMap();
        IActionContext context = ActionContextExt.of(user).getContext();
        List<INameCache> nameCacheList = findRecordName(context, describeApiName, ids);
        nameCacheList.forEach(x -> result.put(x.getId(), x.getName()));
        return result;
    }

    @Override
    public void fillUserInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user) {
        fillUserInfo(sourceObject, objectDataList, user, false);
    }

    @Override
    public void fillUserInfoExcludeSingleValue(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user) {
        List<String> empFields = ObjectDescribeExt.of(sourceObject)
                .stream()
                .filter(a -> FieldDescribeExt.of(a).needFillEmployeeExtAttribute())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        fillUserInfo(sourceObject, objectDataList, user, false, empFields, false);
    }

    @Override
    public Map<String, UserInfo> fillUserInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user, boolean extractExtendInfo) {
        List<String> empFields = ObjectDescribeExt.of(sourceObject)
                .stream()
                .filter(a -> FieldDescribeExt.of(a).needFillEmployeeExtAttribute())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        return fillUserInfo(sourceObject, objectDataList, user, extractExtendInfo, empFields);
    }

    @Override
    public Map<String, UserInfo> fillUserInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList,
                                              User user, boolean extractExtendInfo, Collection<String> employeeFields) {
        return fillUserInfo(sourceObject, objectDataList, user, extractExtendInfo, employeeFields, true);
    }

    private Map<String, UserInfo> fillUserInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList,
                                               User user, boolean extractExtendInfo, Collection<String> employeeFields,
                                               boolean fillSingleEmpInfo) {
        Map<String, UserInfo> userMap = Maps.newHashMap();
        if (CollectionUtils.empty(employeeFields)) {
            return userMap;
        }
        try {
            // 校验一下字段为人员类型字段
            List<IFieldDescribe> empFields = ObjectDescribeExt.of(sourceObject)
                    .getFieldByTypes(FieldDescribeExt.FILL_EMPLOYEE_EXT_TYPES)
                    .stream()
                    .filter(it -> employeeFields.contains(it.getApiName()))
                    .collect(Collectors.toList());

            if (CollectionUtils.empty(empFields)) {
                return userMap;
            }

            Set<String> empIds = Sets.newHashSet();
            objectDataList.forEach(it -> {
                ObjectDataExt dataExt = ObjectDataExt.of(it);
                empFields.forEach(fieldDescribe -> {
                    List<String> employeeIds = dataExt.getEmployeeValues(fieldDescribe.getApiName());
                    if (CollectionUtils.empty(employeeIds)) {
                        return;
                    }
                    empIds.addAll(employeeIds);
                });

                List<TeamMember> teamMembers = dataExt.getTeamMembers();
                List<String> list = teamMembers.stream()
                        .filter(a -> TeamMember.MemberType.EMPLOYEE.equals(a.getMemberType())
                                && NumberUtils.isDigits(a.getEmployee()))
                        .map(TeamMember::getEmployee)
                        .collect(Collectors.toList());
                empIds.addAll(list);
            });

            List<UserInfo> userInfos = orgService.getUserNameByIds(user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser(),
                    Lists.newArrayList(empIds));

            userInfos.add(UserInfo.builder()
                    .id(User.SUPPER_ADMIN_USER_ID)
                    .name(User.getSupperAdminUserName())
                    .build());

            for (UserInfo userInfo : userInfos) {
                if (Objects.nonNull(userInfo.getStatus()) && UserInfo.NORMAL_STATUS != userInfo.getStatus()) {
                    userInfo.setName(userInfo.getName() + "(" + I18NExt.text(I18NKey.HAS_BEEN_DISCONTINU) + ")");
                    userInfo.setNickname(userInfo.getNickname() + "(" + I18NExt.text(I18NKey.HAS_BEEN_DISCONTINU) + ")");
                }
                //清空人员信息上的mobile属性，防止泄露
                userInfo.setMobile(null);
                userMap.put(userInfo.getId(), userInfo);
            }

            //添加人员对应__r对象
            objectDataList.forEach(it -> {
                ObjectDataExt dataExt = ObjectDataExt.of(it);
                if (!extractExtendInfo) {
                    empFields.stream()
                            .filter(employeeField -> !Objects.equals(employeeField.getType(), IFieldType.EMPLOYEE_MANY)
                                    && dataExt.isListSoloValue(employeeField.getApiName())
                                    && fillSingleEmpInfo)
                            .forEach(empField -> {
                                String id = dataExt.getEmployeeFieldValue(empField.getApiName());
                                if (userMap.containsKey(id)) {
                                    dataExt.fillEmployeeFieldObject(empField.getApiName(), userMap.get(id));
                                }
                            });
                    List<String> empFieldList = empFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
                    dataExt.fillEmployeeFields(empFieldList, Lists.newArrayList(userMap.values()));
                }

                Set<String> names = Sets.newHashSet();
                List<TeamMember> teamMembers = dataExt.getTeamMembers();
                teamMembers.stream()
                        .filter(x -> TeamMember.MemberType.EMPLOYEE.equals(x.getMemberType()))
                        .forEach(member -> {
                            String id = member.getEmployee();
                            if (userMap.containsKey(id) && Objects.nonNull(userMap.get(id))) {
                                if (!extractExtendInfo) {
                                    member.fillEmployeeFieldObject(member.toMap(), userMap.get(id));
                                }
                                names.add(userMap.get(id).getName());
                            }
                        });
                long count = teamMembers.stream().filter(x -> !TeamMember.MemberType.EMPLOYEE.equals(x.getMemberType())).count();
                if (CollectionUtils.notEmpty(names)) {
                    dataExt.setRelevantTeamValue(names, count);
                }
            });
        } catch (Exception e) {
            log.error("Error in fillUserInfo, ei:{}, object:{}", user.getTenantId(), sourceObject.getApiName(), e);
        }

        return userMap;
    }

    @Override
    public void fillDepartmentInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user, DeptStatusEnum deptStatus) {
        fillDepartmentInfo(sourceObject, objectDataList, user, deptStatus, false);
    }

    @Override
    public Map<String, QueryDeptInfoByDeptIds.DeptInfo> fillDepartmentInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user,
                                                                           DeptStatusEnum deptStatus, boolean extractExtendInfo) {
        List<String> departmentFields = ObjectDescribeExt.of(sourceObject)
                .stream()
                .filter(a -> FieldDescribeExt.of(a).needFillDepartmentExtAttribute())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        return fillDepartmentInfo(sourceObject, objectDataList, user, deptStatus, extractExtendInfo, departmentFields);
    }

    @Override
    public void fillDepartmentInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user) {
        fillDepartmentInfo(sourceObject, objectDataList, user, DeptStatusEnum.ALL);
    }

    @Override
    public void fillDepartmentInfoExcludeSingleValue(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user) {
        List<String> departmentFields = ObjectDescribeExt.of(sourceObject)
                .stream()
                .filter(a -> FieldDescribeExt.of(a).needFillDepartmentExtAttribute())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        fillDepartmentInfo(sourceObject, objectDataList, user, DeptStatusEnum.ALL, false, departmentFields, false);
    }

    @Override
    public Map<String, QueryDeptInfoByDeptIds.DeptInfo> fillDepartmentInfo(IObjectDescribe sourceObject,
                                                                           List<IObjectData> objectDataList,
                                                                           User user,
                                                                           DeptStatusEnum deptStatus,
                                                                           boolean extractExtendInfo,
                                                                           Collection<String> deptFields) {
        return fillDepartmentInfo(sourceObject, objectDataList, user, deptStatus, extractExtendInfo, deptFields, true);
    }

    private Map<String, QueryDeptInfoByDeptIds.DeptInfo> fillDepartmentInfo(IObjectDescribe sourceObject,
                                                                            List<IObjectData> objectDataList,
                                                                            User user,
                                                                            DeptStatusEnum deptStatus,
                                                                            boolean extractExtendInfo,
                                                                            Collection<String> deptFields,
                                                                            boolean fillSingleDeptInfo) {
        Map<String, QueryDeptInfoByDeptIds.DeptInfo> deptInfoMap = Maps.newHashMap();
        if (CollectionUtils.empty(deptFields)) {
            return deptInfoMap;
        }
        try {
            List<String> departmentFields = ObjectDescribeExt.of(sourceObject)
                    .getFieldByTypes(FieldDescribeExt.FILL_DEPARTMENT_EXT_TYPES)
                    .stream()
                    .filter(it -> deptFields.contains(it.getApiName()))
                    .map(IFieldDescribe::getApiName)
                    .collect(Collectors.toList());
            Set<String> empIds = Sets.newHashSet();
            objectDataList.forEach(it -> {
                ObjectDataExt dataExt = ObjectDataExt.of(it);
                departmentFields.forEach(a -> {
                    List<String> deptIds = dataExt.getDepartmentValues(a);
                    if (CollectionUtils.empty(deptIds)) {
                        return;
                    }
                    empIds.addAll(deptIds);
                });
            });

            List<QueryDeptInfoByDeptIds.DeptInfo> deptInfos = orgService.getDeptInfoNameByIdsAndStatus(user.getTenantId(),
                    user.getUserId(), Lists.newArrayList(empIds), deptStatus);
            Map<String, Map> deptMap = Maps.newHashMap();
            deptInfos.forEach(a -> {
                deptMap.put(a.getDeptId(), (Map) JSON.toJSON(a));
                deptInfoMap.put(a.getDeptId(), a);
            });

            // 添加部门对应__r对象
            if (!extractExtendInfo) {
                objectDataList.forEach(it -> {
                    ObjectDataExt dataExt = ObjectDataExt.of(it);
                    departmentFields.stream()
                            .filter(departmentField -> !departmentField.equals(IFieldType.DEPARTMENT_MANY)
                                    && dataExt.isListSoloValue(departmentField)
                                    && fillSingleDeptInfo)
                            .forEach(a -> {
                                String id = dataExt.getDepartmentFieldValue(a);
                                if (deptMap.containsKey(id)) {
                                    dataExt.fillDepartmentFieldObject(a, deptMap.get(id));
                                }
                            });
                    dataExt.fillDepartmentFields(departmentFields, deptInfos);
                });
            }
        } catch (Exception e) {
            log.error("Error in fillDepartmentInfo, ei:{}, object:{}", user.getTenantId(), sourceObject.getApiName(), e);
        }
        return deptInfoMap;
    }

    @Override
    public void fillButtonInfo(IObjectDescribe sourceObject, List<IObjectData> objectDataList, User user) {

    }

    @Override
    public String getCountryCascadeJsonString() {
        return countryAreaService.getCountryCascadeJsonString();
    }

    @Override
    public void fillPhoneNumberInformation(IObjectDescribe describe, IObjectData objectData) {
        List<IFieldDescribe> filter = ObjectDescribeExt.of(describe)
                .filter(a -> Objects.equals(a.getType(), IFieldType.PHONE_NUMBER) && !ObjectDataExt.isValueEmpty(objectData.get(a.getApiName())));

        Set<String> mobileSet = Sets.newHashSet();
        filter.forEach(x -> mobileSet.add(objectData.get(x.getApiName()).toString()));
        List<QueryPhoneNumberInformation.Result> phoneNumberInformation = phoneNumberService.batchQueryPhoneNumberInfo(mobileSet);
        if (CollectionUtils.notEmpty(phoneNumberInformation)) {
            Map<String, QueryPhoneNumberInformation.Result> map = Maps.newHashMap();
            phoneNumberInformation.forEach(x -> map.put(x.getMobile(), x));
            filter.forEach(x -> Optional.ofNullable(map.get(ConvertUtils.removeSpaceForPhoneNumber(objectData.get(x.getApiName()))))
                    .ifPresent(a -> objectData.set(x.getApiName() + "__p", a)));
        }
    }

    @Override
    public void fillPhoneNumberInformation(IObjectDescribe describe, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }

        List<IFieldDescribe> phoneFields = ObjectDescribeExt.of(describe)
                .filter(a -> Objects.equals(a.getType(), IFieldType.PHONE_NUMBER));

        if (CollectionUtils.empty(phoneFields)) {
            return;
        }

        Set<String> phoneNumberSet = Sets.newHashSet();
        for (IFieldDescribe field : phoneFields) {
            for (IObjectData objectData : objectDataList) {
                if (!ObjectDataExt.isValueEmpty(objectData.get(field.getApiName()))) {
                    phoneNumberSet.add(objectData.get(field.getApiName()).toString());
                }
            }
        }

        if (CollectionUtils.empty(phoneNumberSet)) {
            return;
        }

        List<QueryPhoneNumberInformation.Result> phoneNumberInfos = phoneNumberService.batchQueryPhoneNumberInfo(phoneNumberSet);

        if (CollectionUtils.empty(phoneNumberInfos)) {
            return;
        }

        Map<String, QueryPhoneNumberInformation.Result> phoneLocation = Maps.newHashMap();
        phoneNumberInfos.forEach(x -> phoneLocation.put(x.getMobile(), x));
        for (IFieldDescribe field : phoneFields) {
            for (IObjectData objectData : objectDataList) {
                if (!ObjectDataExt.isValueEmpty(objectData.get(field.getApiName()))
                        && phoneLocation.containsKey(ConvertUtils.removeSpaceForPhoneNumber(objectData.get(field.getApiName()).toString()))) {
                    objectData.set(field.getApiName() + "__p", phoneLocation.get(objectData.get(field.getApiName()).toString()));
                }
            }
        }
    }

    @Override
    public void parsePaymentObjOrderNames(IObjectDescribe describe, List<IObjectData> dataList, User user,
                                          boolean isInList) {
        if (!Utils.CUSTOMER_PAYMENT_API_NAME.equals(describe.getApiName())) {
            return;
        }
        log.info("parsePaymentObjOrderNames,tenantId {}", user.getTenantId());
        List<String> orderIds = dataList.stream()
                .filter(data -> StringUtils.isNotBlank(data.get("order_id", String.class)))
                .flatMap(data -> Stream.of(StringUtils.split(data.get("order_id", String.class), ",")))
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.empty(orderIds)) {
            return;
        }
        List<INameCache> nameCaches = findRecordName(ActionContextExt.of(user).getContext(), Utils.SALES_ORDER_API_NAME, orderIds);
        Map<String, String> orderNames = nameCaches.stream()
                .collect(Collectors.toMap(INameCache::getId,
                        n -> StringUtils.isNotBlank(n.getName()) ? n.getName() : n.getId(), (x, y) -> y));

        for (IObjectData objectData : dataList) {
            String orderId = objectData.get("order_id", String.class);
            if (StringUtils.isNotBlank(orderId)) {
                String updatedOrderId = Stream.of(StringUtils.split(orderId, ","))
                        .map(o -> orderNames.getOrDefault(o, o)).collect(Collectors.joining(","));
                if (isInList) {
                    objectData.set("order_id", updatedOrderId);
                } else {
                    objectData.set("order_id__r", updatedOrderId);
                }
            }
            if (isInList) {
                objectData.set("order_data_id", orderId);
            }
        }
    }

    @Override
    public void fillCountryAreaLabel(IObjectDescribe describe, List<IObjectData> dataList) {
        fillCountryAreaLabel(describe, dataList, RequestContextManager.getContext().getUser());
    }

    @Override
    public void fillCountryAreaLabel(IObjectDescribe describe, List<IObjectData> dataList, User user) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        //灰度了国家省市区存数据库的使用批量查询
        if (CountryAreaService.countryJsonIsParsedFromDB(user.getTenantId())) {
            batchFillCountryAreaLabel(describe, dataList, user);
        } else {
            ObjectDescribeExt.of(describe).getCountryAreaFields().stream()
                    .filter(x -> !IFieldType.TOWN.equals(x.getType()) || !IFieldType.VILLAGE.equals(x.getType()))
                    .forEach(field -> dataList.forEach(data -> {
                        String code = data.get(field.getApiName(), String.class);
                        if (Strings.isNullOrEmpty(code)) {
                            return;
                        }
                        String label = CountryAreaManager.getLabelByCode(user.getTenantId(), code, field.getType());
                        data.set(FieldDescribeExt.getLookupNameByFieldName(field.getApiName()), label);
                    }));
            fillTownAndVillageFieldLabel(describe, dataList, user);
        }
    }


    private void batchFillCountryAreaLabel(IObjectDescribe describe, List<IObjectData> dataList, User user) {
        List<IFieldDescribe> fields = ObjectDescribeExt.of(describe).getCountryAreaFields();
        Set<String> codes = Sets.newHashSet();
        fields.forEach(field -> dataList.forEach(data -> {
            String code = data.get(field.getApiName(), String.class);
            if (!Strings.isNullOrEmpty(code)) {
                codes.add(code);
            }
        }));
        if (CollectionUtils.empty(codes)) {
            return;
        }
        Map<String, String> code2LabelMap = countryAreaService.batchQueryLabelsByCodes(RequestUtil.getCurrentLang().getValue(),
                user.getTenantId(), codes);
        fields.forEach(field -> dataList.forEach(data -> {
            String code = data.get(field.getApiName(), String.class);
            if (!Strings.isNullOrEmpty(code)) {
                String label = code2LabelMap.getOrDefault(code, code);
                data.set(FieldDescribeExt.getLookupNameByFieldName(field.getApiName()), label);
            }
        }));
    }

    private void fillTownAndVillageFieldLabel(IObjectDescribe describe, List<IObjectData> dataList, User user) {
        // 补充乡镇__r
        List<IFieldDescribe> townFields = ObjectDescribeExt.of(describe).getTownFields();
        Set<String> townCodes = getCodesByAreaFields(townFields, dataList);
        fillAreaFieldLabel(user, dataList, townFields, townCodes, CountryAreaService.TOWN);
        // 补充村__r
        List<IFieldDescribe> villageFields = ObjectDescribeExt.of(describe).getVillageFields();
        Set<String> villageCodes = getCodesByAreaFields(villageFields, dataList);
        fillAreaFieldLabel(user, dataList, villageFields, villageCodes, CountryAreaService.VILLAGE);
    }

    private void fillAreaFieldLabel(User user, List<IObjectData> dataList, List<IFieldDescribe> fieldDescribes, Set<String> areaCodes, String areaType) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(fieldDescribes) || CollectionUtils.empty(areaCodes)) {
            return;
        }
        List<Map<String, String>> countryInfos = metaDataGlobalService.batchQueryAreaNamesByCode(user, areaCodes, areaType);
        Map<String, MetaDataGlobalService.CountryInfo> countryInfoMap = MetaDataGlobalService.CountryInfo.fromList(countryInfos).stream()
                .collect(Collectors.toMap(MetaDataGlobalService.CountryInfo::getValue, Function.identity(), (x, y) -> x));
        fieldDescribes.forEach(field -> dataList.forEach(data -> {
            String code = data.get(field.getApiName(), String.class);
            if (Strings.isNullOrEmpty(code)) {
                return;
            }
            String label = Optional.ofNullable(countryInfoMap.get(code))
                    .map(MetaDataGlobalService.CountryInfo::getLabel)
                    .orElse("");
            data.set(FieldDescribeExt.getLookupNameByFieldName(field.getApiName()), label);
        }));
    }

    private Set<String> getCodesByAreaFields(List<IFieldDescribe> townFields, List<IObjectData> dataList) {
        Set<String> result = Sets.newHashSet();
        for (IFieldDescribe townField : townFields) {
            String fieldApiName = townField.getApiName();
            for (IObjectData objectData : dataList) {
                String code = objectData.get(fieldApiName, String.class);
                if (!Strings.isNullOrEmpty(code)) {
                    result.add(code);
                }
            }
        }
        return result;
    }

    @Override
    public void fillImageInformation(IObjectDescribe describe, List<IObjectData> dataList, User user) {
        //外部联系人，图片添加token
        if (!user.isOutUser()) {
            return;
        }
        try {
            List<IFieldDescribe> imageFieldList = ObjectDescribeExt.of(describe)
                    .stream()
                    .filter(a -> Objects.equals(a.getType(), IFieldType.IMAGE))
                    .collect(Collectors.toList());

            if (CollectionUtils.empty(imageFieldList)) {
                return;
            }
            List<String> pathList = Lists.newArrayList();
            imageFieldList.forEach(field -> dataList.forEach(data -> {
                        Object imageFieldValue = data.get(field.getApiName());
                        if (imageFieldValue != null) {
                            //如果图片字段有图片信息，则拼接后添加到pathList，用于查询token
                            List<ImageInfo> imageInfoList = JSONArray.parseArray(JSON.toJSONString(imageFieldValue), ImageInfo.class);
                            imageInfoList.forEach(imageInfo -> {
                                if (imageInfo.getExt() != null && imageInfo.getPath() != null) {
                                    String path = imageInfo.getPath() + "." + imageInfo.getExt();
                                    pathList.add(path);
                                }
                            });
                        }
                    })
            );

            if (CollectionUtils.empty(pathList)) {
                return;
            }
            //存在图片信息，通过dubbo接口查询token
            log.info("imageList is:" + pathList);
            CreateFileShareIds.Arg arg = new CreateFileShareIds.Arg();
            arg.ea = gdsHandler.getEAByEI(user.getTenantId());
            arg.employeeId = user.isSupperAdmin() ? 10000 : Integer.parseInt(user.getUserIdOrOutUserIdIfOutUser());
            arg.pathList = pathList;
            arg.securityGroup = "";

            log.info("token dubbo接口查询 arg:{}", arg);
            CreateFileShareIds.Result fileShareIds = sharedFileService.createFileShareIds(arg);
            Map<String, String> tokenMap = fileShareIds.fileIdMap;
            log.info("token dubbo接口查询 result:" + fileShareIds);
            imageFieldList.forEach(imageNew -> dataList.forEach(data -> {
                        List<Map<String, Object>> newImageInfoList = (List) data.get(imageNew.getApiName());
                        if (CollectionUtils.notEmpty(newImageInfoList)) {
                            newImageInfoList.forEach(imageInfo -> {
                                Object ext = imageInfo.get("ext");
                                Object path = imageInfo.get("path");
                                if (ext != null && path != null) {
                                    String imagePath = path.toString() + "." + ext.toString();
                                    String token = tokenMap.get(imagePath);
                                    imageInfo.put("token", token);
                                }
                            });
                        }
                    })
            );

        } catch (Exception e) {
            log.error("MetaDataMiscServiceImpl fillOutUserInfo error is:", e);
        }
    }

    @Override
    public void fillImageInformation(IObjectDescribe describe, IObjectData objectData, Map<String, List<IObjectData>> detailObjectData,
                                     Map<String, IObjectDescribe> detailDescribeMap, User user) {
        if (!user.isOutUser()) {
            return;
        }
        fillImageInformation(describe, Lists.newArrayList(objectData), user);
        if (CollectionUtils.notEmpty(detailObjectData)) {
            detailObjectData.forEach((apiName, dataList) -> {
                IObjectDescribe detailDescribe = detailDescribeMap.get(apiName);
                if (detailDescribe != null && CollectionUtils.notEmpty(dataList)) {
                    fillImageInformation(detailDescribe, dataList, user);
                }
            });
        }
    }

    @Override
    public void fillMaskFieldValue(User user, List<IObjectData> dataList, IObjectDescribe describe) {
        fillMaskFieldValue(user, dataList, describe, false);
    }

    @Override
    public void fillMaskFieldValue(User user, List<IObjectData> dataList, IObjectDescribe describe, boolean removeOrigValue) {
        fillMaskFieldValue(user, dataList, describe, removeOrigValue, false);
    }

    @Override
    public void fillMaskFieldValue(User user, List<IObjectData> dataList, IObjectDescribe describe,
                                   boolean removeOrigValue, boolean overrideOrigValue) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        if (user.isSupperAdmin()) {
            return;
        }
        List<IFieldDescribe> maskFields = ObjectDescribeExt.of(describe).getMaskFields();
        fillMaskFieldValue(user, dataList, maskFields, removeOrigValue, overrideOrigValue);
    }

    @Override
    public void fillMaskFieldValue(User user, List<IObjectData> dataList, List<IFieldDescribe> fields, boolean removeOrigValue) {
        fillMaskFieldValue(user, dataList, fields, removeOrigValue, false);
    }

    @Override
    public void fillMaskFieldValue(User user, List<IObjectData> dataList, List<IFieldDescribe> fields,
                                   boolean removeOrigValue, boolean overrideOrigValue) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        if (user.isSupperAdmin()) {
            return;
        }
        List<IFieldDescribe> maskFields = CollectionUtils.nullToEmpty(fields).stream()
                .filter(x -> FieldDescribeExt.of(x).isShowMask()).collect(Collectors.toList());
        if (CollectionUtils.empty(maskFields)) {
            return;
        }
        //下游人员直接展示掩码
        if (user.isOutUser()) {
            dataList.forEach(data ->
                    filterMaskFields4OutUser(user, data, maskFields)
                            .forEach(field -> ObjectDataExt.of(data).fillMaskValue(field, removeOrigValue, overrideOrigValue)));
            return;
        }
        List<IFieldDescribe> filteredMaskField;
        if (userRoleInfoService.isAdmin(user)) {
            List<IFieldDescribe> roleConfigMaskFiled = maskFields.stream().filter(field -> FieldDescribeExt.of(field).haveRolesConfig()).collect(Collectors.toList());
            if (CollectionUtils.empty(roleConfigMaskFiled)) {
                return;
            }
            filteredMaskField = maskFieldLogicService.maskFieldRoleFilter(user, roleConfigMaskFiled);
        } else {
            filteredMaskField = maskFieldLogicService.maskFieldRoleFilter(user, maskFields);
        }
        Set<String> ownerIds = dataList.stream()
                .map(x -> ObjectDataExt.of(x).getOwnerId().orElse(null))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        Map<String, MainDeptInfo> mainDeptInfoMap = orgService.getMainDeptInfo(user.getTenantId(),
                user.getUserId(), Lists.newArrayList(ownerIds));
        removeOrFillMaskFieldValue(user, dataList, filteredMaskField, true, removeOrigValue, overrideOrigValue, mainDeptInfoMap);
    }

    private void removeOrFillMaskFieldValue(User user, List<IObjectData> dataList, List<IFieldDescribe> maskFields,
                                            boolean isFill, boolean removeOrigValue, boolean overrideOrigValue,
                                            Map<String, MainDeptInfo> mainDeptInfoMap) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(maskFields)) {
            return;
        }
        dataList.forEach(data -> {
            String ownerId = ObjectDataExt.of(data).getOwnerId().orElse(null);
            //数据没有负责人则统一展示掩码
            if (StringUtils.isEmpty(ownerId)) {
                if (isFill) {
                    ObjectDataExt.of(data).fillMaskValue(maskFields, removeOrigValue, overrideOrigValue);
                } else {
                    ObjectDataExt.of(data).remove(maskFields);
                }
                return;
            }
            maskFields.forEach(field -> {
                //配置了负责人去掩码
                if (FieldDescribeExt.of(field).isRemoveMaskForOwner() && StringUtils.equals(user.getUserId(), ownerId)) {
                    return;
                }
                //配置了主属部门负责人去掩码
                if (FieldDescribeExt.of(field).isRemoveMaskForDataOwnerMainDeptLeader()
                        && CollectionUtils.notEmpty(mainDeptInfoMap)
                        && mainDeptInfoMap.containsKey(ownerId)) {
                    MainDeptInfo deptInfo = mainDeptInfoMap.get(ownerId);
                    if (StringUtils.isNotEmpty(deptInfo.getLeaderId())
                            && StringUtils.equals(user.getUserId(), deptInfo.getLeaderId())) {
                        return;
                    }
                }
                removeOrFillMaskFieldValue(isFill, removeOrigValue, overrideOrigValue, data, field);
            });
        });
    }

    @Override
    public void fillMaskFieldValue(User user, IObjectData masterData, Map<String, List<IObjectData>> detailDataMap,
                                   Map<String, IObjectDescribe> describeMap, boolean removeOrigValue) {
        processMaskFieldValue(user, masterData, detailDataMap, describeMap, true, removeOrigValue);
    }

    @Override
    public void removeMaskFieldValue(User user, IObjectData masterData, Map<String, List<IObjectData>> detailDataMap,
                                     Map<String, IObjectDescribe> describeMap) {
        processMaskFieldValue(user, masterData, detailDataMap, describeMap, false, true);
    }

    private void processMaskFieldValue(User user, IObjectData masterData, Map<String, List<IObjectData>> detailDataMap,
                                       Map<String, IObjectDescribe> describeMap, boolean isFill, boolean removeOrigValue) {
        if (user.isSupperAdmin()) {
            return;
        }
        Map<String, List<IFieldDescribe>> maskFieldMap = Maps.newHashMap();
        describeMap.forEach((k, v) -> {
            List<IFieldDescribe> maskFields = ObjectDescribeExt.of(v).getMaskFields();
            if (CollectionUtils.notEmpty(maskFields)) {
                maskFieldMap.put(k, maskFields);
            }
        });
        if (CollectionUtils.empty(maskFieldMap)) {
            return;
        }
        //下游人员直接掩码处理
        if (user.isOutUser()) {
            if (!isFill) {
                ObjectDataExt.of(masterData).remove(filterMaskFields4OutUser(user, masterData, maskFieldMap.get(masterData.getDescribeApiName())));
                CollectionUtils.nullToEmpty(detailDataMap).forEach((apiName, detailDataList) ->
                        detailDataList.forEach(data -> ObjectDataExt.of(data).remove(filterMaskFields4OutUser(user, data, maskFieldMap.get(apiName)))));
            } else {
                ObjectDataExt.of(masterData).fillMaskValue(filterMaskFields4OutUser(user, masterData, maskFieldMap.get(masterData.getDescribeApiName())), removeOrigValue, false);
                CollectionUtils.nullToEmpty(detailDataMap).forEach((apiName, detailDataList) ->
                        detailDataList.forEach(data -> ObjectDataExt.of(data).fillMaskValue(filterMaskFields4OutUser(user, data, maskFieldMap.get(apiName)), removeOrigValue, false)));
            }
            return;
        }
        Map<String, List<IFieldDescribe>> filteredMaskFieldMap;
        if (userRoleInfoService.isAdmin(user)) {
            Map<String, List<IFieldDescribe>> roleConfigMaskFields = Maps.newHashMap();
            maskFieldMap.forEach((apiName, maskFieldDescribe) -> {
                List<IFieldDescribe> maskFieldList = CollectionUtils.nullToEmpty(maskFieldDescribe)
                        .stream()
                        .filter(field -> FieldDescribeExt.of(field).haveRolesConfig())
                        .collect(Collectors.toList());
                if (CollectionUtils.notEmpty(maskFieldList)) {
                    roleConfigMaskFields.put(apiName, maskFieldList);
                }
            });
            if (CollectionUtils.empty(roleConfigMaskFields)) {
                return;
            }
            filteredMaskFieldMap = maskFieldRoleFilter(user, roleConfigMaskFields);
        } else {
            filteredMaskFieldMap = maskFieldRoleFilter(user, maskFieldMap);
        }
        Map<String, MainDeptInfo> mainDeptInfoMap = orgService.getMainDeptInfo(user.getTenantId(),
                user.getUserId(), masterData.getOwner());
        removeOrFillMaskFieldValue(user, Lists.newArrayList(masterData), filteredMaskFieldMap.get(masterData.getDescribeApiName()),
                isFill, removeOrigValue, false, mainDeptInfoMap);
        CollectionUtils.nullToEmpty(detailDataMap).forEach((apiName, detailDataList) ->
                removeOrFillMaskFieldValue(user, detailDataList, filteredMaskFieldMap.get(apiName), isFill, removeOrigValue,
                        false, mainDeptInfoMap));
    }

    private List<IFieldDescribe> filterMaskFields4OutUser(User user, IObjectData objectData, List<IFieldDescribe> fields) {
        return maskFieldLogicService.maskFieldRoleFilter(user, CollectionUtils.nullToEmpty(fields)).stream()
                .filter(field -> FieldDescribeExt.of(field).noOutOwnerMaskConfig(user, objectData))
                .collect(Collectors.toList());
    }

    private void removeOrFillMaskFieldValue(boolean isFill, boolean removeOrigValue, boolean overrideOrigValue, IObjectData data, IFieldDescribe field) {
        if (isFill) {
            ObjectDataExt.of(data).fillMaskValue(field, removeOrigValue, overrideOrigValue);
        } else {
            ObjectDataExt.of(data).remove(field.getApiName());
        }
    }

    private Map<String, List<IFieldDescribe>> maskFieldRoleFilter(User user, Map<String, List<IFieldDescribe>> maskFieldsMap) {
        Map<String, List<IFieldDescribe>> maskFieldMap = Maps.newHashMap();
        maskFieldsMap.forEach((k, v) -> maskFieldMap.put(k, maskFieldLogicService.maskFieldRoleFilter(user, v)));
        return maskFieldMap;
    }

    @Override
    public void fillDimensionFieldValue(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        try {
            if (user.isOutUser() || RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_735)) {
                return;
            }
            if (CollectionUtils.empty(dataList)) {
                return;
            }
            List<IFieldDescribe> dimensionFields = ObjectDescribeExt.of(objectDescribe)
                    .stream()
                    .filter(x -> Objects.equals(x.getType(), IFieldType.DIMENSION))
                    .collect(Collectors.toList());
            if (CollectionUtils.empty(dimensionFields)) {
                return;
            }
            fillDimensionFieldValue(user, dimensionFields, dataList);
        } catch (Exception e) {
            log.error("Error in fillDimensionFieldValue, ei:{}, object:{}", user.getTenantId(), objectDescribe.getApiName(), e);
        }
    }

    @Override
    public void fillDimensionFieldValue(User user, List<IFieldDescribe> dimensionFields, List<IObjectData> dataList) {
        if (user.isOutUser() || RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_735)) {
            return;
        }
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(dimensionFields)) {
            return;
        }
        List<String> dimensionFieldApiNames = dimensionFields.stream().map(IFieldDescribe::getApiName).collect(Collectors.toList());
        if (CollectionUtils.empty(dimensionFields)) {
            return;
        }
        Set<String> dimensionIds = Sets.newHashSet();
        for (IObjectData data : dataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            for (String dimensionFieldApiName : dimensionFieldApiNames) {
                List<String> dimensionValues = dataExt.getDimensionValues(dimensionFieldApiName);
                if (CollectionUtils.empty(dimensionValues)) {
                    continue;
                }
                dimensionIds.addAll(dimensionValues);
            }
        }
        List<DimensionInfo> dimensionInfos = dimensionLogicService.getDimensionInfoByIds(Lists.newArrayList(dimensionIds), user);
        if (CollectionUtils.empty(dimensionInfos)) {
            return;
        }
        for (IObjectData data : dataList) {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            dataExt.fillDimensionFields(dimensionFieldApiNames, dimensionInfos);
        }
    }

    @Override
    public void fillRichTextImageInfo(IObjectDescribe describe, List<IObjectData> dataList, User user) {
        if (!user.isOutUser()) {
            return;
        }

        List<HtmlRichText> richTextFields = ObjectDescribeExt.of(describe).getRichTextFields();
        if (CollectionUtils.empty(richTextFields)) {
            return;
        }

        String ea = gdsHandler.getEAByEI(user.getTenantId());
        CollectionUtils.nullToEmpty(dataList).forEach(data -> {
            richTextFields.forEach(field -> {
                Object value = data.get(field.getApiName());
                if (Objects.isNull(value)) {
                    return;
                }

                HtmlExt htmlExt = HtmlExt.of(String.valueOf(value));
                List<HtmlExt.HtmlElementExt> allImages = htmlExt.getAllImageElement();
                allImages.forEach(a -> a.convertImgPathForER(ea));
                data.set(field.getApiName(), htmlExt.body());
            });
        });
    }

    @Override
    public void fillCurrencyFieldInfo(User user, List<IFieldDescribe> fieldDescribes, Map<String, String> idCurrencyCodes, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        boolean openMultiCurrency = multiCurrencyLogicService.isOpenMultiCurrency(user);
        if (!openMultiCurrency) {
            return;
        }
        List<String> currencyCodes = Lists.newArrayList(idCurrencyCodes.values());
        if (CollectionUtils.empty(currencyCodes)) {
            return;
        }
        List<MtCurrency> currencyList = multiCurrencyLogicService.findCurrencyByCodes(user, currencyCodes);
        if (CollectionUtils.empty(currencyList)) {
            return;
        }
        MtCurrency functionalCurrency;
        Optional<MtCurrency> functionalCurrencyOptional = currencyList.stream()
                .filter(x -> BooleanUtils.isTrue(x.getIsFunctional()))
                .findFirst();
        if (functionalCurrencyOptional.isPresent()) {
            functionalCurrency = functionalCurrencyOptional.get();
        } else {
            functionalCurrency = multiCurrencyLogicService.findFunctionalCurrency(user);
            if (Objects.nonNull(functionalCurrency)) {
                currencyList.add(functionalCurrency);
            }
        }
        fieldDescribes.forEach(field -> dataList.forEach(data -> {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            String numberValue = dataExt.get(field.getApiName(), String.class);
            if (StringUtils.isBlank(numberValue)) {
                return;
            }
            String numberThousand = ObjectDataExt.formatNumberThousand(numberValue);
            String dataId;
            if (FieldDescribeExt.of(field).isQuoteField()) {
                String fieldApiName = QuoteExt.of((Quote) field).parseQuoteField().getKey();
                dataId = dataExt.get(fieldApiName, String.class);
            } else {
                dataId = dataExt.getId();
            }
            String currencyType = field.getCurrencyType();
            String currencyCode;
            if (Objects.equals(currencyType, MtCurrency.FUNCTIONAL_CURRENCY)) {
                currencyCode = functionalCurrency.getCurrencyCode();
            } else {
                currencyCode = idCurrencyCodes.get(dataId);
            }
            MtCurrency currencyData = currencyList.stream().filter(x -> Objects.equals(currencyCode, x.getCurrencyCode())).findFirst().orElse(null);
            numberThousand = MtCurrency.formatCurrencyNumber(numberThousand, currencyData);
            dataExt.set(FieldDescribeExt.getLookupNameByFieldName(field.getApiName()), numberThousand);
            String currencyFieldApiName = FieldDescribeExt.getCurrencyFieldName(field.getApiName());
            if (Objects.nonNull(currencyData)) {
                CurrencyInfo currencyInfo = CurrencyInfo.builder()
                        .fieldApiName(currencyFieldApiName)
                        .objectApiName(field.getDescribeApiName())
                        .prefix(currencyData.getRealCurrencyPrefix())
                        .suffix(currencyData.getRealCurrencySuffix())
                        .build();
                dataExt.set(currencyFieldApiName, currencyInfo);
            }
        }));
    }

    @Override
    public void fillCurrencyFieldInfo(IObjectDescribe describe, List<IObjectData> dataList, User user) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<IFieldDescribe> realTypeCurrencyFields = ObjectDescribeExt.of(describe).getFormulaOrCurrencyRealTypeCurrencyFields();
        if (CollectionUtils.empty(realTypeCurrencyFields)) {
            return;
        }
        Map<String, String> idCurrencyCodes = dataList.stream()
                .filter(x -> Objects.nonNull(ObjectDataExt.of(x).getCurrency()))
                .collect(Collectors.toMap(DBRecord::getId, y -> ObjectDataExt.of(y).getCurrency(), (x, y) -> x));
        fillCurrencyFieldInfo(user, realTypeCurrencyFields, idCurrencyCodes, dataList);
    }

    @Override
    public void fillExtendFieldInfo(IObjectDescribe describe, List<IObjectData> dataList, User user) {
        fillExtendFieldInfo(describe, dataList, user, false, false);
    }

    @Override
    public void fillExtendFieldInfo(IObjectDescribe describe, List<IObjectData> dataList, User user, boolean notFillQuote, boolean notFillMask) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        fillObjectDataWithRefObject(describe, dataList, user);
        parsePaymentObjOrderNames(describe, dataList, user, false);
        if (!notFillQuote) {
            Boolean oldValue = RequestUtil.setRecordCalculateLog(false);
            quoteValueService.fillQuoteFieldValue(user, dataList, describe, false);
            RequestUtil.setRecordCalculateLog(oldValue);
        }
        fillCountryAreaLabel(describe, dataList, user);
        fillUserInfo(describe, dataList, user);
        fillDepartmentInfo(describe, dataList, user);
        if (!notFillMask) {
            fillMaskFieldValue(user, dataList, describe);
        }
        fillDimensionFieldValue(user, describe, dataList);
        fillDataVisibilityRange(user, describe, dataList);
    }

    @Override
    public void fillSelectLabelInfo(IObjectDescribe describe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<IFieldDescribe> selectFields = ObjectDescribeExt.of(describe)
                .getFieldByTypes(Sets.newHashSet(IFieldType.SELECT_ONE, IFieldType.SELECT_MANY));
        dataList.forEach(objectData -> {
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(selectFields)) {
                selectFields.forEach(field -> {
                    if (FieldDescribeExt.of(field).isSelectOne()) {
                        objectData.set(field.getApiName() + "__r", ObjectDataExt.of(objectData).convertSelectOneData(field));
                    } else if (FieldDescribeExt.of(field).isSelectMany()) {
                        objectData.set(field.getApiName() + "__r", ObjectDataExt.of(objectData).convertSelectManyData(field));
                    }
                });
            }
        });
    }

    @Override
    public void validateDataType(IObjectDescribe describe, List<IObjectData> dataList, User user) {
        CollectionUtils.nullToEmpty(dataList).forEach(data -> {
            objectDataValidator.validateDataType(user, data, describe);
            objectDataValidator.validateMaxLength(user, data, describe);
        });

    }

    @Override
    public boolean fillDataOwnDeptAndOrgByOutUser(User user, IObjectDescribe describe, List<IObjectData> dataList) {
        if (!(user.isOutUser() && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUT_USER_FILL_DEPT_ORG, user.getTenantId()))) {
            return false;
        }
        for (String objectApiName : Lists.newArrayList(Utils.PARTNER_API_NAME, Utils.ACCOUNT_API_NAME)) {
            String objectId = enterpriseRelationService.getUpstreamMapperObjectId(user, objectApiName);
            if (Strings.isNullOrEmpty(objectId)) {
                continue;
            }
            IObjectData data = findObjectDataIgnoreAll(user, objectApiName, objectId);
            if (Objects.isNull(data)) {
                continue;
            }
            for (IObjectData objectData : dataList) {
                if (CollectionUtils.empty(objectData.getDataOwnDepartment())) {
                    objectData.setDataOwnDepartment(data.getDataOwnDepartment());
                }
                if (ObjectDescribeExt.of(describe).isOpenOrganization()
                        && CollectionUtils.empty(objectData.getDataOwnOrganization())) {
                    objectData.setDataOwnOrganization(data.getDataOwnOrganization());
                }
            }
            return true;
        }
        return true;
    }

    private IObjectData findObjectDataIgnoreAll(User user, String objectApiName, String objectId) {
        try {
            return metaDataFindService.findObjectDataIgnoreAll(user, objectId, objectApiName);
        } catch (ObjectDataNotFoundException e) {
            return null;
        }
    }

    @Override
    public void fillOutDataOwnDeptAndOrgByOutOwner(User user, IObjectDescribe objectDescribe, IObjectData objectData) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        if (describeExt.isSlaveObject()) {
            describeExt.getMasterDetailFieldDescribe()
                    .filter(field -> !Strings.isNullOrEmpty(objectData.get(field.getApiName(), String.class)))
                    .map(field -> metaDataFindService.findObjectData(user, objectData.get(field.getApiName(), String.class), field.getTargetApiName()))
                    .ifPresent(data -> {
                        Optional.ofNullable(ObjectDataExt.of(data).getOutDataOwnDepartmentId())
                                .ifPresent(objectDataExt::setOutDataOwnDepartmentId);
                        objectDataExt.setOutDataOwnDepartmentId(ObjectDataExt.of(data).getOutDataOwnDepartmentId());
                        if (describeExt.isExistOutDataOwnOrganization()) {
                            Optional.ofNullable(ObjectDataExt.of(data).getOutDataOwnOrganizationId())
                                    .ifPresent(objectDataExt::setOutDataOwnOrganizationId);
                        }
                    });
        } else {
            fillOutDataOwnDeptAndOrgByOutOwner(user, objectDescribe, Lists.newArrayList(objectData));
        }
    }

    @Override
    public void fillOutDataOwnDeptAndOrgByOutOwner(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        if (CollectionUtils.empty(dataList) || !describeExt.isExistOutDataOwnDepartment()
                || describeExt.isSlaveObject()) {
            return;
        }
        List<IObjectData> fillOutOwnDepOrOrgDataList = dataList.stream()
                .filter(this::needFillOutOwnDepOrOrgData)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(fillOutOwnDepOrOrgDataList)) {
            return;
        }
        List<OuterAccountVo> outerAccountVos = fillOutOwnDepOrOrgDataList.stream()
                .map(this::buildOuterAccountVo)
                .collect(Collectors.toList());
        List<PublicEmployeeObjOwnDepOrOrgData> outerOwnDepOrOrgList = outerOrganizationService.batchGetOuterOwnDepOrOrg(user, outerAccountVos);
        if (CollectionUtils.empty(outerOwnDepOrOrgList)) {
            return;
        }
        boolean existOutDataOwnOrganization = describeExt.isExistOutDataOwnOrganization();
        Map<String, PublicEmployeeObjOwnDepOrOrgData> outOwnDepOrOrgDataMap = outerOwnDepOrOrgList.stream()
                .collect(Collectors.toMap(data -> String.valueOf(data.getOuterUid()), Function.identity(), (x, y) -> x));
        fillOutOwnDepOrOrgDataList.forEach(objectData -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            if (!objectDataExt.getOutOwnerId().isPresent()) {
                return;
            }
            PublicEmployeeObjOwnDepOrOrgData outerOwnDepOrOrg = outOwnDepOrOrgDataMap.get(objectDataExt.getOutOwnerId().get());
            ErDepartmentSimpleData erDepData = outerOwnDepOrOrg.getErDepData();
            if (Objects.nonNull(erDepData)) {
                objectDataExt.setOutDataOwnDepartmentId(erDepData.getId());
            }
            ErDepartmentSimpleData erOrgData = outerOwnDepOrOrg.getErOrgData();
            if (existOutDataOwnOrganization && Objects.nonNull(erOrgData)) {
                objectDataExt.setOutDataOwnOrganizationId(erOrgData.getId());
            }
        });
    }

    @Override
    public void fillDataVisibilityRange(User user, IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        fillDataVisibilityRange(user, objectDescribe, dataList, false);
    }

    @Override
    public Map<String, ConnectedEnterpriseDTO.Helper> fillDataVisibilityRange(User user, IObjectDescribe objectDescribe,
                                                                              List<IObjectData> dataList, boolean extractExtendInfo) {
        Map<String, ConnectedEnterpriseDTO.Helper> resultMap = Maps.newHashMap();
        if (CollectionUtils.empty(dataList)) {
            return resultMap;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        List<DataVisibilityRange> dataVisibilityRangeFields = describeExt.getDataVisibilityRangeFields();
        if (CollectionUtils.empty(dataVisibilityRangeFields)) {
            return resultMap;
        }
        Set<String> tenantIds = Sets.newHashSet();
        Set<String> tenantGroupIds = Sets.newHashSet();

        dataVisibilityRangeFields.forEach(field -> {
            dataList.forEach(objectData -> {
                ConnectedEnterpriseDTO.Helper.fromData(objectData, field).forEach(it -> {
                    if (it.tenant()) {
                        tenantIds.add(it.getId());
                        return;
                    }
                    if (it.tenantGroup()) {
                        tenantGroupIds.add(it.getId());
                    }
                });
            });

        });

        Map<String, SimpleEnterpriseData> enterpriseInfoMap = getEnterpriseInfoMap(tenantIds);

        String upstreamTenantId = getUpstreamTenantId(user, objectDescribe);
        Map<String, QueryTenantGroupByIds.TenantGroupInfo> groupInfoMap = orgService
                .getTenantGroupInfoByIds(upstreamTenantId, user.SUPPER_ADMIN_USER_ID, Lists.newArrayList(tenantGroupIds)).stream()
                .collect(Collectors.toMap(QueryTenantGroupByIds.TenantGroupInfo::getId, Function.identity()));
        if (CollectionUtils.empty(enterpriseInfoMap) && CollectionUtils.empty(groupInfoMap)
                && !tenantIds.contains(IObjectData.PUBLIC_DATA_DOWNSTREAM_TENANT_ID)) {
            return resultMap;
        }

        dataVisibilityRangeFields.forEach(field -> {
            dataList.forEach(objectData -> {
                List<ConnectedEnterpriseDTO> enterpriseDTOList = ConnectedEnterpriseDTO.Helper.fromData(objectData, field);
                if (CollectionUtils.empty(enterpriseDTOList)) {
                    return;
                }
                List<ConnectedEnterpriseDTO.Helper> helperList = ConnectedEnterpriseDTO.Helper.fromEnterprise(enterpriseDTOList, enterpriseInfoMap, groupInfoMap);
                List<Map<String, Object>> mapList = Lists.newArrayList();
                helperList.forEach(it -> {
                    resultMap.putIfAbsent(it.getId(), it);
                    if (!extractExtendInfo) {
                        mapList.add(it.toMap());
                    }
                });
                if (CollectionUtils.empty(mapList)) {
                    return;
                }
                objectData.set(field.getApiName() + FieldDescribeExt.EMPLOYEE_NAME_SUFFIX, mapList);
            });
        });
        return resultMap;
    }

    private String getUpstreamTenantId(User user, IObjectDescribe objectDescribe) {
        if (objectDescribe.isPublicObject()) {
            return objectDescribe.getUpstreamTenantId();
        }
        return user.getTenantId();
    }

    private Map<String, SimpleEnterpriseData> getEnterpriseInfoMap(Set<String> tenantIds) {
        if (CollectionUtils.empty(tenantIds)) {
            return Collections.emptyMap();
        }
        List<Integer> ids = tenantIds.stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        BatchGetSimpleEnterpriseDataArg arg = new BatchGetSimpleEnterpriseDataArg();
        arg.setEnterpriseIds(ids);
        BatchGetSimpleEnterpriseDataResult dataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(arg);
        return dataResult.getSimpleEnterpriseList().stream()
                .collect(Collectors.toMap(it -> String.valueOf(it.getEnterpriseId()), Function.identity()));
    }

    private boolean needFillOutOwnDepOrOrgData(IObjectData objectData) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        return objectDataExt.getOutOwnerId().isPresent() && StringUtils.isAllEmpty(objectDataExt.getOutDataOwnDepartmentId(), objectDataExt.getOutDataOwnOrganizationId());
    }

    private OuterAccountVo buildOuterAccountVo(IObjectData objectData) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        OuterAccountVo outerAccountVo = new OuterAccountVo();
        outerAccountVo.setOuterUid(Long.valueOf(objectDataExt.getOutOwnerId().orElse("0")));
        String outTenantId = objectDataExt.getOutTenantId();
        if (StringUtils.isNotEmpty(outTenantId)) {
            outerAccountVo.setOuterTenantId(Long.valueOf(outTenantId));
        }
        return outerAccountVo;
    }

    public Map<String, List<TeamMemberInfo.Member>> getTeamMemberInfos(User user, List<IObjectData> oldDataList, List<IObjectData> newDataList) {
        if (CollectionUtils.empty(newDataList)) {
            return Maps.newHashMap();
        }
        //获取所有的团队成员的id
        Map<String, List<String>> memberIds = Maps.newHashMap();
        getTeamMemberIds(oldDataList, memberIds);
        getTeamMemberIds(newDataList, memberIds);
        if (CollectionUtils.empty(memberIds) && CollectionUtils.empty(memberIds.values())) {
            return Maps.newHashMap();
        }

        Map<String, List<TeamMemberInfo.Member>> memberInfos = Maps.newHashMap();
        //获取相关团队成员id，去重
        memberIds.forEach((type, ids) -> {
            if (CollectionUtils.empty(ids)) {
                return;
            }
            List<String> uniqueMemberIds = ids.stream().distinct().collect(Collectors.toList());
            if (TeamMember.MemberType.EMPLOYEE.getValue().equals(type)) {
                List<TeamMemberInfo.Member> member = Lists.newArrayList();
                List<UserInfo> userInfos = orgService.getUserNameByIds(user.getTenantId(), user.getUserId(), uniqueMemberIds);
                userInfos.forEach(userInfo -> member.add(TeamMemberInfo.Member.builder().id(userInfo.getId()).name(userInfo.getName()).build()));
                memberInfos.put(TeamMember.MemberType.EMPLOYEE.getValue(), member);
                return;
            }
            if (TeamMember.MemberType.GROUP.getValue().equals(type)) {
                List<TeamMemberInfo.Member> member = Lists.newArrayList();
                List<QueryGroupByIds.UserGroupInfo> groupInfos = orgService.getGroupInfoByIds(user.getTenantId(), user.getUserId(), uniqueMemberIds);
                groupInfos.forEach(groupInfo -> member.add(TeamMemberInfo.Member.builder().id(groupInfo.getId()).name(groupInfo.getName()).build()));
                memberInfos.put(TeamMember.MemberType.GROUP.getValue(), member);
                return;
            }
            if (TeamMember.MemberType.DEPARTMENT.getValue().equals(type)) {
                List<TeamMemberInfo.Member> member = Lists.newArrayList();
                List<QueryDeptInfoByDeptIds.DeptInfo> deptInfoIds = orgService.getDeptInfoNameByIds(user.getTenantId(), user.getUserId(), uniqueMemberIds);
                deptInfoIds.forEach(userInfo -> member.add(TeamMemberInfo.Member.builder().id(userInfo.getDeptId()).name(userInfo.getDeptName()).build()));
                memberInfos.put(TeamMember.MemberType.DEPARTMENT.getValue(), member);
                return;
            }
            if (TeamMember.MemberType.ROLE.getValue().equals(type)) {
                List<TeamMemberInfo.Member> member = Lists.newArrayList();
                List<GetUserRoleInfo.RoleInfo> roleInfos = userRoleInfoService.queryRoleInfoByRoleCode(user, uniqueMemberIds);
                roleInfos.forEach(roleInfo -> member.add(TeamMemberInfo.Member.builder().id(roleInfo.getRoleCode()).name(roleInfo.getRoleName()).build()));
                memberInfos.put(TeamMember.MemberType.ROLE.getValue(), member);
                return;
            }
            if (TeamMember.MemberType.OUT_TENANT_GROUP.getValue().equals(type)) {
                List<TeamMemberInfo.Member> member = Lists.newArrayList();
                List<QueryTenantGroupByIds.TenantGroupInfo> tenantGroupInfos = orgService.getTenantGroupInfoByIds(user.getTenantId(), user.getUserIdOrOutUserIdIfOutUser(), uniqueMemberIds);
                tenantGroupInfos.forEach(tenantGroupInfo -> member.add(TeamMemberInfo.Member.builder().id(tenantGroupInfo.getId()).name(tenantGroupInfo.getName()).build()));
                memberInfos.put(TeamMember.MemberType.OUT_TENANT_GROUP.getValue(), member);
            }
        });
        return memberInfos;
    }

    @Override
    public void fillCountWithMultiRegion(User user, IObjectDescribe objectDescribe, List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList) || Objects.isNull(objectDescribe) || Objects.isNull(user)) {
            return;
        }
        List<Count> fields = ObjectDescribeExt.of(objectDescribe).getCountFields();
        if (CollectionUtils.empty(fields)) {
            return;
        }
        for (IObjectData objectData : objectDataList) {
            for (Count field : fields) {
                Object value = objectData.get(field.getApiName());
                ObjectDataExt.fillCountMultiRegionWithCurrency(value, objectData, field);
            }
        }
    }

    private boolean checkIsOutUser(String userId) {
        return StringUtils.length(userId) > 8;
    }

    private void getTeamMemberIds(List<IObjectData> dataList, Map<String, List<String>> teamMemberIds) {
        dataList.forEach(data -> {
            List<TeamMemberInfoPoJo> teamMemberInfoPoJos = ObjectDataExt.of(data).getRelevantTeamFromObjectData();
            teamMemberInfoPoJos.forEach(teamMemberInfoPoJo -> {
                List<String> teamMemberEmployee = teamMemberInfoPoJo.getTeamMemberEmployee();
                String teamMemberType = TeamMember.MemberType.of(teamMemberInfoPoJo.getTeamMemberType()).getValue();
                List<String> oldTeamMemberIds = teamMemberIds.getOrDefault(teamMemberType, Lists.newArrayList());
                oldTeamMemberIds.addAll(teamMemberEmployee);
                teamMemberIds.put(teamMemberType, oldTeamMemberIds);
            });
        });
    }

    @Override
    public void setupTeamInterconnectedDepartments(User user, IObjectDescribe objectDescribe, List<IObjectData> objectDataList) {
        // 检查灰度配置，只需检查一次
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()
                || !erOrgManagementControlService.interconnectDeptTeamMemberGray(user.getTenantId())) {
            return;
        }
        // 检查当前对象是否开启了相关团队
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), objectDescribe);
        if (BooleanUtils.isFalse(optionalFeaturesSwitch.getIsRelatedTeamEnabled())) {
            return;
        }
        // 收集所有非空的部门ID
        getTeamInterconnectedDepartmentData(user, objectDataList);
    }

    @Override
    public List<IObjectData> setupTeamInterconnectedDepartmentsByIds(User user, IObjectDescribe objectDescribe, List<String> objectDataIds) {
        // 检查灰度配置，只需检查一次
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()
                || !erOrgManagementControlService.interconnectDeptTeamMemberGray(user.getTenantId())) {
            return Collections.emptyList();
        }
        // 检查当前对象是否开启了相关团队
        OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), objectDescribe);
        if (BooleanUtils.isFalse(optionalFeaturesSwitch.getIsRelatedTeamEnabled())) {
            return Collections.emptyList();
        }
        List<IObjectData> objectDataList = metaDataFindService.findObjectDataByIds(user.getTenantId(), objectDataIds, objectDescribe.getApiName());
        return getTeamInterconnectedDepartmentData(user, objectDataList);
    }

    private List<IObjectData> getTeamInterconnectedDepartmentData(User user, List<IObjectData> objectDataList) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.TEAM_INTERCONNECTED_DEPARTMENT_BY_DATA_OWNER_GRAY, user.getTenantId())) {
            return getTeamInterconnectedDepartmentByDataOwner(user, objectDataList);
        }
        Set<String> deptIds = objectDataList.stream()
                .map(IObjectData::getDataOwnDepartment)
                .filter(CollectionUtils::notEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        // 一次性查询所有部门的互联部门ID映射
        Map<String, OuterDepartmentInfo> deptIdToErDeptIdMap = outerOrganizationService.findErDepartmentIdsByDeptIds(user, deptIds, DeptStatusEnum.ALL);

        // 收集所有被修改的对象数据
        List<IObjectData> modifiedDataList = Lists.newArrayList();
        // 对每个 objectData 根据其部门ID查找对应的互联部门
        for (IObjectData objectData : objectDataList) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            String dataOwnDepartmentId = objectDataExt.getDataOwnDepartmentId();
            OuterDepartmentInfo departmentInfo = deptIdToErDeptIdMap.get(dataOwnDepartmentId);
            if (Objects.isNull(departmentInfo)) {
                if (objectDataExt.removeInterconnectDepartmentTeamMembers()) {
                    modifiedDataList.add(objectData);
                }
                continue;
            }

            List<TeamMember> teamMembers = Lists.newArrayList();
            teamMembers.add(new TeamMember(departmentInfo.getOuterDepartmentId(), TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READANDWRITE,
                    DataPrivilegeService.ALL_OUT_TENANT_ID, TeamMember.MemberType.INTERCONNECT_DEPARTMENT));
            if (objectDataExt.addTeamMembers(teamMembers)) {
                modifiedDataList.add(objectData);
            }
        }
        return modifiedDataList;
    }

    private List<IObjectData> getTeamInterconnectedDepartmentByDataOwner(User user, List<IObjectData> objectDataList) {
        Set<String> userIds = objectDataList.stream()
                .map(IObjectData::getOwner)
                .filter(CollectionUtils::notEmpty)
                .flatMap(Collection::stream)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        Map<String, String> erDepartmentIdsByUserIds = outerOrganizationService.findErDepartmentIdsByUserIds(user, userIds);

        // 收集所有被修改的对象数据
        List<IObjectData> modifiedDataList = Lists.newArrayList();
        // 对每个 objectData 根据其负责人ID查找对应的互联部门
        for (IObjectData objectData : objectDataList) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
            String ownerId = objectDataExt.getOwnerId().orElse(null);
            String erDepartmentId = erDepartmentIdsByUserIds.get(ownerId);
            if (StringUtils.isEmpty(erDepartmentId)) {
                if (objectDataExt.removeInterconnectDepartmentTeamMembers()) {
                    modifiedDataList.add(objectData);
                }
                continue;
            }

            List<TeamMember> teamMembers = Lists.newArrayList();
            teamMembers.add(new TeamMember(erDepartmentId, TeamMember.Role.NORMAL_STAFF, TeamMember.Permission.READANDWRITE,
                    DataPrivilegeService.ALL_OUT_TENANT_ID, TeamMember.MemberType.INTERCONNECT_DEPARTMENT));
            if (objectDataExt.addTeamMembers(teamMembers)) {
                modifiedDataList.add(objectData);
            }
        }
        return modifiedDataList;
    }

    @Override
    public void fillCountryAreaLabels4Export(User user, IObjectDescribe masterDescribe, List<IObjectData> masterDataList) {
        // 调用主从对象的国家地区字段标签填充方法，传入空的从对象映射
        fillMasterDetailCountryAreaLabels(user, masterDescribe, masterDataList, Collections.emptyMap(), Collections.emptyMap(), true);
    }

    @Override
    public void fillMasterDetailCountryAreaLabels(User user, IObjectDescribe masterDescribe, List<IObjectData> masterDataList,
                                                  Map<String, IObjectDescribe> detailDescribeMap, Map<String, List<IObjectData>> detailObjectData) {
        fillMasterDetailCountryAreaLabels(user, masterDescribe, masterDataList, detailDescribeMap, detailObjectData, false);
    }

    @Override
    public void fillMasterDetailCountryAreaLabels(User user, IObjectDescribe masterDescribe, List<IObjectData> masterDataList,
                                                  Map<String, IObjectDescribe> detailDescribeMap, Map<String, List<IObjectData>> detailObjectData,
                                                  boolean fillSourceFieldValue) {
        if (CollectionUtils.empty(masterDataList) || Objects.isNull(user) || Objects.isNull(masterDescribe)) {
            return;
        }

        // 收集所有国家地区代码
        Set<String> codes = Sets.newHashSet();
        Map<String, List<IFieldDescribe>> apiNameToFieldsMap = Maps.newHashMap();

        // 获取并缓存主对象的国家地区字段
        List<IFieldDescribe> masterCountryAreaFields = ObjectDescribeExt.of(masterDescribe).getCountryAreaFields();
        if (CollectionUtils.notEmpty(masterCountryAreaFields)) {
            apiNameToFieldsMap.put(masterDescribe.getApiName(), masterCountryAreaFields);
            // 从主对象收集代码
            collectCountryAreaCodes(masterDataList, masterCountryAreaFields, codes);
        }

        // 从从对象收集代码并缓存字段
        if (CollectionUtils.notEmpty(detailObjectData) && CollectionUtils.notEmpty(detailDescribeMap)) {
            detailObjectData.forEach((detailApiName, detailDataList) -> {
                if (CollectionUtils.empty(detailDataList) || !detailDescribeMap.containsKey(detailApiName)) {
                    return;
                }
                // 对每个从对象的数据收集国家地区代码，并缓存字段列表
                List<IFieldDescribe> detailCountryAreaFields = ObjectDescribeExt.of(detailDescribeMap.get(detailApiName)).getCountryAreaFields();
                if (CollectionUtils.notEmpty(detailCountryAreaFields)) {
                    apiNameToFieldsMap.put(detailApiName, detailCountryAreaFields);
                    collectCountryAreaCodes(detailDataList, detailCountryAreaFields, codes);
                }
            });
        }

        if (CollectionUtils.empty(codes)) {
            return;
        }

        // 批量查询code到label的映射
        Map<String, String> code2LabelMap = countryAreaService.batchQueryLabelsByCodes(
                RequestUtil.getCurrentLang().getValue(), user.getTenantId(), codes);

        if (CollectionUtils.empty(code2LabelMap)) {
            return;
        }

        // 填充主对象数据的__r字段
        if (apiNameToFieldsMap.containsKey(masterDescribe.getApiName())) {
            batchFillFieldLabelToData(masterDataList, apiNameToFieldsMap.get(masterDescribe.getApiName()), code2LabelMap, fillSourceFieldValue);
        }

        // 填充从对象数据的__r字段
        if (CollectionUtils.notEmpty(detailObjectData)) {
            detailObjectData.forEach((detailApiName, detailDataList) -> {
                if (CollectionUtils.notEmpty(detailDataList) && apiNameToFieldsMap.containsKey(detailApiName)) {
                    batchFillFieldLabelToData(detailDataList, apiNameToFieldsMap.get(detailApiName), code2LabelMap, fillSourceFieldValue);
                }
            });
        }
    }

    /**
     * 收集国家地区代码
     *
     * @param dataList          数据列表
     * @param countryAreaFields 国家地区字段列表
     * @param codes             代码集合
     */
    private void collectCountryAreaCodes(List<IObjectData> dataList, List<IFieldDescribe> countryAreaFields, Set<String> codes) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(countryAreaFields)) {
            return;
        }

        for (IObjectData data : dataList) {
            for (IFieldDescribe field : countryAreaFields) {
                String code = data.get(field.getApiName(), String.class);
                if (!Strings.isNullOrEmpty(code)) {
                    codes.add(code);
                }
            }
        }
    }

    /**
     * 批量填充字段label到数据的__r字段
     *
     * @param dataList             数据列表
     * @param fieldList            字段列表
     * @param code2LabelMap        code到label的映射
     * @param fillSourceFieldValue 填充源字段值中
     */
    private void batchFillFieldLabelToData(List<IObjectData> dataList, List<IFieldDescribe> fieldList, Map<String, String> code2LabelMap, boolean fillSourceFieldValue) {
        if (CollectionUtils.empty(dataList) || CollectionUtils.empty(fieldList) || CollectionUtils.empty(code2LabelMap)) {
            return;
        }
        fieldList.forEach(field -> dataList.forEach(data -> {
            String code = data.get(field.getApiName(), String.class);
            if (!Strings.isNullOrEmpty(code)) {
                String label = code2LabelMap.getOrDefault(code, code);
                if (fillSourceFieldValue) {
                    data.set(field.getApiName(), label);
                } else {
                    data.set(FieldDescribeExt.getLookupNameByFieldName(field.getApiName()), label);
                }
            }
        }));
    }

    @Data
    private class ProductCategoryPackage {
        private boolean init;
        private List<ProductAllCategoriesModel.CategoryPojo> productAllCategories;
    }
}
