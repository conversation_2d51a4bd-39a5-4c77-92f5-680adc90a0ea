package com.facishare.paas.appframework.metadata.dto.userExtension;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

public interface BatchRefreshHomePageLayoutBiI18nKey {

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    class Arg implements Serializable {
        List<String> tenantIdList;
        String routeTenantId;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @Data
    class Result implements Serializable {
        Set<String> failTenantIdList;
    }
}
