package com.facishare.paas.appframework.metadata.processor;

import com.facishare.fsi.proxy.exception.FsiClientException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/8/25
 */
@Slf4j
public abstract class AbstractFileFieldProcessor implements DataProcessor<IFieldDescribe, IObjectData> {

    protected static final String PATH = "path";

    protected static final String EXT = "ext";

    protected static final String TEMP_PREFIX = "TN_";

    protected static final String TEMP_CDN_PREFIX = "TC_";

    protected static final String EXT_SEPARATOR = ".";

    public static final String FILE_NAME = "filename";

    protected abstract String getFinalFilePath(String tenantId, String userId, String tempFilePath, String fileName, String fileExt);

    protected void processFileData(Map fileData, String filePath) {
        fileData.put(PATH, filePath);
    }

    @Override
    public void process(IFieldDescribe fieldDescribe, IObjectData data) {
        ObjectDataExt.of(data).validateFileFieldDataType(fieldDescribe);

        List<Map> fileDataList = (List<Map>) data.get(fieldDescribe.getApiName());
        for (Map fileData : fileDataList) {
            String filePath = (String) fileData.get(PATH);
            if (!isTempFilePath(filePath)) {
                continue;
            }
            String fileExt = (String) fileData.get(EXT);
            String fileName = (String) fileData.get(FILE_NAME);
            String finalFilePath;
            try {
                finalFilePath = getFinalFilePath(data.getTenantId(), User.SUPPER_ADMIN_USER_ID, filePath, fileName, fileExt);
            } catch (FsiClientException e) {
                if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("10007")) {
                    log.warn("file has already expire,ei:{},api_name:{}", data.getTenantId(), fieldDescribe.getApiName(), e);
                    throw new ValidateException(I18NExt.getOrDefault(I18NKey.FILE_ALREADY_EXPIRE, "[{0}]字段文件已过期，请重新上传文件。", fieldDescribe.getLabel()));// ignoreI18n
                } else {
                    throw e;
                }
            }
           /* if (StringUtils.isNotBlank(finalFilePath) && StringUtils.isNotBlank(fileExt) && !finalFilePath.endsWith(fileExt)) {
                finalFilePath = finalFilePath + EXT_SEPARATOR + fileExt;
            }*/
            processFileData(fileData, finalFilePath);
        }
    }

    protected boolean isTempFilePath(String filePath) {
        return StringUtils.startsWith(filePath, TEMP_PREFIX) || StringUtils.startsWith(filePath, TEMP_CDN_PREFIX);
    }

}
