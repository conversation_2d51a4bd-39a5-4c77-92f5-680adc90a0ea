package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.SelectMany;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2019/07/26
 */
@Component
public class SelectManyImportFieldDataConverter extends BaseImportFieldDataConverter {
    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.SELECT_MANY);
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user) {
        List<String> result = Lists.newArrayList();
        String valueStr = getStringValue(objectData, fieldDescribe.getApiName());
        if (Strings.isNullOrEmpty(valueStr)) {
            return ConvertResult.buildSuccess(null);
        }
        SelectMany selectMany = (SelectMany) fieldDescribe;
        String[] array = valueStr.split("\\|");
        List<String> selectManyValues = Lists.newArrayList(array);
        List<ISelectOption> options = selectMany.getSelectOptions();
        if (array.length == 0 || CollectionUtils.empty(options)) {
            return ConvertResult.buildSuccess(null);
        }

        for (ISelectOption option : options) {
            if (selectManyValues.contains(option.getLabel())) {
                //隐藏选项不能导入  970去掉检验
                /*if (option.isNotUsable()) {
                    return ConvertResult.buildError(I18N.text(I18NKey.OPTION_NOT_USED_CAN_NOT_IMPORT, option.getLabel()));
                }*/
                if (!option.getIsRequired() && !SelectMany.OPTION_OTHER_VALUE.equals(option.getValue())) {
                    result.add(option.getValue());
                }
            }
        }

        //如果是支持其他选项，且没有匹配的选项，则把值放到其他中
        boolean hasOther = false;
        String otherLabel = I18N.text(I18NKey.OTHER);
        for (ISelectOption option : options) {
            if (Objects.equals(option.getValue(), SelectMany.OPTION_OTHER_VALUE) /*&& Objects.equals(option.isNotUsable(), false)*/) {
                hasOther = true;
                otherLabel = option.getLabel();
                break;
            }
        }
        String finalOtherLabel = otherLabel;
        if (hasOther) {
            List<String> optionsLabelNoOther = options.stream().map(ISelectOption::getLabel)
                    .filter(label -> !StringUtils.equals(label, finalOtherLabel))
                    .collect(Collectors.toList());
            List<String> otherLabelList = selectManyValues.stream().filter(x -> !optionsLabelNoOther.contains(x)).collect(Collectors.toList());
            if (otherLabelList.size() > 1) {
                return ConvertResult.buildError(I18N.text(I18NKey.OTHER_OPTION_ONLY_EXIST_ONE, fieldDescribe.getLabel(), otherLabel));
            }
            if (CollectionUtils.notEmpty(otherLabelList)) {
                selectManyValues.remove(otherLabelList.get(0));
                String value = otherLabelList.get(0);
                Pattern compile = Pattern.compile(String.format("(%s[:：]+?)", otherLabel));
                Matcher matcher = compile.matcher(value);
                int end = 0;
                while (matcher.find()) {
                    end = matcher.end();
                }
                if (end != 0) {
                    String otherName = value.substring(end);
                    if (selectMany.getOptionOtherIsRequired() && Strings.isNullOrEmpty(otherName)) {
                        return ConvertResult.buildError(I18N.text(I18NKey.OTHER_OPTION_CAN_NOT_IS_NULL, fieldDescribe.getLabel(), otherLabel));
                    }
                    otherLabelList.set(0, value.substring(0, end - 1));
                    result.add(SelectMany.OPTION_OTHER_VALUE);
                    objectData.set(FieldDescribeExt.of(fieldDescribe).getFieldExtendName(), otherName);
                } else {
                    if (selectMany.getOptionOtherIsRequired()) {
                        if (StringUtils.isEmpty(value) || otherLabel.equals(value)) {
                            return ConvertResult.buildError(I18N.text(I18NKey.OTHER_OPTION_CAN_NOT_IS_NULL, fieldDescribe.getLabel(), otherLabel));
                        }
                    }
                    if (otherLabel.equals(value)) {
                        otherLabelList.set(0, otherLabel);
                        result.add(SelectMany.OPTION_OTHER_VALUE);
                    } else {
                        otherLabelList.set(0, otherLabel);
                        result.add(SelectMany.OPTION_OTHER_VALUE);
                        objectData.set(FieldDescribeExt.of(fieldDescribe).getFieldExtendName(), value);
                    }
                }
                selectManyValues.add(otherLabelList.get(0));
            }
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (String item : selectManyValues) {
            boolean isExist = false;
            for (ISelectOption option : options) {
               /* if (Boolean.TRUE.equals(option.isNotUsable())) {
                    continue;
                }*/
                stringBuilder.append("[").append(option.getLabel()).append("]");
                if (option.getLabel().equals(item)) {
                    isExist = true;
                    break;
                }
            }

            if (!isExist) {
                return ConvertResult.buildError(I18N.text(I18NKey.MUST_WITHIN_LIMITS, fieldDescribe.getLabel(), stringBuilder.toString()));
            }
            stringBuilder.delete(0, stringBuilder.length());
        }

        return ConvertResult.buildSuccess(result);
    }
}
