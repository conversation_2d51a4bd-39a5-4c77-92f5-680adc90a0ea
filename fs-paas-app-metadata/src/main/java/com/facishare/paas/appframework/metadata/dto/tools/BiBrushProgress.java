package com.facishare.paas.appframework.metadata.dto.tools;

import lombok.Builder;
import lombok.Data;

import java.util.Map;
import java.util.Set;

public interface BiBrushProgress {
    @Data
    class Arg {
        private int action;
    }

    @Data
    @Builder
    class Result {
        private Long totalCount;
        private Long successCount;
        private Long failureCount;
        private Map<String, Long> successCountByEnv;
        private Map<String, Long> failCountByEnv;
        private Map<String, Set<String>> successEnterpriseIds;
        private Map<String, Set<String>> failureEnterpriseIds;
    }
}
