package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.metadata.dto.userExtension.BatchRefreshHomePageLayoutBiI18nKey;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "userExtensionResource", desc = "查询userExtension服务")
public interface UserExtensionProxy {

    @POST(value = "refresh/batchRefreshHomePageLayoutBiI18nKey")
    BatchRefreshHomePageLayoutBiI18nKey.Result batchRefreshHomePageLayoutBiI18nKey(@HeaderMap Map<String, String> headers,
                                                                                   @Body BatchRefreshHomePageLayoutBiI18nKey.Arg arg);


}
