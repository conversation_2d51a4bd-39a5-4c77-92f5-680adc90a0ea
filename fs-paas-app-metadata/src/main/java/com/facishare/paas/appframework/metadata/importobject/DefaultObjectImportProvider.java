package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectImportConfig;
import com.facishare.paas.appframework.metadata.UniqueRuleExt;
import com.facishare.paas.appframework.metadata.importobject.ImportObject.RuleField;
import com.facishare.paas.appframework.privilege.EnterpriseRelationServiceProxy;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.facishare.paas.appframework.metadata.ObjectImportConfig.SUPPORT_IMPORT_ID_API_NAME;
import static com.facishare.paas.metadata.api.describe.IObjectDescribe.DEFINE_TYPE_CUSTOM;

/**
 * create by zhaoju on 2019/03/26
 */
@Component("defaultObjectImportProvider")
public class DefaultObjectImportProvider implements ObjectImportInitProvider {
    /**
     * 不支持新建导入
     */
    public static final int UNSUPPORT_INSERT_IMPORT = 3;
    /**
     * 不支持更新导入
     */
    public static final int UNSUPPORT_UPDATE_IMPORT = 0;
    public static final String INSERT_IMPORT = "insert_import";
    public static final String UPDATE_IMPORT = "update_import";

    @Autowired
    private EnterpriseRelationServiceProxy enterpriseRelationService;

    @Override
    public String getObjectCode() {
        return null;
    }

    @Override
    public Optional<ImportObject> getImportObject(IObjectDescribe describe, IUniqueRule uniqueRule) {
        if (Objects.isNull(describe) || ObjectDescribeExt.of(describe).isChangeOrderObject()) {
            return Optional.empty();
        }
        IObjectDescribe objectDescribe = describe.copy();
        generateObjAllObj(objectDescribe);

        ImportObject result = ImportObject.builder()
                .objectCode(getObjectCode(objectDescribe))
                .objectApiName(describe.getApiName())
                .objectName(getObjectName(objectDescribe))
                .unionImportApiNameList(Lists.newArrayList())
                .duplicateJudgmentType(getDuplicateJudgmentType(objectDescribe))
                .isOpenWorkFlow(getOpenWorkFlow(objectDescribe))
                .isApprovalFlow(getApprovalFlow(objectDescribe))
                .isTextureImport(getTextureImport(objectDescribe))
                .primaryAttributeFieldList(getPrimaryAttributeFieldList(objectDescribe))
                .isNoBatch(getNoBatch())
                .importType(importTypeHelper(uniqueRule, objectDescribe))
                .matchingTypes(getMatchingTypeMap(objectDescribe, uniqueRule))
                .isNotSupportSaleEvent(getIsNotSupportSaleEvent(objectDescribe))
                .isVerifyEnterprise(getIsVerifyEnterprise(objectDescribe))
                .isBackFillIndustrialAndCommercialInfo(getIsBackFillIndustrialAndCommercialInfo(objectDescribe))
                .describeType(describe.getDefineType())
                .checkOutOwner(getIsCheckOutOwner(objectDescribe))
                .removeOutTeamMember(getIsRemoveOutTeamMember(objectDescribe))
                .updateOwner(getIsUpdateOwner(objectDescribe))
                .build();
        return Optional.of(result);
    }

    protected boolean getTextureImport(IObjectDescribe objectDescribe) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SUPPORT_TEXTURE_IMPORT_GRAY_EI, objectDescribe.getTenantId());
    }

    private ImportType importTypeHelper(IUniqueRule uniqueRule, IObjectDescribe objectDescribe) {
        ImportType importType = getImportType(objectDescribe, uniqueRule);
        if (ObjectDescribeExt.of(objectDescribe).enabledChangeOrder()) {
            if (ImportType.UNSUPPORT_INSERT_IMPORT == importType) {
                return null;
            }
            return ImportType.UNSUPPORT_UPDATE_IMPORT;
        }
        return importType;
    }

    protected boolean getIsUpdateOwner(IObjectDescribe objectDescribe) {
        Optional<User> user = Optional.ofNullable(RequestContextManager.getContext())
                .map(RequestContext::getUser);
        return user.filter(value -> ObjectImportConfig.isSupportUpdateImportOwner(value, objectDescribe)).isPresent();
    }

    protected boolean getIsRemoveOutTeamMember(IObjectDescribe objectDescribe) {
        // 下游导入，不支持移除外部相关团队的选项
        if (isOutUser()) {
            return false;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        if (describeExt.isSlaveObject()) {
            return false;
        }
        return describeExt.isPRMEnabled() && getIsCheckOutOwner(objectDescribe);
    }

    protected boolean getIsCheckOutOwner(IObjectDescribe objectDescribe) {
        if (isOutUser()) {
            return false;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        if (describeExt.isSlaveObject()) {
            return false;
        }
        return ObjectDescribeExt.of(objectDescribe).getSupportRelationOuterOwnerField().isPresent();
    }

    private boolean isOutUser() {
        return Optional.ofNullable(RequestContextManager.getContext())
                .map(RequestContext::getUser)
                .map(User::isOutUser)
                .orElse(false);
    }

    protected boolean getIsBackFillIndustrialAndCommercialInfo(IObjectDescribe objectDescribe) {
        return false;
    }

    protected boolean getIsVerifyEnterprise(IObjectDescribe objectDescribe) {
        return false;
    }

    protected Boolean getIsEnableUniqueRule(IUniqueRule uniqueRule) {
        return null;
    }

    protected boolean getIsNotSupportSaleEvent(IObjectDescribe objectDescribe) {
        return false;
    }

    private String getObjectCode(IObjectDescribe objectDescribe) {
        return Objects.isNull(getObjectCode()) ? objectDescribe.getApiName() :
                ObjectImportEnum.getObjectCodeByApiName(getObjectCode());
    }

    protected void generateObjAllObj(IObjectDescribe objectDescribe) {
    }

    protected String getObjectName(IObjectDescribe objectDescribe) {
        return objectDescribe.getDisplayName();
    }

    @Deprecated
    protected int getDuplicateJudgmentType(IObjectDescribe objectDescribe) {
        Optional<RuleField> ruleField = RuleField.of(objectDescribe);
        return ruleField.isPresent() ? 1 : 0;
    }

    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        if (Objects.nonNull(uniqueRule) && UniqueRuleExt.of(uniqueRule).isEffectiveWhenImport()) {
            return ImportType.DEFAULT;
        }
        if (supportImportId(objectDescribe)) {
            return ImportType.DEFAULT;
        }
        Optional<RuleField> ruleField = RuleField.of(objectDescribe);
        return ruleField.isPresent() ? ImportType.DEFAULT : ImportType.UNSUPPORT_UPDATE_IMPORT;
    }

    protected Map<String, List<MatchingType>> getMatchingTypeMap(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        ImportType importType = importTypeHelper(uniqueRule, objectDescribe);
        Map<String, List<MatchingType>> resultMap = Maps.newHashMap();
        if (importType != ImportType.UNSUPPORT_INSERT_IMPORT) {
            resultMap.put(INSERT_IMPORT, getMatchingTypesByInsert(objectDescribe));
        }
        if (importType != ImportType.UNSUPPORT_UPDATE_IMPORT) {
            resultMap.put(UPDATE_IMPORT, getMatchingTypesByUpdate(objectDescribe, uniqueRule));
        }
        return resultMap;
    }

    private List<MatchingType> getMatchingTypesByUpdate(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        List<MatchingType> matchingTypes = getMatchingTypesByUpdateWithUniqueRule(objectDescribe, uniqueRule);
        if (AppFrameworkConfig.updateImportSupportSpecifiedField(objectDescribe.getTenantId(), objectDescribe.getApiName())) {
            matchingTypes.add(MatchingType.SPECIFIED_FIELD);
        }
        return matchingTypes;
    }

    protected List<MatchingType> getMatchingTypesByInsert(IObjectDescribe objectDescribe) {
//        if (supportImportId(objectDescribe) && ObjectDescribeExt.of(objectDescribe).hasActiveReferenceFieldDescribes()) {
//            return Lists.newArrayList(MatchingType.ID, MatchingType.NAME);
//        }
        // 产品希望即便没有关联字段，前台也显示支持唯一性ID导入方式
        if (supportImportId(objectDescribe)) {
            return Lists.newArrayList(MatchingType.ID, MatchingType.NAME);
        }
        return Lists.newArrayList(MatchingType.NAME);
    }

    protected List<MatchingType> getMatchingTypesByUpdateWithUniqueRule(IObjectDescribe objectDescribe,
                                                                        IUniqueRule uniqueRule) {
        List<MatchingType> matchingTypes = getMatchingTypesByUpdate(objectDescribe);
        if (Objects.nonNull(uniqueRule) && UniqueRuleExt.of(uniqueRule).isEffectiveWhenImport()) {
            matchingTypes.add(MatchingType.UNIQUE_RULE);
        }
        return matchingTypes;
    }

    protected List<MatchingType> getMatchingTypesByUpdate(IObjectDescribe objectDescribe) {
        if (supportImportId(objectDescribe)) {
            return Lists.newArrayList(MatchingType.ID, MatchingType.NAME);
        }
        return Lists.newArrayList(MatchingType.NAME);
    }

    protected boolean getOpenWorkFlow(IObjectDescribe objectDescribe) {
        return ObjectDescribeExt.of(objectDescribe).isCustomObject();
    }

    /**
     * 从对象不支持触发审批流
     *
     * @param objectDescribe
     * @return
     */
    protected boolean getApprovalFlow(IObjectDescribe objectDescribe) {
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            return false;
        }
        // 优先使用 objectCode，没有的时候用 对象apiName，避免联合导入被当作单条导入了
        boolean importTriggerApproval = AppFrameworkConfig.isImportTriggerApprovalFlowGray(objectDescribe.getTenantId(), objectDescribe.getApiName());
        String objectCode = getObjectCode();
        // 单对象导入直接返回
        if (Strings.isNullOrEmpty(objectCode) || Objects.equals(objectCode, objectDescribe.getApiName())) {
            return importTriggerApproval;
        }

        // 联合导入需要判断联合导入的开关
        return importTriggerApproval
                && AppFrameworkConfig.isUnionInsertImportTriggerApprovalFlowGray(objectDescribe.getTenantId(), objectCode);
    }

    protected List<RuleField> getPrimaryAttributeFieldList(IObjectDescribe objectDescribe) {
        Optional<RuleField> ruleField = RuleField.of(objectDescribe);
        return ruleField.map(Lists::newArrayList).orElse(null);
    }

    protected boolean getNoBatch() {
        return false;
    }

    protected boolean supportImportId(IObjectDescribe objectDescribe) {
        if (ObjectImportConfig.isGrayTenant(RequestContextManager.getContext().getTenantId())) {
            return true;
        }
        return DEFINE_TYPE_CUSTOM.equals(objectDescribe.getDefineType()) || SUPPORT_IMPORT_ID_API_NAME.contains(objectDescribe.getApiName());
    }
}
