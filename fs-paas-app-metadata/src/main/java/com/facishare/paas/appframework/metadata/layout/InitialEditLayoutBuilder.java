package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.config.ButtonConfig;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentInfo;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Builder
public class InitialEditLayoutBuilder extends InitLayoutHandler {
    private ILayout detailLayout;
    private IObjectDescribe describe;
    private List<IObjectDescribe> detailDescribes;
    private boolean createLayoutFromDetail;
    private ObjectConvertRuleService objectConvertRuleService;

    public ILayout getLayout() {
        LayoutComponents.restoreComponentOrder(LayoutExt.of(detailLayout));
        List<IComponent> componentList = LayoutExt.of(detailLayout).getComponentsSilently();
        List<IComponent> orderComponents = ComponentOrder.order(componentList);
        LayoutExt.of(detailLayout).setComponents(orderComponents);
        ILayout newLayout = copyLayout(detailLayout, LayoutTypes.EDIT);
        List<IComponent> components = buildComponents(newLayout);
        newLayout.setComponents(components);
        Map<String, Object> layoutStructure = buildLayoutStructure(components);
        newLayout.setLayoutStructure(layoutStructure);
        LayoutExt.of(newLayout).removeI18n();
        return newLayout;
    }

    private List<IComponent> buildComponents(ILayout newLayout) {
        LayoutExt.of(newLayout).getFormComponent().ifPresent(x -> {
            if (!ComponentExt.FORM_COMPONENT.equals(x.getName())) {
                x.setName(ComponentExt.FORM_COMPONENT);
                x.set("_id", ComponentExt.FORM_COMPONENT);
            }
            x.setHeader(I18N.text(I18NKey.FORM_COMPONENT));
            ComponentExt.of(x).setNameI18nKey(I18NKey.FORM_COMPONENT);
            x.removeHideComponent();
        });
        if (createLayoutFromDetail) {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
            List<IFieldDescribe> fieldDescribes = describeExt.getFieldDescribesSilently();
            HashMap<String, IFieldDescribe> requireFieldMap = Maps.newHashMap();
            List<String> fieldList = LayoutExt.of(newLayout).getFieldList();
            fieldDescribes.forEach(field -> {
                FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(field);
                if (field.isRequired() && fieldDescribeExt.isCustomField() && !fieldList.contains(field.getApiName())) {
                    requireFieldMap.put(field.getApiName(), field);
                }
            });
            LayoutExt.of(newLayout).getFormComponent()
                    .flatMap(FormComponentExt::getBaseFieldSection)
                    .ifPresent(baseFieldSection -> {
                        List<IFormField> fields = baseFieldSection.getFields();
                        Set<Map.Entry<String, IFieldDescribe>> requiredFieldDescribeEntries = requireFieldMap.entrySet();
                        for (Map.Entry<String, IFieldDescribe> fieldDescribeEntry : requiredFieldDescribeEntries) {
                            IFieldDescribe fieldDescribe = fieldDescribeEntry.getValue();
                            FormField formField = new FormField();
                            formField.setReadOnly(false);
                            formField.setRequired(true);
                            formField.setFieldName(fieldDescribe.getApiName());
                            formField.setRenderType(fieldDescribe.getType());
                            fields.add(formField);
                        }
                        baseFieldSection.setFields(fields);
                    });
        }
        List<IComponent> components = LayoutExt.of(newLayout).getComponentsSilently();
        if (CollectionUtils.notEmpty(detailDescribes)) {
            //先计算一下组件的order
            LayoutComponents.restoreComponentOrder(LayoutExt.of(detailLayout));
            //按照布局里配置的显示顺序来对从对象进行排序，先将从  对象按照id排序，防止没有在布局管理配置过顺序的从对象出现乱序。
            Collections.sort(detailDescribes, Comparator.comparing(IObjectDescribe::getId));
            Collections.sort(detailDescribes, Comparator.comparingInt(o -> LayoutExt.of(detailLayout).getDetailComponentOrder(o.getApiName())));

            List<IComponent> detailComponents = detailDescribes.stream().map(this::buildDetailObjectComponent).collect(Collectors.toList());
            components.addAll(detailComponents);
            IComponent tabsComponent = buildTabComponent(detailComponents, false, Lists.newArrayList());
            components.add(tabsComponent);
        }
        //构造标题和按钮组件
        components.add(buildHeadInfo());
        return components;
    }

    private IComponent buildHeadInfo() {
        IComponent component = LayoutComponents.buildHeadInfoComponent();

        // 创建转换规则检查器
        BiFunction<User, String, Boolean> convertRuleChecker = createConvertRuleChecker();
        List<String> addPageButtons = EditLayout.buildDefaultButtonsAdd(detailLayout.getTenantId(), detailLayout.getRefObjectApiName(), convertRuleChecker)
                .stream().map(IButton::getName).collect(Collectors.toList());
        addPageButtons.removeIf(buttonApiName -> describe.isBigObject() && !ButtonConfig.getBigObjectSupportButtonList().contains(buttonApiName));
        ListComponentInfo addPageButtonInfo = (ListComponentInfo) ListComponentInfo.normal(ButtonUsePageType.Create);
        addPageButtonInfo.setOrder(addPageButtons);

        List<String> editPageButtons = EditLayout.buildDefaultButtonsEdit(detailLayout.getTenantId(), detailLayout.getRefObjectApiName(), convertRuleChecker)
                .stream().map(IButton::getName).collect(Collectors.toList());
        editPageButtons.removeIf(buttonApiName -> describe.isBigObject() && !ButtonConfig.getBigObjectSupportButtonList().contains(buttonApiName));
        ListComponentInfo editPageButtonInfo = (ListComponentInfo) ListComponentInfo.normal(ButtonUsePageType.Edit);
        editPageButtonInfo.setOrder(editPageButtons);

        ComponentExt.of(component).setButtonInfo(Lists.newArrayList(addPageButtonInfo, editPageButtonInfo));
        return component;
    }

    /**
     * 创建转换规则检查器
     *
     * @return 转换规则检查器函数
     */
    private BiFunction<User, String, Boolean> createConvertRuleChecker() {
        return (user, objectApiName) -> {
            if (objectConvertRuleService == null) {
                return false; // 如果服务未注入，默认不显示按钮
            }
            return objectConvertRuleService.supportPullOrder(user, objectApiName);
        };
    }

    private IMultiTableComponent buildDetailObjectComponent(IObjectDescribe detailDescribe) {
        IMultiTableComponent component = LayoutComponents.buildDetailObjectComponent(detailDescribe);

        //从对象通用按钮
        List<String> normalButtons = EditLayout.buildDefaultListNormalDetailObjButtonNames(detailDescribe, false);
        ListComponentInfo normalButtonInfo = (ListComponentInfo) ListComponentInfo.listNormal(null);
        normalButtonInfo.setOrder(normalButtons);

        //从对象批量按钮
        List<String> batchButtons = EditLayout.buildDefaultListBatchDetailObjButtonNames(detailDescribe.getApiName());
        ListComponentInfo batchButtonInfo = (ListComponentInfo) ListComponentInfo.listBatch(null);
        batchButtonInfo.setOrder(batchButtons);

        //从对象单条按钮
        List<String> singleButtons = EditLayout.buildDefaultListSingleDetailObjButtonNames(detailDescribe.getTenantId(),
                detailDescribe.getApiName());
        ListComponentInfo singleButtonInfo = (ListComponentInfo) ListComponentInfo.listSingle(null);
        singleButtonInfo.setOrder(singleButtons);

        ComponentExt.of(component).setButtonInfo(Lists.newArrayList(normalButtonInfo, batchButtonInfo, singleButtonInfo));
        return component;
    }

    private Map<String, Object> buildLayoutStructure(List<IComponent> components) {
        components = getExcludeFormTableTextComponents(components);
        Map<String, Object> layoutStructure = Maps.newLinkedHashMap();
        Map<String, Object> row1 = Maps.newLinkedHashMap();
        row1.put(LayoutStructure.COLUMNS, Lists.newArrayList(ImmutableMap.of("width", "100%")));
        List<List<String>> components1 = Lists.newArrayList();
        components1.add(Lists.newArrayList(ComponentExt.HEAD_INFO_COMPONENT_NAME));
        row1.put(LayoutStructure.COMPONENTS, components1);

        Map<String, Object> row2 = Maps.newLinkedHashMap();
        row2.put(LayoutStructure.COLUMNS, Lists.newArrayList(ImmutableMap.of("width", "100%")));
        List<List<String>> components2 = Lists.newArrayList();
        List<String> childComponents2 = components.stream()
                .filter(x -> !ComponentExt.of(x).isMasterDetailComponent()
                        && !ComponentExt.HEAD_INFO_COMPONENT_NAME.equals(x.getName()))
                .map(x -> x.getName())
                .collect(Collectors.toList());
        components2.add(childComponents2);
        row2.put(LayoutStructure.COMPONENTS, components2);

        layoutStructure.put(LayoutStructure.LAYOUT, Lists.newArrayList(row1, row2));
        layoutStructure.put(LayoutStructure.FIELD_ALIGN, LayoutStructure.getFieldAlign(LayoutExt.of(detailLayout)));
        return layoutStructure;
    }

    private Map<String, Object> buildLayoutStructure1(List<IComponent> components) {
        Map<String, Object> layoutStructure = Maps.newLinkedHashMap();
        Map<String, Object> row1 = Maps.newLinkedHashMap();
        row1.put(LayoutStructure.COLUMNS, Lists.newArrayList(ImmutableMap.of("width", "100%")));
        List<List<String>> components1 = components.stream()
                .filter(x -> !ComponentExt.SHORTCUT_COMPONENT_NAME.equals(x.getName())
                        && !ComponentExt.HEAD_INFO_COMPONENT_NAME.equals(x.getName()))
                .map(x -> Lists.newArrayList(x.getName()))
                .collect(Collectors.toList());
        components1.add(0, Lists.newArrayList(ComponentExt.SHORTCUT_COMPONENT_NAME));
        components1.add(Lists.newArrayList(ComponentExt.HEAD_INFO_COMPONENT_NAME));
        row1.put(LayoutStructure.COMPONENTS, components1);

        layoutStructure.put(LayoutStructure.LAYOUT, Lists.newArrayList(row1, row1));
        return layoutStructure;
    }
}
