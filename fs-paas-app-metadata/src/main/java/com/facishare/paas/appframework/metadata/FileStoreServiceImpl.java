package com.facishare.paas.appframework.metadata;

import com.facishare.fsi.proxy.exception.FsiClientException;
import com.facishare.fsi.proxy.model.warehouse.n.fileupload.*;
import com.facishare.fsi.proxy.service.NFileStorageService;
import com.facishare.netdisk.api.model.type.V5FileInfo;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.util.FileAttachmentUtils;
import com.facishare.paas.appframework.metadata.util.FileExtUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneSaveFileFromTempFileRequest;
import com.facishare.stone.sdk.response.StoneSaveFileFromTempFileResponse;
import com.fxiaoke.stone.commons.SystemPresetClient;
import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2018/3/6
 */
@Slf4j
@Service("fileStoreService")
public class FileStoreServiceImpl implements FileStoreService {

    private static final String BUSINESS_CODE = "CRM";

    private static final List<String> DEFAULT_IMAGE_THUMB_SIZE = Lists.newArrayList("108,81,108,81");
    private static final Set<String> SUPPORT_FILE_EXT = Sets.newHashSet("jpg", "png");

    @Resource
    private NFileStorageService nFileStorageService;

    @Autowired
    private GDSHandler gdsHandler;
    @Autowired
    private StoneProxyApi stoneProxyApi;

    @Resource
    private SystemPresetClient metadataExclusiveClient;
    @Resource
    private SystemPresetClient appFrameworkExclusiveClient;

    @Override
    public String uploadTempFile(String tenantId, String userId, byte[] fileData) {
        String ea = getEaByEi(tenantId);

        NTempFileUpload.Arg arg = new NTempFileUpload.Arg();
        arg.setBusiness(BUSINESS_CODE);
        arg.setEa(ea);
        arg.setSourceUser(userId);
        arg.setData(fileData);

        NTempFileUpload.Result result = nFileStorageService.nTempFileUpload(arg, ea);
        if (null == result || Strings.isNullOrEmpty(result.getTempFileName())) {
            log.error("上传文件失败:arg={},result={}", arg, result);
            throw new MetaDataBusinessException(I18N.text(I18NKey.UPLOAD_FILE_FAILED));
        }
        return result.getTempFileName();
    }

    @Override
    public String saveFileFromTempFile(String tenantId, String userId, String tmpPath, String fileExt) {
        String ea = getEaByEi(tenantId);

        NSaveFileFromTempFile.Arg arg = new NSaveFileFromTempFile.Arg();
        arg.setBusiness(BUSINESS_CODE);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        arg.setTempFileName(tmpPath);
        arg.setFileExt(fileExt);

        NSaveFileFromTempFile.Result result = nFileStorageService.nSaveFileFromTempFile(arg, ea);

        if (result == null || Strings.isNullOrEmpty(result.getFinalNPath())) {
            log.error("保存文件失败:arg={},result={}", arg, result);
            throw new MetaDataBusinessException(I18N.text(I18NKey.SAVE_FILE_FAILED));
        }

        return result.getFinalNPath();
    }

    @Override
    public String saveFileFromTempFile(String tenantId, String userId, PathOriginNames pathOriginNames, String fileExt) {
        String ea = getEaByEi(tenantId);

        NSaveFileFromTempFile.Arg arg = new NSaveFileFromTempFile.Arg();
        arg.setBusiness(BUSINESS_CODE);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        arg.setFileExt(fileExt);
        arg.setTempFileName(pathOriginNames.getPath());
        arg.setOriginName(pathOriginNames.getOriginNames());
        NSaveFileFromTempFile.Result result = nFileStorageService.nSaveFileFromTempFile(arg, ea);

        if (result == null || Strings.isNullOrEmpty(result.getFinalNPath())) {
            log.error("saveFileFromTempFile fail, arg={},result={}", arg, result);
            throw new MetaDataBusinessException(I18N.text(I18NKey.SAVE_FILE_FAILED));
        }

        return result.getFinalNPath();
    }

    @Override
    public List<PathPair> saveFileFromTempFiles(String enterpriseAccount, String userId, List<PathOriginNames> pathOriginNamesList, String fileExt) {
        List<PathPair> pathPairList = Lists.newArrayList();
        if (CollectionUtils.empty(pathOriginNamesList)) {
            return pathPairList;
        }
        pathOriginNamesList.forEach(pathOriginNames -> {
            // TODO 待文件服务提供批量接口改成批量接口
            String finalNPath = saveFileFromTempFileByProxy(enterpriseAccount, userId, pathOriginNames, fileExt);
            pathPairList.add(PathPair.of(pathOriginNames.getPath(), finalNPath));
        });
        return pathPairList;
    }

    @Override
    public String saveImageFromTempFile(String tenantId, String userId, String tmpPath, String fileExt, boolean addThumbnail) {
        String ea = getEaByEi(tenantId);

        NSaveFileFromTempFile.Arg arg = new NSaveFileFromTempFile.Arg();
        arg.setBusiness(BUSINESS_CODE);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        arg.setTempFileName(tmpPath);
        arg.setFileExt(fileExt);
        if (addThumbnail) {
            NExtraImageProcessRequest extraProcess = new NExtraImageProcessRequest();
            extraProcess.setMakeThumbnailList(DEFAULT_IMAGE_THUMB_SIZE);
            arg.setExtraProcess(extraProcess);
        }

        NSaveFileFromTempFile.Result result = nFileStorageService.nSaveFileFromTempFile(arg, ea);
        if (result == null || Strings.isNullOrEmpty(result.getFinalNPath())) {
            log.error("保存图片失败:arg={},result={}", arg, result);
            throw new MetaDataBusinessException(I18N.text(I18NKey.SAVE_IMAGE_FAILED));
        }

        return result.getFinalNPath();
    }

    @Override
    public List<String> saveImageFromTempFiles(String tenantId, String userId, List<String> tmpPaths, String fileExt) {
        String ea = getEaByEi(tenantId);
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.UNIFIED_IMAGE_CONVERSION_EI, tenantId)) {
            List<String> finalNPaths = Lists.newArrayList();
            // TODO 待文件服务提供批量接口改成批量接口
            tmpPaths.forEach(tmpPath -> finalNPaths.add(saveFileFromTempFileByProxy(ea, userId, PathOriginNames.of(tmpPath, StringUtils.EMPTY), fileExt)));
            return finalNPaths;
        }
        NSaveImageFromTempFiles.Arg arg = new NSaveImageFromTempFiles.Arg();
        arg.setBusiness(BUSINESS_CODE);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        arg.setTempFileNames(tmpPaths);
        arg.setFileExt(fileExt);
        NSaveImageFromTempFiles.Result result = nFileStorageService.nSaveImageFromTempFiles(arg, ea);
        if (result == null || CollectionUtils.empty(result.getResults())) {
            log.error("保存图片失败:arg={},result={}", arg, result);
            throw new MetaDataBusinessException(I18N.text(I18NKey.SAVE_IMAGE_FAILED));
        }
        return result.getResults().stream().map(NSaveImageFromTempFileResult::getFinalNPath).collect(Collectors.toList());
    }

    /**
     * 调用保存图片服务，如果 userId 为空，使用 系统（-10000）保存
     *
     * @param ea              企业账号
     * @param userId          用户编码
     * @param pathOriginNames 临时文件名
     * @param fileExt         文件后缀名
     * @return 保存成功返回 path
     */
    private String saveFileFromTempFileByProxy(String ea, String userId, PathOriginNames pathOriginNames, String fileExt) {

        if (StringUtils.isBlank(userId)) {
            userId = User.SUPPER_ADMIN_USER_ID;
        }

        StoneSaveFileFromTempFileRequest stoneSaveImageFromTempFileRequest = StoneSaveFileFromTempFileRequest.builder()
                .ea(ea)
                .employeeId(Integer.valueOf(userId))
                .business(BUSINESS_CODE)
                .tempFileName(pathOriginNames.getPath())
                .extensionName(fileExt)
                .build();
        String originNames = pathOriginNames.getOriginNames();
        if (StringUtils.isNotEmpty(originNames)) {
            stoneSaveImageFromTempFileRequest.setOriginName(originNames);
        }
        try {
            StoneSaveFileFromTempFileResponse result = stoneProxyApi.saveFileFromTempFile(stoneSaveImageFromTempFileRequest);
            return result.getPath();
        } catch (FRestClientException e) {
            log.error("保存图片失败:arg={}", stoneSaveImageFromTempFileRequest, e);
            throw new MetaDataBusinessException(I18N.text(I18NKey.SAVE_IMAGE_FAILED));
        }
    }

    @Override
    public List<PathPair> saveImageFromTempFilesAndNames(String tenantId, String userId, List<PathOriginNames> pathOriginNames, String fileExt) {
        if (CollectionUtils.empty(pathOriginNames)) {
            return Lists.newArrayList();
        }
        String ea = getEaByEi(tenantId);
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.UNIFIED_IMAGE_CONVERSION_EI, tenantId)) {
            return saveFileFromTempFiles(ea, userId, pathOriginNames, fileExt);
        }

        NSaveImageFromTempFiles.Arg arg = new NSaveImageFromTempFiles.Arg();
        arg.setBusiness(BUSINESS_CODE);
        arg.setEa(ea);
        arg.setSourceUser("E." + userId);
        arg.setFileExt(fileExt);

        Map<String, String> pathOriginNameMap = pathOriginNames.stream().reduce(Maps.newHashMapWithExpectedSize(pathOriginNames.size()),
                (resultMap, pathOriginName) -> {
                    resultMap.putIfAbsent(pathOriginName.getPath(), Strings.nullToEmpty(pathOriginName.getOriginNames()));
                    return resultMap;
                },
                (a, b) -> a);
        arg.setPathOriginNames(pathOriginNameMap);
        arg.setTempFileNames(Lists.newArrayList(pathOriginNameMap.keySet()));

        NSaveImageFromTempFiles.Result result;
        try {
            result = nFileStorageService.nSaveImageFromTempFiles(arg, ea);
            if (result == null || CollectionUtils.empty(result.getResults())) {
                log.error("saveImageFromTempFilesAndNames fail, ei:{},arg={},result={}", tenantId, arg, result);
                throw new MetaDataBusinessException(I18NExt.text(I18NKey.SAVE_IMAGE_FAILED));
            }
            return result.getResults().stream().map(it -> PathPair.of(it.getTempPath(), it.getFinalNPath())).collect(Collectors.toList());
        } catch (FsiClientException fce) {
            log.warn("saveImageFromTempFilesAndNames fail!, ei:{}4", tenantId, fce);
            if (fce.getMessage().contains(SystemErrorCode.INVALID_EXTENSION.getCode())) {
                throw new ValidateException(I18NExt.text(I18NKey.INVALID_EXTENSION));
            } else if (fce.getMessage().contains(SystemErrorCode.FILE_NOT_EXIST_OR_EXPIRED.getCode()) && fce.getMessage().contains("10007")) {
                throw new ValidateException(I18NExt.text(I18NKey.FILE_NOT_EXIST_OR_EXPIRED));
            } else {
                throw fce;
            }
        }
    }

    private String getEaByEi(String tenantId) {
        return gdsHandler.getEAByEI(tenantId);
    }

    @Override
    public List<V5FileInfo> getNPathsWithoutPermission(User user, List<V5FileInfo> files) {
        String ea = getEaByEi(user.getTenantId());
        return getNPathsWithoutPermission(ea, user.getUserId(), files);
    }

    @Override
    public List<V5FileInfo> getNPathsWithoutPermission(String ea, String userId, List<V5FileInfo> files) {
        // 记录输入参数
        log.info("getNPathsWithoutPermission start - ea:{}, userId:{}, input files size:{}, input files nPaths:{}",
                ea, userId, files.size(), files.stream().map(V5FileInfo::getNPath).collect(Collectors.toList()));

        List<V5FileInfo> results = Lists.newArrayList();
        // 暂时没有批量接口，用多线程代替
        ExecutorService exec = Executors.newCachedThreadPool();
        CompletionService<V5FileInfo> completionService = new ExecutorCompletionService<>(exec);

        for (V5FileInfo file : files) {
            completionService.submit(() -> {
                log.info("Processing file - nPath:{}", file.getNPath());
                NCreateNoPermissionFile.Arg arg = new NCreateNoPermissionFile.Arg();
                arg.setSourceUser("E." + userId);
                arg.setEa(ea);
                arg.setnPath(file.getNPath());
                NCreateNoPermissionFile.Result result = nFileStorageService.nCreateNoPermissionFile(arg, ea);
                String oldNPath = file.getNPath();
                file.setNPath(result.getnPath());
                log.info("File processed - old nPath:{}, new nPath:{}", oldNPath, result.getnPath());
                return file;
            });
        }
        exec.shutdown();
        try {
            log.info("Start collecting results, expected size:{}", files.size());
            for (int i = 0; i < files.size(); i++) {
                V5FileInfo result = completionService.take().get();
                results.add(result);
                log.info("Collected result[{}] - nPath:{}", i + 1, result.getNPath());
            }
            log.info("getNPathsWithoutPermission completed - input size:{}, result size:{}, result nPaths:{}",
                    files.size(), results.size(), results.stream().map(V5FileInfo::getNPath).collect(Collectors.toList()));
            return results;
        } catch (InterruptedException | ExecutionException e) {
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            log.error("getNPathsWithoutPermission error - ea:{}, userId:{}, error:{}", ea, userId, e.getMessage(), e);
            throw new ValidateException(I18NExt.text(I18NKey.ERROR_OBTAINING_NPATH));
        }
    }

    @Override
    public void generateImageSignUrl(IActionContext ctx, List<Map<String, Object>> imgList) {
        generateNPathSignedUrl(ctx, AuthModel.SIGN, imgList);
    }

    @Override
    public void generateImageSignUrl(IActionContext ctx, IObjectDescribe describe, List<IObjectData> dataList) {
        generateNPathSignedUrl(ctx, Sets.newHashSet(IFieldType.IMAGE), describe, dataList);
    }

    public Map<String, String> getFileNameList(List<String> paths, User user) {
        NGetFileName.Arg arg = new NGetFileName.Arg();
        arg.setPathList(paths);
        arg.setSourceUser("E." + user.getUserId());
        String ea = getEaByEi(user.getTenantId());
        arg.setEa(ea);
        NGetFileName.Result result = nFileStorageService.getFileNames(arg, ea);
        return result.getPathOriginNames();
    }

    @Override
    public String generateCpathSignature(User user, String cPath) {
        if (StringUtils.isBlank(cPath)) {
            log.warn("generateCpathSignature failed missing cPath user:{}, cPath:{}", user, cPath);
            throw new MetaDataBusinessException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_ICON));
        }
        return metadataExclusiveClient.signature(user.getTenantIdInt(), cPath);
    }

    @Override
    public String generateCpathAccessUrl(User user, String cPath, String signature) {
        if (StringUtils.isBlank(cPath) || StringUtils.isBlank(signature)) {
            log.warn("generateCpathAccessUrl failed missing cPath user:{}, cPath:{}, signature:{}", user, cPath, signature);
            throw new MetaDataBusinessException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_ICON));
        }
        String fileExt = FileExtUtil.parseFileExt(cPath);
        boolean isSupport = SUPPORT_FILE_EXT.contains(fileExt);
        return appFrameworkExclusiveClient.generateCFileAccessUrl(
                user.getTenantId(),
                user.getUserIdOrOutUserIdIfOutUser(),
                cPath,
                signature,
                isSupport ? fileExt : "jpg",
                "CRM");
    }

    @Override
    public void generateNPathSignedUrl(IActionContext ctx, Set<String> fieldTypes, IObjectDescribe describe, List<IObjectData> dataList) {
        if (Objects.isNull(describe) || CollectionUtils.empty(dataList) || CollectionUtils.empty(fieldTypes)) {
            return;
        }
        try {
            CollectionUtils.nullToEmpty(ObjectDescribeExt.of(describe).getFieldByTypes(fieldTypes))
                    .forEach(f ->
                            {
                                AuthModel authModel = getAuthModel(f.getType());
                                dataList.stream()
                                        .filter(data -> Objects.nonNull(data.get(f.getApiName())))
                                        .forEach(data -> {
                                                    Object raw = data.get(f.getApiName());
                                                    if (!(raw instanceof List)) {
                                                        return;
                                                    }

                                                    @SuppressWarnings("unchecked")
                                                    List<Map<String, Object>> safeList = (List<Map<String, Object>>) raw;
                                                    generateNPathSignedUrl(ctx, authModel, safeList);
                                                }
                                        );
                            }
                    );
        } catch (Exception e) {
            log.error("generateNPathSignedUrl fail, ei:{}, object:{}", ctx.getEnterpriseId(), describe.getApiName(), e);
        }
    }

    @Override
    public void generateNPathSignedUrlForEdit(IActionContext ctx, Set<String> fieldTypes, IObjectDescribe describe, List<IObjectData> dataList) {
        if (Objects.isNull(describe) || CollectionUtils.empty(dataList) || CollectionUtils.empty(fieldTypes)) {
            return;
        }
        try {
            CollectionUtils.nullToEmpty(ObjectDescribeExt.of(describe).getFieldByTypes(fieldTypes))
                    .forEach(f ->
                            {
                                AuthModel authModel = getAuthModel(f.getType());
                                dataList.stream()
                                        .filter(data -> Objects.nonNull(data.get(f.getApiName())))
                                        .forEach(data -> {
                                                    Object raw = data.get(f.getApiName());
                                                    if (!(raw instanceof List)) {
                                                        return;
                                                    }

                                                    @SuppressWarnings("unchecked")
                                                    List<Map<String, Object>> safeList = (List<Map<String, Object>>) raw;
                                                    List<Map<String, Object>> newList = generateNPathSignedUrlAndReturnNewResult(ctx, authModel, safeList);
                                                    data.set(f.getApiName(), newList);
                                                }
                                        );
                            }
                    );
        } catch (Exception e) {
            log.error("generateNPathSignedUrl fail, ei:{}, object:{}", ctx.getEnterpriseId(), describe.getApiName(), e);
        }
    }

    /**
     * 对签名进行加密处理
     *
     * @param enterpriseId 企业ID
     * @param signature    原始签名
     * @return 加密后的签名
     */
    private String encryptSignatureIfNeeded(String enterpriseId, String signature) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ENCRYPT_FILE_SIGNATURE_EI, enterpriseId)) {
            return appFrameworkExclusiveClient.encryptSignature(enterpriseId, signature)
                    .orElse(signature);
        }
        return signature;
    }

    public List<Map<String, Object>> generateNPathSignedUrlAndReturnNewResult(IActionContext ctx, AuthModel authModel, List<Map<String, Object>> list) {
        ArrayList<Map<String, Object>> result = Lists.newArrayList();
        if (CollectionUtils.empty(list)) {
            return result;
        }

        String enterpriseId = ctx.getEnterpriseId();
        for (Map<String, Object> map : list) {
            if (MapUtils.isEmpty(map)) {
                result.add(map);
                continue;
            }

            Object signatureRaw = map.get(ImageExt.SIGNATURE);
            Object pathRaw = map.get(ImageExt.PATH);
            Object fileNameRaw = map.get(ImageExt.FILENAME);
            Object extRaw = map.get(ImageExt.EXT);

            // 在线文档，不做处理
            if (FileAttachmentUtils.isOnLine(map)) {
                result.add(map);
                continue;
            }

            // path 为空，不做处理
            if (ObjectUtils.isEmpty(pathRaw)) {
                result.add(map);
                continue;
            }

            // path 签名为空，不做处理
            if (ObjectUtils.isEmpty(signatureRaw)) {
                result.add(map);
                continue;
            }

            String signature = String.valueOf(signatureRaw);
            String path = String.valueOf(pathRaw);
            String fileName = Objects.isNull(fileNameRaw) ? "" : String.valueOf(fileNameRaw);
            String ext = Objects.isNull(extRaw) ? "" : String.valueOf(extRaw);

            // path 以 http:// 或 https:// 开头，不做处理
            if (StringUtils.startsWithIgnoreCase(path, ImageExt.PREFIX_HTTP)
                    || StringUtils.startsWithIgnoreCase(path, ImageExt.PREFIX_HTTPS)) {
                result.add(map);
                continue;
            }

            try {
                // 先对signature进行解密
                String decryptedSignature = appFrameworkExclusiveClient.decryptSignature(enterpriseId, signature)
                        .orElse(signature);

                String url = appFrameworkExclusiveClient.generateUrl(enterpriseId,
                        ctx.getUserId(), ctx.getOutEnterpriseId(), ctx.getOutUserId(), ctx.getUpstreamOwnerId(),
                        authModel, path, decryptedSignature, ImageExt.EXPIRE_TIME, fileName, ext);

                // 对signature进行加密
                Map<String, Object> newItem = Maps.newHashMap(map);
                String encryptedSignature = encryptSignatureIfNeeded(enterpriseId, signature);
                newItem.put(ImageExt.SIGNATURE, encryptedSignature);
                // 拿到 traceId 放入 signedUrl 中
                String traceId = TraceContext.get().getTraceId();
                String appendTrace = url + "&" + ImageExt.LINK_ID + "=" + traceId;
                newItem.put(ImageExt.SIGNATURE_URL, appendTrace);
                result.add(newItem);
            } catch (Exception e) {
                result.add(map);
                log.error("generateNPathSignUrl fail, ei:{}, uid:{}, oei:{}, out:{}, upstream:{}, path:{}",
                        ctx.getEnterpriseId(), ctx.getUserId(), ctx.getOutEnterpriseId(), ctx.getOutUserId(),
                        ctx.getUpstreamOwnerId(), path, e);
            }
        }
        return result;
    }

    @Override
    public void generateNPathSignedUrl(IActionContext ctx, AuthModel authModel, List<Map<String, Object>> list) {
        if (CollectionUtils.empty(list)) {
            return;
        }

        String enterpriseId = ctx.getEnterpriseId();
        for (Map<String, Object> map : list) {
            if (MapUtils.isEmpty(map)) {
                continue;
            }

            Object signatureRaw = map.get(ImageExt.SIGNATURE);
            Object pathRaw = map.get(ImageExt.PATH);
            Object fileNameRaw = map.get(ImageExt.FILENAME);
            Object extRaw = map.get(ImageExt.EXT);

            // 在线文档，不做处理
            if (FileAttachmentUtils.isOnLine(map)) {
                continue;
            }

            // path 为空，不做处理
            if (ObjectUtils.isEmpty(pathRaw)) {
                continue;
            }

            // path 签名为空，不做处理
            if (ObjectUtils.isEmpty(signatureRaw)) {
                continue;
            }

            String signature = String.valueOf(signatureRaw);
            String path = String.valueOf(pathRaw);
            String fileName = Objects.isNull(fileNameRaw) ? "" : String.valueOf(fileNameRaw);
            String ext = Objects.isNull(extRaw) ? "" : String.valueOf(extRaw);

            // 文件服务生成了 signature 就直接用，不再关注 path 是什么
            // signature 不能使用用直接找 文件服务支持
            // if (!FileAttachmentUtils.isN(path)) {
            //     continue;
            // }

            // path 以 http:// 或 https:// 开头，不做处理
            if (StringUtils.startsWithIgnoreCase(path, ImageExt.PREFIX_HTTP)
                    || StringUtils.startsWithIgnoreCase(path, ImageExt.PREFIX_HTTPS)) {
                continue;
            }

            try {
                // 先对signature进行解密
                String decryptedSignature = appFrameworkExclusiveClient.decryptSignature(enterpriseId, signature)
                        .orElse(signature);

                String url = appFrameworkExclusiveClient.generateUrl(enterpriseId,
                        ctx.getUserId(), ctx.getOutEnterpriseId(), ctx.getOutUserId(), ctx.getUpstreamOwnerId(),
                        authModel, path, decryptedSignature, ImageExt.EXPIRE_TIME, fileName, ext);

                // 对signature进行加密并更新到map中
                String encryptedSignature = encryptSignatureIfNeeded(enterpriseId, signature);
                map.put(ImageExt.SIGNATURE, encryptedSignature);
                // 拿到 traceId 放入 signedUrl 中
                String traceId = TraceContext.get().getTraceId();
                String appendTrace = url + "&" + ImageExt.LINK_ID + "=" + traceId;
                map.put(ImageExt.SIGNATURE_URL, appendTrace);
            } catch (Exception e) {
                log.error("generateNPathSignUrl fail, ei:{}, uid:{}, oei:{}, out:{}, upstream:{}, path:{}",
                        ctx.getEnterpriseId(), ctx.getUserId(), ctx.getOutEnterpriseId(), ctx.getOutUserId(),
                        ctx.getUpstreamOwnerId(), path, e);
            }
        }
    }

    @Override
    public AuthModel getAuthModel(String fieldType) {
        RequestContext requestContext = RequestContextManager.getContext();
        if (Objects.nonNull(requestContext) && requestContext.usePermanentSignedUrl()) {
            return AuthModel.COOKIE;
        }
        return StringUtils.equalsAny(fieldType, IFieldType.FILE_ATTACHMENT) ? AuthModel.SIGN_COOKIE : AuthModel.SIGN;
    }

    public void generateSignedUrl(Map<String, Object> objData, AuthModel authModel, IActionContext ctx) {
        if (MapUtils.isEmpty(objData)) {
            return;
        }

        objData.forEach((field, value) -> {
            if (value instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> mapList = ((List<Object>) value).stream()
                        .filter(Map.class::isInstance)
                        .map(item -> (Map<String, Object>) item)
                        .collect(Collectors.toList());

                generateNPathSignedUrl(ctx, authModel, mapList);
            }
        });
    }

    public void generateNestedSignedUrl(Map<String, Map<String, Object>> nested, AuthModel authModel, IActionContext ctx) {
        if (MapUtils.isEmpty(nested)) {
            return;
        }

        nested.forEach((objApi, snapshot) -> {
            generateSignedUrl(snapshot, authModel, ctx);
        });
    }

    public void cleanSignedUrl(Map<String, Object> objData) {
        if (MapUtils.isEmpty(objData)) {
            return;
        }

        objData.forEach((api, value) -> {
            if (value instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> mapList = ((List<Object>) value).stream()
                        .filter(Map.class::isInstance)
                        .map(item -> (Map<String, Object>) item)
                        .collect(Collectors.toList());

                mapList.forEach(map -> map.remove(ImageExt.SIGNATURE_URL));
            }
        });
    }

    public Map<String, Map<String, Object>> cleanNestedSignedUrl(Map<String, Map<String, Object>> nested) {
        if (MapUtils.isEmpty(nested)) {
            return nested;
        }

        nested.forEach((objApi, objData) -> {
            cleanSignedUrl(objData);
        });

        return nested;
    }
}
