package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.appframework.metadata.LayoutExt;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.SidebarLayoutProcessorImpl;
import org.springframework.stereotype.Component;

@Component
public class SidebarLayoutFactory implements ExtraLayoutFactory {

    @Override
    public String getLayoutType() {
        return LayoutTypes.SIDEBAR;
    }

    @Override
    public LayoutExt getLayoutExt(LayoutProcessorContext context) {
        return new SidebarLayoutProcessorImpl(context.getPageType(), context.getWebLayout(),
                context.getDescribeExt(), context.getObjectData(), context.getComponentConfig())
                .processLayout();
    }

}
