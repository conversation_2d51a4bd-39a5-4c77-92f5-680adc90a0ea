package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.util.LicenseConstants.ModuleCode;
import com.facishare.paas.appframework.metadata.dto.DataConflictsResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.processor.DataProcessors;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.support.GDSHandler;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.validator.data.ObjectValidator;
import com.facishare.paas.pod.client.GroupRouterClient;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 对元数据服务的封装的代理类
 * <p>
 * Created by liyiguang on 2017/6/20.
 */
@Slf4j
@Service("metaDataService")
public class MetaDataServiceImpl implements MetaDataService {

    @Autowired
    @Delegate
    private MetaDataFindService metaDataFindService;

    @Autowired
    @Delegate
    private MetaDataActionService metaDataActionService;

    @Autowired
    @Delegate
    private MetaDataComputeService metaDataComputeService;

    @Autowired
    @Delegate
    private MetaDataMiscService metaDataMiscService;

    @Autowired
    private DataProcessors dataProcessors;

    @Autowired
    private GDSHandler gdsHandler;

    @Autowired
    private AppDefaultRocketMQProducer mqMessageProducer;

    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private RecordTypeLogicService recordTypeLogicService;
    @Autowired
    private DescribeLogicService describeLogicService;

    @Autowired
    private LicenseService licenseService;

    @Autowired
    private ObjectValidator objectValidator;

    private Cache<String, String> databaseIpLocalCache;

    public MetaDataServiceImpl() {
        databaseIpLocalCache = CacheBuilder.newBuilder()
                .expireAfterAccess(1, TimeUnit.HOURS)
                .maximumSize(10000)
                .build();
    }

    @Override
    public String checkCountLimit(User user, String checkType, String objectDescribeApiName) {
        if ("layout".equals(checkType)) {
            layoutLogicService.checkLayoutCountLimit(user, objectDescribeApiName, ILayout.DETAIL_LAYOUT_TYPE);
        }
        if ("record_type".equals(checkType)) {
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), objectDescribeApiName);
            recordTypeLogicService.checkRecordTypeCountLimit(user, objectDescribe);
        }
        if ("describe".equals(checkType)) {
            describeLogicService.checkDescribeCountLimit(user);
        }
        return "";
    }

    @Override
    public void processData(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.PARALLEL_PROCESS_DATA_TEMP_FILE_GRAY, objectDescribe.getTenantId())
                && dataList.size() > 5) {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
            Lists.partition(dataList, calculatePartitionSize(dataList.size()))
                    .forEach(objectDataList -> parallelTask.submit(() -> dataProcessors.process(objectDescribe, objectDataList)));
            try {
                parallelTask.await(10, TimeUnit.SECONDS, true);
            } catch (TimeoutException e) {
                log.warn("parallel process data temp file gray fail! ei:{}, dataSize:{}", objectDescribe.getTenantId(), dataList.size(), e);
                throw new MetaDataException(SystemErrorCode.METADATA_TIMEOUT_ERROR);
            } catch (Exception e) {
                log.warn("parallel process data failed! ei:{}, dataSize:{}", objectDescribe.getTenantId(), dataList.size(), e);
                // 使用ExceptionUtils在异常链中查找AppBusinessException
                AppBusinessException appBusinessException = ExceptionUtils.throwableOfType(e, AppBusinessException.class);
                if (appBusinessException != null) {
                    throw appBusinessException;
                } else {
                    throw new MetaDataException(e.getMessage());
                }
            }
            return;
        }
        dataProcessors.process(objectDescribe, dataList);
    }

    /**
     * 计算分区大小
     *
     * @param dataSize           数据总量
     * @param expectedPartitions 期望的分区数（默认为10）
     * @return 分区大小
     */
    private int calculatePartitionSize(int dataSize, int expectedPartitions) {
        // 参数校验
        if (dataSize <= 0) {
            return 1;
        }
        if (expectedPartitions <= 0) {
            expectedPartitions = 10; // 默认分区数
        }

        // 计算每个分区的大小
        int partitionSize = (int) Math.ceil((double) dataSize / expectedPartitions);

        // 确保分区大小在合理范围内
        partitionSize = Math.max(1, partitionSize);  // 最小分区大小为1
        partitionSize = Math.min(partitionSize, 1000);  // 最大分区大小为1000

        return partitionSize;
    }

    // 向后兼容的重载方法
    private int calculatePartitionSize(int dataSize) {
        return calculatePartitionSize(dataSize, 5);  // 使用默认分区数
    }

    @Override
    public void sendActionMq(User user, List<IObjectData> objectDataList, ObjectAction objectAction) {
        if (CollectionUtils.empty(objectDataList) || RequestUtil.isSystemTenant(user.getTenantId())) {
            return;
        }

        String ea = gdsHandler.getEAByEI(user.getTenantId());
        for (IObjectData objectData : objectDataList) {
            log.debug("send action message,action:{},tenantId:{},apiName:{},dataId:{}", objectAction.getActionCode(),
                    objectData.getTenantId(), objectData.getDescribeApiName(), objectData.getId());
            byte[] bytes = ObjectDataExt.of(objectData).toMessageData(objectAction, ea, user.getUserIdOrOutUserIdIfOutUser());
            if (bytes != null) {
                mqMessageProducer.sendMessage(bytes);
            }
        }
    }

    @Override
    public String findDatabaseIpByTenantId(String tenantId) {
        try {
            String databaseIp = databaseIpLocalCache.get(tenantId, () -> {
                String host = GroupRouterClient.getGroupName(tenantId);
                log.warn("get host from podApiClient,tenantId:{},host:{}", tenantId, host);
                return host;
            });

            return databaseIp;
        } catch (Exception e) {
            log.error("findDatabaseIpByTenantId error,tenantId:{}", tenantId, e);
            return null;
        }
    }

    @Override
    public boolean isSupportOrFilter(String tenantId, String describeApiName) {
        return objectListFiltersOrAppByLicense(tenantId) && AppFrameworkConfig.isGrayListSearchObject(tenantId, describeApiName);
    }

    @Override
    public boolean objectListFiltersOrAppByLicense(String tenantId) {
        Map<String, Boolean> supportOrFilterMap = licenseService.existModule(tenantId, Sets.newHashSet(ModuleCode.CUSTOM_OBJECT_LIST_FILTERS_OR_APP));
        return supportOrFilterMap.getOrDefault(ModuleCode.CUSTOM_OBJECT_LIST_FILTERS_OR_APP, false);
    }

    public void batchValidateDataConstraintByFields(User user, List<IObjectData> dataList, IObjectDescribe objectDescribe, boolean isUpdate) {
        IActionContext context = buildContext(user);
        try {
            objectValidator.batchValidate(dataList, objectDescribe, isUpdate, context);
        } catch (MetadataServiceException e) {
            log.warn("batchValidateDataList error,user:{}", user, e);
            throw new ValidateException(e.getMessage());
        }
    }

    @Override
    public DataConflictsResult processDataConflicts(User user, IObjectDescribe objectDescribe, IObjectData dbObjectData, IObjectData objectData) {
        Map<String, Object> diffMap = ObjectDataExt.of(dbObjectData).diff(objectData, objectDescribe);
        ObjectDataExt.of(diffMap).removeCalculateField(objectDescribe);
        //order_by字段不需要展示冲突
        diffMap.remove(IObjectData.ORDER_BY);

        List<String> fields = Lists.newArrayList(diffMap.keySet());
        if (CollectionUtils.empty(fields)) {
            return null;
        }
        Map<String, Object> lastData = ObjectDataExt.of(dbObjectData).toMap(fields);
        Map<String, Object> currentData = ObjectDataExt.of(objectData).toMap(fields);

        //fields只下发对象字段apiName，去掉__o和其他扩展字段
        fields = fields.stream().filter(objectDescribe::containsField).collect(Collectors.toList());
        //补充富文本字段的__o
        fillExtraForRichText(objectDescribe, dbObjectData, objectData, fields, lastData, currentData);
        List<IObjectData> objectDataList = Lists.newArrayList(ObjectDataExt.of(lastData), ObjectDataExt.of(currentData));
        fillExtendFieldInfo(user, objectDescribe, objectDataList);
        return DataConflictsResult.builder()
                .fields(fields)
                .lastData(lastData)
                .currentData(currentData)
                .build();
    }

    private void fillExtraForRichText(IObjectDescribe describe, IObjectData oldData, IObjectData newData,
                                      List<String> fields, Map<String, Object> lastData, Map<String, Object> currentData) {
        fields.stream()
                .filter(describe::containsField)
                .filter(x -> RichTextExt.isProcessableRichText(describe.getFieldDescribe(x)))
                .forEach(x -> {
                    String extraFieldApiName = RichTextExt.getRichTextAbstractName(x);
                    if (!lastData.containsKey(extraFieldApiName)) {
                        lastData.put(extraFieldApiName, oldData.get(extraFieldApiName));
                    }
                    if (!currentData.containsKey(extraFieldApiName)) {
                        currentData.put(extraFieldApiName, newData.get(extraFieldApiName));
                    }
                });
    }

    private void fillExtendFieldInfo(User user, IObjectDescribe describe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        //补充国家省市区的__r
        fillCountryAreaLabel(describe, dataList, user);
        List<IObjectData> synchronizedDataList = ObjectDataExt.synchronize(dataList);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            //添加lookup字段的主属性__r
            fillObjectDataWithRefObject(describe, synchronizedDataList, user);
        });
        parallelTask.submit(() -> {
            //人员字段的__r，__l
            fillUserInfo(describe, synchronizedDataList, user);
        });
        parallelTask.submit(() -> {
            //部门字段的__r，__l
            fillDepartmentInfo(describe, synchronizedDataList, user);
        });
        parallelTask.submit(() -> {
            fillDataVisibilityRange(user, describe, synchronizedDataList);
        });
        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("fillExtendFieldInfo error,tenantId:{},objectApiName:{},dataNum:{}", user.getTenantId(), describe.getApiName(),
                    dataList.size(), e);
        }
    }

    private IActionContext buildContext(User user) {
        return ActionContextExt.of(user, RequestContextManager.getContext())
                .allowUpdateInvalid(false)
                .getContext();
    }
}
