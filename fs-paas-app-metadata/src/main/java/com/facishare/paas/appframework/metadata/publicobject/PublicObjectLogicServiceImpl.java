package com.facishare.paas.appframework.metadata.publicobject;

import com.facishare.crm.operate.report.OperateReportUtil;
import com.facishare.crm.operate.report.model.ProductLine;
import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18<PERSON><PERSON>ey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.ObjectDescribeFinder;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicFieldDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobParamDTO;
import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobResultDTO;
import com.facishare.paas.appframework.metadata.publicobject.module.*;
import com.facishare.paas.appframework.metadata.util.OperateReport;
import com.facishare.paas.appframework.privilege.EnterpriseRelationLogicService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.appframework.privilege.util.AppIdUtil;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2023/12/11
 */
@Slf4j
@Service("publicObjectLogicService")
public class PublicObjectLogicServiceImpl implements PublicObjectLogicService {

    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private PublicObjectJobService publicObjectJobService;
    @Autowired
    private PublicObjectVerifyService publicObjectVerifyService;
    @Autowired
    private PublicObjectInviteService publicObjectInviteService;
    @Autowired
    private PublicObjectEnterpriseRelationService publicObjectEnterpriseRelationService;
    @Autowired
    @Qualifier("publicObjectMigrateProducer")
    private AppDefaultRocketMQProducer publicObjectMigrateProducer;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private EnterpriseRelationLogicService enterpriseRelationLogicService;
    @Autowired
    private LogService logService;
    @Autowired
    private UserRoleInfoService userRoleInfoService;
    @Autowired
    private CRMNotificationService crmNotificationService;

    @Override
    public PublicObjectStatusResult queryStatus(User user, String objectApiName) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), objectApiName);
        if (!enablePublicObject(user, objectDescribe)) {
            return PublicObjectStatusResult.builder()
                    .publicObjectStatus(PublicObjectStatusType.DISABLE)
                    .build();
        }
        Optional<PublicObjectJobInfo> publicObjectJobInfo = publicObjectJobService.queryPublicObjectJobByType(user, objectApiName,
                Lists.newArrayList(PublicObjectJobType.VERIFY_JOB, PublicObjectJobType.OPEN_JOB, PublicObjectJobType.CLOSE_JOB));
        return publicObjectJobInfo
                .filter(it -> filterWithJobType(it, objectDescribe))
                .map(it -> {
                    // 查询到任务，优先根据任务状态展示状态
                    PublicObjectStatusType stateType = PublicObjectStatusType.from(it.getJobType(), it.getJobStatus(), objectDescribe.isPublicObject());
                    return PublicObjectStatusResult.builder()
                            .publicObjectStatus(stateType)
                            .jobId(it.getJobId())
                            .jobType(it.getType())
                            .build();
                }).orElseGet(() -> {
                    // 没有查询到任务，根据描述是否开启公共对象展示状态
                    PublicObjectStatusType stateType = PublicObjectStatusType.from(null, null, objectDescribe.isPublicObject());
                    return PublicObjectStatusResult.builder()
                            .publicObjectStatus(stateType)
                            .build();
                });
    }

    private boolean filterWithJobType(PublicObjectJobInfo jobInfo, IObjectDescribe objectDescribe) {
        PublicObjectJobType jobType = jobInfo.getJobType();
        PublicObjectJobStatus jobStatus = jobInfo.getJobStatus();
        if (objectDescribe.isPublicObject()) {
            return (jobType == PublicObjectJobType.OPEN_JOB && jobStatus == PublicObjectJobStatus.SUCCESS)
                    || (jobType == PublicObjectJobType.CLOSE_JOB && jobStatus != PublicObjectJobStatus.SUCCESS);
        } else {
            return jobType == PublicObjectJobType.VERIFY_JOB
                    || (jobType == PublicObjectJobType.OPEN_JOB && jobStatus != PublicObjectJobStatus.SUCCESS)
                    || (jobType == PublicObjectJobType.CLOSE_JOB && jobStatus == PublicObjectJobStatus.SUCCESS);
        }
    }

    /**
     * 判断对象是否支持开启公共对象
     * 1、不在灰度名单不支持
     * 2、没购买互联企业应用包的企业不支持
     * 3、从对象不支持
     * 4、主对象不支持
     *
     * @param user
     * @param objectDescribe
     * @return true 支持 false 不支持
     */
    private boolean enablePublicObject(User user, IObjectDescribe objectDescribe) {
        if (!PublicObjectGrayConfig.isGrayPublicObject(user.getTenantId(), objectDescribe.getApiName())) {
            return false;
        }
        if (!enterpriseRelationLogicService.supportInterconnectBaseAppLicense(user.getTenantId())) {
            return false;
        }
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            return false;
        }
        return !describeLogicService.isMasterObject(user.getTenantId(), objectDescribe.getApiName());
    }

    @Override
    @Transactional
    public String createJob(User user, PublicObjectJobParamDTO param) {
        PublicObjectJobInfo publicObjectJobInfo = param.toPublicObjectJobInfo();
        publicObjectVerifyService.verifyBeforeCreateJob(user, publicObjectJobInfo);
        String jobId = publicObjectJobService.createPublicObjectJob(user, publicObjectJobInfo);
        sendMessage(user, jobId, publicObjectJobInfo);
        return jobId;
    }

    private void sendMessage(User user, String jobId, PublicObjectJobInfo publicObjectJobInfo) {
        if (publicObjectJobInfo.getJobType() == PublicObjectJobType.INVITATION_JOB) {
            publicObjectInviteService.sendInvitationMessage(user, publicObjectJobInfo, jobId);
            return;
        }
        sendPublicObjectMigrateMessage(user, publicObjectJobInfo.getObjectApiName(), jobId);
    }

    private void sendPublicObjectMigrateMessage(User user, String objectApiName, String jobId) {
        PublicObjectJobInfo publicObjectJob = publicObjectJobService.queryPublicObjectJobById(user, objectApiName, jobId);
        if (publicObjectJob.getJobStatus() != PublicObjectJobStatus.WAITING) {
            log.warn("sendPublicObjectMigrate fail! ei:{}, objectApiName:{}, jobId:{}, jobStatus:{}", user.getTenantId(),
                    objectApiName, jobId, publicObjectJob.getJobStatus());
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        PublicObjectJobMessage objectJobMessage = PublicObjectJobMessage.from(publicObjectJob);
        SendResult sendResult = publicObjectMigrateProducer.sendMessage(objectJobMessage.toBytes());
        log.info("sendPublicObjectMigrate success! ei:{}, objectApiName:{}, jobId:{}, jobStatus:{}", user.getTenantId(),
                objectApiName, jobId, sendResult.getMsgId());
    }

    @Override
    public PublicObjectJobResult queryJob(User user, String objectApiName, String jobId) {
        PublicObjectJobInfo publicObjectJobInfo = publicObjectJobService.queryPublicObjectJobById(user, objectApiName, jobId);
        if (Objects.isNull(publicObjectJobInfo)) {
            return null;
        }
        PublicObjectJobResultInfo jobResultInfo = publicObjectJobInfo.getJobResult();
        PublicObjectJobStatus jobStatus = publicObjectJobInfo.getJobStatus();
        if (jobStatus.isExecutionState()) {
            Integer integer = publicObjectJobService.queryPublicObjectJobDetailCount(user, jobId, null);
            PublicObjectJobResultDTO jobResultDTO = PublicObjectJobResultDTO.fromPublicObjectJobResultInfo(jobResultInfo);
            jobResultDTO.setCompleteCount(integer);
            return PublicObjectJobResult.builder()
                    .jobParam(PublicObjectJobParamDTO.fromPublicObjectJobInfo(publicObjectJobInfo))
                    .jobResult(jobResultDTO)
                    .jobType(publicObjectJobInfo.getJobType())
                    .jobStatus(jobStatus)
                    .build();
        }
        List<PublicObjectJobDetailInfo> publicObjectJobDetailInfos = publicObjectJobService.queryPublicObjectJobDetail(user, jobId, PublicObjectJobStatus.FAILED);

        List<PublicObjectJobResultDTO.PublicObjectJobResultItemDTO> resultItemDTOS = getJobResultItems(user, publicObjectJobDetailInfos, jobResultInfo);
        PublicObjectJobResultDTO jobResultDTO = PublicObjectJobResultDTO.fromPublicObjectJobResultInfo(jobResultInfo);
        jobResultDTO.setJobResultItems(resultItemDTOS);
        jobResultDTO.setCompleteCount(jobResultInfo.getTotalCount());
        return PublicObjectJobResult.builder()
                .jobParam(PublicObjectJobParamDTO.fromPublicObjectJobInfo(publicObjectJobInfo))
                .jobResult(jobResultDTO)
                .jobType(publicObjectJobInfo.getJobType())
                .jobStatus(jobStatus)
                .build();
    }

    @Override
    public void updateDisplayStatus(User user, String describeApiName, String jobId, String displayStatus) {
        publicObjectJobService.updatePublicObjectJobDisplayStatus(user, describeApiName, jobId, displayStatus);
    }

    private List<PublicObjectJobResultDTO.PublicObjectJobResultItemDTO> getJobResultItems(User user, List<PublicObjectJobDetailInfo> publicObjectJobDetailInfos, PublicObjectJobResultInfo jobResultInfo) {
        Set<String> tenantIds = publicObjectJobDetailInfos.stream()
                .map(PublicObjectJobDetailInfo::getDownstreamTenantId)
                .collect(Collectors.toSet());
        tenantIds.add(user.getTenantId());
        Map<String, SimpleEnterpriseData> enterpriseInfoMap = getEnterpriseInfoMap(tenantIds);
        List<PublicObjectJobResultDTO.PublicObjectJobResultItemDTO> results = publicObjectJobDetailInfos.stream()
                .map(it -> {
                    String enterpriseName = Optional.ofNullable(enterpriseInfoMap.get(it.getDownstreamTenantId()))
                            .map(SimpleEnterpriseData::getEnterpriseName)
                            .orElse(null);
                    return PublicObjectJobResultDTO.PublicObjectJobResultItemDTO.builder()
                            .tenantId(it.getDownstreamTenantId())
                            .tenantName(enterpriseName)
                            .errorMessage(it.getJobResult().errorMessage())
                            .build();
                })
                .collect(Collectors.toList());
        // 如果1企业有错误，将错误信息到第一条
        if (CollectionUtils.notEmpty(jobResultInfo.getErrorMessages())) {
            String tenantId = user.getTenantId();
            String enterpriseName = Optional.ofNullable(enterpriseInfoMap.get(tenantId))
                    .map(SimpleEnterpriseData::getEnterpriseName)
                    .orElse(tenantId);
            results.add(0, PublicObjectJobResultDTO.PublicObjectJobResultItemDTO.builder()
                    .tenantId(tenantId)
                    .tenantName(enterpriseName)
                    .errorMessage(jobResultInfo.errorMessage())
                    .sharedEnterprise(true)
                    .build());
        }
        return results;
    }

    private Map<String, SimpleEnterpriseData> getEnterpriseInfoMap(Set<String> tenantIds) {
        if (CollectionUtils.empty(tenantIds)) {
            return Collections.emptyMap();
        }
        List<Integer> ids = tenantIds.stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        BatchGetSimpleEnterpriseDataArg arg = new BatchGetSimpleEnterpriseDataArg();
        arg.setEnterpriseIds(ids);
        BatchGetSimpleEnterpriseDataResult dataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(arg);
        return dataResult.getSimpleEnterpriseList().stream()
                .collect(Collectors.toMap(it -> String.valueOf(it.getEnterpriseId()), Function.identity()));
    }

    @Override
    public DesignerResourceResult findDesignerResource(User user, String objectApiName) {
        return publicObjectVerifyService.findDesignerResource(user, objectApiName);
    }

    @Override
    public PublicObjectJobVerifyResult verifyJob(User user, String describeApiName, PublicObjectJobParamVerifyInfo jobParam) {
        IObjectDescribe objectDescribe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        return publicObjectVerifyService.verifyJob(user, jobParam, objectDescribe);
    }

    @Override
    public void appendPublicFields(User user, String describeApiName, List<PublicFieldDTO> fields) {
        IObjectDescribe describeInDB = describeLogicService.findObjectWithoutCopy(user.getTenantId(), describeApiName);
        if (!describeInDB.isPublicObject() || !Objects.equals(user.getTenantId(), describeInDB.getUpstreamTenantId())) {
            log.warn("appendPublicFields fail! ei:{}, describeApiName:{}, upstreamTenantId:{}", user.getTenantId(), describeApiName, describeInDB.getUpstreamTenantId());
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        // 没有新增公共字段，不需要走后续的校验逻辑
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(describeInDB);
        if (!isAppendPublicFields(objectDescribeExt, fields)) {
            return;
        }

        PublicObjectJobParamVerifyInfo jobParam = PublicObjectJobParamVerifyInfo.builder()
                .fields(fields)
                .upstreamTenantId(user.getTenantId())
                .jobType(PublicObjectJobType.APPEND_FIELD)
                .build();
        IObjectDescribe objectDescribe = objectDescribeExt.copy();
        PublicObjectJobVerifyResult verifyResult = publicObjectVerifyService.verifyJob(user, jobParam, objectDescribe);
        if (!verifyResult.success()) {
            throw new ValidateException(verifyResult.getMessage());
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribeExt.copyOnWrite());
        List<IFieldDescribe> toUpdateFields = ObjectDescribeExt.of(objectDescribe).getPublicFields().stream()
                .filter(it -> !IFieldDescribe.DEFINE_TYPE_SYSTEM.equals(it.getDefineType())
                        && !describeExt.getPublicField(it.getApiName()).isPresent())
                .collect(Collectors.toList());
        describeLogicService.updateFieldDescribe(user, describeExt.getObjectDescribe(), toUpdateFields);
    }

    private boolean isAppendPublicFields(ObjectDescribeExt objectDescribeExt, List<PublicFieldDTO> fields) {
        if (CollectionUtils.empty(fields)) {
            return false;
        }
        Set<String> fieldNames = objectDescribeExt.getPublicFields().stream()
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        for (PublicFieldDTO field : fields) {
            if (!fieldNames.contains(field.getFieldApiName())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public PublicObjectJobInvitationInfo findInvitationInfo(User user, String describeApiName, String token) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), describeApiName);
        return publicObjectInviteService.findInvitationInfo(user, describe, token);
    }

    @Override
    public void agreeInvitation(User user, String describeApiName, String token) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), describeApiName);
        PublicObjectInviteService.InviteTokenInfo tokenInfo = publicObjectInviteService.agreeInvitation(user, describe, token);
        sendPublicObjectMigrateMessage(tokenInfo.toUpstreamUser(), describeApiName, tokenInfo.getJobId());

        String enterpriseName = getEnterpriseName(tokenInfo.getUpstreamTenantId());
        logService.log(user, EventType.MODIFY, ActionType.PUBLIC_OBJECT_OPEN, describe.getApiName(),
                I18NExt.text(I18NKey.AGREE_INVITES_PUBLIC_OBJECT, enterpriseName, describe.getDisplayName()));
    }

    @Override
    public void rejectInvitation(User user, String describeApiName, String token) {
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), describeApiName);
        PublicObjectInviteService.InviteTokenInfo tokenInfo = publicObjectInviteService.rejectInvitation(user, describe, token);

        String enterpriseName = getEnterpriseName(tokenInfo.getUpstreamTenantId());
        logService.log(user, EventType.MODIFY, ActionType.PUBLIC_OBJECT_OPEN, describe.getApiName(),
                I18NExt.text(I18NKey.REJECT_INVITES_PUBLIC_OBJECT, enterpriseName, describe.getDisplayName()));
    }

    @Override
    public EnterpriseRelationResult findConnectedEnterpriseRelation(User user, String objectApiName, EnterpriseRelationQueryParam queryParam) {
        return publicObjectEnterpriseRelationService.findConnectedEnterpriseRelation(user, objectApiName, queryParam);
    }

    @Override
    public EnterpriseRelationResult findUnconnectedEnterpriseRelation(User user, String objectApiName, EnterpriseRelationQueryParam queryParam) {
        return publicObjectEnterpriseRelationService.findUnconnectedEnterpriseRelation(user, objectApiName, queryParam);
    }

    @Override
    public void completedCallback(User user, String objectApiName, String jobId, String upstreamTenantId) {
        PublicObjectJobInfo jobInfo = publicObjectJobService.queryPublicObjectJobById(User.systemUser(upstreamTenantId), objectApiName, jobId);
        if (Objects.isNull(jobInfo)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        ActionType actionType = getActionTypeByCallback(user, jobInfo);
        if (Objects.isNull(actionType)) {
            return;
        }
        IObjectDescribe objectDescribe = describeLogicService.findObject(user.getTenantId(), objectApiName);
        //  上游企业回调
        if (Objects.equals(user.getTenantId(), upstreamTenantId)) {
            PublicObjectJobType jobType = jobInfo.getJobType();
            if (PublicObjectJobType.OPEN_JOB != jobType && PublicObjectJobType.CLOSE_JOB != jobType) {
                return;
            }
            String enterpriseName = getEnterpriseName(upstreamTenantId);
            logService.log(user, EventType.MODIFY, actionType, objectDescribe.getApiName(),
                    I18NExt.text(I18NKey.PUBLIC_OBJECT_UPSTREAM_TENANT_OBJECT, enterpriseName, objectDescribe.getDisplayName()));
            postMessage(user, objectDescribe.getApiName());
            return;
        }

        // 下游企业完成回调
        // 开启成功记录修改记录，发送消息
        // 开启失败，邀请任务需要发送消息
        PublicObjectJobDetailInfo publicObjectJobDetailInfo = publicObjectJobService.queryPublicObjectJobDetailByDownstreamTenantId(User.systemUser(upstreamTenantId), objectApiName, jobId, user.getTenantId())
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));

        if (publicObjectJobDetailInfo.getJobStatus() == PublicObjectJobStatus.SUCCESS) {
            String enterpriseName = getEnterpriseName(upstreamTenantId);
            sendNotification(user, actionType, enterpriseName, objectDescribe, publicObjectJobDetailInfo);
            logService.log(user, EventType.MODIFY, actionType, objectDescribe.getApiName(),
                    I18NExt.text(I18NKey.PUBLIC_OBJECT_UPSTREAM_TENANT_OBJECT, enterpriseName, objectDescribe.getDisplayName()));
            return;
        }
        if (PublicObjectJobType.INVITATION_JOB == jobInfo.getJobType()) {
            String enterpriseName = getEnterpriseName(upstreamTenantId);
            sendNotification(user, actionType, enterpriseName, objectDescribe, publicObjectJobDetailInfo);
        }
    }

    private void postMessage(User user, String describeApiName) {
        try {
            ObjectDescribeFinder objectDescribeFinder = ObjectDescribeFinder
                    .builder()
                    .user(user)
                    .visibleScope(Sets.newHashSet(IObjectDescribe.PUBLIC_OBJECT_VISIBLE_SCOPE))
                    .onlyVisibleScope(true)
                    .build();
            List<IObjectDescribe> publicObjectList = describeLogicService.findObjectsByTenantId(objectDescribeFinder);
            // 已开启公共不需要上报，新企业才上报
            if (isFirstPublicObject(describeApiName, publicObjectList)) {
                OperateReportUtil.postMessage(user.getTenantId(), user.getUserId(),
                        ProductLine.CRM_SERVICE, OperateReport.PUBLIC_OBJECT, "num", OperateReport.ADD);
            }
            // 新的对象需要上报
            OperateReportUtil.postMessage(user.getTenantId(), user.getUserId(),
                    ProductLine.CRM_SERVICE, OperateReport.PUBLIC_OBJECT, "object_api_name", OperateReport.ADD, describeApiName, user.getTenantId(), null);
        } catch (Exception e) {
            log.warn("Failed to post message! ei:{}, describeApiName:{}", user.getTenantId(), describeApiName, e);
        }
    }

    private boolean isFirstPublicObject(String describeApiName, List<IObjectDescribe> publicObjectList) {
        if (CollectionUtils.empty(publicObjectList)) {
            return true;
        }
        if (publicObjectList.size() > 1) {
            return false;
        }
        return Objects.equals(describeApiName, publicObjectList.get(0).getApiName());
    }

    private void sendNotification(User user, ActionType actionType, String enterpriseName, IObjectDescribe objectDescribe, PublicObjectJobDetailInfo publicObjectJobDetailInfo) {
        List<String> userIds = userRoleInfoService.getUsersByRole(user, PrivilegeConstants.ADMIN_ROLE_CODE);
        userIds.add(String.valueOf(publicObjectJobDetailInfo.getLastModifiedBy()));
        Set<Integer> receiverIDs = userIds.stream()
                .map(Integer::valueOf)
                .collect(Collectors.toSet());

        Map<String, String> defaultParameterValues = Maps.newHashMap();
        String describeDisplayNameKey = GetI18nKeyUtil.getDescribeDisplayNameKey(objectDescribe.getApiName());
        defaultParameterValues.put(describeDisplayNameKey, objectDescribe.getDisplayName());

        String title = actionType.getName();

        String fullContent;
        InternationalItem fullContentInfo;
        if (publicObjectJobDetailInfo.getJobStatus() == PublicObjectJobStatus.SUCCESS) {
            fullContent = I18NExt.text(I18NKey.PUBLIC_OBJECT_UPSTREAM_TENANT_OBJECT, enterpriseName, objectDescribe.getDisplayName());
            fullContentInfo = InternationalItem.builder()
                    .internationalKey(I18NKey.PUBLIC_OBJECT_UPSTREAM_TENANT_OBJECT)
                    .internationalParameters(Lists.newArrayList(enterpriseName, objectDescribe.getDisplayName()))
                    .defaultParameterValues(defaultParameterValues)
                    .build();
        } else {
            fullContent = I18NExt.text(I18NKey.PUBLIC_OBJECT_UPSTREAM_TENANT_OBJECT, enterpriseName, objectDescribe.getDisplayName()) + ";" +
                    publicObjectJobDetailInfo.getJobResult().errorMessage();
            fullContentInfo = null;
        }

        NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                .senderId(User.SUPPER_ADMIN_USER_ID)
                .type(CRMNotification.CUSTOM_REMIND_RECORD_TYPE)
                .title(title)
                .titleInfo(InternationalItem.builder()
                        .internationalKey(actionType.getI18NKey())
                        .build())
                .remindSender(false)
                .fullContent(fullContent)
                .fullContentInfo(fullContentInfo)
                .appId(AppIdUtil.getAppId(null))
                .receiverIDs(receiverIDs)
                .build();
        crmNotificationService.sendNewCrmNotification(user, newCrmNotification);
    }

    private String getEnterpriseName(String tenantId) {
        Map<String, SimpleEnterpriseData> enterpriseInfoMap = getEnterpriseInfoMap(Sets.newHashSet(tenantId));
        return Optional.ofNullable(enterpriseInfoMap.get(tenantId))
                .map(SimpleEnterpriseData::getEnterpriseName)
                .orElse("");
    }

    public Optional<SimpleEnterpriseInfo> findEnterpriseSimpleInfo(User user, String describeApiName) {
        IObjectDescribe describe = describeLogicService.findObject(user.getTenantId(), describeApiName);
        if (!describe.isPublicObject()) {
            return Optional.empty();
        }
        String upstreamTenantId = describe.getUpstreamTenantId();
        Map<String, SimpleEnterpriseData> enterpriseInfoMap = getEnterpriseInfoMap(Sets.newHashSet(upstreamTenantId));
        return Optional.ofNullable(enterpriseInfoMap.get(upstreamTenantId))
                .map(SimpleEnterpriseInfo::from);
    }

    private ActionType getActionTypeByCallback(User user, PublicObjectJobInfo jobInfo) {
        PublicObjectJobStatus jobStatus = jobInfo.getJobStatus();
        if (jobStatus != PublicObjectJobStatus.SUCCESS && jobStatus != PublicObjectJobStatus.RUNNING) {
            return null;
        }
        PublicObjectJobType jobType = jobInfo.getJobType();
        if (jobType.isOpenJOb()) {
            return ActionType.PUBLIC_OBJECT_OPEN;
        }
        if (jobType.isCloseJob()) {
            return ActionType.PUBLIC_OBJECT_CLOSE;
        }
        return null;
    }
}
