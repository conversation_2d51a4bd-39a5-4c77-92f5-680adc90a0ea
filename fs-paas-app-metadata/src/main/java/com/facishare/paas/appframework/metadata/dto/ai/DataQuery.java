package com.facishare.paas.appframework.metadata.dto.ai;

import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/15
 */
public interface DataQuery {

    /**
     * 自然语言转SQL的文档 https://365.kdocs.cn/l/cggnEhok5X3X
     */
    @Data
    @Builder
    class Arg {
        /**
         * 用户输入
         */
        private String userInput;
        /**
         * 绑定的对象API名称
         */
        private String bindingObjectApiName;
        /**
         * 会话ID
         */
        private String sessionId;
        /**
         * 历史对话记录
         */
        private List<AIDto.Message> history;
        /**
         * 查询参数配置
         */
        private Parameters parameters;
    }

    class Result extends AIDto.Ret<GenerateSearchQueryResult> {


        public ISearchTemplateQuery searchTemplateQuery() {
            if (isSuccess()) {
                return SearchTemplateQueryExt.fromJsonString(getResult().getSearchQuery());
            }
            return null;
        }
    }

    @Data
    class GenerateSearchQueryResult {
        private Boolean success;
        private String objectApiName;
        private List<String> columns;
        private Integer total;
        private String type;
        private String searchQuery;
        private List<Map<String, Object>> dataList;
    }

    @Data
    @Builder
    class Parameters {
        private String model;
        private String embeddingPrompt;
        private String sqlPrompt;
        private Integer ragSize;
        private Integer aiInputSize;
        private Integer maxObject;
        private Boolean hasDetails;
        private Boolean useRag;
        private Boolean useAIobject;
        private Boolean replaceTime;
        private Boolean execute;
        private Boolean limitedObject;
        private List<String> limitedObjectList;
    }
}
