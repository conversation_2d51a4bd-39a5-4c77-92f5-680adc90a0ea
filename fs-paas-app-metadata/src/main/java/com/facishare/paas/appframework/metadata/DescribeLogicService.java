package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.ref.RefMessage;
import com.facishare.paas.appframework.metadata.dto.*;
import com.facishare.paas.appframework.metadata.expansion.DescribeExpansionRender;
import com.facishare.paas.appframework.metadata.expansion.DescribeExtra;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.checker.CheckerResult;
import com.facishare.paas.metadata.api.describe.DynamicDescribe;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectFieldExtra;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.facishare.paas.metadata.ui.layout.ILayout;
import org.redisson.api.RLock;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 定义describe的业务逻辑
 * Created by zhouwr on 2017/10/31
 */
public interface DescribeLogicService {

    /**
     * 根据APIName获取对象描述
     *
     * @param tenantId 企业id
     * @param apiName  对象ApiName
     * @return 对象描述
     */
    IObjectDescribe findObject(String tenantId, String apiName);

    IObjectDescribe findObjectWithDefaultLang(String tenantId, String apiName);

    IObjectDescribe findObjectWithoutCopy(String tenantId, String apiName);

    IObjectDescribe findObjectWithoutCopyIfGray(String tenantId, String apiName);

    IObjectDescribe findObjectUseThreadLocalCache(String tenantId, String apiName);

    IObjectDescribe findObjectWithoutCopyUseThreadLocalCache(String tenantId, String apiName);

    /**
     * 根据DescribeId获取对象描述
     *
     * @param id       Describe Id
     * @param tenantId 企业Id
     * @return 对象描述
     */
    IObjectDescribe findObjectById(String tenantId, String id);

    /**
     * 根据APIName获取对象描述(包含被删除的)
     *
     * @param tenantId
     * @param apiName
     * @return
     */
    IObjectDescribe findObjectIncludeDeleted(String tenantId, String apiName);

    /**
     * 根据版本号查询对象描述
     *
     * @param tenantId 企业id
     * @param apiName  对象的apiName
     * @return
     */
    IObjectDescribe findObjectByRevision(String tenantId, String apiName);

    /**
     * 根据企业id查询全部对象描述
     */
    List<IObjectDescribe> findAllObjectsByTenantId(String tenantId, String describeDefineType, boolean isOnlyActivate,
                                                   boolean isExcludeDetailObj, boolean isExcludeDetailWithMasterCreated, boolean isAsc, String sourceInfo);

    /**
     * 根据企业id获取对象描述列表
     *
     * @param tenantId           企业id
     * @param describeDefineType 对象类型(custom：自定义对象 package：预设对象)
     * @param isOnlyActivate     是否只查询启用的对象
     * @param isExcludeDetailObj 是否排除从对象
     * @param isAsc              是否按照升序排序
     * @return 对象描述列表（不包含字段描述信息）
     */
    List<IObjectDescribe> findObjectsByTenantId(String tenantId, String describeDefineType, boolean isOnlyActivate,
                                                boolean isExcludeDetailObj, boolean isExcludeDetailWithMasterCreated, boolean isAsc, String sourceInfo);

    List<IObjectDescribe> findSystemObjectDescribes(String tenantId, String describeDefineType, boolean isOnlyActivate,
                                                    boolean isExcludeDetailObj, boolean isExcludeDetailWithMasterCreated,
                                                    boolean isAsc);


    List<IObjectDescribe> findObjectsByTenantId(User user, String describeDefineType, boolean isOnlyActivate,
                                                boolean isExcludeDetailObj, boolean isExcludeDetailWithMasterCreated, boolean isAsc, String sourceInfo);

    List<IObjectDescribe> findObjectsByTenantId(User user, String describeDefineType, boolean isOnlyActivate, boolean isExcludeDetailObj, boolean isExcludeDetailWithMasterCreated, boolean isAsc, String sourceInfo, ObjectType objectType);

    List<IObjectDescribe> findObjectsByTenantId(ObjectDescribeFinder objectDescribeFinder);

    List<IObjectDescribe> queryAvailableObject(User user, List<IObjectDescribe> describeList);

    List<IObjectDescribe> findObjectsByTenantId(String tenantId, boolean isOnlyUdObj, boolean isOnlyActivate,
                                                boolean isExcludeDetailObj, boolean isAsc);

    /**
     * 根据企业id获取对象描述列表
     *
     * @param tenantId           企业id
     * @param isOnlyUdObj        是否只查询自定义对象（665预设对象迁移之后该参数已经失效）
     * @param isOnlyActivate     是否只查询启用的对象
     * @param isExcludeDetailObj 是否排除从对象
     * @param isAsc              是否按照升序排序
     * @return 对象描述列表（不包含字段描述信息）
     */
    List<IObjectDescribe> findObjectsByTenantId(String tenantId, boolean isOnlyUdObj, boolean isOnlyActivate,
                                                boolean isExcludeDetailObj, boolean isAsc, String sourceInfo);

    List<IObjectDescribe> findDescribeList(String tenantId, boolean isOnlyUdObj, String packageName,
                                           boolean includeUnActived, boolean isExcludeDetail, boolean isExcludeDetailWithMasterCreated, String sourceInfo);

    List<IObjectDescribe> findByExample(String tenantId, Map<String, Object> example);

    List<IObjectDescribe> findByExample(String tenantId, Map<String, Object> example, IActionContext context);

    /**
     * 批量获取描述
     *
     * @param tenantId
     * @param apiNames
     * @return
     */
    Map<String, IObjectDescribe> findObjects(String tenantId, Collection<String> apiNames);

    Map<String, IObjectDescribe> findObjectsWithoutCopy(String tenantId, Collection<String> apiNames);

    Map<String, IObjectDescribe> findObjectsWithoutCopyIfGray(String tenantId, Collection<String> apiNames);

    List<IObjectDescribe> findObjectList(String tenantId, Collection<String> apiNames);

    /**
     * 批量获取displayName
     *
     * @Deprecated {@link #queryDisplayNameByApiNames(String, List<String>)}
     */
    @Deprecated
    Map<String, String> findDisplayNameByApiNames(String tenantId, Collection<String> apiNames);

    /**
     * 查询剩余可用对象数
     */
    int useableDescribeCount(User user);

    int useableDescribeCount(User user, boolean includeBigObject, boolean includeSocialObject, boolean onlyVisibleScope);

    /**
     * 查询已创建的自定义对象数
     */
    int getCustomDescribeCount(String tenantId);

    int getCustomDescribeCount(String tenantId, boolean includeBigObject, boolean includeSocialObject, boolean onlyVisibleScope);

    /**
     * 查询主对象的所有从对象
     *
     * @param tenantId 企业id
     * @param apiName  主对象的api_name
     * @return 当前主对象的所有从对象
     */
    List<IObjectDescribe> findDetailDescribes(String tenantId, String apiName);

    List<IObjectDescribe> findDetailDescribesWithoutCopy(String tenantId, String apiName);

    List<IObjectDescribe> findDetailDescribesWithoutCopyIfGray(String tenantId, String apiName);

    List<IObjectDescribe> findSimpleDetailDescribes(String tenantId, String apiName);

    /**
     * 查询和主对象一起新建的所有从对象
     */
    List<IObjectDescribe> findDetailDescribesCreateWithMaster(String tenantId, String apiName);

    List<IObjectDescribe> findDescribeListWithoutFields(String tenantId, Collection<String> apiNames, boolean checkDetailObjectButton);

    /**
     * 查询主对象的所有关联对象和从对象
     *
     * @param tenantId 企业id
     * @param apiName  主对象的api_name
     * @return
     */
    List<IObjectDescribe> findRelatedDescribes(String tenantId, String apiName);

    List<IObjectDescribe> findRelatedDescribesWithoutCopy(String tenantId, String apiName);

    List<IObjectDescribe> findRelatedDescribesWithoutCopyIfGray(String tenantId, String apiName);

    /**
     * 查询有列表权限的关联对象和从对象
     *
     * @param user    用户信息
     * @param apiName 主对象的api_name
     * @return
     */
    List<IObjectDescribe> findRelatedDescribesByListPrivilege(User user, String apiName);

    /**
     * 查询主对象的所有相关对象的关联字段
     *
     * @param tenantId
     * @param targetApiName
     * @return
     */
    List<IFieldDescribe> findRelatedFields(String tenantId, String targetApiName);

    /**
     * 查询主对象的所有有列表权限的相关对象的关联字段
     *
     * @param user
     * @param targetApiName
     * @return
     */
    List<IFieldDescribe> findRelatedFieldsByListPrivilege(User user, String targetApiName);

    List<IObjectDescribe> findDetailDescribesCreateWithMasterWithoutCopy(String tenantId, String apiName);

    List<IObjectDescribe> findDetailDescribesCreateWithMasterWithoutCopyIfGray(String tenantId, String apiName);

    /**
     * 查询主从一起新建并且配置了隐藏按钮的从对象
     *
     * @param tenantId
     * @return
     */
    List<String> findDetailApiNamesCreateWithMasterAndHiddenButton(String tenantId);

    /**
     * 查询主对象的所有关联对象
     *
     * @param tenantId 企业id
     * @param apiName  主对象的api_name
     * @return
     */
    List<IObjectDescribe> findLookupDescribes(String tenantId, String apiName, boolean excludeInvalid);

    /**
     * 查询主对象的所有关联多选对象
     *
     * @param tenantId
     * @param apiName
     * @param excludeInvalid
     * @return
     */
    List<IObjectDescribe> findLookupManyDescribes(String tenantId, String apiName, boolean excludeInvalid);

    /**
     * 查询从对象的lookup对象和主对象
     *
     * @param tenantId
     * @param describe
     * @return
     */
    List<IObjectDescribe> findAssociationDescribes(String tenantId, IObjectDescribe describe);

    List<IObjectDescribe> findAssociationDescribesWithoutCopyIfGray(String tenantId, IObjectDescribe describe);

    List<IObjectDescribe> findAssociationDescribes(String tenantId, List<IObjectDescribe> objectDescribes);

    List<IObjectDescribe> filterDescribesWithActionCode(User user, List<IObjectDescribe> describeList, String actionCode);

    /**
     * 查询具有指定功能权限的对象列表
     *
     * @param user               用户信息
     * @param actionCode         功能权限code
     * @param isOnlyUdObj        是否只查询自定义对象（665预设对象迁移以后该参数已经失效）
     * @param isOnlyActivate     是否只查询启用的对象
     * @param isExcludeDetailObj 是否排除从对象
     * @param isAsc              是否按照升序排序
     * @return 对象描述列表（不包含字段描述信息）
     */
    List<IObjectDescribe> findDescribeByPrivilegeAndModule(User user,
                                                           String actionCode,
                                                           boolean isOnlyUdObj,
                                                           boolean isOnlyActivate,
                                                           boolean isExcludeDetailObj,
                                                           boolean isAsc);

    List<IObjectDescribe> findDescribeByPrivilegeAndModule(User user,
                                                           String actionCode,
                                                           boolean isOnlyUdObj,
                                                           boolean isOnlyActivate,
                                                           boolean isExcludeDetailObj,
                                                           boolean isAsc,
                                                           String sourceInfo);

    List<IObjectDescribe> findAssociationDescribesWithoutCopy(String tenantId, List<IObjectDescribe> objectDescribes);

    List<IObjectDescribe> findAssociationDescribesWithoutCopyIfGray(String tenantId, List<IObjectDescribe> objectDescribes);

    boolean isMasterObject(String tenantId, String apiName);

    DescribeResult createDescribe(User user, String jsonData, String jsonLayout, String jsonListLayout, boolean isActive, boolean isIncludeLayout);

    DescribeResult createDescribe(User user, IObjectDescribe objectDescribe, ILayout createLayout, ILayout listLayout, boolean isIncludeLayout);

    IObjectDescribe create(boolean isActive, IObjectDescribe objectDescribe);

    DescribeResult updateDescribe(User user, String jsonData, String jsonLayout, boolean isActive, boolean isIncludeLayout);

    IObjectDescribe deleteDescribe(User user, String apiName);

    IObjectDescribe deleteDescribeDirect(User user, String apiName);

    IObjectDescribe disableDescribe(User user, String apiName);

    IObjectDescribe enableDescribe(User user, String apiName);

    IObjectDescribe addDescribeCustomField(User user, String describeAPIName, String fieldDescribeJson,
                                           List<FieldLayoutPojo> layoutPojoList, List<IFieldDescribe> fieldList);

    IObjectDescribe addDescribeCustomField(User user, String describeAPIName, String fieldDescribeJson, String persistentDataCalc,
                                           List<FieldLayoutPojo> layoutPojoList, List<IFieldDescribe> fieldList);

    IObjectDescribe batchAddDescribeCustomField(User user, String describeAPIName, List<IFieldDescribe> fieldDescribeList);


    boolean updateLookupRoles(String tenantId, String objectApiName, String fieldApiName, Boolean isChecked, String roleType);

    void checkIfHasApprovalFlowDefinition(User user, String describeAPIName);

    boolean updateLookupRolesList(String tenantId, List<String> objectApiNames, String fieldApiName, Boolean isChecked, String roleType);

    IObjectDescribe updateCustomFieldDescribe(User user, String describeAPIName, String fieldDescribeJson,
                                              List<FieldLayoutPojo> layoutPojoList, List<IFieldDescribe> fieldList);

    /**
     * @param fieldList 字段组中的字段
     * @return
     */
    IObjectDescribe updateCustomFieldDescribe(User user, String describeAPIName, String fieldDescribeJson, String persistDataCalc,
                                              List<FieldLayoutPojo> layoutPojoList, List<IFieldDescribe> fieldList);

    IObjectDescribe deleteCustomField(User user, String describeAPIName, String fieldApiName);

    void deleteFieldDirect(User user, IObjectDescribe objectDescribe, Collection<String> fieldNames);

    CheckerResult disableField(User user, String describeApiName, String fieldApiName);

    IObjectDescribe enableField(User user, String describeApiName, String fieldApiName);

    IObjectDescribe enableField(User user, String describeApiName, String fieldApiName, String persistentDataCalc);

    List<IconExt> findIconList();

    DescribeResult findDescribeAndLayout(User user, String describeApiName, boolean isIncludeLayout, String layoutApiName);

    //    DescribeDetailResult
    DescribeDetailResult findDescribeByApiName(RequestContext context,
                                               String describeApiName,
                                               Boolean isIncludeLayout,
                                               String layoutType,
                                               String recordType,
                                               Boolean isIncludeRefDescribeList,
                                               Boolean isIncludeDetailDescribeList,
                                               Boolean isIncludeDeleted,
                                               String dataId);

    DescribeDetailResult findDescribeByApiName(User user,
                                               String describeApiName,
                                               Boolean isIncludeLayout,
                                               String layoutType,
                                               String recordType,
                                               Boolean isIncludeRefDescribeList,
                                               Boolean isIncludeDetailDescribeList,
                                               Boolean isIncludeDeleted,
                                               IObjectData data);

    void processWaterMarkField(IObjectDescribe objectDescribe, User user);

    void processWaterMarkFieldWithExt(IObjectDescribe objectDescribe, User user, Map<String, IObjectDescribe> describeExtMap);

    FieldResult findCustomFieldDescribe(String tenantId, String describeAPIName, String fieldApiName);

    FieldResult findCustomFieldDescribe(String tenantId, String describeAPIName, String fieldApiName, boolean defaultLang);

    void checkDescribeCountLimit(User user);

    QueryResult<ObjectDescribe> findDescribeListWithFields(User user, boolean isActive, int limit, int offset,
                                                           String orderBy, boolean isAsc);

    List<IObjectDescribe> findDescribeListWithFields(User user);

    List<IObjectDescribe> findDescribeListWithoutFields(String tenantId, Collection<String> apiNames);

    void checkDisableFields(User user, String describeApiName, List<String> fieldList);

    void checkCustomFieldCountLimit(User user, IObjectDescribe describeDraft, List<IFieldDescribe> newFieldList);

    List<ResourcesRecord> getCustomFieldCountLimit(User user, String apiName);

    IObjectDescribe initializeDescribe(User user, String jsonData, String jsonDetailLayout, String jsonListLayout);

    IObjectDescribe updateSfaDescribe(User user, String jsonData, String jsonDetailLayout, String jsonListLayout, String detailApiName, Map<String, String> functionsMapping);

    void checkDescribeCountLimit(User user, boolean isEnable);

    void checkDescribeCountLimit(User user, boolean isEnable, boolean isBigObject, boolean isSocialObject);

    void checkDescribeCountLimit(User user, boolean isEnable, boolean isBigObject, boolean isSocialObject, int countOperateNum);

    void checkDescribeMasterDetailLimit(String tenantId, IObjectDescribe objectDescribe);

    void checkMasterDetailDescribeByChangeOrder(User user, IObjectDescribe describe, boolean isCreateDescribe);

    void openDetailObjChangeOrder(User user, IObjectDescribe describe);

    void touchDescribe(IObjectDescribe describe);

    IObjectDescribe updateFieldDescribe(User user, IObjectDescribe describe, List<IFieldDescribe> updatedFields);

    IObjectDescribe updateFieldDescribe(IObjectDescribe describe, List<IFieldDescribe> updatedFields);

    IObjectDescribe update(User user, IObjectDescribe objectDescribe);

    IObjectDescribe update(IObjectDescribe objectDescribe);

    Map<String, DynamicDescribe> findBySelectFields(String tenantId, Map<String, List<String>> describeAndFields,
                                                    List<String> describeSelects, List<String> fieldSelects);

    IObjectDescribe findObjectIncludeMultiField(String tenantId, String apiName);

    Map<String, IObjectDescribe> findObjectsIncludeMultiField(String tenantId, Collection<String> apiNames);

    void batchUpdateLookupRoles(String tenantId, Map<String, List<IFieldDescribe>> fieldDescribeMap);

    void initObjectFunctionPrivilege(User user, String apiName);

    Map<String, Map<String, Object>> fillQuoteFieldOption(IObjectDescribe describe);

    void processSelectOneByStageInstance(User user, IObjectDescribe describe, ILayout layout, IObjectData data);

    List<String> getSelectOneFieldsUsedByStageInstance(User user, IObjectDescribe describe, ILayout layout, IObjectData data);

    IObjectDescribe updateDescribeWithSubmitCalculateJob(IObjectDescribe objectDescribe, User user);

    Map<String, String> getLookupObjectLabels(String tenantId, List<IObjectDescribe> describeList);

    void syncMasterAndDetailOwnerField(User user, IObjectDescribe objectDescribe);

    void syncMasterAndDetailOwnerField(User user, IObjectDescribe objectDescribe, IObjectDescribe objectDescribeInDB);

    Map<String, Map<String, Object>> fillRefObjFieldSupportDisplayName(User user, IObjectDescribe describe);

    DescribeResult updateDescribe(User user, IObjectDescribe objectDescribe, ILayout layout, boolean isIncludeLayout);

    void checkDisplayFields(User user, IObjectDescribe objectDescribe, IObjectDescribe objectDescribeInDB);

    RLock tryLockObject(String tenantId, String objectApiName);

    List<IObjectDescribe> getWhatCountDescribes(String tenantId, List<String> describeApiNames);

    void processWhatListField(IFieldDescribe fieldDescribe, IObjectDescribe objectDescribe);

    List<IObjectDescribe> findDescribeByFieldTypes(String tenantId, List<String> fieldTypes, List<String> groupTypes);

    DescribeExtra updateDescribeExtra(User user, DescribeExtra describeExtra);

    /**
     * 更新字段描述附加属性
     */
    DescribeExtra updateDescribeExtra(User user, String describeApiName, Map<String, Object> describeExtraMap);

    void upsertObjectFieldExtra(User user, String describeApiName, List<IObjectFieldExtra> upsert);

    Map<String, List<IObjectFieldExtra>> findDescribeExtra(User user, Collection<String> describeApiNames);

    DescribeExtra findDescribeExtraByRenderType(User user, IObjectDescribe describe, List<IObjectDescribe> detailDescribeList,
                                                DescribeExpansionRender.RenderType renderType, boolean describeCacheable);

    DescribeExtra findDescribeExtraByRenderType(User user, IObjectDescribe describe, List<IObjectDescribe> detailDescribeList,
                                                DescribeExpansionRender.RenderType renderType, boolean describeCacheable,
                                                Boolean computeCalculateRelation, IObjectData objectData);

    DescribeExtra findDescribeExtra(User user, IObjectDescribe describe);

    Map<String, String> queryDisplayNameByApiNames(String tenantId, List<String> apiNames);

    Map<String, String> queryDisplayNameByApiNames(String tenantId, List<String> apiNames, Boolean includeInvalid);

    boolean isRelatedListFormSupportObject(User user, String targetApiName, IObjectDescribe describe, String relatedFieldName);

    CheckFieldsForCalc.Result checkFieldsForCalc(User user, String describeAPIName, List<String> factor, boolean fieldInfo, List<IFieldDescribe> fieldDescribes);

    void maxCountValidate(User user, boolean fieldInfo, List<IFieldDescribe> newFields);

    Map<String, String> queryAllSlave2Master(User user);

    void batchUpsertRelatedListAddToLayoutConfig(User user, List<ObjectReferenceWrapper> referenceFieldDescribes);

    ManageGroup queryObjectManageGroup(User user, String sourceInfo);

    ManageGroup queryObjectManageGroup(User user, String sourceInfo, boolean useOldMangeGroupGray);

    void batchUpdateFieldDescribe(User user, List<DynamicDescribe> dynamicDescribeList, Map<String, Set<String>> failApiNameMap);

    void executeReferenceByField(RefMessage.ActionType actionType, List<IFieldDescribe> sourceFieldDescList, IObjectDescribe sourceObjDesc);

    /**
     * 对象描述和对象下所有布局
     *
     * @param tenantId        租户ID
     * @param describeApiName 对象API
     * @return 对象描述和对象下所有布局
     */
    DescribeAndLayoutList.Result findDescribeAndLayoutList(String tenantId, String describeApiName);

    void checkPublicObjectBeforeUpdateDescribe(User user, IObjectDescribe describe, IObjectDescribe describeInDB);

    Boolean isExistObjectByApiName(String tenantId, String apiName);

}
