package com.facishare.paas.appframework.metadata;

import cn.hutool.core.lang.copier.Copier;
import com.facishare.crm.operate.report.OperateReportUtil;
import com.facishare.crm.operate.report.model.ProductLine;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.util.FunctionQueryTemplateUtils;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.filter.ObjectDataFilter;
import com.facishare.paas.appframework.metadata.i18nKeyPlatform.OnTimeTransValue;
import com.facishare.paas.appframework.metadata.relation.SourceTypes;
import com.facishare.paas.appframework.metadata.relation.TargetTypes;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.MtConvertRule;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.appframework.metadata.util.OperateReport;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.dto.CreateFunctionPrivilege;
import com.facishare.paas.appframework.privilege.util.ActionCodeConvertUtil;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.service.IObjectMappingRuleService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ActionEnum;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.ObjectMappingParams;
import com.facishare.paas.metadata.impl.ObjectMappingRuleInfo;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.metadata.repository.model.MtConvertRule.*;

@Slf4j
@Service("objectConvertRuleService")
public class ObjectConvertRuleServiceImpl implements ObjectConvertRuleService {
    @Autowired
    private IRepository<MtConvertRule> repository;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private IObjectMappingRuleService mappingRuleService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private FunctionLogicService functionLogicService;
    @Autowired
    private LicenseService licenseService;
    @Autowired
    private I18nSettingService i18nSettingService;

    @Transactional
    @Override
    public void create(User user, List<IObjectMappingRuleInfo> ruleList) {
        if (CollectionUtils.empty(ruleList)) {
            return;
        }
        // 转换规则内部对象构建
        List<MtConvertRule> convertRuleList = ruleList.stream()
                .map(x -> convertConvertRule(x, ruleList.size()))
                .collect(Collectors.toList());
        MtConvertRule masterConvertRule = getMasterConvertRule(convertRuleList);
        validate(user, masterConvertRule);
        repository.bulkUpsert(user, convertRuleList);
        List<IObjectMappingRuleInfo> ruleInfos = Lists.newArrayList();
        ruleList.forEach(rule -> ruleInfos.add(convertMappingRule(rule)));
        objectMappingService.createRule(user, ruleInfos);
        convertRuleList.stream()
                .filter(rule -> StringUtils.isEmpty(rule.getMasterConvertRuleApiName()))
                .findFirst()
                .ifPresent(rule -> {
                    String sourceObjectDescribeApiName = rule.getSourceObjectDescribeApiName();
                    String targetObjectDescribeApiName = rule.getTargetObjectDescribeApiName();
                    addConvertRuleFunctionPrivilege(User.systemUser(user.getTenantId()), sourceObjectDescribeApiName, targetObjectDescribeApiName);
                });

        saveReference(user, masterConvertRule);
        postMessage(user, convertRuleList, masterConvertRule);
    }

    private void validate(User user, MtConvertRule masterConvertRule) {
        MtConvertRule existConvertRule = findOne(user, masterConvertRule.getApiName(), null);
        if (Objects.nonNull(existConvertRule)) {
            throw new ValidateException(I18NExt.text(I18NKey.CONVERT_RULE_API_NAME_EXIST));
        }
    }

    private static void postMessage(User user, List<MtConvertRule> convertRuleList, MtConvertRule masterConvertRule) {
        // 设置增加 生成了多少规则 埋点
        OperateReportUtil.postMessage(user.getTenantId(), user.getUserId(),
                ProductLine.CRM_SERVICE, OperateReport.CONVERT_RULE, "num", OperateReport.ADD);
        Boolean backWrite = BooleanUtils.isTrue(masterConvertRule.getBackWrite());
        // 设置增加 回写规则开关 埋点
        OperateReportUtil.postMessage(user.getTenantId(), user.getUserId(),
                ProductLine.CRM_SERVICE, OperateReport.CONVERT_RULE, "back-write-switch", BooleanUtils.toStringTrueFalse(backWrite));
        // 设置增加 关闭逻辑开关 埋点
        Boolean closeLogic = BooleanUtils.isTrue(masterConvertRule.getCloseLogic());
        OperateReportUtil.postMessage(user.getTenantId(), user.getUserId(),
                ProductLine.CRM_SERVICE, OperateReport.CONVERT_RULE, "close-logic-switch", BooleanUtils.toStringTrueFalse(closeLogic));
        // 规则中源对象名称和目标对象名称，对应数量数多少 埋点
        convertRuleList.forEach(rule -> {
            OperateReportUtil.postMessage(user.getTenantId(), user.getUserId(),
                    ProductLine.CRM_SERVICE, OperateReport.CONVERT_RULE, "source-object", OperateReport.ADD, rule.getSourceObjectDescribeApiName(), rule.getId(), null);
            OperateReportUtil.postMessage(user.getTenantId(), user.getUserId(),
                    ProductLine.CRM_SERVICE, OperateReport.CONVERT_RULE, "target-object", OperateReport.ADD, rule.getTargetObjectDescribeApiName(), rule.getId(), null);
        });
    }

    private void addConvertRuleFunctionPrivilege(User user, String sourceObjectDescribeApiName, String targetObjectDescribeApiName) {
        List<CreateFunctionPrivilege.FunctionPojo> functions = Lists.newArrayList();
        functions.add(CreateFunctionPrivilege.FunctionPojo
                .buildFunctionPojo(user.getTenantId(), sourceObjectDescribeApiName, ObjectAction.TRANSFORM.getActionCode()));
        functions.add(CreateFunctionPrivilege.FunctionPojo
                .buildFunctionPojo(user.getTenantId(), targetObjectDescribeApiName, ObjectAction.REFERENCE_CREATE.getActionCode()));
        functionPrivilegeService.batchCreateFunc(user, functions);
        List<String> addFuncCodes = Lists.newArrayList();
        addFuncCodes.add(ActionCodeConvertUtil.convert2FuncCode(sourceObjectDescribeApiName, ObjectAction.TRANSFORM.getActionCode()));
        addFuncCodes.add(ActionCodeConvertUtil.convert2FuncCode(targetObjectDescribeApiName, ObjectAction.REFERENCE_CREATE.getActionCode()));
        functionPrivilegeService.updateUserDefinedFuncAccess(user, PrivilegeConstants.ADMIN_ROLE_CODE, addFuncCodes, Lists.newArrayList());
    }

    private void saveReference(User user, MtConvertRule mtConvertRule) {
        if (!StringUtils.equals(WHERE_TYPE_FUNCTION, mtConvertRule.getWhereType())) {
            return;
        }
        String functionApiName = FunctionQueryTemplateUtils.getFunctionApiNameByWheres(WheresExt.castToWheresList(mtConvertRule.getWheres()));
        if (StringUtils.isEmpty(functionApiName)) {
            return;
        }
        ReferenceData referenceData = ReferenceData.builder()
                .sourceType(SourceTypes.SCOPE_RULE)
                .sourceLabel(mtConvertRule.getName())
                .sourceValue(mtConvertRule.getApiName())
                .targetType(TargetTypes.FUNCTION)
                .targetValue(functionApiName)
                .build();
        functionLogicService.saveRelation(user, Lists.newArrayList(referenceData));
    }

    private void saveAndCleanReference(User user, MtConvertRule mtConvertRule, MtConvertRule mtConvertRuleInDB) {
        if (Objects.isNull(mtConvertRuleInDB)) {
            return;
        }
        if (!StringUtils.equals(WHERE_TYPE_FUNCTION, mtConvertRuleInDB.getWhereType())
                && !StringUtils.equals(WHERE_TYPE_FUNCTION, mtConvertRule.getWhereType())) {
            return;
        }
        String functionApiNameInDB = FunctionQueryTemplateUtils.getFunctionApiNameByWheres(WheresExt.castToWheresList(mtConvertRuleInDB.getWheres()));
        String functionApiName = FunctionQueryTemplateUtils.getFunctionApiNameByWheres(WheresExt.castToWheresList(mtConvertRule.getWheres()));
        if (StringUtils.equals(functionApiNameInDB, functionApiName)) {
            return;
        }
        if (StringUtils.isNotEmpty(functionApiNameInDB)) {
            deleteFunctionReference(user, Lists.newArrayList(mtConvertRuleInDB));
        }
        if (StringUtils.isEmpty(functionApiName)) {
            return;
        }
        ReferenceData referenceData = ReferenceData.builder()
                .sourceType(SourceTypes.SCOPE_RULE)
                .sourceLabel(mtConvertRule.getName())
                .sourceValue(mtConvertRule.getApiName())
                .targetType(TargetTypes.FUNCTION)
                .targetValue(functionApiName)
                .build();
        functionLogicService.saveRelation(user, Lists.newArrayList(referenceData));
    }

    private IObjectMappingRuleInfo convertMappingRule(IObjectMappingRuleInfo rule) {
        IObjectMappingRuleInfo ruleInfo = new ObjectMappingRuleInfo();
        ruleInfo.setAction(ActionEnum.CONVERSION_RULES);
        ruleInfo.setRuleName(rule.getRuleName());
        ruleInfo.setTenantId(rule.getTenantId());
        ruleInfo.setRuleApiName(rule.getRuleApiName());
        ruleInfo.setMasterApiName(rule.getMasterApiName());
        ruleInfo.setMasterRuleApiName(rule.getMasterRuleApiName());
        ruleInfo.setTargetApiName(rule.getTargetApiName());
        ruleInfo.setSourceApiName(rule.getSourceApiName());
        ruleInfo.setFieldMapping(rule.getFieldMapping());
        ruleInfo.setDefineType(rule.getDefineType());
        ruleInfo.setStatus(rule.getStatus());
        return ruleInfo;
    }

    private MtConvertRule convertConvertRule(IObjectMappingRuleInfo rule) {
        return convertConvertRule(rule, 1);
    }

    private MtConvertRule convertConvertRule(IObjectMappingRuleInfo rule, int ruleSize) {
        MtConvertRule mtConvertRule = new MtConvertRule();
        mtConvertRule.setId(rule.getId());
        mtConvertRule.setName(rule.getRuleName());
        mtConvertRule.setApiName(rule.getRuleApiName());
        mtConvertRule.setRuleApiName(rule.getRuleApiName());
        mtConvertRule.setSourceObjectDescribeApiName(rule.getSourceApiName());
        mtConvertRule.setTargetObjectDescribeApiName(rule.getTargetApiName());
        mtConvertRule.setActive(rule.getStatus() == 0);
        mtConvertRule.setMasterConvertRuleApiName(rule.getMasterRuleApiName());
        mtConvertRule.setCreateTime(rule.getCreateTime());
        if (StringUtils.isBlank(rule.getMasterApiName())) {
            mtConvertRule.setMdType(MASTER_OBJECT);
        } else {
            mtConvertRule.setMdType(DETAIL_OBJECT);
        }
        if (ruleSize == 1) {
            mtConvertRule.setMdType(SINGLE_OBJECT);
        }
        mtConvertRule.setDefineType(rule.get(DEFINE_TYPE, String.class));
        mtConvertRule.setAssociatedFieldApiName(rule.get(ASSOCIATED_FIELD_API_NAME, String.class));
        mtConvertRule.setWhereType(rule.get(WHERE_TYPE, String.class));
        mtConvertRule.setWheres(rule.get(WHERES, List.class));
        mtConvertRule.setMessage(rule.get(MESSAGE, String.class));
        mtConvertRule.setBackWrite(rule.get(IS_BACK_WRITE, Boolean.class));
        mtConvertRule.setBackWriteModel(rule.get(BACK_WRITE_MODEL, String.class));
        mtConvertRule.setCloseLogic(rule.get(IS_CLOSE_LOGIC, Boolean.class));
        mtConvertRule.setCloseStrategy(rule.get(CLOSE_STRATEGY, Integer.class));
        mtConvertRule.setCloseFieldApiName(rule.get(CLOSE_FIELD_API_NAME, String.class));
        Object closeFieldValue = rule.get(CLOSE_FIELD_VALUE);
        mtConvertRule.setCloseFieldValue(Objects.nonNull(closeFieldValue) ? String.valueOf(closeFieldValue) : "");
        mtConvertRule.setCloseWheres(rule.get(CLOSE_WHERES, List.class));
        mtConvertRule.setExcessCheckMode(rule.get(EXCESS_CHECK_MODE, String.class));
        mtConvertRule.setExcessCondition(rule.get(EXCESS_CONDITION, String.class));
        mtConvertRule.setDefaultToZero(rule.get(DEFAULT_TO_ZERO, Boolean.class));
        mtConvertRule.setExcessPrompt(rule.get(EXCESS_PROMPT, String.class));
        mtConvertRule.setRemark(rule.get(REMARK, String.class));
        mtConvertRule.setSceneType(rule.get(SCENE_TYPE, Integer.class));
        mtConvertRule.setStrategy(rule.get(STRATEGY, Integer.class));
        CombinedFields combinedFields = castToCombinedFields(rule.get(COMBINED_BY_FIELDS, Map.class));
        mtConvertRule.setCombinedByFields(combinedFields);
        return mtConvertRule;
    }

    @Override
    @Transactional
    public void update(User user, List<IObjectMappingRuleInfo> ruleList) {
        if (CollectionUtils.empty(ruleList)) {
            return;
        }
        List<MtConvertRule> mtConvertRuleList2Create = Lists.newArrayList();
        List<MtConvertRule> mtConvertRuleList2Update = Lists.newArrayList();
        List<MtConvertRule> mtConvertRuleList2Delete = Lists.newArrayList();
        List<IObjectMappingRuleInfo> ruleInfos = Lists.newArrayList();
        ruleList.forEach(rule -> {
            if (StringUtils.isEmpty(rule.getId())) {
                mtConvertRuleList2Create.add(convertConvertRule(rule, ruleList.size()));
            } else {
                mtConvertRuleList2Update.add(convertConvertRule(rule, ruleList.size()));
            }
            ruleInfos.add(convertMappingRule(rule));
        });
        IObjectMappingRuleInfo mappingRuleInfo = ObjectMappingExt.of(ruleList).getObjectMappingRuleInfo();
        List<MtConvertRule> mtConvertRules = findConvertRuleInInternalObjByApiName(user, mappingRuleInfo.getRuleApiName());
        mtConvertRules.forEach(rule -> {
            if (ruleList.stream().noneMatch(r -> Objects.equals(r.getRuleApiName(), rule.getApiName()))) {
                mtConvertRuleList2Delete.add(rule);
            }
        });
        MtConvertRule masterConvertRule = getMasterConvertRule(mtConvertRuleList2Update);
        masterConvertRule.setLastModifiedTime(System.currentTimeMillis());
        IActionContext context = ActionContextExt.of(user, RequestContextManager.getContext())
                .setIsSpecifyTime(Boolean.TRUE)
                .setSkipRemoveNotChangeData(Boolean.TRUE)
                .getContext();
        // 全量更新可更新最后修改时间
        repository.bulkUpdate(context, user, Lists.newArrayList(masterConvertRule));
        mtConvertRuleList2Update.removeIf(rule -> StringUtils.equals(rule.getApiName(), masterConvertRule.getApiName()));
        repository.bulkUpdate(user, mtConvertRuleList2Update);
        repository.bulkUpsert(user, mtConvertRuleList2Create);
        repository.bulkDelete(user, mtConvertRuleList2Delete);
        objectMappingService.updateRule(user, ruleInfos);
        mtConvertRuleList2Update.add(masterConvertRule);
        syncTransValue(mtConvertRuleList2Update, user.getTenantId());
        saveAndCleanReference(user, convertConvertRule(mappingRuleInfo), getMasterConvertRule(mtConvertRules));
    }

    private void syncTransValue(List<MtConvertRule> mtConvertRuleList2Update, String tenantId) {
        Map<String, String> keyToNewName = Maps.newHashMap();
        for (MtConvertRule mtConvertRule : mtConvertRuleList2Update) {
            keyToNewName.put(TranslateUtils.getConvertRuleNameKey(mtConvertRule.getRuleApiName()), mtConvertRule.getName());
            keyToNewName.put(TranslateUtils.getDataRangePromptKey(mtConvertRule.getRuleApiName()), mtConvertRule.getMessage());
            keyToNewName.put(TranslateUtils.getExcessInspectionPromptKey(mtConvertRule.getRuleApiName()), mtConvertRule.getExcessPrompt());
        }
        i18nSettingService.syncTransValue(keyToNewName, I18N.getContext().getLanguage(), tenantId);
    }

    @Override
    public List<IObjectMappingRuleInfo> findRuleList(User user, String ruleName, String searchQueryInfo) {
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter(),
                FilterExt.of(Operator.NEQ, MD_TYPE, String.valueOf(DETAIL_OBJECT)).getFilter());
        if (StringUtils.isNoneBlank(ruleName)) {
            filters.add(FilterExt.of(Operator.LIKE, IObjectData.NAME, ruleName).getFilter());
        }
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        Query query = builderBySearchQuery(searchQuery);
        query.resetOrders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.LAST_MODIFIED_TIME, false)));
        List<MtConvertRule> convertRules = repository.findBy(user, query, MtConvertRule.class);
        // todo 内存中过滤，后续可优化需底层支持
        Map<String, String> displayNames = queryDescribeDisplayNameByConvertRules(user, convertRules);
        batchTranslateValue(convertRules, user, true);
        List<IObjectMappingRuleInfo> ruleList = convertRules.stream().map(rule -> simpleConvertMtConvertRuleToMappingRule(rule, displayNames)).collect(Collectors.toList());
        filterRuleBySearchQueryInfo(searchQueryInfo, ruleList);
        ruleList.sort(Comparator.comparingInt(rule -> MtConvertRule.DEFINE_TYPE_SYSTEM.equals(rule.getDefineType()) ? -1 : 1));
        return ruleList;
    }

    @Override
    public List<IObjectMappingRuleInfo> findRuleListByOriginalApiName(User user, String originalApiName) {
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter(),
                FilterExt.of(Operator.NEQ, MD_TYPE, String.valueOf(DETAIL_OBJECT)).getFilter());
        if (StringUtils.isNoneBlank(originalApiName)) {
            filters.add(FilterExt.of(Operator.LIKE, "source_object_describe_api_name", originalApiName).getFilter());
        }
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        Query query = builderBySearchQuery(searchQuery);
        query.resetOrders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.LAST_MODIFIED_TIME, false)));
        List<MtConvertRule> convertRules = repository.findBy(user, query, MtConvertRule.class);
        batchTranslateValue(convertRules, user, false);
        return convertRules.stream().map(rule -> simpleConvertMtConvertRuleToMappingRule(rule, Maps.newHashMap())).collect(Collectors.toList());
    }

    private void filterRuleBySearchQueryInfo(String searchQueryInfo, List<IObjectMappingRuleInfo> ruleList) {
        if (StringUtils.isEmpty(searchQueryInfo)) {
            return;
        }
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(SearchTemplateQueryExt.fromJsonString(searchQueryInfo));
        ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                .queryExt(searchTemplateQueryExt)
                .build();
        List<IObjectData> objectDataList = ruleList.stream()
                .map(this::convertObjectData)
                .collect(Collectors.toList());
        List<String> ruleIds = dataFilter.doFilter(objectDataList, true)
                .stream()
                .map(IObjectData::getId)
                .collect(Collectors.toList());
        ruleList.removeIf(rule -> !ruleIds.contains(rule.getId()));
    }

    private IObjectData convertObjectData(IObjectMappingRuleInfo rule) {
        IObjectData objectData = new ObjectData();
        objectData.setId(rule.getId());
        objectData.set(IObjectMappingRuleInfo.RUlE_NAME, rule.getRuleName());
        objectData.set(IObjectMappingRuleInfo.SOURCE_DISPLAY_NAME, rule.getSourceDisplayName());
        objectData.set(IObjectMappingRuleInfo.TARGET_DISPLAY_NAME, rule.getTargetDisplayName());
        return objectData;
    }

    private Map<String, String> queryDescribeDisplayNameByConvertRules(User user, List<MtConvertRule> convertRules) {
        Set<String> apiNameSet = Sets.newHashSet();
        convertRules.forEach(a -> {
            apiNameSet.add(a.getSourceObjectDescribeApiName());
            apiNameSet.add(a.getTargetObjectDescribeApiName());
        });

        return describeLogicService.queryDisplayNameByApiNames(user.getTenantId(), Lists.newArrayList(apiNameSet));
    }

    private IObjectMappingRuleInfo convertMtConvertRuleToMappingRule(Map<String, IObjectDescribe> objectDescribes, MtConvertRule mtConvertRule, Map<String, String> displayNames) {
        IObjectMappingRuleInfo ruleInfo = simpleConvertMtConvertRuleToMappingRule(mtConvertRule, displayNames);
        convertCloseFieldValueByFieldType(objectDescribes, mtConvertRule, ruleInfo);
        ruleInfo.set(ASSOCIATED_FIELD_API_NAME, mtConvertRule.getAssociatedFieldApiName());
        ruleInfo.set(WHERE_TYPE, mtConvertRule.getWhereType());
        ruleInfo.set(WHERES, CollectionUtils.nullToEmpty(mtConvertRule.getWheres()));
        ruleInfo.set(MESSAGE, mtConvertRule.getMessage());
        ruleInfo.set(IS_BACK_WRITE, mtConvertRule.getBackWrite());
        ruleInfo.set(BACK_WRITE_MODEL, mtConvertRule.getBackWriteModel());
        ruleInfo.set(IS_CLOSE_LOGIC, mtConvertRule.getCloseLogic());
        ruleInfo.set(CLOSE_STRATEGY, mtConvertRule.getCloseStrategy());
        ruleInfo.set(CLOSE_FIELD_API_NAME, mtConvertRule.getCloseFieldApiName());
        ruleInfo.set(CLOSE_WHERES, CollectionUtils.nullToEmpty(mtConvertRule.getCloseWheres()));
        ruleInfo.set(EXCESS_CHECK_MODE, mtConvertRule.getExcessCheckMode());
        ruleInfo.set(EXCESS_CONDITION, mtConvertRule.getExcessCondition());
        ruleInfo.set(DEFAULT_TO_ZERO, mtConvertRule.getDefaultToZero());
        ruleInfo.set(EXCESS_PROMPT, mtConvertRule.getExcessPrompt());
        ruleInfo.set(REMARK, mtConvertRule.getRemark());
        ruleInfo.set(COMBINED_BY_FIELDS, mtConvertRule.getCombinedByFields());
        return ruleInfo;
    }

    private static void convertCloseFieldValueByFieldType(Map<String, IObjectDescribe> objectDescribes, MtConvertRule mtConvertRule, IObjectMappingRuleInfo ruleInfo) {
        String closeFieldApiName = mtConvertRule.getCloseFieldApiName();
        if (StringUtils.isEmpty(closeFieldApiName) || Objects.isNull(mtConvertRule.getCloseFieldValue())) {
            return;
        }
        Optional.of(objectDescribes.get(mtConvertRule.getSourceObjectDescribeApiName()))
                .ifPresent(describe -> ruleInfo.set(CLOSE_FIELD_VALUE, mtConvertRule.convertCloseFieldValueSilently(describe)));
    }


    private static IObjectMappingRuleInfo simpleConvertMtConvertRuleToMappingRule(MtConvertRule rule, Map<String, String> displayNames) {
        IObjectMappingRuleInfo ruleInfo = new ObjectMappingRuleInfo();
        ruleInfo.setId(rule.getId());
        ruleInfo.setRuleName(rule.getName());
        ruleInfo.setRuleApiName(rule.getApiName());
        String targetObjectDescribeApiName = rule.getTargetObjectDescribeApiName();
        ruleInfo.setTargetApiName(targetObjectDescribeApiName);
        String sourceObjectDescribeApiName = rule.getSourceObjectDescribeApiName();
        ruleInfo.setSourceApiName(sourceObjectDescribeApiName);
        if (CollectionUtils.notEmpty(displayNames)) {
            ruleInfo.setTargetDisplayName(displayNames.get(targetObjectDescribeApiName));
            ruleInfo.setSourceDisplayName(displayNames.get(sourceObjectDescribeApiName));
        }
        ruleInfo.setDefineType(rule.getDefineType());
        ruleInfo.setMasterRuleApiName(rule.getMasterConvertRuleApiName());
        ruleInfo.setDefineType(rule.getDefineType());
        ruleInfo.setLastModifiedBy(rule.getLastModifiedBy());
        ruleInfo.setLastModifiedTime(rule.getLastModifiedTime());
        ruleInfo.setCreatedBy(rule.getCreateBy());
        ruleInfo.setCreateTime(rule.getCreateTime());
        ruleInfo.setStatus(rule.isActive() ? 0 : 1);
        ruleInfo.set(SCENE_TYPE, rule.getSceneType());
        ruleInfo.set(STRATEGY, rule.getStrategy());
        return ruleInfo;
    }

    @Override
    public void updateStatus(User user, String ruleApiName, int status) {
        boolean isActive = status == 0;
        List<MtConvertRule> convertRules = findConvertRuleInInternalObjByApiName(user, ruleApiName, !isActive);
        if (CollectionUtils.empty(convertRules)) {
            return;
        }
        if (isActive) {
            //校验目标对象是否存在
            String targetApiName = convertRules.get(0).getTargetObjectDescribeApiName();
            IObjectDescribe target = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), targetApiName);
            if (Objects.isNull(target) || Objects.equals(target.isActive(), Boolean.FALSE)) {
                throw new ValidateException(I18N.text(I18NKey.TARGET_OBJECT_UNEXIST_OR_DISABLED));
            }

            String sourceApiName = convertRules.get(0).getSourceObjectDescribeApiName();
            IObjectDescribe source = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), sourceApiName);
            if (Objects.isNull(source) || Objects.equals(source.isActive(), Boolean.FALSE)) {
                throw new ValidateException(I18N.text(I18NKey.SOURCE_OBJECT_UNEXIST_OR_DISABLED));
            }
        }
        convertRules.forEach(convertRule -> convertRule.setActive(isActive));
        repository.bulkUpdateByFields(user, convertRules, Lists.newArrayList(IS_ACTIVE));
    }


    @Override
    public void disableRuleByTargetDescribe(User user, String describeApiName) {
        List<IFilter> filters = getActiveFilters(user);
        addFilters(filters, null, describeApiName);
        bulkDisableByFilter(user, filters);
    }

    @Override
    public void disableRuleBySourceDescribe(User user, String describeApiName) {
        List<IFilter> filters = getActiveFilters(user);
        addFilters(filters, describeApiName, null);
        bulkDisableByFilter(user, filters);

    }

    private void bulkDisableByFilter(User user, List<IFilter> filters) {
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .build();
        List<MtConvertRule> mtConvertRules = repository.findBy(user, query, MtConvertRule.class);
        mtConvertRules.forEach(rule -> rule.setActive(false));
        repository.bulkUpdate(user, mtConvertRules);
    }

    @Override
    @Transactional
    public void delete(User user, String ruleApiName) {
        List<MtConvertRule> mtConvertRules = findConvertRuleInInternalObjByApiName(user, ruleApiName);
        repository.bulkDelete(user, mtConvertRules);
        List<String> ruleApiNames = Lists.newArrayList();
        mtConvertRules.forEach(rule -> ruleApiNames.add(rule.getRuleApiName()));
        deleteMappingRuleByApiName(user, ruleApiNames);
        deleteFunctionReference(user, mtConvertRules);
    }

    private void deleteFunctionReference(User user, List<MtConvertRule> convertRuleList) {
        MtConvertRule masterConvertRule = getMasterConvertRule(convertRuleList);
        if (Objects.isNull(masterConvertRule)) {
            return;
        }
        if (StringUtils.equals(WHERE_TYPE_FUNCTION, masterConvertRule.getWhereType())) {
            String functionApiName = FunctionQueryTemplateUtils.getFunctionApiNameByWheres(WheresExt.castToWheresList(masterConvertRule.getWheres()));
            if (StringUtils.isEmpty(functionApiName)) {
                return;
            }
            functionLogicService.deleteRelation(user, SourceTypes.SCOPE_RULE, masterConvertRule.getApiName(), functionApiName);
        }
    }

    private void deleteMappingRuleByApiName(User user, List<String> ruleApiNames) {
        try {
            mappingRuleService.bulkDeleteByRuleApiName(user.getTenantId(), ruleApiNames);
        } catch (Exception e) {
            log.warn("Error in delete mapping rule", e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public List<IObjectMappingRuleInfo> findConvertRuleByApiName(User user, String ruleApiName) {
        if (StringUtils.isEmpty(ruleApiName)) {
            return Lists.newArrayList();
        }
        List<MtConvertRule> mtConvertRuleList = findConvertRuleInInternalObjByApiName(user, ruleApiName, null, true);
        if (CollectionUtils.empty(mtConvertRuleList)) {
            return Lists.newArrayList();
        }
        Map<String, IObjectDescribe> objectDescribeMap = getObjectDescribeMapFromConvertRuleList(user, mtConvertRuleList);
        List<IObjectMappingRuleInfo> mtConvertRules = convert2MappingRuleInfos(user, objectDescribeMap, mtConvertRuleList);
        List<IObjectMappingRuleInfo> mappingRuleInfoList = findObjectMappingRuleByApiName(user, ruleApiName);
        mtConvertRules.forEach(convertRule -> {
            String convertRuleApiName = convertRule.getRuleApiName();
            mappingRuleInfoList.stream()
                    .filter(rule -> Objects.equals(rule.getRuleApiName(), convertRuleApiName))
                    .findFirst()
                    .ifPresent(rule -> {
                        convertRule.setFieldMapping(CollectionUtils.nullToEmpty(rule.getFieldMapping()));
                        convertRule.setMasterApiName(rule.getMasterApiName());
                    });
        });
        return mtConvertRules;
    }

    private Map<String, IObjectDescribe> getObjectDescribeMapFromConvertRuleList(User user, List<MtConvertRule> mtConvertRuleList) {
        List<String> sourceApiNames = mtConvertRuleList.stream()
                .map(MtConvertRule::getSourceObjectDescribeApiName)
                .collect(Collectors.toList());
        return describeLogicService.findObjectsWithoutCopy(user.getTenantId(), sourceApiNames);
    }

    private List<IObjectMappingRuleInfo> convert2MappingRuleInfos(User user, Map<String, IObjectDescribe> objectDescribeMap, List<MtConvertRule> mtConvertRuleList) {
        Map<String, String> displayNames = queryDescribeDisplayNameByConvertRules(user, mtConvertRuleList);
        List<IObjectMappingRuleInfo> mappingRuleList = mtConvertRuleList.stream()
                .map(rule -> convertMtConvertRuleToMappingRule(objectDescribeMap, rule, displayNames))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(mtConvertRuleList) && CollectionUtils.empty(mappingRuleList)) {
            throw new ValidateException(I18NExt.text(I18NKey.DATA_NOT_MATCH_CONVERT_RULE));
        }
        return mappingRuleList;
    }

    private List<IObjectMappingRuleInfo> convert2MappingRuleInfos4PullOrder(User user, Map<String, IObjectDescribe> objectDescribeMap, List<MtConvertRule> mtConvertRuleList, List<IObjectMappingRuleInfo> mappingRuleInfoList, String recordType) {
        Map<String, String> displayNames = queryDescribeDisplayNameByConvertRules(user, mtConvertRuleList);
        List<IObjectMappingRuleInfo> mappingRuleList = mtConvertRuleList.stream()
                .filter(rule -> StringUtils.isEmpty(recordType) || isRecordTypeMatch(rule, mappingRuleInfoList, Lists.newArrayList(recordType), true))
                .map(rule -> convertMtConvertRuleToMappingRule(objectDescribeMap, rule, displayNames))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(mtConvertRuleList) && CollectionUtils.empty(mappingRuleList)) {
            throw new ValidateException(I18NExt.text(I18NKey.DATA_NOT_MATCH_CONVERT_RULE));
        }
        return mappingRuleList;
    }

    private List<IObjectMappingRuleInfo> convert2MappingRuleInfos4PushOrder(User user, Map<String, IObjectDescribe> objectDescribeMap, List<MtConvertRule> mtConvertRuleList, List<IObjectMappingRuleInfo> mappingRuleInfoList, List<IObjectData> sourceDataList) {
        Map<String, String> displayNames = queryDescribeDisplayNameByConvertRules(user, mtConvertRuleList);
        List<IObjectMappingRuleInfo> mappingRuleList = mtConvertRuleList.stream()
                .filter(rule -> pullOrderRuleFilter(objectDescribeMap, rule, sourceDataList, mappingRuleInfoList))
                .map(rule -> convertMtConvertRuleToMappingRule(objectDescribeMap, rule, displayNames))
                .collect(Collectors.toList());
        if (CollectionUtils.notEmpty(mtConvertRuleList) && CollectionUtils.empty(mappingRuleList)) {
            throw new ValidateException(I18NExt.text(I18NKey.DATA_NOT_MATCH_CONVERT_RULE));
        }
        return mappingRuleList;
    }

    private boolean pullOrderRuleFilter(Map<String, IObjectDescribe> objectDescribeMap, MtConvertRule rule, List<IObjectData> sourceDataList, List<IObjectMappingRuleInfo> mappingRuleInfoList) {
        if (CollectionUtils.empty(sourceDataList)) {
            return true;
        }
        List<String> recordTypes = sourceDataList.stream().map(IObjectData::getRecordType).distinct().collect(Collectors.toList());
        if (!isRecordTypeMatch(rule, mappingRuleInfoList, recordTypes, false)) {
            return false;
        }
        IObjectDescribe objectDescribe = objectDescribeMap.get(rule.getSourceObjectDescribeApiName());
        if (rule.needCloseSourceOrder()) {
            String closeFieldApiName = rule.getCloseFieldApiName();
            boolean anyMatch = sourceDataList.stream()
                    .anyMatch(sourceObjectData -> Objects.equals(sourceObjectData.get(closeFieldApiName), rule.convertCloseFieldValueSilently(objectDescribe)));
            if (anyMatch) {
                return false;
            }
        }
        if (MtConvertRule.WHERE_TYPE_FUNCTION.equals(rule.getWhereType())) {
            return true;
        }
        List<String> ids = sourceDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        IFilter filter = FilterExt.of(Operator.IN, IObjectData.ID, ids).getFilter();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.ofWheresAndFilter(rule.toWheres(objectDescribe), filter);
        ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                .describeExt(ObjectDescribeExt.of(objectDescribe))
                .queryExt(queryExt)
                .build();
        List<IObjectData> objectDataList = dataFilter.doFilter(sourceDataList, true);
        return sourceDataList.size() == objectDataList.size();
    }

    private boolean isRecordTypeMatch(MtConvertRule rule, List<IObjectMappingRuleInfo> mappingRuleInfoList, List<String> recordTypes, boolean isTargetRecordType) {
        if (CollectionUtils.empty(mappingRuleInfoList)) {
            return true;
        }
        return mappingRuleInfoList.stream()
                .filter(ruleInfo -> Objects.equals(ruleInfo.getRuleApiName(), rule.getApiName()))
                .findFirst()
                .map(ruleInfo -> allRecordTypesMatch(recordTypes, ruleInfo, isTargetRecordType))
                .orElse(true);
    }

    private boolean allRecordTypesMatch(List<String> recordTypes, IObjectMappingRuleInfo ruleInfo, boolean isTargetRecordType) {
        List<IObjectMappingRuleDetailInfo> fieldMapping = ruleInfo.getFieldMapping();
        if (CollectionUtils.empty(fieldMapping)) {
            return true;
        }
        return fieldMapping.stream()
                .filter(detailInfo -> StringUtils.equals(detailInfo.getSourceFieldName(), IObjectData.RECORD_TYPE))
                .findFirst()
                .map(detailInfo -> {
                    List<IObjectMappingRuleEnumInfo> optionMapping = detailInfo.getOptionMapping();
                    if (CollectionUtils.empty(optionMapping)) {
                        return true;
                    }
                    Set<String> recordTypesInRule = optionMapping.stream()
                            .map(code -> isTargetRecordType ? code.getTargetEnumCode() : code.getSourceEnumCode())
                            .collect(Collectors.toSet());
                    return recordTypesInRule.containsAll(recordTypes);
                })
                .orElse(true);
    }

    @Override
    public List<IObjectMappingRuleInfo> findConvertRuleByDescribeApiName(User user, String sourceDescribeApiName, String targetDescribeApiName, String sourceId) {
        return findConvertRuleByDescribeApiName(user, sourceDescribeApiName, targetDescribeApiName, Lists.newArrayList(sourceId));
    }

    @Override
    public List<IObjectMappingRuleInfo> findConvertRuleByDescribeApiName(User user, String sourceDescribeApiName, String targetDescribeApiName, List<String> sourceIds) {
        return findConvertRuleByDescribeApiName(user, sourceDescribeApiName, targetDescribeApiName, sourceIds, null, false);
    }

    @Override
    public List<IObjectMappingRuleInfo> findConvertRuleByDescribeApiName(User user, String sourceDescribeApiName, String targetDescribeApiName, List<String> sourceIds, String recordType, boolean isDoublePull) {
        if (StringUtils.isAllEmpty(sourceDescribeApiName, targetDescribeApiName)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        boolean isPushOrder = CollectionUtils.notEmpty(sourceIds) && StringUtils.isNotEmpty(sourceDescribeApiName);
        List<IObjectData> sourceDataList = null;
        if (isPushOrder) {
            int manyMaxLimit = FieldManyMaxConfig.getObjectReferenceManyMaxLimit(user.getTenantId(), sourceDescribeApiName);
            if (sourceIds.size() > manyMaxLimit) {
                throw new ValidateException(I18NExt.text(I18NKey.CONVERT_RULE_SOURCE_IDS_TOO_MUCH, manyMaxLimit));
            }
            sourceDataList = metaDataFindService.findObjectDataByIds(user.getTenantId(), sourceIds, sourceDescribeApiName);
        }
        List<IFilter> filters = getActiveFilters(user);
        addFilters(filters, sourceDescribeApiName, targetDescribeApiName);
        List<MtConvertRule> mtConvertRuleList = find(user, () -> SearchQueryImpl.filters(filters));
        if (isDoublePull) {
            mtConvertRuleList.removeIf(MtConvertRule::isGroupingByRule);
        }
        List<IObjectMappingRuleInfo> mappingRuleInfoList = findObjectMappingRuleBySourceTarget(user, sourceDescribeApiName, targetDescribeApiName);
        Map<String, IObjectDescribe> objectDescribeMap = getObjectDescribeMapFromConvertRuleList(user, mtConvertRuleList);
        if (isPushOrder) {
            return convert2MappingRuleInfos4PushOrder(user, objectDescribeMap, mtConvertRuleList, mappingRuleInfoList, sourceDataList);
        }
        return convert2MappingRuleInfos4PullOrder(user, objectDescribeMap, mtConvertRuleList, mappingRuleInfoList, recordType);
    }

    private List<IFilter> getActiveFilters(User user) {
        return Lists.newArrayList(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.EQ, IS_ACTIVE, Boolean.TRUE.toString()).getFilter());
    }

    @Override
    public boolean count(User user, String sourceDescribeApiName, String targetDescribeApiName) {
        if (!supportConvertRule(user.getTenantId())) {
            return false;
        }

        List<String> deletedStatusList = Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()), String.valueOf(DELETE_STATUS.DELETE.getValue()));
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter(),
                FilterExt.of(Operator.IN, IObjectData.IS_DELETED, deletedStatusList).getFilter());
        addFilters(filters, sourceDescribeApiName, targetDescribeApiName);
        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        Query query = Query.builder()
                .searchQuery(searchQuery)
                .build();
        Integer count = repository.findCountOnly(user, query, MtConvertRule.class);
        if (Objects.isNull(count)) {
            return false;
        }
        return count > 0;
    }

    @Override
    public boolean supportPullOrder(User user, String describeApiName) {
        Supplier<Boolean> supportPullOrder;
        try {
            supportPullOrder = () -> count(user, null, describeApiName);
        } catch (Exception e) {
            supportPullOrder = () -> false;
        }
        return ContextCacheUtil.findByCache(user, describeApiName,"PULL_ORDER_SWITCH",
                supportPullOrder);
    }

    private static void addFilters(List<IFilter> filters, String sourceDescribeApiName, String targetDescribeApiName) {
        if (StringUtils.isNotEmpty(sourceDescribeApiName)) {
            filters.add(FilterExt.of(Operator.EQ, SOURCE_OBJECT_DESCRIBE_API_NAME, sourceDescribeApiName).getFilter());
        }
        if (StringUtils.isNotEmpty(targetDescribeApiName)) {
            filters.add(FilterExt.of(Operator.EQ, TARGET_OBJECT_DESCRIBE_API_NAME, targetDescribeApiName).getFilter());
        }
    }

    @Override
    public List<IObjectMappingRuleInfo> findObjectMappingRuleByApiName(User user, String ruleApiName) {
        IActionContext context = ActionContextExt.of(user).getContext();
        List<IObjectMappingRuleInfo> mappingRuleInfoList;
        try {
            mappingRuleInfoList = mappingRuleService.getObjectMappingRuleByRuleApiNameAction(user.getTenantId(), ruleApiName, ActionEnum.CONVERSION_RULES, context);
        } catch (MetadataServiceException e) {
            log.warn("Error in findConvertRuleByApiName Convert Rule", e);
            throw new MetaDataBusinessException(e.getMessage());
        }
        return mappingRuleInfoList;
    }

    @Override
    public List<IObjectMappingRuleInfo> findObjectMappingRuleBySourceTarget(User user, String sourceDescribeApiName, String targetDescribeApiName) {
        IActionContext context = ActionContextExt.of(user).getContext();
        List<IObjectMappingRuleInfo> mappingRuleInfoList;
        try {
            IObjectMappingParams params = new ObjectMappingParams();
            params.setSourceApiName(sourceDescribeApiName);
            params.setTargetApiName(targetDescribeApiName);
            mappingRuleInfoList = mappingRuleService.getObjectMappingRuleByRuleNameSourceTargetAction(user.getTenantId(), params, ActionEnum.CONVERSION_RULES, 0, context);
        } catch (MetadataServiceException e) {
            log.warn("Error in findObjectMappingRuleBySourceTarget Convert Rule", e);
            throw new MetaDataBusinessException(e.getMessage());
        }
        return mappingRuleInfoList;
    }

    @Override
    public List<MtConvertRule> findConvertRuleInInternalObjByApiName(User user, String ruleApiName) {
        return findConvertRuleInInternalObjByApiName(user, ruleApiName, null, false);
    }


    @Override
    public List<MtConvertRule> findConvertRuleInInternalObjByApiName(User user, String ruleApiName, Boolean isActive) {
        return findConvertRuleInInternalObjByApiName(user, ruleApiName, isActive, false);
    }

    @Override
    public List<MtConvertRule> findConvertRuleInInternalObjByApiName(User user, String ruleApiName, Boolean isActive, boolean isOnTime) {
        List<MtConvertRule> convertRules = Lists.newArrayList();
        MtConvertRule mtConvertRule = findOne(user, ruleApiName, isActive, isOnTime);
        if (Objects.isNull(mtConvertRule)) {
            return convertRules;
        }
        convertRules.add(mtConvertRule);
        if (MASTER_OBJECT != mtConvertRule.getMdType()) {
            return convertRules;
        }
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, MASTER_CONVERT_RULE_API_NAME, ruleApiName).getFilter());
        if (Objects.nonNull(isActive)) {
            filters.add(FilterExt.of(Operator.EQ, IS_ACTIVE, BooleanUtils.toStringTrueFalse(isActive)).getFilter());
        }
        List<MtConvertRule> detailConvertRules = find(user, () -> SearchQueryImpl.filters(filters), isOnTime);
        convertRules.addAll(detailConvertRules);
        return convertRules;
    }


    @Override
    public List<MtConvertRule> find(User user, Supplier<SearchQuery> supplier) {
        return find(user, supplier, false);
    }

    @Override
    public List<MtConvertRule> find(User user, Supplier<SearchQuery> supplier, boolean isOnTime) {
        List<IFilter> filters = Lists.newArrayList(
                FilterExt.of(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId()).getFilter());
        filters.add(FilterExt.of(Operator.EQ, IObjectData.IS_DELETED, String.valueOf(DELETE_STATUS.NORMAL.getValue())).getFilter());
        SearchQuery searchQuery = supplier.get().and(filters);
        Query query = builderBySearchQuery(searchQuery);
        List<MtConvertRule> ruleList = repository.findBy(user, query, MtConvertRule.class).stream().filter(Objects::nonNull).collect(Collectors.toList());
        //转换多语
        batchTranslateValue(ruleList, user, isOnTime);
        return ruleList;
    }

    private MtConvertRule findOne(User user, String apiName, Boolean isActive, boolean isOnTime) {
        List<IFilter> filters = Lists.newArrayList(FilterExt.of(Operator.EQ, API_NAME, apiName).getFilter());
        if (Objects.nonNull(isActive)) {
            filters.add(FilterExt.of(Operator.EQ, IS_ACTIVE, BooleanUtils.toStringTrueFalse(isActive)).getFilter());
        }
        MtConvertRule mtConvertRule = find(user, () -> SearchQueryImpl.filters(filters), isOnTime)
                .stream().findFirst().orElse(null);
        if (Objects.isNull(mtConvertRule)) {
            return null;
        }
        batchTranslateValue(Lists.newArrayList(mtConvertRule), user, isOnTime);
        return mtConvertRule;
    }


    private MtConvertRule findOne(User user, String apiName, Boolean isActive) {
        return findOne(user, apiName, isActive, false);
    }

    private Query builderBySearchQuery(SearchQuery searchQuery) {
        return Query.builder()
                .searchQuery(searchQuery)
                .orders(Lists.newArrayList(OrderByExt.orderByField(IObjectData.CREATE_TIME, true)))
                .limit(2000)
                .offset(0)
                .build();
    }

    @Override
    public boolean supportConvertRule(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.CONVERT_RULE_EI_GRAY, tenantId)
                && licenseService.isSupportConvertRule(tenantId);
    }


    private void batchTranslateValue(List<MtConvertRule> convertRules, User user, boolean isOnTime) {
        List<String> keyList = Lists.newArrayList();

        for (MtConvertRule convertRule : convertRules) {
            keyList.add(TranslateUtils.getConvertRuleNameKey(convertRule.getRuleApiName()));
            keyList.add(TranslateUtils.getExcessInspectionPromptKey(convertRule.getRuleApiName()));
            keyList.add(TranslateUtils.getDataRangePromptKey(convertRule.getRuleApiName()));
        }
        Map<String, Localization> localizationMap = i18nSettingService.getLocalization(keyList, user.getTenantId(), false, isOnTime);
        for (MtConvertRule convertRule : convertRules) {
            convertRule.setName(getTransValue(TranslateUtils.getConvertRuleNameKey(convertRule.getRuleApiName()), convertRule.getName(),
                    localizationMap, RequestContextManager.getContext().getLang().getValue()));
            convertRule.setMessage(getTransValue(TranslateUtils.getDataRangePromptKey(convertRule.getRuleApiName()), convertRule.getMessage(),
                    localizationMap, RequestContextManager.getContext().getLang().getValue()));
            convertRule.setExcessPrompt(getTransValue(TranslateUtils.getExcessInspectionPromptKey(convertRule.getRuleApiName()), convertRule.getExcessPrompt(),
                    localizationMap, RequestContextManager.getContext().getLang().getValue()));
        }

    }

    private String getTransValue(String key, String defaultValue, Map<String, Localization> localizationMap, String lang) {
        if (Objects.isNull(localizationMap)) {
            return defaultValue;
        }
        Localization localization = localizationMap.get(key);
        if (Objects.isNull(localization)) {
            return defaultValue;
        }
        return localization.get(lang, defaultValue);
    }

}
