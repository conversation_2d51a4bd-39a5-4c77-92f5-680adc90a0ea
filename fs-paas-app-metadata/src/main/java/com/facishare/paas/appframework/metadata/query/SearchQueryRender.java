package com.facishare.paas.appframework.metadata.query;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.coordination.CrmService;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.follow.FollowLogicService;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.DataRightsParameter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2020/06/23
 */
@Data
public class SearchQueryRender {
    private boolean isRelatedPage;
    private boolean isIgnoreSceneFilter;
    private boolean isIgnoreSceneRecordType;
    private boolean usePattern;

    private Boolean isAdmin;

    @NonNull
    private DataPrivilegeService dataPrivilegeService;
    @NonNull
    private UserRoleInfoService userRoleInfoService;
    @NonNull
    private CrmService crmService;
    @NonNull
    private FunctionPrivilegeService functionPrivilegeService;

    @NonNull
    private ISearchTemplate searchTemplate;
    @NonNull
    private IObjectDescribe describe;
    @NonNull
    private User user;

    private SearchTemplateQueryExt templateQueryExt;
    @NonNull
    private FollowLogicService followLogicService;
    @NonNull
    private MetaDataFindService metaDataFindService;

    private Map<String, String> variables;

    @Builder
    private SearchQueryRender(boolean isRelatedPage,
                              boolean isIgnoreSceneFilter,
                              boolean isIgnoreSceneRecordType,
                              Boolean usePattern,
                              DataPrivilegeService dataPrivilegeService,
                              FunctionPrivilegeService functionPrivilegeService,
                              UserRoleInfoService userRoleInfoService,
                              CrmService crmService,
                              ISearchTemplate searchTemplate,
                              IObjectDescribe describe,
                              String searchQueryInfo,
                              User user,
                              FollowLogicService followLogicService,
                              MetaDataFindService metaDataFindService) {
        this.isRelatedPage = isRelatedPage;
        this.isIgnoreSceneFilter = isIgnoreSceneFilter;
        this.isIgnoreSceneRecordType = isIgnoreSceneRecordType;
        this.usePattern = BooleanUtils.isTrue(usePattern);
        this.dataPrivilegeService = dataPrivilegeService;
        this.functionPrivilegeService = functionPrivilegeService;
        this.userRoleInfoService = userRoleInfoService;
        this.crmService = crmService;
        this.searchTemplate = searchTemplate;
        this.describe = describe;
        if (Strings.isNullOrEmpty(searchQueryInfo)) {
            this.templateQueryExt = SearchTemplateQueryExt.of(new SearchTemplateQuery());
        } else {
            this.templateQueryExt = SearchTemplateQueryExt.of(SearchTemplateQueryExt.fromJsonString(searchQueryInfo));
        }
        this.user = user;
        this.followLogicService = followLogicService;
        this.metaDataFindService = metaDataFindService;
    }

    public Query render() {
        // 处理权限
        handleDataPrivilege();
        handleFollowScene();
        handleRecentVisitScene();
        handleDisplayName();
        SearchQuery searchQuery = defineSearchQuery();
        //是否忽略场景中配置的业务类型
        return buildQuery(searchQuery);

    }

    private void handleRecentVisitScene() {
        metaDataFindService.handleRecentVisitScene(user, describe.getApiName(), searchTemplate, templateQueryExt);
    }

    private void handleDisplayName() {
        metaDataFindService.processFilterSupportDisplayName(user.getTenantId(), describe, templateQueryExt.toSearchTemplateQuery());
    }

    private void handleFollowScene() {
        followLogicService.handleFollowScene(user, describe.getApiName(), searchTemplate, templateQueryExt);
    }

    private Query buildQuery(SearchQuery searchQuery) {
        // 处理一下排序字段
        templateQueryExt.handelOrders(searchTemplate, ObjectDescribeExt.of(describe), isRelatedPage);

        Query query = Query.fromSearchTemplateQuery(templateQueryExt.getQuery());
        query.setSearchQuery(searchQuery);
        query.setSearchTemplate(searchTemplate);
        return query;
    }

    private SearchQuery defineSearchQuery() {
        if (usePattern && StringUtils.isNotBlank(templateQueryExt.getPattern())) {
            // 使用pattern模式：pattern查询 + 场景筛选
            SearchQuery searchQuery = buildPatternSearchQuery();
            SearchQuery sceneSearchQuery = getFromSceneFilter();
            return searchQuery.and(sceneSearchQuery);
        }

        // 使用标准模式：组合三种查询条件
        return combineStandardSearchQueries();
    }

    /**
     * 构建基于pattern的搜索查询
     * 包含：pattern解析、无效字段过滤、变量处理、记录类型处理
     */
    private SearchQuery buildPatternSearchQuery() {
        // 从pattern解析SearchQuery
        SearchQuery searchQuery = templateQueryExt.parsePatternToSearchQuery();

        // 过滤字段被删除的filter
        AtomicBoolean atomicBoolean = new AtomicBoolean();
        searchQuery.removeIf(this::needRemove, atomicBoolean);

        // 处理变量
        renderFilterValuesVariables(searchQuery.getAllFilters());

        // 处理记录类型
        return applyRecordTypeFilter(searchQuery);
    }

    private boolean needRemove(SearchQuery query) {
        if (query.isEmpty()) {
            return true;
        }
        if (!query.isFilterNode()) {
            return false;
        }
        return CollectionUtils.empty(removeInvalidFilter(Lists.newArrayList(query.getFilter()), !RequestUtil.isCepRequest()));
    }

    /**
     * 组合标准模式的搜索查询
     * 包含：查询过滤器 + 查询条件 + 场景筛选
     */
    private SearchQuery combineStandardSearchQueries() {
        SearchQuery fromQueryFilters = getFromQueryFilters();
        SearchQuery fromQueryWheres = getFromQueryWheres();
        SearchQuery fromSceneFilter = getFromSceneFilter();
        return fromQueryFilters.and(fromQueryWheres, fromSceneFilter);
    }

    /**
     * 应用记录类型过滤器
     */
    private SearchQuery applyRecordTypeFilter(SearchQuery searchQuery) {
        // 忽略场景中的业务类型
        if (isIgnoreSceneRecordType) {
            return searchQuery;
        }

        // 使用场景中定义的业务类型
        IFilter recordTypeFilter = ObjectDescribeExt.of(describe).getRecordTypeOption(searchTemplate.getRecordType())
                .map(it -> FilterExt.of(Operator.EQ, ISearchTemplate.RECORD_TYPE, it.getApiName()).getFilter())
                .orElse(null);

        return searchQuery.and(recordTypeFilter);
    }

    private SearchQuery getFromQueryWheres() {
        // 只过滤字段被删除的filter
        List<Wheres> wheresList = removeInvalidFilterFromWheres(templateQueryExt.getWheres(), true);
        // 处理 filterValues 变量
        renderFilterValuesVariablesFromWheres(wheresList);
        return SearchQueryImpl.wheres(wheresList);
    }

    private SearchQuery getFromQueryFilters() {
        List<IFilter> filterList = templateQueryExt.getFilters().stream()
                .peek(it -> FilterExt.of(it).replaceCurrentUser(user.getUserIdOrOutUserIdIfOutUser()))
                .collect(Collectors.toList());
        List<IFilter> filters = removeInvalidFilter(filterList, !RequestUtil.isCepRequest());
        // 处理变量
        renderFilterValuesVariables(filterList);

        SearchQuery searchQuery = SearchQueryImpl.filters(filters);
        return applyRecordTypeFilter(searchQuery);
    }

    private List<Wheres> removeInvalidFilterFromWheres(List<Wheres> wheres, boolean includeUnActive) {
        List<IFilter> filters = SearchTemplateExt.wheresToFilters(wheres).stream()
                .peek(it -> FilterExt.of(it).replaceCurrentUser(user.getUserIdOrOutUserIdIfOutUser()))
                .collect(Collectors.toList());
        return SearchTemplateExt.filterToWheres(removeInvalidFilter(filters, includeUnActive));
    }

    private List<IFilter> removeInvalidFilter(List<IFilter> filters, boolean includeUnActive) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.UN_REMOVE_WHERES_UN_ACTIVE_FILTER_FIELD_OBJECT, describe.getApiName())) {
            return filters;
        }
        // cep请求，禁用、删除的字段、选项 不参与筛选
        return FilterExt.handleFilter(filters, describe, includeUnActive);
    }

    private SearchQuery getFromSceneFilter() {
        // 筛选忽略场景中的筛选条件
        if (isIgnoreSceneFilter) {
            return SearchQueryImpl.wheres(Collections.emptyList());
        }
        List<IFilter> filters = SearchTemplateExt.of(searchTemplate).getFiltersWithoutDefaultScenePlaceholderFilter(user.getUserIdOrOutUserIdIfOutUser());
        metaDataFindService.processFilterSupportDisplayName(user.getTenantId(), describe, filters);
        // 处理变量
        renderFilterValuesVariables(filters);
        return SearchQueryImpl.wheres(SearchTemplateExt.filterToWheres(removeInvalidFilter(filters, !RequestUtil.isCepRequest())));
    }

    private void handleDataPrivilege() {
        SearchTemplateExt searchTemplateExt = SearchTemplateExt.of(searchTemplate);
        // 处理外部数据权限
        if (user.isOutUser()) {
            // 调用深研接口查询外部数据权限配置
            OutDataPrivilege outDataPrivilege = dataPrivilegeService.getOutDataPrivilege(user,
                    RequestContextManager.getContext().getAppId(), describe.getApiName());
            DataRightsParameter dataRightsParameter = buildDataRightParameter(searchTemplateExt);
            dataRightsParameter.setLinkAppDataAuthRange(outDataPrivilege.getValue());
            templateQueryExt.setPermissionType(2);
            templateQueryExt.setDataRightsParameter(dataRightsParameter);
            return;
        }

        // 全部筛选场景，管理员或对象配置了「系统级查看全部」或者「对象级查看全部」的功能权限不需要走数据权限
        if (searchTemplateExt.isAll()) {
            // 人员对象，管理员需要走数据权限
            if (isAdmin() && !Utils.PERSONNEL_OBJ_API_NAME.equals(describe.getApiName())) {
                return;
            }
            if (functionPrivilegeService.funDataViewPrivilegeCheck(user, describe)) {
                return;
            }
        }

        // 权限处理
        DataRightsParameter dataRightsParameter = buildDataRightParameter(searchTemplateExt);
        templateQueryExt.setPermissionType(1);
        templateQueryExt.setDataRightsParameter(dataRightsParameter);
    }

    private DataRightsParameter buildDataRightParameter(SearchTemplateExt searchTemplateExt) {
        DataRightsParameter dataRightsParameter = searchTemplateExt.getDataRightsParameter(isSubCascadeConfig());
        ObjectDescribeExt.of(describe).getMasterDetailFieldDescribe().ifPresent(it -> {
            // 从对象走主对象的数据权限
            dataRightsParameter.setIsDetailObject(true);
            dataRightsParameter.setMasterIdFieldApiName(it.getApiName());
            dataRightsParameter.setMasterObjectApiName(it.getTargetApiName());
        });
        return dataRightsParameter;
    }

    private boolean isAdmin() {
        if (Objects.nonNull(isAdmin)) {
            return isAdmin;
        }
        return isAdmin = user.isSupperAdmin() || userRoleInfoService.isAdmin(user);
    }

    private boolean isSubCascadeConfig() {
        return crmService.getIsSubCascadeConfig(user.getTenantId());
    }

    private void renderFilterValuesVariablesFromWheres(List<Wheres> wheresList) {
        wheresList.forEach(wheres -> renderFilterValuesVariables(wheres.getFilters()));
    }

    private void renderFilterValuesVariables(List<IFilter> filters) {
        metaDataFindService.renderERVarFromFilterValues(user, filters);
    }

}
