package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.netdisk.api.model.type.V5FileInfo;
import com.facishare.paas.appframework.common.service.NetworkDiskService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FileStoreService;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public abstract class AbstractFileImportDataConverter extends BaseImportFieldDataConverter {

    @Autowired
    private NetworkDiskService networkDiskService;

    @Autowired
    protected FileStoreService fileStoreService;

    protected String checkImportFilesOrConvertFiles(User user, List<String> paths, String fieldLabel, String fileNameKey, long maxSize, List<Map<String, String>> result) {
        //通过接口查询导入图片信息(大小、扩展名等)，不能查出代表不存在
        final List<V5FileInfo> files = networkDiskService.getFileInfoByNPath(user, paths);
        //部分文件不存在
        if (files.size() != paths.size()) {
            //部分图片不存在
            List<String> inExistence = paths.stream()
                    .filter(i -> !files.stream().map(V5FileInfo::getNPath).collect(Collectors.toList()).contains(i))
                    .collect(Collectors.toList());
            if (!CollectionUtils.empty(inExistence)) {
                StringBuilder tips = new StringBuilder("[" + fieldLabel + "]" + I18NExt.text(I18NKey.PARTIAL_NOT_EXIST));
                for (String s : inExistence) {
                    tips.append(s).append(",");
                }
                tips.replace(tips.length() - 1, tips.length(), ".");
                return tips.toString();
            }
        }
        //并发获取新的NPath
        fileStoreService.getNPathsWithoutPermission(user, files);
        // 单个文件有大小限制
        // 构造能保存的字段格式
        for (V5FileInfo info : files) {
            if (info.getFileSize() > maxSize) {
                //区分图片和附件
                if (I18NKey.IMAGE.equals(fileNameKey)) {
                    return I18NExt.text(I18NKey.IMAGE_MAX_SIZE);
                } else {
                    return I18NExt.text(I18NKey.ATTACH_MAX_SIZE);
                }
            }
            String nPath = info.getNPath();
            if (I18NKey.IMAGE.equals(fileNameKey)) {
                Map<String, String> imageInfoMap = Maps.newLinkedHashMap();
                imageInfoMap.put("ext", info.getFileExtension());
                imageInfoMap.put("path", nPath);
                result.add(imageInfoMap);
            } else {
                Map<String, String> attachInfoMap = Maps.newLinkedHashMap();
                String attachName = info.getFileName();
                if (!attachName.contains("." + info.getFileExtension())) {
                    attachName = attachName + "." + info.getFileExtension();
                }
                attachInfoMap.put("filename", attachName);
                attachInfoMap.put("ext", info.getFileExtension());
                attachInfoMap.put("size", String.valueOf(info.getFileSize()));
                attachInfoMap.put("create_time", String.valueOf(info.getCreateTime()));
                attachInfoMap.put("path", nPath);
                result.add(attachInfoMap);
            }
        }
        return null;
    }
}
