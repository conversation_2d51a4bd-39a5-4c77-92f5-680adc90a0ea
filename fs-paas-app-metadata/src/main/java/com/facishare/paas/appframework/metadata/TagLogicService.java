package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.tag.TagGroupTag;
import com.facishare.paas.metadata.api.DataAndSubTag;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.ISubTagDescribe;
import com.facishare.paas.metadata.api.describe.ITagDescribe;
import com.facishare.paas.metadata.api.search.Ranges;
import com.facishare.paas.metadata.impl.search.TagQueryInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 组户标签有关接口
 *
 * <AUTHOR>
 * @date 2020/2/11 5:37 下午
 */
public interface TagLogicService {

    /**
     * 根据对象描述查询所有标签
     *
     * @param objectApiName 对象描述ApiName
     * @param keyword       支持模糊查询
     * @param isActive      启用/禁用
     * @return 标签列表
     */
    List<ISubTagDescribe> findAllTagsFilterByRole(String objectApiName, String keyword, Boolean isActive, IActionContext context);

    List<ISubTagDescribe> findAllTags(String objectApiName, String tenantId, String keyword, Boolean isActive);

    /**
     * 查询所有描述分组
     *
     * @param objectApiName 对象描述
     * @param tenantId      企业ID
     * @return 标签列表
     */
    List<ITagDescribe> findAllTagGroups(String objectApiName, String tenantId);

    /**
     * 根据标签名称查询标签列表，支持模糊匹配
     *
     * @param objectApiName 对象描述
     * @param tenantId      企业ID
     * @param name          标签名称
     * @return 标签列表
     */
    List<ISubTagDescribe> findTagsByName(String objectApiName, String tenantId, String name);

    /**
     * 根据标签分组id列表查询标签
     *
     * @param objectApiName 对象apiName
     * @param tenantId      企业ID
     * @param groupIds      标签分组ID
     * @return 标签列表
     */
    List<ITagDescribe> findTagGroupsByIds(String objectApiName, String tenantId, Collection<String> groupIds);


    /**
     * 根据标签组id批量查询标签组
     */
    List<ITagDescribe> findTagGroupsByIds(String tenantId, Collection<String> groupIds);

    List<ITagDescribe> findTagGroupsByIds(String tenantId, Collection<String> groupIds, boolean onTime);

    /**
     * 从营销通查询出标签
     *
     * @param name 为null时，查询全部，不为null时，模糊查询
     */
    List<TagGroupTag> findTagsByRest(String name, String tenantId, String userId);

    /**
     * 根据数据ID查询该数据已经打的标签
     * 根据tagGroup中的range判断当前用户是否有标签的查看权限
     *
     * @param describeApiName 对象apiName
     * @param dataId          数据ID
     * @return 标签列表
     */
    List<ISubTagDescribe> findTagsByDataId(String describeApiName, String dataId, IActionContext context);

    /**
     * 根据标签ID查询标签
     *
     * @param id
     * @param user
     * @return
     */
    ISubTagDescribe findTagById(String id, User user);

    ISubTagDescribe findTagById(String id, User user, boolean isOnTime);
    /**
     * 批量解除标签和指定数据的关联
     *
     * @param dataId          数据ID
     * @param tagIds          标签ID列表
     * @param tenantId        企业ID
     * @param describeApiName 对象APIName
     * @return true/false
     */
    boolean batchRemoveTagsForData(User user, String dataId, List<String> tagIds, String tenantId, String describeApiName);

    /**
     * 根据标签名称批量给数据关联标签
     * <p>
     * 注意，底层打标签是全量接口，本次的参数会覆盖库里的
     *
     * @param dataId          数据ID
     * @param tagNames        标签名称列表,key:标签分组名称   value: 标签名称列表
     * @param user            用户
     * @param describeApiName 对象描述
     * @return
     */
    boolean batchAddTagsByTagName(String dataId, Map<String, List<String>> tagNames, User user, String describeApiName);

    /**
     * 批量给某条数据打标签
     *
     * @param dataId          数据ID
     * @param tagIds          标签列表
     * @param describeApiName 描述
     * @param user            用户
     * @return
     */
    boolean batchBoundTags(String dataId, Collection<String> tagIds, String describeApiName, User user);

    /**
     * 批量数据批量打标签，全量更新
     *
     * @param describeApiName
     * @param user
     * @return
     */
    Map<String, List<String>> multiDataBatchBoundTags(Map<String, List<String>> dataIdTagMap, String describeApiName, User user);

    /**
     * 批量数据覆盖标签
     *
     * @param dataIds         数据ID
     * @param tagIds          标签列表
     * @param describeApiName 描述
     * @param user            用户
     * @return
     */
    boolean bulkUpdateTags(List<String> dataIds, Collection<String> tagIds, String describeApiName, User user);

    /**
     * 根据标签组名称查询标签组
     *
     * @return
     */
    ITagDescribe findTagGroupByName(String describeApiName, String tagGroupName, String tenantId);

    /**
     * 查询指定对象下的所有的标签分组
     */
    List<ITagDescribe> findTagGroups(String tenantId);


    List<ITagDescribe> findTagGroups(String tenantId, boolean isOntime);
    /**
     * 根据标签组id查询标签
     * 支持模糊查询
     */
    List<ISubTagDescribe> findTagsByTagGroupId(String tagGroupId, String tenantId);

    /**
     * 是否支持标签功能
     */
    boolean isSupportTag(String describeApiName, User user);

    /**
     * 判断指定对象类型是否存在任何标签数据
     *
     * @param describeApiName 对象apiName
     * @param user            用户信息
     * @return true表示该对象类型下存在标签数据，false表示不存在
     */
    boolean hasAnyTagData(String describeApiName, User user);

    Map<String, Long> batchQuerySubTagCount(String tenantId, TagQueryInfo tagQueryInfo);

    List<ISubTagDescribe> findSubTagByQueryInfo(String tenantId, String tagId, TagQueryInfo tagQueryInfo);

    List<ISubTagDescribe> findSubTagByQueryInfo(String tenantId, String tagId, TagQueryInfo tagQueryInfo, boolean isOnTime);

    List<ITagDescribe> findTagGroupByQueryInfo(String tenantId, TagQueryInfo tagQueryInfo);

    List<ITagDescribe> findTagGroupByQueryInfo(String tenantId, TagQueryInfo tagQueryInfo, boolean isOnTime);
    /**
     * 创建标签分组
     */
    ITagDescribe createTagGroup(String name,
                                String apiName,
                                List<String> describeApiNames,
                                Boolean isAppliedToAll,
                                User user);

    /**
     * 创建标签分组
     */
    ITagDescribe createTagGroup(String name,
                                String apiName,
                                List<String> describeApiNames,
                                Boolean isAppliedToAll,
                                List<ISubTagDescribe> labelNames,
                                String tagDefineType,
                                Ranges ranges,
                                Boolean isMutex,
                                String groupDescription,
                                User user);

    /**
     * 创建默认标签分组
     */
    ITagDescribe createTagGroup(User user);

    /**
     * 批量查询各个标签分组下的标签数量
     */
    Map<String, Long> findTagCountByGroup(String tenantId);

    /**
     * 更新指定标签组名称
     */
    ITagDescribe updateTagGroupName(ITagDescribe group, User user);

    /**
     * 删除指定标签分组信息
     *
     * @param groupId  分组id
     * @param tenantId 企业ID
     */
    boolean deleteTagGroup(String groupId, String tenantId);

    /**
     * 创建新的标签
     *
     * @param tag  标签
     * @param user 用户
     */
    ISubTagDescribe createTag(ISubTagDescribe tag, User user);

    /**
     * 创建新的标签
     *
     * @param describeApiName 描述
     * @param tagName         标签名称
     * @param groupId         标签组
     * @param user            用户
     * @return
     */
    ISubTagDescribe createTag(String describeApiName, String tagName, String groupId, User user, int grade);

    ISubTagDescribe createTag(String describeApiName, String tagName, String groupId, User user);


    /**
     * 更新标签
     *
     * @param fields 需要更新的字段
     */
    ISubTagDescribe updateTag(ISubTagDescribe tag, Collection<String> fields, User user);

    /**
     * 更新
     *
     * @param describeApiName
     * @param tagId
     * @param tagName
     * @param user
     * @return
     */
    ISubTagDescribe updateTag(String describeApiName, String tagId, String tagName, User user);

    /**
     * 删除标签
     *
     * @param groupId  标签分组ID
     * @param tagId    标签ID
     * @param tenantId 企业ID
     */
    boolean deleteTag(String groupId, String tagId, String tenantId);

    /**
     * 禁用标签
     */
    boolean disableTag(String tagId, User user);

    /**
     * 启用标签
     */
    boolean enableTag(String tagId, User user);

    /**
     * 模糊查询所有标签
     */
    List<ISubTagDescribe> findTagsByKeyword(String keyword, User user);

    /**
     * 根据apiName查询标签组
     */
    ITagDescribe findTagGroupByApiName(String apiName, User user);

    List<DataAndSubTag> findAllTagByBulkDataId(String apiName, Set<String> dataIds, User user);

    boolean appendTags(String dataId, Collection<String> tagIds, String describeApiName, User user);

    boolean bulkAppendTags(Map<String, List<String>> dataIdTagMap, String describeApiName, User user);

    void enableOrDisableTagGroup(ServiceContext context, List<String> id, boolean enable);

    Map<String, List<ISubTagDescribe>> bulkFindSubTagByTagIds(ServiceContext context, List<String> groupIdList);

    Map<String, List<ISubTagDescribe>> bulkFindSubTagByTagIds(ServiceContext context, List<String> groupIdList, boolean isOnTime);

    /**
     * 查询所有描述分组，根据参数对象是否在标签分组的适用对象中
     *
     * @param objectApiName
     * @param tenantId
     * @return
     */
    List<ITagDescribe> findAllTagGroupByObjectApiNameInObjRange(String objectApiName, String tenantId);

    List<ISubTagDescribe> findSubTagBySubTagIds(User user, Set<String> subTagIds);
}
