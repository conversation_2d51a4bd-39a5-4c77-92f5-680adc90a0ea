package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.service.ITagDataRelationService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TagLogicServiceImplTest {

    @Mock
    private ITagDataRelationService tagDataRelationService;

    @Mock
    private IActionContext actionContext;

    @InjectMocks
    private TagLogicServiceImpl tagLogicService;

    private User user;
    private String describeApiName;
    private String tenantId;

    @BeforeEach
    void setUp() {
        tenantId = "74255";
        describeApiName = "AccountObj";
        user = User.systemUser(tenantId);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试正常情况下，当标签数据存在时，hasAnyTagData方法返回true
     */
    @Test
    @DisplayName("正常场景 - 标签数据存在时返回true")
    void testHasAnyTagData_WhenTagDataExists_ReturnsTrue() throws Exception {
        // 准备测试数据
        ActionContextExt mockActionContextExt = mock(ActionContextExt.class);

        // 配置Mock行为
        try (MockedStatic<ActionContextExt> mockedActionContextExt = mockStatic(ActionContextExt.class)) {
            mockedActionContextExt.when(() -> ActionContextExt.of(user)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(actionContext);
            when(tagDataRelationService.existsDataTag(actionContext, tenantId, describeApiName)).thenReturn(true);

            // 执行被测试方法
            boolean result = tagLogicService.hasAnyTagData(describeApiName, user);

            // 验证结果
            assertTrue(result);

            // 验证Mock交互
            verify(tagDataRelationService).existsDataTag(actionContext, tenantId, describeApiName);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试正常情况下，当标签数据不存在时，hasAnyTagData方法返回false
     */
    @Test
    @DisplayName("正常场景 - 标签数据不存在时返回false")
    void testHasAnyTagData_WhenTagDataNotExists_ReturnsFalse() throws Exception {
        // 准备测试数据
        ActionContextExt mockActionContextExt = mock(ActionContextExt.class);

        // 配置Mock行为
        try (MockedStatic<ActionContextExt> mockedActionContextExt = mockStatic(ActionContextExt.class)) {
            mockedActionContextExt.when(() -> ActionContextExt.of(user)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(actionContext);
            when(tagDataRelationService.existsDataTag(actionContext, tenantId, describeApiName)).thenReturn(false);

            // 执行被测试方法
            boolean result = tagLogicService.hasAnyTagData(describeApiName, user);

            // 验证结果
            assertFalse(result);

            // 验证Mock交互
            verify(tagDataRelationService).existsDataTag(actionContext, tenantId, describeApiName);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异常情况下，当tagDataRelationService抛出MetadataServiceException时，hasAnyTagData方法返回true
     */
    @Test
    @DisplayName("异常场景 - MetadataServiceException异常时返回true")
    void testHasAnyTagData_WhenMetadataServiceExceptionThrown_ReturnsTrue() throws Exception {
        // 准备测试数据
        ActionContextExt mockActionContextExt = mock(ActionContextExt.class);

        // 配置Mock行为
        try (MockedStatic<ActionContextExt> mockedActionContextExt = mockStatic(ActionContextExt.class)) {
            mockedActionContextExt.when(() -> ActionContextExt.of(user)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(actionContext);
            when(tagDataRelationService.existsDataTag(actionContext, tenantId, describeApiName))
                    .thenThrow(mock(MetadataServiceException.class));

            // 执行被测试方法
            boolean result = tagLogicService.hasAnyTagData(describeApiName, user);

            // 验证结果
            assertTrue(result);

            // 验证Mock交互
            verify(tagDataRelationService).existsDataTag(actionContext, tenantId, describeApiName);
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试异常情况下，当tagDataRelationService抛出RuntimeException时，hasAnyTagData方法返回true
     */
    @Test
    @DisplayName("异常场景 - RuntimeException异常时返回true")
    void testHasAnyTagData_WhenRuntimeExceptionThrown_ReturnsTrue() throws Exception {
        // 准备测试数据
        ActionContextExt mockActionContextExt = mock(ActionContextExt.class);

        // 配置Mock行为
        try (MockedStatic<ActionContextExt> mockedActionContextExt = mockStatic(ActionContextExt.class)) {
            mockedActionContextExt.when(() -> ActionContextExt.of(user)).thenReturn(mockActionContextExt);
            when(mockActionContextExt.getContext()).thenReturn(actionContext);
            when(tagDataRelationService.existsDataTag(actionContext, tenantId, describeApiName))
                    .thenThrow(new RuntimeException("Test runtime exception"));

            // 执行被测试方法
            boolean result = tagLogicService.hasAnyTagData(describeApiName, user);

            // 验证结果
            assertTrue(result);

            // 验证Mock交互
            verify(tagDataRelationService).existsDataTag(actionContext, tenantId, describeApiName);
        }
    }
}
