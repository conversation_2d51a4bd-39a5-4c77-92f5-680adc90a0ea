package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FormComponentExt;
import com.facishare.paas.appframework.metadata.FormTable;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.layout.component.SummaryKeyComponentInfo;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.PaymentFieldDescribe;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;



@ExtendWith(MockitoExtension.class)
class FormComponentRenderTest {



  @Mock
  private ObjectDescribeExt objectDescribeExt;

  @Mock
  private User user;

  @Mock
  private IObjectData data;

  @Mock
  private FunctionPrivilegeService functionPrivilegeService;

  @Mock
  private DescribeLogicService describeLogicService;

  private List<FormComponentExt> formComponentExtList;
  private List<SummaryKeyComponentInfo> summaryKeyComponentInfos;
  private List<FormTable> formTableList;
  private List<IFormField> orderFormFieldList;
  private Collection<String> unauthorizedFields;

  @BeforeEach
  void setUp() {
    formComponentExtList = Lists.newArrayList();
    summaryKeyComponentInfos = Lists.newArrayList();
    formTableList = Lists.newArrayList();
    orderFormFieldList = Lists.newArrayList();
    unauthorizedFields = Lists.newArrayList();
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试FormComponentRender构造函数正常场景
   */
  @Test
  @DisplayName("正常场景 - 构造FormComponentRender对象")
  void testFormComponentRenderConstructor_Success() {
    // 执行被测试方法
    FormComponentRender formComponentRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .summaryKeyComponentInfos(summaryKeyComponentInfos)
        .formTableList(formTableList)
        .layoutType("DETAIL")
        .recordType("test_record")
        .data(data)
        .functionPrivilegeService(functionPrivilegeService)
        .unauthorizedFields(unauthorizedFields)
        .dbLayoutType("test_layout")
        .orderFormFieldList(orderFormFieldList)
        .build();

    // 验证结果
    assertNotNull(formComponentRender);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试构造函数传入null参数
   */
  @Test
  @DisplayName("异常场景 - 传入null参数")
  void testFormComponentRenderConstructor_NullParameters() {
    // 执行被测试方法
    FormComponentRender formComponentRender = FormComponentRender.builder()
        .user(null)
        .describeExt(null)
        .formComponentExtList(null)
        .build();

    // 验证结果
    assertNotNull(formComponentRender);
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试render方法执行渲染逻辑
   */
  @Test
  @DisplayName("正常场景 - 执行表单组件渲染")
  void testRender_Success() {
    FormComponentRender formComponentRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .summaryKeyComponentInfos(summaryKeyComponentInfos)
        .formTableList(formTableList)
        .layoutType("DETAIL")
        .recordType("test_record")
        .data(data)
        .functionPrivilegeService(functionPrivilegeService)
        .unauthorizedFields(unauthorizedFields)
        .dbLayoutType("test_layout")
        .orderFormFieldList(orderFormFieldList)
        .build();

    // 执行被测试方法
    assertDoesNotThrow(() -> {
      formComponentRender.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试render方法不同布局类型
   */
  @Test
  @DisplayName("正常场景 - 测试不同布局类型渲染")
  void testRender_DifferentLayoutTypes() {
    // 测试EDIT布局类型
    FormComponentRender editRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("EDIT")
        .build();

    assertDoesNotThrow(() -> {
      editRender.render();
    });

    // 测试ADD布局类型
    FormComponentRender addRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("ADD")
        .build();

    assertDoesNotThrow(() -> {
      addRender.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试LIST布局类型渲染
   */
  @Test
  @DisplayName("正常场景 - 测试LIST布局类型渲染")
  void testRender_ListLayoutType() {
    // 准备测试数据
    FormComponentExt mockFormComponent = mock(FormComponentExt.class);
    formComponentExtList.add(mockFormComponent);

    FormComponentRender listRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("LIST")
        .recordType("")
        .build();

    assertDoesNotThrow(() -> {
      listRender.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试DETAIL布局类型渲染
   */
  @Test
  @DisplayName("正常场景 - 测试DETAIL布局类型渲染")
  void testRender_DetailLayoutType() {
    FormComponentRender detailRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("DETAIL")
        .build();

    assertDoesNotThrow(() -> {
      detailRender.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试带有FormTable的渲染
   */
  @Test
  @DisplayName("正常场景 - 测试带有FormTable的渲染")
  void testRender_WithFormTable() {
    // 准备测试数据
    FormTable mockFormTable = mock(FormTable.class);
    formTableList.add(mockFormTable);

    FormComponentRender renderWithTable = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .formTableList(formTableList)
        .layoutType("EDIT")
        .build();

    assertDoesNotThrow(() -> {
      renderWithTable.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试带有SummaryKeyComponentInfo的渲染
   */
  @Test
  @DisplayName("正常场景 - 测试带有SummaryKeyComponentInfo的渲染")
  void testRender_WithSummaryKeyComponent() {
    // 准备测试数据
    SummaryKeyComponentInfo mockSummaryComponent = mock(SummaryKeyComponentInfo.class);
    summaryKeyComponentInfos.add(mockSummaryComponent);

    FormComponentRender renderWithSummary = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .summaryKeyComponentInfos(summaryKeyComponentInfos)
        .layoutType("DETAIL")
        .build();

    assertDoesNotThrow(() -> {
      renderWithSummary.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试带有recordType的LIST布局渲染
   */
  @Test
  @DisplayName("正常场景 - 测试带有recordType的LIST布局渲染")
  void testRender_ListWithRecordType() {
    FormComponentRender listRenderWithRecordType = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("LIST")
        .recordType("test_record_type")
        .build();

    assertDoesNotThrow(() -> {
      listRenderWithRecordType.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试带有所有参数的完整渲染
   */
  @Test
  @DisplayName("正常场景 - 测试完整参数渲染")
  void testRender_FullParameters() {
    // 准备测试数据
    FormComponentExt mockFormComponent = mock(FormComponentExt.class);
    formComponentExtList.add(mockFormComponent);

    FormTable mockFormTable = mock(FormTable.class);
    formTableList.add(mockFormTable);

    SummaryKeyComponentInfo mockSummaryComponent = mock(SummaryKeyComponentInfo.class);
    summaryKeyComponentInfos.add(mockSummaryComponent);

    unauthorizedFields.add("unauthorized_field");

    FormComponentRender fullRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .summaryKeyComponentInfos(summaryKeyComponentInfos)
        .formTableList(formTableList)
        .layoutType("EDIT")
        .recordType("test_record")
        .data(data)
        .functionPrivilegeService(functionPrivilegeService)
        .unauthorizedFields(unauthorizedFields)
        .dbLayoutType("test_layout")
        .orderFormFieldList(orderFormFieldList)
        .build();

    assertDoesNotThrow(() -> {
      fullRender.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试带有DescribeLogicService的渲染
   */
  @Test
  @DisplayName("正常场景 - 测试带有DescribeLogicService的渲染")
  void testRender_WithDescribeLogicService() {
    FormComponentRender renderWithService = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("DETAIL")
        .objectDescribeService(describeLogicService)
        .build();

    assertDoesNotThrow(() -> {
      renderWithService.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试LIST布局类型且有FormTable的渲染
   */
  @Test
  @DisplayName("正常场景 - 测试LIST布局类型且有FormTable的渲染")
  void testRender_ListLayoutWithFormTable() {
    // 准备测试数据
    FormComponentExt mockFormComponent = mock(FormComponentExt.class);
    formComponentExtList.add(mockFormComponent);

    FormTable mockFormTable = mock(FormTable.class);
    formTableList.add(mockFormTable);

    FormComponentRender listRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .formTableList(formTableList)
        .layoutType("LIST")
        .recordType("")
        .build();

    assertDoesNotThrow(() -> {
      listRender.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试外部用户的渲染
   */
  @Test
  @DisplayName("正常场景 - 测试外部用户的渲染")
  void testRender_OutUser() {
    // 准备测试数据
    lenient().when(user.isOutUser()).thenReturn(true);

    FormComponentRender outUserRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("EDIT")
        .build();

    assertDoesNotThrow(() -> {
      outUserRender.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试从对象的渲染
   */
  @Test
  @DisplayName("正常场景 - 测试从对象的渲染")
  void testRender_SlaveObject() {
    // 准备测试数据
    lenient().when(objectDescribeExt.isSlaveObject()).thenReturn(true);

    FormComponentRender slaveObjectRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("ADD")
        .build();

    assertDoesNotThrow(() -> {
      slaveObjectRender.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试带有支付字段的渲染
   */
  @Test
  @DisplayName("正常场景 - 测试带有支付字段的渲染")
  void testRender_WithPaymentField() {
    // 准备测试数据
    PaymentFieldDescribe paymentFieldDescribe = mock(PaymentFieldDescribe.class);
    lenient().when(paymentFieldDescribe.getPayStatusFieldApiName()).thenReturn("pay_status");
    lenient().when(paymentFieldDescribe.getPayTimeFieldApiName()).thenReturn("pay_time");
    lenient().when(paymentFieldDescribe.getPayTypeFieldApiName()).thenReturn("pay_type");
    lenient().when(objectDescribeExt.getPaymentFieldDescribe()).thenReturn(Optional.of(paymentFieldDescribe));

    FormComponentRender paymentRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("EDIT")
        .build();

    assertDoesNotThrow(() -> {
      paymentRender.render();
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试带有租户ID的用户渲染
   */
  @Test
  @DisplayName("正常场景 - 测试带有租户ID的用户渲染")
  void testRender_WithTenantId() {
    // 准备测试数据
    when(user.getTenantId()).thenReturn("test_tenant_123");

    FormComponentRender tenantRender = FormComponentRender.builder()
        .user(user)
        .describeExt(objectDescribeExt)
        .formComponentExtList(formComponentExtList)
        .layoutType("EDIT")
        .build();

    assertDoesNotThrow(() -> {
      tenantRender.render();
    });
  }


}
