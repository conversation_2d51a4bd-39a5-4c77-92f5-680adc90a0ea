package com.facishare.paas.appframework.metadata.changeorder.convert;

import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.describe.SelectOne;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ChangeOrderObjectFieldConvert的JUnit 5测试类
 * 测试变更单对象字段转换器功能
 *
 * GenerateByAI
 * 测试内容描述：测试产品分类字段处理和相关判断逻辑
 */
@ExtendWith(MockitoExtension.class)
class ChangeOrderObjectFieldConvertTest {

    @Mock
    private FieldDescribeExt mockFieldExt;

    @Mock
    private SelectOne mockSelectOne;

    /**
     * 测试辅助类，包含要测试的逻辑
     * 避免Spring上下文依赖问题
     */
    static class ProductCategoryFieldHelper {

        /**
         * 处理产品分类字段，设置 option_type 为 'category'
         */
        public static void handleProductCategoryField(String quotedObjectApiName, String quotedFieldName, FieldDescribeExt fieldExt) {
            // 检查是否为选择字段
            if (!fieldExt.isSelectField()) {
                return;
            }

            // 检查是否为产品分类字段
            boolean isProductCategoryField = isProductCategoryField(quotedObjectApiName, quotedFieldName, fieldExt);

            if (isProductCategoryField) {
                fieldExt.setOptionalType("category");
                SelectOne selectOne = fieldExt.getFieldDescribe();
                selectOne.setSelectOptions(Collections.emptyList());
            }
        }

        /**
         * 判断是否为产品分类字段
         */
        public static boolean isProductCategoryField(String quotedObjectApiName, String quotedFieldName, FieldDescribeExt fieldExt) {
            // 1. 检查是否为产品或商品对象的分类字段（必须是选择字段）
            if (("ProductObj".equals(quotedObjectApiName) || "SPUObj".equals(quotedObjectApiName))
                    && "category".equals(quotedFieldName) && fieldExt.isSelectField()) {
                return true;
            }

            // 2. 检查字段的 option_type 是否已经是 'category'
            if (fieldExt.isSelectField()) {
                return "category".equals(fieldExt.getOptionalType());
            }

            return false;
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理产品分类字段 - 非选择字段情况
     */
    @Test
    @DisplayName("产品分类字段处理 - 非选择字段直接返回")
    void testHandleProductCategoryField_NotSelectField() {
        // Arrange: 设置非选择字段
        when(mockFieldExt.isSelectField()).thenReturn(false);

        // Act: 调用处理方法
        ProductCategoryFieldHelper.handleProductCategoryField("ProductObj", "category", mockFieldExt);

        // Assert: 验证只检查了字段类型，没有进行其他操作
        verify(mockFieldExt, times(1)).isSelectField();
        verify(mockFieldExt, never()).setOptionalType(anyString());
        verify(mockFieldExt, never()).getFieldDescribe();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理产品分类字段 - 产品对象分类字段
     */
    @Test
    @DisplayName("产品分类字段处理 - 产品对象分类字段设置category类型")
    void testHandleProductCategoryField_ProductCategoryField() {
        // Arrange: 设置为选择字段且为产品分类字段
        when(mockFieldExt.isSelectField()).thenReturn(true);
        when(mockFieldExt.getFieldDescribe()).thenReturn(mockSelectOne);

        // Act: 调用处理方法
        ProductCategoryFieldHelper.handleProductCategoryField("ProductObj", "category", mockFieldExt);

        // Assert: 验证设置了category类型并清空了选项
        verify(mockFieldExt, times(2)).isSelectField(); // 在handleProductCategoryField和isProductCategoryField中各调用一次
        verify(mockFieldExt, times(1)).setOptionalType("category");
        verify(mockFieldExt, times(1)).getFieldDescribe();
        verify(mockSelectOne, times(1)).setSelectOptions(Collections.emptyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理产品分类字段 - 商品对象分类字段
     */
    @Test
    @DisplayName("产品分类字段处理 - 商品对象分类字段设置category类型")
    void testHandleProductCategoryField_SPUCategoryField() {
        // Arrange: 设置为选择字段且为商品分类字段
        when(mockFieldExt.isSelectField()).thenReturn(true);
        when(mockFieldExt.getFieldDescribe()).thenReturn(mockSelectOne);

        // Act: 调用处理方法
        ProductCategoryFieldHelper.handleProductCategoryField("SPUObj", "category", mockFieldExt);

        // Assert: 验证设置了category类型并清空了选项
        verify(mockFieldExt, times(2)).isSelectField(); // 在handleProductCategoryField和isProductCategoryField中各调用一次
        verify(mockFieldExt, times(1)).setOptionalType("category");
        verify(mockFieldExt, times(1)).getFieldDescribe();
        verify(mockSelectOne, times(1)).setSelectOptions(Collections.emptyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试处理产品分类字段 - 非产品分类字段不处理
     */
    @Test
    @DisplayName("产品分类字段处理 - 非产品分类字段不进行处理")
    void testHandleProductCategoryField_NotProductCategoryField() {
        // Arrange: 设置为选择字段但非产品分类字段
        when(mockFieldExt.isSelectField()).thenReturn(true);
        when(mockFieldExt.getOptionalType()).thenReturn("normal");

        // Act: 调用处理方法
        ProductCategoryFieldHelper.handleProductCategoryField("AccountObj", "name", mockFieldExt);

        // Assert: 验证没有进行category设置
        verify(mockFieldExt, times(2)).isSelectField(); // 在handleProductCategoryField和isProductCategoryField中各调用一次
        verify(mockFieldExt, never()).setOptionalType(anyString());
        verify(mockFieldExt, never()).getFieldDescribe();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试判断产品分类字段 - 产品对象分类字段
     */
    @Test
    @DisplayName("产品分类字段判断 - 产品对象分类字段返回true")
    void testIsProductCategoryField_ProductCategoryField() {
        // Arrange: 设置为选择字段
        when(mockFieldExt.isSelectField()).thenReturn(true);

        // Act: 调用判断方法
        boolean result = ProductCategoryFieldHelper.isProductCategoryField("ProductObj", "category", mockFieldExt);

        // Assert: 验证返回true
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试判断产品分类字段 - 商品对象分类字段
     */
    @Test
    @DisplayName("产品分类字段判断 - 商品对象分类字段返回true")
    void testIsProductCategoryField_SPUCategoryField() {
        // Arrange: 设置为选择字段
        when(mockFieldExt.isSelectField()).thenReturn(true);

        // Act: 调用判断方法
        boolean result = ProductCategoryFieldHelper.isProductCategoryField("SPUObj", "category", mockFieldExt);

        // Assert: 验证返回true
        assertTrue(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试判断产品分类字段 - 已设置category类型的字段
     */
    @Test
    @DisplayName("产品分类字段判断 - 已设置category类型的字段返回true")
    void testIsProductCategoryField_AlreadyCategoryType() {
        // Arrange: 设置字段已经是category类型
        when(mockFieldExt.isSelectField()).thenReturn(true);
        when(mockFieldExt.getOptionalType()).thenReturn("category");

        // Act: 调用判断方法
        boolean result = ProductCategoryFieldHelper.isProductCategoryField("AccountObj", "name", mockFieldExt);

        // Assert: 验证返回true
        assertTrue(result);
        verify(mockFieldExt, times(1)).isSelectField();
        verify(mockFieldExt, times(1)).getOptionalType();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试判断产品分类字段的参数化测试
     */
    @ParameterizedTest
    @MethodSource("provideNonProductCategoryFieldArguments")
    @DisplayName("产品分类字段判断 - 非产品分类字段参数化测试")
    void testIsProductCategoryField_NonProductCategoryFields(String objectApiName, String fieldName, boolean expected) {
        // Arrange: 设置非category类型
        when(mockFieldExt.isSelectField()).thenReturn(true);
        when(mockFieldExt.getOptionalType()).thenReturn("normal");

        // Act: 调用判断方法
        boolean result = ProductCategoryFieldHelper.isProductCategoryField(objectApiName, fieldName, mockFieldExt);

        // Assert: 验证返回期望结果
        assertEquals(expected, result);
    }

    /**
     * 提供非产品分类字段的测试参数
     */
    static Stream<Arguments> provideNonProductCategoryFieldArguments() {
        return Stream.of(
                Arguments.of("AccountObj", "category", false),
                Arguments.of("ProductObj", "name", false),
                Arguments.of("SPUObj", "name", false),
                Arguments.of("ContactObj", "category", false),
                Arguments.of("OtherObj", "category", false)
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试判断产品分类字段 - 非选择字段情况
     */
    @Test
    @DisplayName("产品分类字段判断 - 非选择字段返回false")
    void testIsProductCategoryField_NotSelectField() {
        // Arrange: 设置非选择字段
        when(mockFieldExt.isSelectField()).thenReturn(false);

        // Act: 调用判断方法
        boolean result = ProductCategoryFieldHelper.isProductCategoryField("ProductObj", "category", mockFieldExt);

        // Assert: 验证返回false
        assertFalse(result);
        verify(mockFieldExt, never()).getOptionalType();
    }
}
