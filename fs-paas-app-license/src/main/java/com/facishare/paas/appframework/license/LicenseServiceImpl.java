package com.facishare.paas.appframework.license;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.APPException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.dto.QuotaInfo;
import com.facishare.paas.appframework.license.dto.GetVersion.VersionInfo;
import com.facishare.paas.appframework.license.dto.ModuleParaLicense;
import com.facishare.paas.appframework.license.exception.LicenseException;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig;
import com.facishare.paas.license.Result.*;
import com.facishare.paas.license.arg.*;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.LicenseObjectInfoContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.exception.PaasMessage;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.JudgeModulePojo;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.license.pojo.ModuleParaPojo;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("licenseService")
public class LicenseServiceImpl implements LicenseService {
    public static final String VERSION_PRODUCT_TYPE = "0";

    private static final Set<String> WECHAT_STANDARD_LICENSE_APPS = ImmutableSet.of("wechat_standard_100_appwechat_standard_50_app",
            "wechat_standard_30_app", "wechat_strengthen_edition", "wechat_standard_edition", "wechat_standardpro_upgrade_strengthen_edition",
            "wechat_standard_upgrade_strengthen_edition", "wechat_scrm_private_500_industry", "wechat_scrm_private_100_industry",
            "wechat_scrm_private_500plus_industry", "wechat_scrm_private_30_industry", "wechat_scrm_private_200_industry");
    //@Autowired
    //private LicenseProxy licenseProxy;

    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    @Override
    public List<String> getVersionAndPackages(String tenantId) {
        List<ProductVersionPojo> versionPojoList = getLicenseVersion(tenantId);
        return versionPojoList
                .stream()
                .map(ProductVersionPojo::getCurrentVersion).collect(Collectors.toList());
    }

    private VersionInfo getTenantVersion(String tenantId) {
        List<ProductVersionPojo> versionPojoList = getLicenseVersion(tenantId);
        Optional<ProductVersionPojo> versionPojo = CollectionUtils.nullToEmpty(versionPojoList)
                .stream()
                .filter(a -> Objects.equals(a.getProductType(), VERSION_PRODUCT_TYPE))
                .findFirst();
        return versionPojo.map(VersionInfo::from).orElse(null);
    }

    private List<ProductVersionPojo> getLicenseVersion(String tenantId) {
        LicenseContext context = buildLicenseContext(tenantId);
        QueryProductArg arg = new QueryProductArg();
        arg.setLicenseContext(context);
        LicenseVersionResult licenseVersionResult = licenseClient.queryProductVersion(arg);

        if (Objects.isNull(licenseVersionResult)) {
            throw new APPException("license service error");
        }

        if (!isSuccess(licenseVersionResult)) {
            log.error("queryProductVersion error,arg:{},result:{}", arg, licenseVersionResult);
            throw new LicenseException(licenseVersionResult.getErrMessage());
        }

        return CollectionUtils.nullToEmpty(licenseVersionResult.getResult());
    }

    @Override
    public String getVersion(String tenantId) {
        VersionInfo tenantVersion = getTenantVersion(tenantId);
        return Objects.nonNull(tenantVersion) ? tenantVersion.getCurrentVersion() : null;
    }


    @Override
    public VersionInfo getVersionInfo(String tenantId) {
        return getTenantVersion(tenantId);
    }

    @Override
    public int getQuotaByModule(String tenantId, String paraKey) {
        LicenseContext context = buildLicenseContext(tenantId);
        QueryModuleParaArg arg = new QueryModuleParaArg();
        arg.setContext(context);
        arg.setParaKeys(Sets.newHashSet(paraKey));
        ParaInfoResult paraInfoResult = licenseClient.queryModulePara(arg);

        if (Objects.isNull(paraInfoResult)) {
            throw new APPException("license service error");
        }

        if (!isSuccess(paraInfoResult)) {
            log.error("queryModulePara error,arg:{},result:{}", arg, paraInfoResult);
            throw new LicenseException(paraInfoResult.getErrMessage());
        }

        return CollectionUtils.empty(paraInfoResult.getResult()) ? 0 :
                Integer.parseInt(paraInfoResult.getResult().get(0).getParaValue());
    }

    private boolean isSuccess(Result result) {
        return Objects.equals(result.getErrCode(), PaasMessage.SUCCESS.getCode());
    }

    @Override
    public Set<String> getModule(String tenantId) {
        LicenseContext context = buildLicenseContext(tenantId);
        QueryModuleArg arg = new QueryModuleArg();
        arg.setLicenseContext(context);
        ModuleInfoResult moduleInfoResult = licenseClient.queryModule(arg);


        if (Objects.isNull(moduleInfoResult)) {
            throw new APPException("license service error");
        }

        if (!isSuccess(moduleInfoResult)) {
            log.error("queryModule error,arg:{},result:{}", arg, moduleInfoResult);
            throw new LicenseException(moduleInfoResult.getErrMessage());
        }

        return CollectionUtils.nullToEmpty(moduleInfoResult.getResult())
                .stream()
                .map(ModuleInfoPojo::getModuleCode).collect(Collectors.toSet());
    }

    private LicenseContext buildLicenseContext(String tenantId) {
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
        licenseContext.setTenantId(tenantId);
        licenseContext.setUserId(DefObjConstants.SUPER_PRIVILEGE_USER_ID);
        return licenseContext;
    }


    @Override
    public Map<String, List<ModuleParaLicense>> batchGetModuleLicenses(User user, Map<String, Set<String>> map) {
        LicenseContext context = buildLicenseContext(user.getTenantId());
        BatchQueryModuleParaArg arg = new BatchQueryModuleParaArg();
        arg.setContext(context);
        arg.setModuleCodeParaKeyMap(map);
        Result<Map<String, List<ModuleParaPojo>>> batchQueryModulePara = licenseClient.batchQueryModulePara(arg);


        if (Objects.isNull(batchQueryModulePara)) {
            throw new APPException("license service error");
        }

        if (!isSuccess(batchQueryModulePara)) {
            log.error("batchQueryModulePara error,arg:{},result:{}", arg, batchQueryModulePara);
            throw new LicenseException(batchQueryModulePara.getErrMessage());
        }

        if (CollectionUtils.empty(batchQueryModulePara.getResult())) {
            return buildEmptyLicenses(user, map);
        }
        Map<String, List<ModuleParaLicense>> resultSet = Maps.newHashMap();
        batchQueryModulePara.getResult().forEach((k, v) -> {
            resultSet.put(k,
                    CollectionUtils.nullToEmpty(v)
                            .stream()
                            .map(ModuleParaLicense::from)
                            .collect(Collectors.toList()));
        });

        return resultSet;
    }

    private Map<String, List<ModuleParaLicense>> buildEmptyLicenses(User user, Map<String, Set<String>> moduleCodeParaKeyMap) {
        Map<String, List<ModuleParaLicense>> resultMap = Maps.newHashMap();
        moduleCodeParaKeyMap.forEach((moduleCode, paraKeys) -> {
            List<ModuleParaLicense> licenses = paraKeys.stream()
                    .map(paraKey -> ModuleParaLicense.builder()
                            .tenantId(user.getTenantId())
                            .moduleCode(moduleCode)
                            .paraKey(paraKey)
                            .build())
                    .collect(Collectors.toList());
            resultMap.put(moduleCode, licenses);
        });
        return resultMap;
    }

    @Override
    public Set<String> queryAvailableObject(String tenantId) {
        Set<String> availableObject = getObjectApiNames(tenantId);
        return appendChangeOrderObject(tenantId, availableObject);
    }

    @Override
    public Set<String> getObjectApiNames(String tenantId, String crmKey, String moduleType) {
        LicenseContext context = buildLicenseContext(tenantId);
        LicenseObjectInfoContext arg = new LicenseObjectInfoContext();
        arg.setContext(context);
        arg.setCrmKey(crmKey);
        arg.setModuleType(moduleType);
        Result<Set<String>> queryApiNameByLicense = licenseClient.queryApiNameByLicense(arg);

        if (Objects.isNull(queryApiNameByLicense)) {
            throw new APPException("license service error");
        }

        if (!isSuccess(queryApiNameByLicense)) {
            log.error("queryApiNameByLicense error,arg:{},result:{}", arg, queryApiNameByLicense);
            throw new LicenseException(queryApiNameByLicense.getErrMessage());
        }

        return CollectionUtils.nullToEmpty(queryApiNameByLicense.getResult());
    }

    @Override
    public Set<String> getObjectApiNames(String tenantId) {
        return getObjectApiNames(tenantId, "crm_manage_custom_object", "0");
    }

    /**
     * 变更单需求，变更单对象的license 跟随原单对象的license
     *
     * @param tenantId
     * @param availableObject
     * @return
     */
    private Set<String> appendChangeOrderObject(String tenantId, Set<String> availableObject) {
        Set<String> result = Sets.newHashSet();
        for (String apiName : availableObject) {
            result.add(apiName);
            if (!ChangeOrderConfig.changeOrderDescribeGray(tenantId, apiName)) {
                continue;
            }
            ChangeOrderConfig.ChangeOrderConfigItem changeOrderConfigItem = ChangeOrderConfig.getChangeOrderConfigItem(apiName);
            if (Objects.isNull(changeOrderConfigItem)) {
                continue;
            }
            result.add(changeOrderConfigItem.getChangeOrderApiName());
        }
        return result;

    }

    @Override
    public Map<String, Boolean> existModule(String tenantId, Set<String> productCodes) {
        List<String> codes = Lists.newArrayList(productCodes);
        JudgeModuleArg arg = new JudgeModuleArg();
        arg.setContext(buildLicenseContext(tenantId));
        arg.setModuleCodes(codes);
        Result<JudgeModulePojo> result = licenseClient.judgeModule(arg);

        Map<String, Boolean> map = codes.stream().collect(Collectors.toMap(a -> a, b -> false));
        if (Objects.nonNull(result.getResult())) {
            CollectionUtils.nullToEmpty(result.getResult().getModuleFlags()).forEach(a -> map.put(a.getModuleCode(), a.isFlag()));
        }
        return map;
    }

    /**
     * 判断当前企业购版的 License 是包含 crm 模块
     *
     * @param tenantId 租户id
     * @return true 有 crm 的 License
     */
    @Override
    public boolean isOpenCRM(String tenantId) {
        List<ProductVersionPojo> licenseVersion = getLicenseVersion(tenantId);
        return CollectionUtils.nullToEmpty(licenseVersion).stream()
                .filter(it -> !LicenseConstants.Versions.VERSION_OFFICE.equals(it.getCurrentVersion()))
                .anyMatch(it -> VERSION_PRODUCT_TYPE.equals(it.getProductType()));
    }

    @Override
    public boolean isSupportMultiLanguage(String tenantId) {
        Map<String, Boolean> existModule = existModule(tenantId, Sets.newHashSet(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP));
        return existModule.getOrDefault(LicenseConstants.ModuleCode.MULTI_LANGUAGE_APP, false);
    }

    @Override
    public boolean isSupportChangeOrder(String tenantId) {
        Map<String, Boolean> existModule = existModule(tenantId, Sets.newHashSet(LicenseConstants.ModuleCode.OBJECT_CHANGE_ORDER_APP));
        return existModule.getOrDefault(LicenseConstants.ModuleCode.OBJECT_CHANGE_ORDER_APP, false);
    }

    @Override
    public boolean isSupportConvertRule(String tenantId) {
        Map<String, Boolean> existModule = existModule(tenantId, Sets.newHashSet(LicenseConstants.ModuleCode.PUSH_PULL_ORDER_APP));
        return existModule.getOrDefault(LicenseConstants.ModuleCode.PUSH_PULL_ORDER_APP, false);
    }

    @Override
    public boolean isSupportOneFlowApp(String tenantId) {
        return UdobjGrayConfig.isAllow(UdobjGrayConfigKey.ONE_FLOW_GRAY_EI, tenantId)
                && existModule(tenantId, Sets.newHashSet(LicenseConstants.ModuleCode.ONE_FLOW_APP))
                        .getOrDefault(LicenseConstants.ModuleCode.ONE_FLOW_APP, false);
    }

    @Override
    public boolean isSupportBigObject(String tenantId) {
        Map<String, Boolean> existModule = existModule(tenantId, Sets.newHashSet(LicenseConstants.ModuleCode.BIG_OBJECT_APP));
        return existModule.getOrDefault(LicenseConstants.ModuleCode.BIG_OBJECT_APP, false);
    }

    @Override
    public boolean isSupportMultiRegion(String tenantId) {
        Map<String, Boolean> existModule = existModule(tenantId, Sets.newHashSet(LicenseConstants.ModuleCode.MULTI_REGION_APP));
        return existModule.getOrDefault(LicenseConstants.ModuleCode.MULTI_REGION_APP, false);
    }

    @Override
    public boolean hasAILicence(String tenantId) {
        Map<String, Boolean> existModule = existModule(tenantId, Sets.newHashSet(LicenseConstants.ModuleCode.AI_PRODUCT_APP));
        return existModule.getOrDefault(LicenseConstants.ModuleCode.AI_PRODUCT_APP, false);
    }

    @Override
    public boolean acquireUsedValue(String tenantId, String licenseKey, String paraKey, int count) {
        AcquireUsedValueArg arg = new AcquireUsedValueArg();
        arg.setContext(buildLicenseContext(tenantId));
        arg.setModuleCode(licenseKey);
        arg.setParaKey(paraKey);
        arg.setPermits(count);
        AcquireUsedValueResult result = null;
        try {
            result = licenseClient.acquireUsedValue(arg);
        } catch (com.facishare.paas.license.exception.LicenseException e) {
            log.warn("acquireUsedValue error,arg:{},result:{}", arg, e.getMessage());
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }

        if (Objects.nonNull(result) && Objects.nonNull(result.getResult())) {
            return result.getResult().isAvailable();
        }
        return false;
    }

    @Override
    public Map<String, String> acquireUsedValue(String tenantId, Set<String> paraKeySet) {
        OverviewInfoArg arg = new OverviewInfoArg();
        arg.setContext(buildLicenseContext(tenantId));
        arg.setParaKeys(paraKeySet);

        OverviewInfoResult result = licenseClient.acquireOverviewUsedValue(arg);
        if (Objects.nonNull(result) && CollectionUtils.notEmpty(result.getResult())) {
            return result.getResult().stream().collect(Collectors.toMap(a -> a.getParaKey(), b -> b.getUsedValue(), (x, y) -> y));
        }
        return Maps.newHashMap();
    }

    @Override
    public boolean isSupportWechatStandardLicense(String tenantId) {
        Map<String, Boolean> existModule = existModule(tenantId, WECHAT_STANDARD_LICENSE_APPS);
        return existModule.values().stream().anyMatch(BooleanUtils::isTrue);
    }

    @Override
    public Map<String, Map<String, QuotaInfo>> batchGetQuotaByModulePara(User user, Map<String, Set<String>> moduleParaMap) {
        if (CollectionUtils.empty(moduleParaMap)) {
            return Maps.newHashMap();
        }
        Map<String, List<ModuleParaLicense>> batchQueryModulePara = batchGetModuleLicenses(user, moduleParaMap);
        // 获取所有参数键用于查询已使用值
        Set<String> allParaKeys = moduleParaMap.values().stream()
                .flatMap(Set::stream)
                .collect(Collectors.toSet());

        Map<String, Map<String, QuotaInfo>> resultMap = Maps.newHashMap();
        batchQueryModulePara.forEach((moduleCode, moduleParaLicenses) -> {
            Map<String, QuotaInfo> quotaInfoMap = Maps.newHashMap();
            CollectionUtils.nullToEmpty(moduleParaLicenses).forEach(pojo -> {
                try {
                    int totalValue = StringUtils.isNotEmpty(pojo.getParaValue()) ?
                            Integer.parseInt(pojo.getParaValue()) : 0;
                    int usedValue = pojo.getUsedValue();
                    int availableValue = Math.max(0, totalValue - pojo.getUsedValue());
                    QuotaInfo quotaInfo = QuotaInfo.builder()
                            .total(totalValue)
                            .used(usedValue)
                            .available(availableValue)
                            .build();

                    quotaInfoMap.put(pojo.getParaKey(), quotaInfo);
                } catch (NumberFormatException e) {
                    log.warn("parse quota value error, moduleCode:{}, paraKey:{}, paraValue:{}",
                            moduleCode, pojo.getParaKey(), pojo.getParaValue());
                    QuotaInfo quotaInfo = QuotaInfo.builder()
                            .total(0)
                            .used(0)
                            .available(0)
                            .build();
                    quotaInfoMap.put(pojo.getParaKey(), quotaInfo);
                }
            });
            resultMap.put(moduleCode, quotaInfoMap);
        });
        return resultMap;
    }
}
