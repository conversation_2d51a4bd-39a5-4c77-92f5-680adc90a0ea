package com.facishare.paas.appframework.license;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.dto.GetVersion;
import com.facishare.paas.appframework.license.dto.ModuleParaLicense;
import com.facishare.paas.appframework.license.dto.QuotaInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 版本管理服务
 * <p>
 * Created by liyiguang on 2017/6/23.
 */
public interface LicenseService {

    /**
     * 获取企业的所有版本和资源包
     *
     * @param tenantId
     * @return
     */
    List<String> getVersionAndPackages(String tenantId);

    /**
     * 获取企业的版本
     *
     * @param tenantId
     * @return
     */
    String getVersion(String tenantId);

    /**
     * 获取企业的版本信息
     *
     * @param tenantId
     * @return
     */
    GetVersion.VersionInfo getVersionInfo(String tenantId);

    /**
     * 获取企业指定模块的数量上限
     *
     * @param tenantId
     * @param paraKey
     * @return
     */
    int getQuotaByModule(String tenantId, String paraKey);

    /**
     * 获取企业开通的模块
     *
     * @param tenantId
     * @return
     */
    Set<String> getModule(String tenantId);

    Map<String, List<ModuleParaLicense>> batchGetModuleLicenses(User user, Map<String, Set<String>> map);

    Set<String> queryAvailableObject(String tenantId);

    /**
     * 获取企业支持的预设对象
     *
     * @param tenantId
     * @return
     */
    Set<String> getObjectApiNames(String tenantId, String crmKey, String moduleType);

    Set<String> getObjectApiNames(String tenantId);

    /**
     * 是否开通指定module
     */
    Map<String, Boolean> existModule(String tenantId, Set<String> moduleCodes);

    boolean isOpenCRM(String tenantId);

    boolean isSupportMultiLanguage(String tenantId);

    boolean isSupportChangeOrder(String tenantId);

    boolean isSupportConvertRule(String tenantId);

    boolean isSupportOneFlowApp(String tenantId);

    boolean isSupportBigObject(String tenantId);

    boolean isSupportMultiRegion(String tenantId);

    boolean hasAILicence(String tenantId);

    boolean acquireUsedValue(String tenantId, String licenseKey, String paraKey, int count);

    Map<String, String> acquireUsedValue(String tenantId, Set<String> paraKeySet);

    /**
     * 企业微信标准版接口
     *
     * @param tenantId
     * @return
     */
    boolean isSupportWechatStandardLicense(String tenantId);

    /**
     * 批量获取模块参数配额和使用情况
     *
     * @param tenantId 租户ID
     * @param moduleParaMap 模块编码和参数键的映射
     * @return 模块编码和配额信息的映射
     */
    Map<String, Map<String, QuotaInfo>> batchGetQuotaByModulePara(User user, Map<String, Set<String>> moduleParaMap);
}
