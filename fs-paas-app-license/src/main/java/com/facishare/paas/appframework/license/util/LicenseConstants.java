package com.facishare.paas.appframework.license.util;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

/**
 * Created by zhouwr on 2017/10/13
 */
public class LicenseConstants {

    public interface Versions {
        // 办公版(原协同，非CRM版本)
        String VERSION_OFFICE = "office_edition";
        //基础版
        String VERSION_BASIC = "basic_edition";
        //标准版
        String VERSION_STANDARD = "standard_edition";
        //专业版
        String VERSION_STANDARDPRO = "standardpro_edition";
        //旗舰版
        String VERSION_ENTERPRISE = "enterprise_edition";
        //旗舰增强版
        String VERSION_STRENGTHEN = "strengthen_edition";
        //访销版
        String VERSION_PROMOTION_SALES = "promotion_sales_edition";
        //企信版 针对线上bug添加该版本
        String WECHAT_STANDARDDPRO_EDITION = "wechat_standardpro_edition";

        String DINGTALK_STRENGTHEN_EDITION = "dingtalk_strengthen_edition";

        List<String> SORTED_VERSION_LIST = Collections.unmodifiableList(Lists.newArrayList(VERSION_ENTERPRISE,
                VERSION_STRENGTHEN, VERSION_STANDARDPRO, VERSION_STANDARD, VERSION_BASIC, VERSION_PROMOTION_SALES,
                WECHAT_STANDARDDPRO_EDITION, DINGTALK_STRENGTHEN_EDITION));

        String VERSION_STANDARDPRO_STR = "standardpro";
        String VERSION_ENTERPRISE_STR = "enterprise";
        String VERSION_STRENGTHEN_STR = "strengthen";

        static boolean isStandardProUpVersion(String version) {
            if (StringUtils.isBlank(version)) {
                return false;
            }
            return version.contains(VERSION_STANDARDPRO_STR) || version.contains(VERSION_ENTERPRISE_STR) || version.contains(VERSION_STRENGTHEN_STR);
        }
    }

    public interface Packages {
        //资源扩展包
        String EXTENTION_PACKAGE = "extention_package";
        //快销模块包
        String FMCG_PACKAGE = "fmcg_package";
        //企业互联包
        String ENTERPRISE_INTERCONNECTING_PACKAGE = "enterprise_interconnecting_package";
        //制造业包
        String MANUFACTURE_PACKAGE = "manufacture_package";
    }

    public interface ModuleCode {
        String CUSTOM_OBJECT = "custom_object";
        String BIG_OBJECT_APP = "big_object_app";
        String SOCIAL_OBJECT = "social_object";
        String PRINT_TEMPLATES = "print_templates";
        String EMAIL_TEMPLATES = "email_templates";
        String SMART_FORM = "smart_form";
        String CUSTOM_FUNCTION = "custom_function";
        String GRADE_INSTRUMENT = "grade_instrument";
        String OBJECT_MULTI_LAYOUT = "object_multi_layout";
        String OBJECT_MULTI_TYPE = "object_multi_type";
        String LAYOUT_RULES = "layout_rules";
        String FIELD_INFO = "field_type";
        String CUSTOM_ROLES = "custom_roles";
        String DATA_ROLES = "data_roles";
        String UI_EVENT = "layout_ui_event";
        String MASTER_DETAIL_OBJECT = "master_detail_object";
        String MASTER_RELATED_OBJECT = "master_related_object";
        String MASTER_RELATED_OBJECT_COMPONENT = "master_related_object_component";
        String ACCOUNT_HIERARCHY_APP = "account_hierarchy_app";
        String CONTACT_RELATION_APP = "contact_relation_app";
        //作战地图
        String CUSTOMER_BATTLE_MAP_APP = "customer_battle_map_app";
        // 互联应用
        String INTERCONNECT_APP_BASIC_APP = "interconnect_app_basic_app";
        String CUSTOM_BUTTON = "custom_button";
        // 筛选支持或
        String CUSTOM_OBJECT_LIST_FILTERS_OR_APP = "custom_object_list_filters_or_app";
        // 线索归集
        String LEADS_DEDUPLICATION_APP = "leads_deduplication_app";
        // 多组织
        String MULTI_BUSINESS_UNIT_APP = "multi_business_unit_app";
        //多语言
        String MULTI_LANGUAGE_APP = "multi_language_app";
        //日志分析
        String LOG_ANALYSIS_APP = "user_usage_stats_app";
        //变更单
        String OBJECT_CHANGE_ORDER_APP = "object_change_order_app";
        // 推拉单
        String PUSH_PULL_ORDER_APP = "push_pull_order_app";
        String MULTI_REGION_APP = "multi_region_app";
        String AI_PRODUCT_APP = "ai_product_app";
        // OneFlow应用
        String ONE_FLOW_APP = "one_flow_app";
    }


//    public interface ParaKeys {
//        //自定义对象类型个数上限 请求paas的参数
//        String CUSTOM_OBJECTS_TYPE_LIMIT = "custom_objects_type_limit";
//        //自定义对象个数上限 请求paas的参数
//        String CUSTOM_OBJECTS_LIMIT = "custom_objects_limit";
//        //自定义对象layout个数上限 请求paas的参数
//        String CUSTOM_OBJECT_LAYOUT_LIMIT = "custom_object_layout_limit";
//        //自定义角色个数上限 请求paas的参数
//        String CUSTOM_ROLES_LIMIT = "custom_roles_limit";
//        //数据来源共享数量限制
//        String DATA_SHARE_RULES_LIMIT = "data_share_rules_limit";
//        //条件共享数量限制
//        String CONDITIONAL_SHARING_LIMIT = "conditional_sharing_limit";
//        //自定义函数数量限制
//        String CUSTOM_FUNCTION_LIMIT = "custom_function_limit";
//
//        String PRINT_TEMPLATES_LIMIT = "print_templates_limit";
//
//        //邮件模板
//        String EMAIL_TEMPLATES_LIMIT = "email_templates_limit";
//
//        //智能表单
//        String SMART_FORM_LIMIT= "smart_form_limit";
//
//        //打分器规则个数
//        String SCORE_RULES_LIMIT= "grade_instrument_rules_limit";
//        //打分器指标个数
//        String SCORE_TARGET_LIMIT="grade_instrument_target_limit";
//
//        //打分器指标个数
//        String LAYOUT_RULES_LIMIT= "layout_rules_limit";
//
//        //各种字段类型数量
//        String PICTURE_LIMIT= "picture_limit";
//        String ATTACHMENT_LIMIT= "attachment_limit";
//        String REFERENCE_FIELD_LIMIT= "reference_field_limit";
//        String INCREASE_NUMBER_LIMIT="increase_number_limit";
//        String COMPUTED_FIELD_LIMIT= "computed_field_limit";
//        String STATISTICAL_FIELD_LIMIT= "statistical_field_limit";
//        String QUOTE_FIELD_LIMIT= "quote_field_limit";
//
//    }
}