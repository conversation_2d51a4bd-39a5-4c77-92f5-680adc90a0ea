<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <artifactId>fs-paas-appframework</artifactId>
        <groupId>com.facishare</groupId>
        <version>dev-stage-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>fs-paas-app-privilege-temp</artifactId>
    <packaging>jar</packaging>
    <name>fs-paas-app-privilege-temp</name>

    <properties>
        <appserver-common-tools.version>1.1-SNAPSHOT</appserver-common-tools.version>
        <jsonassert.version>1.3.0</jsonassert.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-qixin-rest-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-performance-log</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-common-mds-event</artifactId>
            <version>1.0.4-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>protostuff-core</artifactId>
                    <groupId>io.protostuff</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.qixin.plugin</groupId>
            <artifactId>fs-qixin-plugin-api</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-auth-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mongo-java-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-app-center-mq-api</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-open-app-pay-mq-api</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
            <version>${fs-fsi-proxy.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-jdbc</artifactId>
            <version>8.0.32</version>
        </dependency>

<!--        <dependency>-->
<!--        <groupId>org.springframework.amqp</groupId>-->
<!--        <artifactId>spring-rabbit</artifactId>-->
<!--        <version>1.3.6.RELEASE</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.facishare.appserver</groupId>
            <artifactId>fs-appserver-common-tools</artifactId>
            <version>${appserver-common-tools.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-fcp-biz-server</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-fcp-common</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>config-core</artifactId>
                    <groupId>com.github.colin-lee</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-metadata</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>fs-common-util</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-config</artifactId>
            <version>${project.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.squareup.okhttp3</groupId>-->
<!--            <artifactId>logging-interceptor</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.facishare.paas</groupId>
            <artifactId>fs-paas-auth-client</artifactId>
        </dependency>

    </dependencies>



</project>
