package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContext.Attributes;
import com.facishare.paas.appframework.core.model.RequestContext.CacheKeys;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;

import static com.facishare.paas.appframework.core.model.RequestContext.CacheKeys.EXCHANGE_RATES;

/**
 * Created by zhouwr on 2020/4/30
 */
public final class ContextCacheUtil {

    public static void openContextCache() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return;
        }
        context.setAttribute(Attributes.OPEN_CONTEXT_CACHE, true);
    }

    public static boolean isOpenContextCache() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        Boolean openCache = context.getAttribute(Attributes.OPEN_CONTEXT_CACHE);
        return Boolean.TRUE.equals(openCache);
    }

    public static void cacheFunctionPrivilege(String userId, Map<String, Boolean> functionPrivilege) {
        if (functionPrivilege == null || functionPrivilege.isEmpty()) {
            return;
        }
        if (!isOpenContextCache()) {
            return;
        }
        CacheContext context = CacheContext.getContext();
        functionPrivilege.forEach((k, v) -> context.setCache(buildFunctionPrivilegeKey(userId, k), v));
    }

    public static Map<String, Boolean> getFunctionPrivilegeCache(String userId, List<String> funcCodes) {
        if (funcCodes == null || funcCodes.isEmpty()) {
            return Maps.newHashMap();
        }
        CacheContext context = CacheContext.getContext();
        if (context == null) {
            return Maps.newHashMap();
        }
        Map<String, Boolean> result = Maps.newHashMap();
        funcCodes.stream().forEach(funcCode -> {
            Boolean value = context.getCache(buildFunctionPrivilegeKey(userId, funcCode));
            if (value != null) {
                result.put(funcCode, value);
            }
        });
        return result;
    }

    private static String buildFunctionPrivilegeKey(String userId, String funcCode) {
        return String.format(CacheKeys.FUNCTION_PRIVILEGE, userId, funcCode);
    }

    public static void cacheDataPrivilege(String userId, Map<String, String> privilegeMap) {
        if (privilegeMap == null || privilegeMap.isEmpty()) {
            return;
        }
        if (!isOpenContextCache()) {
            return;
        }
        CacheContext context = CacheContext.getContext();
        privilegeMap.forEach((id, permission) -> context.setCache(buildDataPrivilegeKey(userId, id), permission));
    }

    public static String getDataPrivilegeCache(String userId, String dataId) {
        CacheContext context = CacheContext.getContext();
        if (context == null) {
            return null;
        }
        return context.getCache(buildDataPrivilegeKey(userId, dataId));
    }

    public static Map<String, String> getDataPrivilegesCache(String userId, List<String> dataIdList) {
        if (dataIdList == null || dataIdList.isEmpty()) {
            return Maps.newHashMap();
        }
        CacheContext context = CacheContext.getContext();
        if (context == null) {
            return Maps.newHashMap();
        }
        Map<String, String> result = Maps.newHashMap();
        dataIdList.forEach(dataId -> {
            String permission = getDataPrivilegeCache(userId, dataId);
            if (permission != null) {
                result.put(dataId, permission);
            }
        });
        return result;
    }

    private static String buildDataPrivilegeKey(String userId, String dataId) {
        return String.format(CacheKeys.DATA_PRIVILEGE, userId, dataId);
    }

    public static void cacheFieldPermission(String userId, String objectApiName, Map<String, Integer> permissionMap) {
        if (!isOpenContextCache()) {
            return;
        }
        CacheContext context = CacheContext.getContext();
        context.setCache(buildFieldPermissionKey(userId, objectApiName), permissionMap);
    }

    public static Pair<Boolean, Optional<Map<String, Integer>>> getFieldPermissionCache(String userId, String objectApiName) {
        CacheContext context = CacheContext.getContext();
        if (context == null) {
            return new Pair<>(false, Optional.empty());
        }
        String key = buildFieldPermissionKey(userId, objectApiName);
        if (!context.containsCache(key)) {
            return new Pair<>(false, Optional.empty());
        }
        return new Pair<>(true, Optional.ofNullable(context.getCache(key)));
    }

    private static String buildFieldPermissionKey(String userId, String objectApiName) {
        return String.format(CacheKeys.FIELD_PERMISSION, userId, objectApiName);
    }

    public static void hmset(String key, Map<String, ?> hash) {
        CacheContext context = CacheContext.getContext();
        if (Objects.isNull(context)) {
            return;
        }
        Map<String, Object> cache = context.getCache(key);
        if (cache == null) {
            context.setCache(key, Maps.newHashMap(hash));
        } else {
            cache.putAll(hash);
        }
    }

    public static void set(String key, Object hash) {
        CacheContext context = CacheContext.getContext();
        if (Objects.isNull(context)) {
            return;
        }
        if (Objects.isNull(hash)) {
            return;
        }
        context.setCache(key, hash);
    }

    public static void hset(String key, String field, Object value) {
        CacheContext context = CacheContext.getContext();
        if (Objects.isNull(context)) {
            return;
        }
        Map<String, Object> cache = context.getCache(key);
        if (cache == null) {
            context.setCache(key, Maps.newHashMap(ImmutableMap.of(field, value)));
        } else {
            cache.put(field, value);
        }
    }

    @SuppressWarnings("unchecked")
    public static <V> Optional<V> hget(String key, String field) {
        CacheContext context = CacheContext.getContext();
        if (context == null) {
            return Optional.empty();
        }
        Map<String, Object> cache = context.getCache(key);
        if (CollectionUtils.isEmpty(cache)) {
            return Optional.empty();
        }
        return (Optional<V>) Optional.ofNullable(cache.get(field));
    }

    public static <T> T get(String key) {
        CacheContext context = CacheContext.getContext();
        if (Objects.isNull(context) || !context.containsCache(key)) {
            return null;
        }
        return context.getCache(key);
    }

    public static IObjectDescribe getOrElse(String tenantId, String describeApiName, Supplier<IObjectDescribe> other) {
        Objects.requireNonNull(other);
        CacheContext context = CacheContext.getContext();
        String describeCacheKey = String.format(RequestContext.CacheKeys.OBJECT_DESCRIBE, tenantId, describeApiName);
        return Optional.ofNullable(context)
                .map(it -> it.<IObjectDescribe>getCache(describeCacheKey))
                .orElseGet(() -> {
                    IObjectDescribe describe = other.get();
                    Optional.ofNullable(context).ifPresent(it -> it.setCache(describeCacheKey, describe));
                    return describe;
                });
    }

    public static void cacheUserSceneConfig(String userId, String configKey, Object value) {
        if (!isOpenContextCache()) {
            return;
        }
        CacheContext context = CacheContext.getContext();
        if (context == null) {
            return;
        }
        String key = String.format(CacheKeys.USER_SCENE_CONFIG, userId, configKey);
        context.setCache(key, value);
    }

    public static <V> Pair<Boolean, Optional<V>> getUserSceneConfigCache(String userId, String configKey) {
        if (!isOpenContextCache()) {
            return new Pair<>(false, Optional.empty());
        }
        CacheContext context = CacheContext.getContext();
        if (context == null) {
            return new Pair<>(false, Optional.empty());
        }
        String key = String.format(CacheKeys.USER_SCENE_CONFIG, userId, configKey);
        if (!context.containsCache(key)) {
            return new Pair<>(false, Optional.empty());
        }
        return new Pair<>(true, Optional.ofNullable(context.getCache(key)));
    }

    public static String buildTenantExchangeRatesKey(String tenantId) {
        return String.format(EXCHANGE_RATES, tenantId);
    }


    public static <T> T findByCache(User user, String objectDescribeApiName, String keyPrefix, Supplier<T> supplier) {
        String cacheKey = String.format(keyPrefix + "_%s_%s", user.getTenantId(), objectDescribeApiName);
        CacheContext cacheContext = CacheContext.getContext();
        Object cache = cacheContext.getCache(cacheKey);
        if (Objects.nonNull(cache)) {
            return (T) cache;
        }
        T result = supplier.get();
        cacheContext.setCache(cacheKey, result);
        return result;
    }
}
