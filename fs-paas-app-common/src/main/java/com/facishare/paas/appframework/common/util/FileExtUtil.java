package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.google.common.base.Strings;
import org.apache.commons.lang.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> create by liy on 2024/7/8
 */
public class FileExtUtil {

    public static final String IMAGE_FLAG = "@@image@@";
    
    // #号转义常量
    private static final String HASH_ESCAPE = "%23";

    /* 过滤作为目录名称的字符串中特殊字符*/
    private static final String SPECIAL_PATTERN = "[/\\\\:\"<>?|*\\.]";
    private static final Pattern pattern = Pattern.compile(SPECIAL_PATTERN);
    private static final Pattern spacePattern = Pattern.compile("\\u0003");
    private static final String SUFFIX_PATTERN = "^\\w+\\.\\w+\\.\\w+$";
    
    /**
     * 解析文件后缀
     */
    public static String parseFileExt(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return null;
        }
        String[] splits = fileName.split("\\.");
        if (splits.length < 2) {
            return null;
        }
        return splits[1];
    }

    /**
     * 转义文件名中的特殊字符（使用URL编码）
     * 
     * @param fileName 原始文件名
     * @return 转义后的文件名
     */
    public static String escapeFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return fileName;
        }
        
        try {
            return URLEncoder.encode(fileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            // 降级方案：手动转义关键字符
            return fileName.replace("%", "%25").replace("#", "%23");
        }
    }

    /**
     * 反转义文件名中的特殊字符（使用URL解码）
     * 
     * @param escapedFileName 转义后的文件名
     * @return 反转义后的文件名
     */
    public static String unescapeFileName(String escapedFileName) {
        if (StringUtils.isBlank(escapedFileName)) {
            return escapedFileName;
        }
        
        try {
            return URLDecoder.decode(escapedFileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            // 降级方案：手动反转义关键字符（按顺序很重要）
            return escapedFileName.replace("%23", "#").replace("%25", "%");
        } catch (IllegalArgumentException e) {
            // 如果不是有效的URL编码格式，可能是旧格式，尝试简单替换
            return escapedFileName.replace(HASH_ESCAPE, "#");
        }
    }

    public static boolean isEmbeddedImageField(IFieldDescribe fieldDescribe) {
        // 检查字段是否是嵌入式图片字段
        if (!IFieldType.IMAGE.equals(fieldDescribe.getType())) {
            return false;
        }

        // 检查字段名是否包含嵌入式图片标识
        String apiName = fieldDescribe.getApiName();
        return StringUtils.isNotEmpty(apiName) && apiName.contains(IMAGE_FLAG);
    }

    /**
     * 提取原始字段名（移除图片标识）
     */
    public static String extractImageOriginalFieldName(String apiName) {
        if (apiName.contains(IMAGE_FLAG)) {
            return apiName.substring(0, apiName.indexOf(IMAGE_FLAG));
        }
        return apiName;
    }

    /**
     * 过滤文件名中的特殊字符
     *
     * @param str 原始字符串
     * @return 过滤后的字符串
     */
    public static String filterName(String str) {
        if (StringUtils.isBlank(str)) {
            return I18NExt.getOrDefault(I18NKey.UNNAME, "Unnamed");
        }
        Matcher m = spacePattern.matcher(str);
        String spaceStr = m.replaceAll(" ").trim();
        String result = pattern.matcher(spaceStr).replaceAll("").trim();
        if (Strings.isNullOrEmpty(result)) {
            return I18NExt.getOrDefault(I18NKey.UNNAME, "Unnamed");
        }
        return result;
    }

    /**
     * 根据fullName获取图片/附件的文件名 fileName.ext#Npath
     *
     * @param fullName 完整文件名
     * @return 文件名
     */
    public static String getFileName(String fullName) {
        int wellCount = StringUtils.countMatches(fullName, "#");
        String nameAndExt;
        if (wellCount == 2) {
            nameAndExt = StringUtils.substringBefore(fullName, "#");
        } else {
            nameAndExt = StringUtils.substringBeforeLast(fullName, "#");
        }
        
        // 反转义文件名中的特殊字符
        nameAndExt = unescapeFileName(nameAndExt);
        
        String name = StringUtils.substringBeforeLast(nameAndExt, ".");
        String ext = StringUtils.substringAfterLast(nameAndExt, ".");

        String realName = filterName(name);
        if (StringUtils.isBlank(realName)) {
            realName = I18NExt.getOrDefault(I18NKey.UNNAME, "Unnamed");
        }
        if (StringUtils.isNotBlank(ext)) {
            return realName + "." + ext;
        }
        return realName;
    }

    /**
     * 根据fullname获取图片/附件的NPath
     *
     * @param fullName 完整文件名
     * @return NPath
     */
    public static String getPath(String fullName) {
        int wellCount = StringUtils.countMatches(fullName, "#");
        String path;
        if (wellCount == 2) {
            path = StringUtils.substringBetween(fullName, "#");
        } else {
            path = StringUtils.substringAfterLast(fullName, "#");
        }
        if (path.matches(SUFFIX_PATTERN)) {
            return StringUtils.substringBeforeLast(path, ".");
        }
        return path;
    }

    /**
     * 为文件名添加扩展名
     *
     * @param fileName 文件名
     * @return 带扩展名的文件名
     */
    public static String fillFileExt(String fileName) {
        return fileName + ".xlsx";
    }

    /**
     * 根据fullName获取signature字段 fileName.ext#Npath#signature
     *
     * @param fullName 完整文件名
     * @return signature字段，如果不存在则返回null
     */
    public static String getSignature(String fullName) {
        if (StringUtils.isBlank(fullName)) {
            return null;
        }
        int wellCount = StringUtils.countMatches(fullName, "#");
        if (wellCount == 2) {
            // 格式：fileName.ext#Npath#signature
            return StringUtils.substringAfterLast(fullName, "#");
        }
        return null;
    }

    /**
     * 重命名文件
     *
     * @param dataName 数据名称
     * @param filename 原文件名
     * @return 重命名后的文件名
     */
    public static String renameFileName(String dataName, String filename) {
        String legalName = filterName(dataName);
        if (StringUtils.isNotBlank(legalName)) {
            return legalName + "_" + filename;
        }
        return filename;
    }

    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 文件扩展名
     */
    public static String getFileExtension(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return null;
        }
        String ext = StringUtils.substringAfterLast(fileName, ".");
        if (StringUtils.isBlank(ext)) {
            return "jpg";
        }
        return ext;
    }
}
